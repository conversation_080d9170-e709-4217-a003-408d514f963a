package com.legendshop.app.service.impl;

import com.google.code.kaptcha.Producer;
import com.legendshop.app.service.CaptchaService;
import com.legendshop.common.data.cache.util.RedisUtil;
import com.legendshop.common.data.cache.util.StringRedisUtil;
import com.legendshop.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CaptchaServiceImpl implements CaptchaService {

    @Autowired
    private Producer captchaProducer;

    @Autowired
    private RedisUtil redisUtil;

    private static final String CAPTCHA_PREFIX = "captcha:";
    private static final long CAPTCHA_EXPIRE_MINUTES = 5;

    @Override
    public Map<String, String> generateCaptcha() {
        // 生成验证码文本
        String captchaText = captchaProducer.createText();
        
        // 生成验证码图片
        BufferedImage image = captchaProducer.createImage(captchaText);
        
        // 将图片转换为Base64
        String captchaImage = convertToBase64(image);
        
        // 生成唯一key
        String captchaKey = UUID.randomUUID().toString();
        
        // 将验证码存入Redis，设置5分钟过期
        redisUtil.setObject(
            CAPTCHA_PREFIX + captchaKey,
            captchaText,
            CAPTCHA_EXPIRE_MINUTES,
            TimeUnit.MINUTES
        );


        String key ="captcha:" + captchaKey;
        log.info("校验验证码，redisKey={}", key);
        Object object = redisUtil.getObject(key);
        String redisCaptcha="";
        if (AppUtils.isNotBlank(object)){
            redisCaptcha=object.toString();
        }
        log.info("从Redis获取到的验证码={}", redisCaptcha);

        // 返回结果
        Map<String, String> result = new HashMap<>();
        result.put("key", captchaKey);
        result.put("image", captchaImage);
        
        return result;
    }

    /**
     * 将BufferedImage转换为Base64字符串
     * @param image 图片对象
     * @return Base64编码的图片字符串
     */
    private String convertToBase64(BufferedImage image) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "jpg", outputStream);
            byte[] imageBytes = outputStream.toByteArray();
            return "data:image/jpeg;base64," + Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            throw new RuntimeException("验证码图片转换失败", e);
        }
    }
} 