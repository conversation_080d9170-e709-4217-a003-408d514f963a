/*
 *
 * LegendShop 多用户商城系统
 *
 *  版权所有,并保留所有权利。
 *
 */
package com.legendshop.business.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.legendshop.bestsign.api.client.BestSignClient;
import cn.legendshop.common.core.constant.HttpStatusConstants;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.IndustrialTypeEnum;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyConstant;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyLogDTO;
import cn.legendshop.common.rabbitmq.utils.ThirdLogSendMsgUtil;
import cn.legendshop.common.utils.AESUtil;
import cn.legendshop.tender.api.client.UserCompanyVendorInfoClient;
import cn.legendshop.tender.api.dto.UserCompanyVendorInfoDTO;
import cn.legendshop.user.api.client.ThirdPartyLoginClient;
import cn.legendshop.user.api.client.ThirdPartyUserClient;
import cn.legendshop.user.api.client.UserServiceClient;
import com.legendshop.base.config.PropertiesUtil;
import com.legendshop.base.constants.ErpLoginTargetEnum;
import com.legendshop.base.event.EventProcessor;
import com.legendshop.base.event.SendSMSEvent;
import com.legendshop.base.event.ShortMessageInfo;
import com.legendshop.base.exception.BusinessException;
import com.legendshop.base.helper.ResourceBundleHelper;
import com.legendshop.base.model.UserMessages;
import com.legendshop.base.util.CommonServiceUtil;
import com.legendshop.base.util.ValidationUtil;
import com.legendshop.business.page.FowardPage;
import com.legendshop.business.page.FrontPage;
import com.legendshop.business.page.RedirectPage;
import com.legendshop.client.AuthTokenClient;
import com.legendshop.common.data.cache.util.RedisUtil;
import com.legendshop.core.base.BaseController;
import com.legendshop.core.constant.PathResolver;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.model.ErrorForm;
import com.legendshop.model.ValidationMessage;
import com.legendshop.model.constant.*;
import com.legendshop.model.dto.UserResultDto;
import com.legendshop.model.dto.response.ThirdLoginResponseDTO;
import com.legendshop.model.entity.*;
import com.legendshop.model.entity.icard.ICard;
import com.legendshop.model.form.UserForm;
import com.legendshop.model.form.UserMobileForm;
import com.legendshop.model.securityCenter.UserInformation;
import com.legendshop.model.vo.QuickLoginVO;
import com.legendshop.security.UserManager;
import com.legendshop.security.model.SecurityUserDetail;
import com.legendshop.security.service.LoginService;
import com.legendshop.spi.manager.MailManager;
import com.legendshop.spi.service.*;
import com.legendshop.util.AppUtils;
import com.legendshop.util.HttpUtil;
import com.legendshop.util.JSONUtil;
import com.legendshop.util.RandomStringUtils;
import com.legendshop.web.helper.IPHelper;
import com.legendshop.web.util.ValidateCodeUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.legendshop.util.AppUtils.isNotBlank;

/**
 * 用户控制器。
 */
@Controller
public class UserController extends BaseController {
	/**
	 * The log.
	 */
	private final Logger log = LoggerFactory.getLogger(UserController.class);

	@Autowired
	private MailManager mailManager;
	@Autowired
	private LoginService loginService;
	@Autowired
	private SMSLogService smsLogService;
	@Autowired
	private BestSignClient bestSignClient;
	@Autowired
	private AuthTokenClient authTokenClient;
	@Autowired
	private UserServiceClient userServiceClient;
	@Autowired
	private ShopDetailService shopDetailService;
	@Autowired
	private UserDetailService userDetailService;
	@Autowired
	private ErrorHandleSerivce errorHandleSerivce;
	@Autowired
	private SystemConfigService systemConfigService;
	@Autowired
	private ThirdLogSendMsgUtil thirdLogSendMsgUtil;
	@Autowired
	private SensitiveWordService sensitiveWordService;
	@Autowired
	private ThirdPartyLoginClient thirdPartyLoginClient;
	@Autowired
	private UserCenterPersonInfoService userCenterPersonInfoService;
	@Autowired
	private UserCompanyVendorInfoClient userCompanyVendorInfoClient;

	@Autowired
	private ICardService iCardService;

	@Autowired
	private PropertiesUtil propertiesUtil;

	@Autowired
	private RedisUtil redisUtil;

	// 招标平台通过接口导入的供应商默认ip为*********，用做第一次登录识别，登录验证手机后会改为127.0.0.1
	private final String tenderRegIp = "*********";

	/**
	 * 短信发送者
	 */
	@Resource(name = "sendSMSProcessor")
	private EventProcessor<ShortMessageInfo> sendSMSProcessor;


	@Autowired
	private PasswordEncoder passwordEncoder;

	@Autowired
	private ThirdPartyUserClient thirdPartyUserClient;


	private final String lfzxUrl = "http://mq.nisco.cn/erp/mqz/mqzcDataToMRO";

	private final String erpLoginUrl = "https://dmzesb.nisco.cn/dmzesb/ZHCGW/BIGPURCHASESYS/services/IQCommonPostInterface?_pageId=iqjcForMroFunc&_action=erpLogin";

	private final String tenderLoginUrl = "http://mq.nisco.cn/erp/mqz/mqzcDataToMRO";

	private final String newTenderLoginUrl = "https://www.njxic.com/bidprocurement/common-login/login/mroLogin";


//	private final String tenderLoginUrl1 = "http://trade.nisco.cn/bidprocurement/common-login/login/mroLogin";


	private String key = "ab3456ha89012346";

	private String iv = "ab3456ha89012346";

	private static final String SMS_RATE_LIMIT_KEY_PREFIX = "sms_rate_limit:";
	private static final String SMS_DAILY_COUNT_KEY_PREFIX = "sms_daily_count:";
	private static final Integer DAY_LIMIT = 3;

	/**
	 * 弹出层用户登录
	 */
	@ResponseBody
	@RequestMapping("/userLogin")
	public QuickLoginVO userLogin(HttpServletRequest request, HttpServletResponse response, String userName, String pwd, String randNum) {
		// 检查图片验证码是否正确
		if (!ValidateCodeUtil.validateCode(randNum, request.getSession())) {
			return QuickLoginVO.error(QuickLoginStatusEnum.VERIFY_CODE_ERROR);
		}
		//解密
		//密码解密
		String decodePassowrd =  AESUtil.decrypt(pwd, "UTF-8", key, iv);
		pwd = decodePassowrd;
		Authentication authentication;
		// 判断是否为第三方登录(ERP、招标平台)
		ThirdLoginResponseDTO thirdLoginResponse = thirdLogin(userName, pwd);
		if (thirdLoginResponse.getThirdLogin() == ThirdLoginResponseEnum.NOT_THIRD_LOGIN) {
			authentication = loginService.onAuthentication(request, response, userName, pwd);
		} else {
			if (thirdLoginResponse.isStatus()) {
				UserDetail userDetail = userDetailService.getUserByUniqueProof(thirdLoginResponse.getUsername());
				if (userDetail == null) {
					// 用户未绑定 -- 需要跳转到用户信息不全页面
					if (thirdLoginResponse.getThirdLogin() == ThirdLoginResponseEnum.ERP) {
						return new QuickLoginVO(QuickLoginStatusEnum.ERP_THIRD_NOT_BIND, userName, pwd);
					} else {
						return new QuickLoginVO(QuickLoginStatusEnum.THIRD_LOGIN_EXCEPTION, userName, pwd);
					}
				}
				// 用户绑定
				authentication = loginService.onAuthentication(request, response, userDetail.getUserName(), true);
			} else {
				// 密码错误或其他异常
				log.info("thirdLogin ----- message():{}", thirdLoginResponse.getMessage());
				return new QuickLoginVO(QuickLoginStatusEnum.THIRD_LOGIN_EXCEPTION, thirdLoginResponse.getMessage());
			}
		}
		if (AppUtils.isBlank(authentication)) {
			return QuickLoginVO.error(QuickLoginStatusEnum.LOGIN_FAIL);
		} else {
			return QuickLoginVO.error(QuickLoginStatusEnum.LOGIN_SUCCESS);
		}
	}

	@RequestMapping("/thirdSupplement")
	public String thirdSupplement(HttpServletRequest request, @RequestParam("no") String no, @RequestParam("password") String password, @RequestParam("type") String type) {
		request.setAttribute("no", no);
		request.setAttribute("password", password);
		if (StringUtils.isNotBlank(type) && type.equals("ERP")) {
			return PathResolver.getPath(FrontPage.ERP_INFO_SUPPLEMENT);
		} else {
			return PathResolver.getPath(FrontPage.TENDER_INFO_SUPPLEMENT);
		}
	}

	/**
	 * 弹出层快速注册
	 */
	@ResponseBody
	@RequestMapping("/quickregister")
	public String quickregister(HttpServletRequest request, HttpServletResponse response, UserMobileForm userMobileForm) {
		// 检查短信验证码是否正确
		if (!smsLogService.verifyCodeAndClear(userMobileForm.getMobile(), userMobileForm.getMobileCode())) {
			log.error("Incorrect mobile code");
			return Constants.FAIL;
		}
		// 检查图片验证码是否正确
		if (!ValidateCodeUtil.validateCode(userMobileForm.getRandNum(), request.getSession())) {
			log.error("Incorrect validate code");
			return Constants.FAIL;
		}
		boolean existed = userDetailService.isPhoneExist(userMobileForm.getMobile());
		if (existed) {
			// 检查是否重名
			UserMessages messages = new UserMessages();
			messages.addCallBackList(ResourceBundleHelper.getString("errors.phone"));
			request.setAttribute(UserMessages.MESSAGE_KEY, messages);
			request.setAttribute("userForm", userMobileForm);
			return Constants.FAIL;
		}
		User user = userDetailService.saveUserReg(IPHelper.getIpAddr(request), userMobileForm, passwordEncoder.encode(userMobileForm.getPassword()));
		request.setAttribute("userName", userMobileForm.getNickName());
		if (user == null) {
			throw new BusinessException("register user error");
		}
		// 手机注册的用户，用户安全等级+1,手机验证变为1
		userDetailService.updatePhoneVerifn(user.getName());
		try {
			userServiceClient.sendVoucherToUser(user.getId(), user.getUserName(), userMobileForm.getErpNo() == null ? "" : userMobileForm.getErpNo(), false, user.getUserMobile(), SecurityConstants.FROM_IN);
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (Constants.USER_STATUS_ENABLE.equals(user.getEnabled())) {
			// 用户注册即登录
			loginService.onAuthentication(request, response, userMobileForm.getMobile(), userMobileForm.getPassword());
		}
		return Constants.SUCCESS;
	}

	/**
	 * Login.
	 *
	 * @param request  the request
	 * @param response the response
	 * @return the string
	 */
	@RequestMapping("/login")
	public String login(HttpServletRequest request, HttpServletResponse response) {
		request.setAttribute("key", key);
		request.setAttribute("iv", iv);
		//获取参数 跳转参数
		HttpSession session = request.getSession();
		session.setAttribute(Constants.LOGIN_SOURCE_TYPE, request.getParameter(Constants.LOGIN_SOURCE_TYPE));
		return PathResolver.getPath(FrontPage.LOGIN);
	}

	/**
	 * LoginErp.
	 *
	 * @param request  the request
	 * @param response the response
	 * @return the string
	 */
	@RequestMapping("/loginErp")
	public String loginErp(HttpServletRequest request, HttpServletResponse response) {
		request.setAttribute("key", key);
		request.setAttribute("iv", iv);
		return PathResolver.getPath(FrontPage.LOGIN_ERP);
	}

	/**
	 * LoginTender.
	 *
	 * @param request  the request
	 * @param response the response
	 * @return the string
	 */
	@RequestMapping("/loginTender")
	public String loginTender(HttpServletRequest request, HttpServletResponse response) {
		request.setAttribute("key", key);
		request.setAttribute("iv", iv);
		return PathResolver.getPath(FrontPage.LOGIN_TENDER);
	}

	/**
	 * Login.
	 *
	 * @param request  the request
	 * @param response the response
	 * @return the string
	 */
	@RequestMapping("/storeLogin")
	public String storeLogin(HttpServletRequest request, HttpServletResponse response) {
		request.setAttribute("key", key);
		request.setAttribute("iv", iv);
		return PathResolver.getPath(FrontPage.SHOPLOGIN);
	}


	@RequestMapping("/employeeLogin")
	public String employeeLogin(HttpServletRequest request, HttpServletResponse response) {
		request.setAttribute("key", key);
		request.setAttribute("iv", iv);
		return PathResolver.getPath(FrontPage.EMPLOYEE_LOGIN);
	}

	/**
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/memberEmployeeLogin")
	public String memberEmployeeLogin(HttpServletRequest request, HttpServletResponse response) {
		request.setAttribute("key", key);
		request.setAttribute("iv", iv);
		return PathResolver.getPath(FrontPage.MEMBER_EMPLOYEE_LOGIN);
	}


	@RequestMapping("/erpLogin")
	public String erpLogin(HttpServletRequest request, HttpServletResponse response, String token, String companyNumber, String employeeNumber
		, @RequestParam(name = "toCompanyNumber", required = false) String toCompanyNumber,
						   @RequestParam(name = "target", required = false) String target) {
		// 通过auth 验证Token是否正确
		R<Boolean> result = authTokenClient.authSignatureToken(token, SecurityConstants.FROM_IN);
		if (result.getStatus() != HttpStatusConstants.HTTP_RES_CODE_200.intValue()) {
			UserMessages userMessages = new UserMessages();
			userMessages.setTitle("参数异常");
			userMessages.setDesc("token 已失效");
			request.setAttribute("User_Messages", userMessages);
			return "/common/error";
		}
		String path = "";

		UserDetail userDetail = userDetailService.getUserDetailByUniformNo(companyNumber);
		if (AppUtils.isBlank(userDetail)) {
			UserMessages userMessages = new UserMessages();
			userMessages.setTitle("账号异常");
			userMessages.setDesc("该公司税号查找不到用户信息");
			request.setAttribute("User_Messages", userMessages);
			return "/common/error";
		}
		if (ErpLoginTargetEnum.IM.getValue().equals(target)) {
			if (StringUtils.isBlank(toCompanyNumber)) {
				UserMessages userMessages = new UserMessages();
				userMessages.setTitle("账号异常");
				userMessages.setDesc("商家信息不能为空");
				request.setAttribute("User_Messages", userMessages);
				return "/common/error";
			}
			UserDetail toUser = userDetailService.getUserDetailByUniformNo(toCompanyNumber);
			if (null == toUser || null == toUser.getShopId()) {
				UserMessages userMessages = new UserMessages();
				userMessages.setTitle("账号异常");
				userMessages.setDesc("没找到商家信息");
				request.setAttribute("User_Messages", userMessages);
				return "/common/error";
			}
			path = "redirect:/p/im/index/" + toUser.getShopId();
		} else {
			path = PathResolver.getPath(FowardPage.HOME_TENDER_LIST);
		}
		SecurityUserDetail user = UserManager.getUser(request);
		if (AppUtils.isBlank(user)) {
			//免密登录，ERP登录主要调用添加商品到需求列表，不需要有后台权限
			loginService.onAuthenticationForErp(request, response, userDetail.getUserName(), employeeNumber);
		} else {
			//如果是同一个ERP帐号，不需要叫用户重新登录，
			if (!user.getNickName().equals(employeeNumber)) {
				path = PathResolver.getPath(FowardPage.HOME_LOGOUT);
			}
		}
		return path;
	}


	/**
	 * 找回密码
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/retrievepassword")
	public String retrievepassword(HttpServletRequest request, HttpServletResponse response) {
		String result = PathResolver.getPath(FrontPage.RETRIEVE_PD);
		return result;
	}

	/**
	 * 去往错误界面
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/failPage/{errorCode}")
	public String failPageWithErrorCoe(HttpServletRequest request, HttpServletResponse response, @PathVariable String errorCode) {
		ErrorForm errorForm = errorHandleSerivce.getErrorForm(errorCode);
		request.setAttribute("errorForm", errorForm);
		return PathResolver.getPath(FrontPage.FAIL_PAGE);
	}

	@RequestMapping("/failPage")
	public String failPage(HttpServletRequest request, HttpServletResponse response) {
		ErrorForm errorForm = errorHandleSerivce.getErrorForm("defalut");
		request.setAttribute("errorForm", errorForm);
		return PathResolver.getPath(FrontPage.FAIL_PAGE);
	}

	/**
	 * 去往验证身份页面
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/proveStatus")
	public String proveStatus(HttpServletRequest request, HttpServletResponse response, String name) {
		UserInformation userInfo = null;
		if (checkPhone(name)) {
			userInfo = userDetailService.getUserInfoByPhone(name);
		} else if (checkEmail(name)) {
			userInfo = userDetailService.getUserInfoByMail(name);
		} else {
			userInfo = userDetailService.getUserInfo(name); // user name
		}

		if (AppUtils.isNotBlank(userInfo)) {
			// 处理手机号码
			if (AppUtils.isNotBlank(userInfo.getUserMobile())) {
				String mobile = mobileSubstitution(userInfo.getUserMobile());
				request.setAttribute("mobileScreen", mobile);
			}

			// 处理邮箱号码
			if (AppUtils.isNotBlank(userInfo.getUserMail())) {
				String mail = mailSubstitution(userInfo.getUserMail());
				userInfo.setUserMail(mail);
			}

			if (userInfo.getPhoneVerifn() == 0 && userInfo.getMailVerifn() == 0) {
				return PathResolver.getPath(FrontPage.INTERCEPT);
			}

			request.setAttribute("userInfo", userInfo);
			return PathResolver.getPath(FrontPage.PROVE_STATUS);

		} else {
			return PathResolver.getPath(FrontPage.INTERCEPT);
		}
	}

	@RequestMapping(value = "/sendSMSCode", method = RequestMethod.POST)
	public @ResponseBody
	String sendSMSCode(HttpServletRequest request, HttpServletResponse response, String userName, String mobile, String randNum) {

		// 检查图片验证码是否正确
		if (!ValidateCodeUtil.validateCodeAndKeep(randNum, request.getSession())) {
			log.error("Incorrect validate code");
			return Constants.FAIL;
		}

		String mobileCode = CommonServiceUtil.getRandomSMSCode();
		// 通过用户名查找用户手机，然后发送短信验证码
		UserInformation userInfo = userDetailService.getUserInfo(userName);
		if (AppUtils.isNotBlank(userInfo)){
			mobile = userInfo.getUserMobile();
		}
		sendSMSProcessor.process(new SendSMSEvent(mobile, mobileCode, userName, SendSMSCodeSourceEnum.FORGET).getSource());
		// 将验证码传入db
		userDetailService.updateUserSecurity(userName, mobileCode);

		return Constants.SUCCESS;
	}

	/**
	 * 手机注册获取验证码
	 */
	@RequestMapping(value = "/sendSMSCodeByReg", method = RequestMethod.POST)
	public @ResponseBody String sendSMSCodeByReg(HttpServletRequest request, HttpServletResponse response, String tel, String randNum, String userName) {
		// 检查手机号是否有效 hutool
		if (!ValidationUtil.checkPhone(tel)) {
			return "手机号码格式不正确";
		}
		// 检查图片验证码是否正确
		if (!ValidateCodeUtil.validateCodeAndKeep(randNum, request.getSession())) {
			return "图片验证码不正确";
		}

		// 检查发送频率
		String rateLimitKey = SMS_RATE_LIMIT_KEY_PREFIX + tel;
		String dailyCountKey = SMS_DAILY_COUNT_KEY_PREFIX + tel;

		// Check if the user has sent an SMS in the last minute
		if (ObjectUtil.isNotEmpty(redisUtil.getObject(rateLimitKey))) {
			return "1分钟内只能发送1次短信";
		}

		// Check if the user has sent more than 3 SMS in the last day
		Integer dailyCount = (Integer) redisUtil.getObject(dailyCountKey);
		if (dailyCount != null && dailyCount >= DAY_LIMIT) {
			return "一天最多只能发送" + DAY_LIMIT + "次短信";
		}

		String mobileCode = CommonServiceUtil.getRandomSMSCode();
		SecurityUserDetail user = UserManager.getUser(request);
		// 如果已经登录的拿，没有登录的，用手机号拿
		if (user != null) {
			userName = user.getUsername();
		}else{
			User userByMobile = userDetailService.getUserByMobile(tel);
			if (AppUtils.isNotBlank(userByMobile) ){
				userName = userByMobile.getName();
			}else{
				return "手机号码未注册";
			}
		}

		sendSMSProcessor.process(new SendSMSEvent(tel, mobileCode, userName, SendSMSCodeSourceEnum.REG).getSource());

		// Update rate limits
		// Set the rate limit key with an expiration of 1 minute
		redisUtil.setObject(rateLimitKey, true, 1L, TimeUnit.MINUTES);

		// Increment the daily count and set the expiration to 1 day if not already set
		int newDailyCount = (dailyCount == null) ? 1 : dailyCount + 1;
		redisUtil.setObject(dailyCountKey, newDailyCount, 1L, TimeUnit.DAYS);
		log.info("发送短信验证码成功，将验证码传入db userName{}，mobileCode{}",userName,mobileCode);
		// 将验证码传入db
		userDetailService.updateUserSecurity(userName, mobileCode);
		return Constants.SUCCESS;
	}

	/**
	 * 手机注册获取验证码--不需要验证码
	 */
	@RequestMapping(value = "/sendSMSCodeByRegNotVer", method = RequestMethod.POST)
	public @ResponseBody
	String sendSMSCodeByRegNotVer(HttpServletRequest request, HttpServletResponse response, String tel, String userName) {

		String mobileCode = CommonServiceUtil.getRandomSMSCode();

		SecurityUserDetail user = UserManager.getUser(request);

		if (user != null) {
			userName = user.getUsername();
		}
		sendSMSProcessor.process(new SendSMSEvent(tel, mobileCode, userName, SendSMSCodeSourceEnum.REG).getSource());
		return Constants.SUCCESS;
	}

	/**
	 * 第三方手机注册获取验证码
	 */
	@RequestMapping(value = "/sendSMSCodeByThirdReg", method = RequestMethod.POST)
	public @ResponseBody
	String sendSMSCodeByThirdReg(HttpServletRequest request, HttpServletResponse response, String tel, String randNum) {

		String mobileCode = CommonServiceUtil.getRandomSMSCode();
		SecurityUserDetail user = UserManager.getUser(request);

		String userName = "";
		if (user != null) {
			userName = user.getUsername();
		}

		sendSMSProcessor.process(new SendSMSEvent(tel, mobileCode, userName, SendSMSCodeSourceEnum.THIRD_REG).getSource());
		return Constants.SUCCESS;
	}

	/**
	 * 发送邮件
	 *
	 * @param request
	 * @param response
	 * @param userName
	 * @return
	 */
	@RequestMapping(value = "/confirmationMail", method = RequestMethod.POST)
	public String confirmationMail(HttpServletRequest request, HttpServletResponse response, String userName) {
		User user = userDetailService.getUserByName(userName);
		String userEmail = userDetailService.getUserEmail(user.getId());
		String code = getRandomString(50);
		mailManager.verifyEmail(user, userEmail, code);

		// 将邮箱及验证码 更新到 ls_usr_security
		userDetailService.updateSecurityMail(userName, code, userEmail);
		return PathResolver.getPath(FrontPage.SEND_EMAIL_PAGE);
	}

	/**
	 * 找回密码 对比验证码是否一致
	 *
	 * @param request
	 * @param response
	 * @param code
	 * @return
	 */
	@RequestMapping(value = "/verifySMSCode", method = RequestMethod.POST)
	public @ResponseBody
	Boolean verifySMSCode(HttpServletRequest request, HttpServletResponse response, String userName, String mobile,String code) {
		//如果没有用户名，用手机号获取用户名做校验
		if(AppUtils.isBlank(userName)){
			if(AppUtils.isNotBlank(mobile)){
				User userByMobile = userDetailService.getUserByMobile(mobile);
				if(AppUtils.isNotBlank(userByMobile)){
					userName = userByMobile.getUserName();
				}else{
					return false;
				}
			}else{
				return false;
			}
		}
		//校验验证码
		log.info("userName"+"开始校验验证码");
		Boolean test = userDetailService.verifySMSCode(userName, code);
		if (test) {
			UserDetail userDetail = userDetailService.getUserDetail(userName);
			// 判断用户和手机是否为空
			if (AppUtils.isNotBlank(userDetail) && AppUtils.isNotBlank(userDetail.getUserMobile())) {
				// 详细校验安全码
				test = smsLogService.verifyMobileCode(code, userDetail.getUserMobile());
			} else
				test = false;

		}
		return test;
	}

	/**
	 * 去往修改密码页面
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/changePwd", method = RequestMethod.POST)
	public String changePwd(HttpServletRequest request, HttpServletResponse response, String tel, String code) {
		User userByMobile = userDetailService.getUserByMobile(tel);
		if (AppUtils.isBlank(userByMobile)){
			return PathResolver.getPath(FrontPage.FAIL_PAGE);
		}
		String userName = userByMobile.getUserName();
		request.setAttribute("userName", userName);
		request.setAttribute("code", code);
		// 判断当前用户有没有这个code,防止页面直接修改
		String redisCode = (String)redisUtil.getObject(userName);
		// 如果redis的code过期、或者使用其他的code不跳转
		if (StringUtils.isNotBlank(redisCode) &&  code.equals(redisCode)) {
			return PathResolver.getPath(FrontPage.CHANGE_PWD);
		}else{
			return PathResolver.getPath(FrontPage.FAIL_PAGE);
		}
	}

	/**
	 * 邮件链接回来后，直接去密码修改页面
	 *
	 * @param request
	 * @param response
	 * @param userName
	 * @param code
	 * @return
	 */
	@RequestMapping(value = "/setNewPwd/{userName}/{code}")
	public String setNewPwd(HttpServletRequest request, HttpServletResponse response, @PathVariable String userName, @PathVariable String code) {
		// 验证链接的是否为24小时内，次数为0
		UserSecurity security = userDetailService.getUserSecurity(userName);

		if (AppUtils.isNotBlank(security)) {
			Date end = new Date();
			long day = (end.getTime() - security.getSendMailTime().getTime()) / (24 * 60 * 60 * 1000);
			// 判断是否为24小时内以及是第一次点击
			if (day >= 1 || security.getTimes() >= 1) {
				return PathResolver.getPath(FrontPage.FAIL_PAGE);
			}

			request.setAttribute("userName", userName);
			request.setAttribute("code", code);
			return PathResolver.getPath(FrontPage.SETNEWPWD);
		} else {
			return PathResolver.getPath(FrontPage.FAIL_PAGE);
		}
	}

	/**
	 * 成功页面
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/pwdResetSuccess", method = RequestMethod.POST)
	public String success(HttpServletRequest request, HttpServletResponse response, String newPassword, String userName, String code) {
		if (AppUtils.isBlank(code) || AppUtils.isBlank(userName)) {
			return PathResolver.getPath(FrontPage.PWD_RESET_FAIL);
		}
		// 判断当前用户有没有这个code,防止页面直接修改
		String redisCode = (String)redisUtil.getObject(userName);
		// 如果redis的code过期、或者使用其他的code 直接失败
        if (!StringUtils.isNotBlank(redisCode) || !code.equals(redisCode)) {
			return PathResolver.getPath(FrontPage.PWD_RESET_FAIL);
        }
        // 验证邮箱验证码
		boolean isMailCodeExists = userDetailService.isMailCodeExist(userName, code);
		if (isMailCodeExists) {
			Boolean result = userDetailService.updateNewPassword(userName, passwordEncoder.encode(newPassword));
			if (!result) {
				return PathResolver.getPath(FrontPage.PWD_RESET_FAIL);
			} else {
				// 清除邮箱验证码
				userDetailService.clearMailCode(userName);
				return PathResolver.getPath(FrontPage.PWD_RESET_SUCCESS);
			}
		} else if (checkSecurityCode(userName, code)) {// 验证短信验证码
			Boolean result = userDetailService.updateNewPassword(userName, passwordEncoder.encode(newPassword));
			if (!result) {
				return PathResolver.getPath(FrontPage.PWD_RESET_FAIL);
			} else {
				// 清除短信验证码
				UserDetail userDetail = userDetailService.getUserDetail(userName);
				log.info("修改密码成功！");
				if (userDetail.getUserRegip().equals("*********")) {
					String ip = IPHelper.getIp();
					userDetail.setUserRegip(ip == null ? "127.0.0.1" : ip);
					userDetailService.updateUserDetail(userDetail);
					log.info("修改ip成功,ip为：{}", ip);
				}
				userDetailService.clearValidateCode(userName);
				return PathResolver.getPath(FrontPage.PWD_RESET_SUCCESS);
			}
		} else {
			return PathResolver.getPath(FrontPage.PWD_RESET_FAIL);
		}
	}

	/**
	 * 检查手机验证码和数据库中的是否一致
	 *
	 * @param userName
	 * @param securityCode
	 * @return
	 */
	private boolean checkSecurityCode(String userName, String securityCode) {
		if (AppUtils.isBlank(securityCode)) {
			return false;
		}
		UserSecurity userSecurity = userDetailService.getUserSecurity(userName);
		if (userSecurity == null || userSecurity.getValidateCode() == null) {
			return false;
		}
		return userSecurity.getValidateCode().equals(securityCode);
	}

	/**
	 * 用户注册动作 在旧模版中使用
	 *
	 * @param request  the request
	 * @param response the response
	 * @param userForm the user form
	 * @return the string
	 */
	@RequestMapping("/userReg")
	public String doUserReg(HttpServletRequest request, HttpServletResponse response, UserForm userForm) {
		// 检查图片验证码是否正确
		if (!ValidateCodeUtil.validateCode(userForm.getRandNum(), request.getSession())) {
			log.error("Incorrect validate code");
			return PathResolver.getPath(RedirectPage.REG);
		}

		ValidationMessage message = userForm.validate();
		if (message.isFailed()) {
			log.error("register failed: " + message);
			return PathResolver.getPath(RedirectPage.REG);
		}

		if (AppUtils.isBlank(userForm.getUserName()) || !ValidationUtil.checkUserName(userForm.getUserName())) {
			log.error("register failed: 用户名不能为空或非法");
			return PathResolver.getPath(RedirectPage.REG);
		}

		String ip = IPHelper.getIpAddr(request);
		UserResultDto userResultDto = userDetailService.saveUserReg(ip, userForm, passwordEncoder.encode(userForm.getPassword()));
		if (!userResultDto.success()) {
			throw new BusinessException("register user error");
		}
		if (Constants.USER_STATUS_ENABLE.equals(userResultDto.getUser().getEnabled())) {
			// 用户注册即登录
			loginService.onAuthentication(request, response, userForm.getNickName(), userForm.getPassword());
		}

		return PathResolver.getPath(RedirectPage.AFTER_OPERATION);
	}

	/**
	 * 加载开店页面,该动作需要用户登录
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */

	@RequestMapping("/openShop")
	public String openShop(HttpServletRequest request, HttpServletResponse response) throws Exception {

		SecurityUserDetail user = UserManager.getUser(request);

		request.setAttribute("key", key);
		request.setAttribute("iv", iv);
		if (user == null) {
			return PathResolver.getPath(FrontPage.LOGIN);
		}

		// 用户必须登录，不再用spring security来限制，因为买家和商家一起访问该页面
		if (!UserManager.hasFunction(user, FunctionEnum.FUNCTION_USER.value())) {
			return PathResolver.getPath(FrontPage.LOGIN);
		}

		// 商家的所有者
		String shopUserId = UserManager.getShopUserId(user);
		ShopDetail shopDetail = null;
		if (shopUserId != null) {
			shopDetail = shopDetailService.getShopDetailByUserId(shopUserId);
		}
		if (shopDetail == null) {// 开店页面
			return PathResolver.getPath(FrontPage.OPEN_SHOP);
		} else {
			if (ShopStatusEnum.NORMAL.value().equals(shopDetail.getStatus())) { // 审核成功后提醒
				if (UserManager.isShopUser(user)) {
					return PathResolver.getPath(RedirectPage.SHOP_SETTING);
				} else {
					return PathResolver.getPath(FrontPage.AFTER_OPENSHOP);
				}

			} else {// 审核中
				request.setAttribute("shopDetail1", shopDetail);
				return PathResolver.getPath(FrontPage.OPEN_SHOP);
			}
		}

	}

	/**
	 * 用户在登录后的开店流程
	 */
	@RequestMapping("/p/doOpenShop")
	public String doOpenShop(HttpServletRequest request, HttpServletResponse response, ShopDetail shopDetail) {
		// 检查图片验证码是否正确
		if (!ValidateCodeUtil.validateCode(shopDetail.getRandNum(), request.getSession())) {
			log.error("Incorrect validate code");
			return PathResolver.getPath(RedirectPage.OPEN_SHOP);
		}
		if (AppUtils.isBlank(shopDetail.getContactName())) {
			log.error("ContactName is null");
			return PathResolver.getPath(RedirectPage.OPEN_SHOP);
		}
		if (AppUtils.isBlank(shopDetail.getContactMobile())) {
			log.error("ContactMobile is null");
			return PathResolver.getPath(RedirectPage.OPEN_SHOP);
		}

		if (AppUtils.isBlank(shopDetail.getSiteName())) {
			log.error("SiteName is null");
			return PathResolver.getPath(RedirectPage.OPEN_SHOP);
		}
		if (AppUtils.isBlank(shopDetail.getIdCardNum())) {
			log.error("IdCardNum is null");
			return PathResolver.getPath(RedirectPage.OPEN_SHOP);
		}

		shopDetail.setIp(IPHelper.getIpAddr(request));

		SecurityUserDetail user = UserManager.getUser(request);
		String userId = user.getUserId();
		String userName = user.getUsername();

		boolean result = false;
		if (AppUtils.isNotBlank(shopDetail.getShopId())) {
			result = shopDetailService.reApplyOpenShop(shopDetail, userId, userName);
		} else {
			result = userDetailService.saveShopReg(userName, shopDetail);
		}
		if (!result) {
			throw new BusinessException("register shop error");
		}
		// 返回申请开店成功页面
		return PathResolver.getPath(RedirectPage.OPENSHOP_SUCCES);
	}

	@RequestMapping("/p/doOpenShopSuc")
	public String doOpenShopSuc(HttpServletRequest request, HttpServletResponse response) {
		return PathResolver.getPath(FrontPage.OPEN_SUCCESS);
	}

	/**
	 * 用户通过手机注册
	 *
	 * @param request        the request
	 * @param response       the response
	 * @param userMobileForm 用户注册表达
	 * <AUTHOR>
	 * @time 2015-11-11
	 */
	@Transactional
	@RequestMapping("/userRegByPhone")
	public String userRegByPhone(HttpServletRequest request, HttpServletResponse response, UserMobileForm userMobileForm) {

		// 检查图片验证码是否正确
		if (!ValidateCodeUtil.validateCode(userMobileForm.getRandNum(), request.getSession())) {
			log.error("Incorrect validate code");
			return PathResolver.getPath(RedirectPage.REG);
		}

		boolean existed = userDetailService.isPhoneExist(userMobileForm.getMobile());
		if (existed) {
			// 检查是否重名
			UserMessages messages = new UserMessages();
			messages.addCallBackList(ResourceBundleHelper.getString("errors.phone"));
			request.setAttribute(UserMessages.MESSAGE_KEY, messages);
			request.setAttribute("userForm", userMobileForm);
			return PathResolver.getPath(RedirectPage.REG);
		}

		if (AppUtils.isBlank(userMobileForm.getUserName()) || !ValidationUtil.checkUserName(userMobileForm.getUserName())) {
			log.error("register failed: 用户名不能为空或非法");
			return PathResolver.getPath(RedirectPage.REG);
		}
		User user = userDetailService.saveUserReg(IPHelper.getIpAddr(request), userMobileForm, passwordEncoder.encode(userMobileForm.getPassword()));
		request.setAttribute("userName", userMobileForm.getNickName());
		if (user == null) {
			throw new BusinessException("register user error");
		}
		if (StringUtils.isNotBlank(userMobileForm.getOpenId())) {
			Boolean bindUserResult = thirdPartyLoginClient.bindUser(user.getId(), user.getUserName(), userMobileForm.getOpenId(), SecurityConstants.FROM_IN).getResult();
			if (!bindUserResult) {
				throw new BusinessException("注册失败：用户绑定openId失败");
			}
		}

		//发放新人代金券
		userServiceClient.sendVoucherToUser(user.getId(), user.getName(), userMobileForm.getErpNo() == null ? "" : userMobileForm.getErpNo(), false, user.getUserMobile(), SecurityConstants.FROM_IN);
		// 手机注册的用户，用户安全等级+1,手机验证变为1
		userDetailService.updatePhoneVerifn(user.getName());


		if (Constants.USER_STATUS_ENABLE.equals(user.getEnabled())) {
			// 用户注册即登录
			loginService.onAuthentication(request, response, userMobileForm.getMobile(), userMobileForm.getPassword());

			// 注册环信，账号为手机号
			return PathResolver.getPath(RedirectPage.AFTER_OPERATION) + "?userName=" + user.getUserName();
		}
		return null;
	}

	/**
	 * Erp员工登录
	 */
	@RequestMapping("/erpStaffLogin")
	public String erpStaffLogin(HttpServletRequest request, HttpServletResponse response, String erpNo, String password) {

		//密码解密
		String decodePassowrd = AESUtil.decrypt(password, "UTF-8", key, iv);
		password = decodePassowrd;

		// 请求Erp接口，判断Erp员工工号与密码
		Map<String, Object> params = new HashMap<>();
		params.put("username", erpNo);
		params.put("password", password);
		String pattern = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])[A-Za-z\\d!@#$%^&*(),.?\":{}|<>]{8,20}$";
		if(StringUtils.isBlank(password) || !password.matches(pattern)) {
			throw new cn.legendshop.common.core.exception.BusinessException("密码应为8-20位字母、须包含特殊字符、数字和大小写字母");
		}
		String result = null;
		int httpStatus = 500;
		Map dataMap;
		try {
			request.setAttribute("key", key);
			request.setAttribute("iv", iv);
			HttpRequest httpRequest = cn.hutool.http.HttpUtil.createPost(erpLoginUrl);
			httpRequest.header("Content-Type", "application/x-www-form-urlencoded");
			httpRequest.form(params);
			HttpResponse httpResponse = httpRequest.execute();
			result = httpResponse.body();
			httpStatus = 200;
			if (AppUtils.isBlank(result)) {
				log.error("register failed:ERP接口请求链接错误");
				return PathResolver.getPath(FrontPage.LOGIN);
			}
			dataMap = JSONUtil.getMap(result);
		} catch (Exception e) {
			throw new cn.legendshop.common.core.exception.BusinessException("ERP员工登录接口请求失败");
		} finally {
			thirdLogSendMsgUtil.sendThirdPartyLogMQ(new ThirdPartyLogDTO("legendshop-web-service",
				ThirdPartyConstant.ERP, erpLoginUrl, JSONUtil.getJson(params), httpStatus, result));
		}
		String statusCode = (String) dataMap.get("statusCode");
		String message = (String) dataMap.get("message");
		if (StringUtils.isBlank(statusCode) || !"200".equals(statusCode)) {
			log.info(message);
			request.setAttribute("userNameAndPasswordVer", 1);
			//request.setAttribute("message", message);
			return PathResolver.getPath(FrontPage.LOGIN);
		}
//		// 请求成功且用户信息正确，创建平台用户
		// 查询数据库，查询用户信息，第一次登录需要填写手机与用户名
		UserDetail userDetail = userDetailService.getUserDetailByErpNo(erpNo);

		if (AppUtils.isBlank(userDetail)) {
			// 没有登录过,需要补全信息
			request.setAttribute("no", erpNo);
			request.setAttribute("password", password);
			return PathResolver.getPath(FrontPage.ERP_INFO_SUPPLEMENT);
		} else {

			//关联员工卡
			ICard iCard = new ICard();
			iCard.setJobNo(erpNo);
			iCard.setPassword(password);
			try {
				//异常处理，不影响账号创建
				R<Object> icardResult = iCardService.bindingICard(iCard, userDetail.getUserId());
				if (!icardResult.isSuccess()) {
					log.info("erp账号登录绑定员工卡失败：{}", icardResult.getMsg());
				}
			} catch (Exception e) {
				log.info("erp账号登录绑定员工卡失败：{}", e.getMessage());
			}

			loginService.onAuthentication(request, response, userDetail.getUserName(), true);

//			return PathResolver.getPath(FrontPage.HOME);
			return loginRedirect(request);
		}
	}


	@RequestMapping("/thirdLogin")
	public String thirdLogin(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "code") String code) {
		R<String> verifyLoginCode = thirdPartyUserClient.verifyLoginCode(code,true, SecurityConstants.FROM_IN);
		if (!verifyLoginCode.isSuccess()){
            log.error("第三方登录code 验证失败! code:{}",code);
			request.setAttribute("userNameAndPasswordVer", 1);
			request.setAttribute("message", verifyLoginCode.getMsg());
			return PathResolver.getPath(FrontPage.LOGIN);
		}
		String userId = verifyLoginCode.getResult();
		// 查询数据库，查询用户信息，第一次登录需要填写手机与用户名
		UserDetail userDetail = userDetailService.getUserDetailById(userId);
		loginService.onAuthentication(request, response, userDetail.getUserName(), true);
//		return PathResolver.getPath(FrontPage.HOME);
		return loginRedirect(request);
	}




	/**
	 * 招标平台员工登录
	 */
	@RequestMapping("/tenderStaffLogin")
	public String tenderStaffLogin(HttpServletRequest request, HttpServletResponse response, String userNo, String password) {

		//密码解密
		String decodePassowrd = AESUtil.decrypt(password, "UTF-8", key, iv);
		password = decodePassowrd;

		request.setAttribute("key", key);
		request.setAttribute("iv", iv);

		Map<String, Object> tenderloginResult = new HashMap<>();
		Map<String, Object> newTenderloginResult = new HashMap<>();

		String twoTendLoginType = "^M[A-Z0-9]{6,7}$";
		Pattern twoTendLoginTypePattern = Pattern.compile(twoTendLoginType);


//		request.setAttribute("userNameAndPasswordVer", 1);
//		request.setAttribute("message", newTenderloginResult.get("msg"));
//		log.error("register failed:招标平台接口2登录失败!");
//		//添加招标平台登录的提示信息
//		return PathResolver.getPath(FrontPage.LOGIN);


//		request.setAttribute("userNameAndPasswordVer", 1);
//		request.setAttribute("message", newTenderloginResult.get("msg"));
//		return PathResolver.getPath(FrontPage.LOGIN);


		//不去掉M校验新接口
		log.error("新招标平台接口不去掉M校验!");
		newTenderloginResult = newTenderLogin(userNo, password);
		boolean newTenderLoginSuccessResult = (Boolean) newTenderloginResult.get("success");


		if ((Boolean) newTenderloginResult.getOrDefault("isArrearage", false)) {
			log.error("register failed:招标平台接口登录,欠费!");
			request.setAttribute("userNameAndPasswordVer", 1);
			request.setAttribute("message", newTenderloginResult.get("msg"));
			//添加招标平台登录的提示信息
			return PathResolver.getPath(FrontPage.LOGIN);
		}


		//去掉M校验新接口
		if (!newTenderLoginSuccessResult) {
			log.error("新招标平台接口去掉M校验!");
			newTenderloginResult = newTenderLogin(userNo.substring(1), password);
			newTenderLoginSuccessResult = (Boolean) newTenderloginResult.get("success");


			if ((Boolean) newTenderloginResult.getOrDefault("isArrearage", false)) {
				log.error("register failed:招标平台接口登录,欠费!");
				request.setAttribute("userNameAndPasswordVer", 1);
				request.setAttribute("message", newTenderloginResult.get("msg"));
				//添加招标平台登录的提示信息
				return PathResolver.getPath(FrontPage.LOGIN);
			}

			if(!newTenderLoginSuccessResult) {
				request.setAttribute("userNameAndPasswordVer", 1);
				request.setAttribute("message", tenderloginResult.get("msg"));
				log.error("register failed:招标平台接口登录失败!");
				//添加招标平台登录的提示信息
				return PathResolver.getPath(FrontPage.LOGIN);
			}

		}


//		if (!newTenderLoginSuccessResult) {
//			log.error("register failed:新招标平台接口登录失败!");
//			log.error("招标平台接口不去掉M校验!");
//			tenderloginResult = tenderLogin(userNo, password);
//			boolean success = (Boolean) tenderloginResult.get("success");
//			if (!success) {
//				request.setAttribute("userNameAndPasswordVer", 1);
//				request.setAttribute("message", tenderloginResult.get("msg"));
//				log.error("register failed:招标平台接口登录失败!");
//				//添加招标平台登录的提示信息
//				return PathResolver.getPath(FrontPage.LOGIN);
//			} else {
//				if ((Boolean) tenderloginResult.getOrDefault("isArrearage", false)) {
//					log.error("register failed:招标平台接口登录,欠费!");
//					request.setAttribute("userNameAndPasswordVer", 1);
//					request.setAttribute("message", newTenderloginResult.get("msg"));
//					//添加招标平台登录的提示信息
//					return PathResolver.getPath(FrontPage.LOGIN);
//				}
//			}
//		}

		// 请求成功且用户信息正确，创建平台用户
		// 查询数据库，查询用户信息，第一次登录需要填写手机与用户名
		UserDetail userDetail = this.userDetailService.getUserDetailByTenderNo(userNo);
		// 判断用户存在，注册ip是否为*********
		if (AppUtils.isNotBlank(userDetail) && userDetail.getUserRegip().equals(tenderRegIp)) {
			// 判断信息是否齐全，是否需要补全信息
			ShopDetail shopDetail = this.shopDetailService.getShopDetailById(userDetail.getShopId());
			if (shopDetail.getShopAddr() == null) {
				// 跳转到补全页面
				request.setAttribute("shopDetailInfo", shopDetail);
				return PathResolver.getPath(FrontPage.SHOP_DETAIL_SUPPLEMENT);
			}

			loginService.onAuthentication(request, response, userDetail.getUserName(), true);
			return loginRedirect(request);
//			Boolean decorateHomeEnable = propertiesUtil.getDecorateHomeEnable();
//			if (decorateHomeEnable) {
//				return "redirect:" + propertiesUtil.getDecorateHome(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode());
//			} else {
//				return PathResolver.getPath(FrontPage.HOME);
//			}

		}
		if (AppUtils.isBlank(userDetail)) {
			String isTenderM = "^m[a-zA-Z0-9]{1,30}$";
			Pattern pattern = Pattern.compile(isTenderM);
			if (pattern.matcher(userNo.toLowerCase()).matches()) {
				request.setAttribute("userNameAndPasswordVer", 2);
				return PathResolver.getPath(FrontPage.LOGIN);
			}
			// 没有登录过,需要补全信息
			request.setAttribute("no", userNo);
			return PathResolver.getPath(FrontPage.TENDER_INFO_SUPPLEMENT);
		} else {
			loginService.onAuthentication(request, response, userDetail.getUserName(), true);
//			Boolean decorateHomeEnable = propertiesUtil.getDecorateHomeEnable();
//			if (decorateHomeEnable) {
//				return "redirect:" + propertiesUtil.getDecorateHome(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode());
//			} else {
//				return PathResolver.getPath(FrontPage.HOME);
//			}
			return loginRedirect(request);
		}
	}

	private String loginRedirect(HttpServletRequest request){
		String decorateHome;
		SecurityUserDetail su = UserManager.getUser(request);
		if (isNotBlank(su)) {
			// 默认店铺进入平台首页涉及到登录页面
			if (IndustrialTypeEnum.CONSUMER_PRODUCTS.getFlag().equals(su.getLoginSourceType())) {
				decorateHome = systemConfigService.getDecorateHome(IndustrialTypeEnum.CONSUMER_PRODUCTS.getCode());
			} else if (IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getFlag().equals(su.getLoginSourceType())) {
				decorateHome = systemConfigService.getDecorateHome(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode());
			} else {
				decorateHome = systemConfigService.getDecorateHome(IndustrialTypeEnum.ORIGINAL_PRODUCTS.getCode());
			}
		}
		else{
			decorateHome = systemConfigService.getDecorateHome(IndustrialTypeEnum.ORIGINAL_PRODUCTS.getCode());
		}

		Boolean decorateHomeEnable = propertiesUtil.getDecorateHomeEnable();
		// 判断是否跳转不同的装修首页
		if (decorateHomeEnable) {
			return "redirect:" + decorateHome;
		} else {
			return PathResolver.getPath(FrontPage.HOME);
		}
	}


	/**
	 * @param userNo
	 * @param password
	 * @return
	 */
	private Map<String, Object> tenderLogin(String userNo, String password) {
		Map<String, Object> resultMap = new HashMap<>();
		String result = "";
		int httpStatus = 500;

		// 请求Erp接口，判断招标平台员工工号与密码
		Map<String, String> params = new HashMap<>();
		params.put("userNo", userNo);
		params.put("psw", password);
		params.put("typeID", "001A");
		Map<String, String> headers = new HashMap<>();
		headers.put("Content-Type", "application/x-www-form-urlencoded");

		try {
			result = HttpUtil.httpPost(tenderLoginUrl, headers, params);
			httpStatus = 200;
		} catch (Exception e) {
			log.error("调用tender接口异常 登录地址:{}", tenderLoginUrl);
			resultMap.put("success", false);
			resultMap.put("msg", "招标平台登录认证接口请求错误");
			resultMap.put("loginError", true);            // 添加请求错误标识
			return resultMap;
		} finally {
			thirdLogSendMsgUtil.sendThirdPartyLogMQ(new ThirdPartyLogDTO("legendshop-auth-service",
				ThirdPartyConstant.ERP, tenderLoginUrl, JSONUtil.getJson(params), httpStatus, result));
		}
		if (AppUtils.isBlank(result)) {
			log.error("register failed:招标平台接口请求链接错误! 登录地址:{}", tenderLoginUrl);
			resultMap.put("success", false);
			return resultMap;
		}
		Map dataMap = JSONUtil.getMap(result);
		String statusCode = (String) dataMap.get("status");
		if (Objects.nonNull(statusCode)) {
			String msg = (String) dataMap.get("msg");
			resultMap.put("msg", msg);
			if (!"succ".equals(statusCode)) {
				log.error("register failed:招标平台接口请求链接失败! 登录地址:{}", tenderLoginUrl);
				//如果是欠费情况 ，也不允许登录
				if (msg.contains("服务费")) {
					resultMap.put("isArrearage", true);
				}
				resultMap.put("success", false);
				return resultMap;
			} else {
				//如果是欠费情况
				if (msg.contains("服务费")) {
					resultMap.put("isArrearage", true);
				}
				resultMap.put("success", true);
				return resultMap;
			}
		}
		resultMap.put("success", false);
		return resultMap;
	}


	/**
	 * @param userNo
	 * @param password
	 * @return
	 */
	private Map<String, Object> newTenderLogin(String userNo, String password) {
		Map<String, Object> returnMap = new HashMap<>();
		String result = "";
		int httpStatus = 500;

		Map<String, String> params = new HashMap<>();
		params.put("loginType", "00");
		params.put("toolType", "07");
		params.put("userName", userNo);
		params.put("password", Base64.getEncoder().encodeToString(password.getBytes()));        // 密码64加密

		Map<String, String> headers = new HashMap<>();
		headers.put("Content-Type", "application/json");

		try {
			result = HttpRequest.post(newTenderLoginUrl).header("Content-Type", headers.get("Content-Type")).body(JSONUtil.getJson(params)).execute().body();
			httpStatus = 200;
		} catch (Exception e) {
			log.error("调用tender接口异常 登录地址:{}", newTenderLoginUrl);
			returnMap.put("success", false);
			returnMap.put("msg", "招标平台登录认证接口请求错误");
			returnMap.put("loginError", true);            // 添加请求错误标识
			return returnMap;
		} finally {
			thirdLogSendMsgUtil.sendThirdPartyLogMQ(new ThirdPartyLogDTO("legendshop-auth-service",
				ThirdPartyConstant.ERP, newTenderLoginUrl, JSONUtil.getJson(params), httpStatus, result));
		}
		if (AppUtils.isBlank(result)) {
			log.error("register failed:招标平台接口请求链接错误! 登录地址:{}", newTenderLoginUrl);
			returnMap.put("success", false);
			return returnMap;
		}

		Map dataMap = JSONUtil.getMap(result);
		Map header = (Map) dataMap.get("header");

		if (Objects.nonNull(header)) {
			String ret = (String) header.get("ret");
			String msg = (String) header.get("msg");
			returnMap.put("msg", msg);

			//ret不为0，帐号验证失败
			if (!"0".equals(ret)) {
				returnMap.put("success", false);
				//如果是欠费情况 ，也不允许登录
				if (msg.contains("服务费")) {
					returnMap.put("isArrearage", true);
				}
				return returnMap;
			} else {
				returnMap.put("success", true);
				//如果是欠费情况 ，也不允许登录
				if (msg.contains("服务费")) {
					returnMap.put("isArrearage", true);
				}
				return returnMap;
			}
		}

		returnMap.put("success", false);
		return returnMap;
	}


	@Transactional
	@RequestMapping("/shopDetailSupplement")
	public String shopDetailSupplement(HttpServletRequest request, HttpServletResponse response, ShopDetail shopDetail) {

		ShopDetail oldShopDetail = shopDetailService.getShopDetailById(shopDetail.getShopId());
		// 补全数据
		oldShopDetail.setShanRong("");
		oldShopDetail.setShanRongAccount("");
		oldShopDetail.setShopAddr(shopDetail.getShopAddr());
		oldShopDetail.setProvinceid(shopDetail.getProvinceid());
		oldShopDetail.setCityid(shopDetail.getCityid());
		oldShopDetail.setAreaid(shopDetail.getAreaid());
		oldShopDetail.setReceiptAccount("");
		oldShopDetail.setOpeningBank("");
		// 登录
		shopDetailService.update(oldShopDetail);
		loginService.onAuthentication(request, response, shopDetail.getUserName(), true);
		return PathResolver.getPath(RedirectPage.AFTER_OPERATION) + "?userName=" + shopDetail.getUserName();
	}

	/**
	 * 根据参数创建用户
	 */
	@Transactional
	@RequestMapping("/createUser")
	public String createUser(HttpServletRequest request, HttpServletResponse response, UserMobileForm userMobileForm) {

		UserDetail userDetail = userDetailService.getUserDetailByPhone(userMobileForm.getMobile());
		request.setAttribute("key", key);
		request.setAttribute("iv", iv);
		if (userDetail != null && userDetail.getErpNo() == null && StringUtils.isBlank(userMobileForm.getUserName())) {
			// 关联账号
			userDetail.setErpNo(userMobileForm.getErpNo());
			userDetailService.updateUserDetail(userDetail);
			loginService.onAuthentication(request, response, userMobileForm.getMobile(), userMobileForm.getPassword());
			return PathResolver.getPath(RedirectPage.AFTER_OPERATION) + "?userName=" + userDetail.getUserName();
		} else if (userDetail != null && userDetail.getErpNo() != null && userMobileForm.getUserName() == null) {
			request.setAttribute("", "");
			return PathResolver.getPath(FrontPage.LOGIN);
		} else if (userDetail == null && userMobileForm.getUserName() != null) {
			User user = userDetailService.saveUserReg(IPHelper.getIpAddr(request), userMobileForm, passwordEncoder.encode(userMobileForm.getPassword()));
			if (AppUtils.isNotBlank(userMobileForm.getErpNo())) {
				// 关联erp工号和用户详情
				userDetailService.updateErpNoByUserId(user.getId(), userMobileForm.getErpNo());
			}

			if (AppUtils.isNotBlank(userMobileForm.getErpNo())) {
				//关联员工卡
				ICard iCard = new ICard();
				iCard.setJobNo(userMobileForm.getErpNo());
				iCard.setPassword(userMobileForm.getPassword());
				try {
					//异常处理，不影响账号创建
					R<Object> icardResult = iCardService.bindingICard(iCard, user.getId());
					if (!icardResult.isSuccess()) {
						log.info("erp账号登录绑定员工卡失败：", icardResult.getMsg());
					}

				} catch (Exception e) {
					log.info("erp账号登录绑定员工卡失败：", e.getMessage());
				}
			}

			if (AppUtils.isNotBlank(userMobileForm.getTenderNo())) {
				// 关联招标工号和用户详情
				userDetailService.updateTenderNoByUserId(user.getId(), userMobileForm.getTenderNo());
			}

			request.setAttribute("userName", userMobileForm.getNickName());
			if (user == null) {
				throw new BusinessException("register user error");
			}
			// 手机注册的用户，用户安全等级+1,手机验证变为1
			userDetailService.updatePhoneVerifn(user.getName());

			userServiceClient.sendVoucherToUser(user.getId(), user.getUserName(), userMobileForm.getErpNo() == null ? "" : userMobileForm.getErpNo(), false, user.getUserMobile(), SecurityConstants.FROM_IN);

			if (Constants.USER_STATUS_ENABLE.equals(user.getEnabled())) {
				// 用户注册即登录
				loginService.onAuthentication(request, response, userMobileForm.getMobile(), userMobileForm.getPassword());
				// 用户绑定成功，跳转到绑定成功页面
				return PathResolver.getPath(RedirectPage.AFTER_OPERATION) + "?userName=" + user.getUserName();
			}
		}
		return PathResolver.getPath(FrontPage.LOGIN);
	}


	/**
	 * 企业用户注册
	 *
	 * @param request        the request
	 * @param response       the response
	 * @param userMobileForm the 注册信息表单
	 * <AUTHOR>
	 */
	@RequestMapping("/userRegCompanyByPhone")
	public String userRegCompanyByPhone(HttpServletRequest request, HttpServletResponse response, UserMobileForm userMobileForm) {
		try {
			userMobileForm.setIp(IPHelper.getIp());
		} catch (Exception e) {
			userMobileForm.setIp("127.0.0.1");
		}
		R<UserMobileForm> registeredResult = userDetailService.companyUserRegistered(userMobileForm);
		if (!registeredResult.isSuccess()) {
			return PathResolver.getPath(RedirectPage.HOME_QUERY);
		} else {
			UserMobileForm result = registeredResult.getResult();
			// 用户注册即登录
			loginService.onAuthentication(request, response, userMobileForm.getMobile(), userMobileForm.getPassword());
			return PathResolver.getPath(RedirectPage.AFTER_OPERATION) + "?userName=" + result.getUserName();
		}
	}

	/**
	 * 加载用户注册页面.
	 *
	 * @param request  the request
	 * @param response the response
	 * @return the string
	 */
	@RequestMapping("/reg")
	public String reg(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "openId", required = false) String openId) {
		// 分销的用户名称
		String uid = request.getParameter("uid");
		if (AppUtils.isNotBlank(uid)) {
			request.setAttribute("uid", uid);
		}
		//获取参数 跳转参数
		HttpSession session = request.getSession();
		session.setAttribute(Constants.LOGIN_SOURCE_TYPE, request.getParameter(Constants.LOGIN_SOURCE_TYPE));
		// 判断访问设备：PC端或者移动端
		String userAgent = request.getHeader("user-agent");
		if (userAgent.toLowerCase().contains("Mobile".toLowerCase())) {
			String url = null;
			String mobileDomainName = propertiesUtil.getMobileDomainName();
			if (AppUtils.isNotBlank(uid)) {
				url = mobileDomainName + "/#/register?uid=" + uid;
			} else {
				url = mobileDomainName + "/#/register";
			}
			return "redirect:" + url;
		}
		if (StringUtils.isNotBlank(openId)) {
			request.setAttribute("openId", openId);
		}

		return PathResolver.getPath(FrontPage.REG);
	}

	/**
	 * 加载用户注册页面【企业】.
	 *
	 * @param request  the request
	 * @param response the response
	 * @return the string
	 */
	@RequestMapping("/regCompany")
	public String regCompany(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "openId", required = false) String openId) {
		// 分销的用户名称
		String uid = request.getParameter("uid");
		if (AppUtils.isNotBlank(uid)) {
			request.setAttribute("uid", uid);
		}
		//获取参数 跳转参数
		HttpSession session = request.getSession();
		session.setAttribute(Constants.LOGIN_SOURCE_TYPE, request.getParameter(Constants.LOGIN_SOURCE_TYPE));

		// 判断访问设备：PC端或者移动端
		// TODO 企业注册，移动端可能需要改造
		String userAgent = request.getHeader("user-agent");
		if (userAgent.toLowerCase().contains("Mobile".toLowerCase())) {
			String url = null;
			String mobileDomainName = propertiesUtil.getMobileDomainName();
			if (AppUtils.isNotBlank(uid)) {
				url = mobileDomainName + "/#/register?uid=" + uid;
			} else {
				url = mobileDomainName + "/#/register";
			}
			return "redirect:" + url;
		}
		if (StringUtils.isNotBlank(openId)) {
			request.setAttribute("openId", openId);
		}
		return PathResolver.getPath(FrontPage.REG_COMPANY);
	}

	/**
	 * 检测是否是移动设备访问
	 */
	private boolean checkMobileAgent(String userAgent) {
		if (AppUtils.isBlank(userAgent)) {
			return false;
		}
		String phoneReg = "\\b(ip(hone|od)|android|opera m(ob|in)i" + "|windows (phone|ce)|blackberry" + "|s(ymbian|eries60|amsung)|p(laybook|alm|rofile/midp"
			+ "|laystation portable)|nokia|fennec|htc[-_]" + "|mobile|up.browser)\\b";
		String tableReg = "\\b(ipad|tablet|(Nexus 7)|up.browser" + ")\\b";

		// 移动设备正则匹配：手机端、平板
		Pattern phonePat = Pattern.compile(phoneReg, Pattern.CASE_INSENSITIVE);
		Pattern tablePat = Pattern.compile(tableReg, Pattern.CASE_INSENSITIVE);

		// 匹配
		Matcher matcherPhone = phonePat.matcher(userAgent);
		Matcher matcherTable = tablePat.matcher(userAgent);
		return matcherPhone.find() || matcherTable.find();
	}

	// 协议弹框
	@RequestMapping("/agreementoverlay")
	public String agreeMentOverlay(HttpServletRequest request, HttpServletResponse response) {
		SystemConfig systemConfig = systemConfigService.getSystemConfig();
		String content = systemConfig.getRegProtocolTemplate();
		request.setAttribute("regItem", content);
		return PathResolver.getPath(FrontPage.AGREEMENT_OVERLAY);
	}

	/**
	 * Adds the shop.
	 *
	 * @return the string
	 */
	@RequestMapping("/isUserExist")
	public @ResponseBody
	Boolean isUserExist(String userName) {
		if (AppUtils.isBlank(userName)) {
			return true;// 用户名不允许有空值
		}
		return userDetailService.isUserExist(userName.trim());
	}

	/**
	 * 用户是否已经登录 True: 已经登录 False: 没有登录
	 *
	 * @param request the request
	 * @return Boolean
	 */
	@RequestMapping("/isUserLogin")
	public @ResponseBody
	Boolean isUserLogin(HttpServletRequest request) {
		SecurityUserDetail user = UserManager.getUser(request);
		return UserManager.isUserLogined(user);
	}

	@RequestMapping("/isEmailExist")
	public @ResponseBody
	Boolean isEmailExist(String email) {
		return userDetailService.isEmailExist(email);
	}

	@RequestMapping("/isPhoneExist")
	public @ResponseBody
	Boolean isPhoneExist(String Phone) {
		return userDetailService.isPhoneExist(Phone);
	}

	@ResponseBody
	@RequestMapping("/isPhoneExistAndErpNoIsNull")
	public Boolean isPhoneExistAndErpNoIsNull(String Phone) {
		return userDetailService.isPhoneExistAndNoIsNull(Phone);
	}

	@ResponseBody
	@RequestMapping("/thirdPartUserAccountCheck")
	public int thirdPartUserAccountCheck(String Phone) {
		return userDetailService.thirdPartUserAccountCheck(Phone);
	}

	@RequestMapping("/isUniformNoExist")
	@ResponseBody
	public Boolean isUniformNoExist(String uniformNo) {
		R<UserCompanyVendorInfoDTO> result = userCompanyVendorInfoClient.findByUniformNo(uniformNo, SecurityConstants.FROM_IN);
		if (result.getStatus() == 200) {
			return AppUtils.isNotBlank(result.getResult());
		}
		return false;
	}

	// 判断量富征信的接口是否正常
	@RequestMapping("/isCompanyVer")
	@ResponseBody
	public Boolean isCompanyVer(String uniformNo) {
		String result = "";
		int httpStatus = 500;
		try {
			result = HttpUtil.httpGet(lfzxUrl + "?companyCode=" + uniformNo + "&typeID=001");
			httpStatus = 200;
		} catch (Exception e) {
			throw new cn.legendshop.common.core.exception.BusinessException("量富征信接口请求失败！");
		} finally {
			thirdLogSendMsgUtil.sendThirdPartyLogMQ(new ThirdPartyLogDTO("legendshop-web-service",
				ThirdPartyConstant.ERP, lfzxUrl, JSONUtil.getJson(uniformNo), httpStatus, result));
		}

		try {
			Map dataMap = JSONUtil.getMap(result);
			String bo = (String) dataMap.get("Result");
			if ("true".equals(bo)) {
				// 判断 dataMap.get("status") 是否包含在业或者存续
				String status = Optional.ofNullable(String.valueOf(dataMap.get("status"))).orElse("");
				return status.contains("在业") || status.contains("存续");
			}
			return false;
		} catch (Exception e) {
			log.error("接口查询异常：" + e.getMessage());
			return false;
		}
	}

	@RequestMapping("/isFullNameExist")
	@ResponseBody
	public Boolean isFullNameExist(String fullName) {
		R<UserCompanyVendorInfoDTO> result = userCompanyVendorInfoClient.findByFullName(fullName, SecurityConstants.FROM_IN);
		return result.hasBody();
	}

	@RequestMapping("/isUserNameExist")
	public @ResponseBody
	Boolean isUserNameExist(String userName) {
		return userDetailService.isUserExist(userName);
	}

	/**
	 * 检查验证码是否有效
	 *
	 * @param mobile
	 * @param verifyCode
	 * @return
	 */
	@RequestMapping("/verifyCode")
	public @ResponseBody
	boolean verifyCode(String mobile, String verifyCode) {
		return smsLogService.verifyCodeAndClear(mobile, verifyCode, SMSTypeEnum.VAL);
	}

	/**
	 * 提交表单前检查验证码是否有效
	 *
	 * @param mobile
	 * @param verifyCode
	 * @return
	 */
	@RequestMapping("/verifyCodeBeforeSubmit")
	public @ResponseBody
	boolean verifyCodeBeforeSubmit(String mobile, String verifyCode) {
		return smsLogService.verifyCodeBeforeSubmit(mobile, verifyCode, SMSTypeEnum.VAL);
	}

	/**
	 * 判断昵称是否存在
	 *
	 * @param nickName
	 * @return
	 */
	@RequestMapping(value = "/isNickNameExist", produces = "application/json;charset=UTF-8")
	public @ResponseBody
	String isNickNameExist(String nickName) {
		if (AppUtils.isBlank(nickName)) {
			return "true";
		}
		if (sensitiveWordService.checkSensitiveWordsNameExist(nickName)) {
			return "sensitivity";
		}
		if (userDetailService.isNickNameExist(nickName)) {
			return "true";
		} else {
			return "false";
		}
	}

	/**
	 * 判断店铺名称是否存在
	 *
	 * @param siteName
	 * @return
	 */
	@RequestMapping("/isSiteNameExist")
	public @ResponseBody
	Boolean isSiteNameExist(String siteName) {
		if (AppUtils.isBlank(siteName)) {
			return true;
		}
		boolean result = userDetailService.isSiteNameExist(siteName.trim());
		return result;
	}

	/**
	 * 验证用户名
	 *
	 * @param userName
	 * @return
	 */
	@RequestMapping("/verifyName")
	@ResponseBody
	public Boolean verifyName(String userName) {
		if (ValidationUtil.checkEmail(userName)) {
			return userDetailService.isEmailExist(userName);
		} else if (ValidationUtil.checkPhone(userName)) {
			return userDetailService.isPhoneExist(userName);
		} else if (ValidationUtil.checkAlpha(userName)) {
			return userDetailService.isNickNameExist(userName);
		}
		return true;
	}

	/**
	 * 重置用户名密码
	 *
	 * @param userName
	 * @param userMail
	 * @return
	 */
	@RequestMapping("/resetPassword")
	public @ResponseBody
	String resetPassword(String userName, String userMail) {
		if (AppUtils.isBlank(userName) || AppUtils.isBlank(userMail)) {
			return "fail";
		}
		try {
			String newPassword = RandomStringUtils.randomNumeric(10, 6);
			if (userDetailService.updatePassword(userName, userMail, passwordEncoder.encode(newPassword))) {
				return null;
			} else {
				return "fail";
			}
		} catch (Exception e) {
			log.error("", e);
			return "fail";
		}
	}


	/**
	 * 图形验证码 前端ajax验证 验证码 此时不清除session中的验证码
	 *
	 * @param randNum
	 * @return
	 */
	@RequestMapping("/validateRandImg")
	public @ResponseBody
	boolean validateRandImg(HttpServletRequest request, HttpServletResponse response, String randNum) {
		return ValidateCodeUtil.validateCodeAndKeep(randNum, request.getSession());
	}

	/**
	 * 设置生日日期
	 *
	 * @param birthDate the birth date
	 * @param request   the request
	 */
	private void setBirthDate(String birthDate, HttpServletRequest request) {
		try {
			String year = birthDate.substring(0, 4);
			String month = birthDate.substring(4, 6);
			String day = birthDate.substring(6, 8);
			request.setAttribute("userBirthYear", year);
			request.setAttribute("userBirthMonth", month);
			request.setAttribute("userBirthDay", day);
		} catch (Exception e) {

		}

	}

	/**
	 * 页面加密手机号码，保留前3个和后3个
	 *
	 * @param mobile
	 * @return
	 */
	private String mobileSubstitution(String mobile) {
		if (mobile == null) {
			return null;
		}
		if (mobile.length() < 11) {
			return mobile;
		}
		return mobile.replaceAll(mobile.substring(3, mobile.length() - 3), "******");
	}

	private String mailSubstitution(String mail) {
		int pos = mail.indexOf("@");
		if (pos != 0) {
			String preFix = mail.substring(0, pos);
			String postFix = mail.substring(pos);
			int mailPrefixLength = preFix.length();
			StringBuilder sb = new StringBuilder();
			if (mailPrefixLength < 3) {
				for (int i = 0; i < mailPrefixLength; i++) {
					sb.append("*");
				}
			} else {
				sb.append(preFix.subSequence(0, 1));
				for (int i = 1; i < mailPrefixLength - 1; i++) {
					sb.append("*");
				}
				sb.append(preFix.subSequence(mailPrefixLength - 1, mailPrefixLength));
			}
			return sb.toString() + postFix;
		}
		return "";
	}

	/**
	 * Check email.
	 *
	 * @param email the email
	 * @return true, if successful
	 */
	private boolean checkEmail(String email) {
		if (AppUtils.isBlank(email)) {
			return false;
		}
		Pattern pattern = Pattern.compile("^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\\.[a-zA-Z0-9_-]{2,3}){1,2})$");
		Matcher matcher = pattern.matcher(email);
		return matcher.matches();
	}

	/**
	 * Check phone.
	 *
	 * @param phone the phone
	 * @return true, if successful
	 */
	private boolean checkPhone(String phone) {
		if (AppUtils.isBlank(phone)) {
			return false;
		}
		Pattern pattern = Pattern.compile("^(1)\\d{10}$"); // 以1开始的11位数字
		Matcher matcher = pattern.matcher(phone);
		return matcher.matches();
	}

	// 随机产生的邮箱验证码
	private String getRandomString(int length) {
		String base = "abcdefghijklmnopqrstuvwxyz0123456789";
		Random random = new Random();
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(base.length());
			sb.append(base.charAt(number));
		}
		return sb.toString();
	}

	/**
	 * 判断用户是否登录
	 */
	@RequestMapping(value = "/loginFlag", produces = "application/json;charset=UTF-8")
	@ResponseBody
	public String loginFlag(HttpServletRequest request) {
		SecurityUserDetail user = UserManager.getUser(request);
		if (AppUtils.isBlank(user)) {
			return Constants.FAIL;
		}
		return Constants.SUCCESS;
	}

	/**
	 * @param username: 第三方平台用户名或账号
	 * @param password: 校验密码
	 * @return String : 登录成功返回SUCCESS，失败返回错误信息
	 * @Description: : 第三方[erp、招标平台]登录
	 */
	private ThirdLoginResponseDTO thirdLogin(String username, String password) {
		// 封装返回值
		ThirdLoginResponseDTO thirdLoginResponse = new ThirdLoginResponseDTO(username, password);
		// 封装请求参数
		Map<String, Object> params = new HashMap<>();
		String requestUrl;
		String pattern = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])[A-Za-z\\d!@#$%^&*(),.?\":{}|<>]{8,20}$";
		if(StringUtils.isBlank(password) || !password.matches(pattern)) {
			throw new cn.legendshop.common.core.exception.BusinessException("密码应为8-20位字母、须包含特殊字符、数字和大小写字母");
		}
		// 判断用户是否为ERP或者招标
		switch (thirdLoginResponse.isThirdAccount()) {
			case ERP:
				params.put("username", username);
				params.put("password", password);
				requestUrl = erpLoginUrl;
				break;
			case TENDER:
				params.put("userNo", username);
				params.put("psw", password);
				params.put("typeID", "001A");
				requestUrl = tenderLoginUrl;
				break;
			default:
				return thirdLoginResponse;
		}
		// 初始化返回值和返回状态
		String result = null;
		int httpStatus = 500;
		try {
			// 发送登录校验请求
			HttpRequest httpRequest = cn.hutool.http.HttpUtil.createPost(requestUrl);
			httpRequest.header("Content-Type", "application/x-www-form-urlencoded");
			httpRequest.form(params);
			HttpResponse httpResponse = httpRequest.execute();
			result = httpResponse.body();
			httpStatus = 200;
		} catch (Exception e) {
			e.printStackTrace();
			thirdLoginResponse.error("请求异常 e:" + e.getMessage());
			return thirdLoginResponse;
		} finally {
			thirdLogSendMsgUtil.sendThirdPartyLogMQ(new ThirdPartyLogDTO("legendshop-web-service",
				thirdLoginResponse.getThirdLogin().description(), requestUrl, JSONUtil.getJson(params), httpStatus, result));
		}
		if (AppUtils.isBlank(result)) {
			log.error("register failed:ERP接口请求链接错误 请求返回值为空");
			thirdLoginResponse.error("请求返回值为空");
			return thirdLoginResponse;
		}
		Map dataMap = JSONUtil.getMap(result);
		if (thirdLoginResponse.getThirdLogin() != ThirdLoginResponseEnum.ERP) {
			String statusCode = (String) dataMap.get("status");
			String message = (String) dataMap.get("msg");
			if (statusCode == null) {
				message = (String) dataMap.get("errMsg");
				log.info(message);
				thirdLoginResponse.error(message);
			} else if (!"succ".equals(statusCode)) {
				log.info(message);
				thirdLoginResponse.error(message);
			}
		} else {
			String statusCode = (String) dataMap.get("statusCode");
			String message = (String) dataMap.get("message");
			if (statusCode == null) {
				message = (String) dataMap.get("errMsg");
				log.info(message);
				thirdLoginResponse.error(message);
			} else if (StringUtils.isBlank(statusCode) || !"200".equals(statusCode)) {
				log.info(message);
				thirdLoginResponse.error(message);
			}
		}
		return thirdLoginResponse;
	}
}