package com.legendshop.controller.admin;

import com.google.code.kaptcha.Producer;
import com.legendshop.framework.cache.client.CacheClient;
import com.legendshop.model.dto.RandNumDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.image.BufferedImage;

@Controller
public class ValidateCodeController {

    @Autowired
    private Producer captchaProducer;

    @GetMapping("/validateCode")
    @ResponseBody
    public void getValidateCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.setContentType("image/jpeg");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        // 生成验证码文本
        String capText = captchaProducer.createText();
        
        // 将验证码保存到session
        HttpSession session = request.getSession();
        session.setAttribute("RANDOM_VALIDATE_CODE_KEY", new RandNumDto(capText));

        // 生成验证码图片
        BufferedImage bi = captchaProducer.createImage(capText);
        ServletOutputStream out = response.getOutputStream();
        ImageIO.write(bi, "jpg", out);
        out.flush();
        out.close();
    }
} 