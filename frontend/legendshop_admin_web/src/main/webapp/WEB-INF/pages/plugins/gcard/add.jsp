<!Doctype html>
<html class="no-js fixed-layout">
<%@ page language="java" pageEncoding="UTF-8" %>
<%@ include file="/WEB-INF/pages/plugins/admin/back-common.jsp" %>
<%@ include file="/WEB-INF/pages/common/taglib.jsp" %>
<%@ include file="/WEB-INF/pages/common/layer.jsp" %>
<%@ include file="/WEB-INF/pages/common/laydate.jsp" %>
<%@ taglib uri="http://displaytag.sf.net" prefix="display" %>
<%@ page import="static cn.legendshop.user.api.enums.CardSettlementModeEnum.PLATFORM_JOIN" %>
<%@ page import="static cn.legendshop.user.api.enums.CardSettlementModeEnum.PLATFORM_NO_JOIN" %>
<%@ page import="static cn.legendshop.user.api.enums.UserGCardStatusEnum.FORBID" %>
<%@ page import="static cn.legendshop.user.api.enums.UserGCardStatusEnum.ENABLED" %>
<%@ page import="static cn.legendshop.user.api.enums.UserGCardStatusEnum.USED" %>
<%--分类/店铺适用类型--%>
<c:set value="<%=PLATFORM_JOIN.value()%>" var="PLATFORM_JOIN"/>
<c:set value="<%=PLATFORM_NO_JOIN.value()%>" var="PLATFORM_NO_JOIN"/>
<c:set value="<%=FORBID.value()%>" var="FORBID"/>
<c:set value="<%=ENABLED.value()%>" var="ENABLED"/>
<c:set value="<%=USED.value()%>" var="USED"/>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>营销管理 - 后台管理</title>
    <meta name="description" content="LegendShop 多用户商城系统">
    <meta name="keywords" content="index">
    <meta name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="renderer" content="webkit">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <link rel="icon" href="${contextPath}/favicon.ico" type="image/x-icon"/>
    <link rel="apple-touch-icon-precomposed"
          href="${contextPath}/favicon.ico">
    <meta name="apple-mobile-web-app-title" content="Amaze UI"/>
    <link rel="stylesheet" type="text/css" media="screen"
          href="${contextPath}/resources/common/css/errorform.css"/>
    <link href="<ls:templateResource item='/resources/plugins/select2-3.5.2/select2.css'/>" rel="stylesheet"/>
    <style type="text/css">


        .shopList th {
            height: 36px;
        }

        .shopList tr td {
            border-bottom: 1px solid #dddddd;
            border-width: 0px 1px 1px 0px;
            padding: 10px 0px;
            text-align: center;
        }

        .shopList tr td:last-child {
            border-right: 1px solid #dddddd;
        }

        label {
            font-weight: normal !important;
            margin-bottom: 2px !important;
            line-height: 25px;
        }

    </style>
</head>

<body>
<jsp:include page="/admin/top"/>
<div class="am-cf admin-main">
    <!-- sidebar start -->
    <jsp:include page="/WEB-INF/pages/plugins/admin/frame/left.jsp"></jsp:include>
    <!-- sidebar end -->
    <div class="admin-content" id="admin-content"
         style="overflow-x: auto;">
        <div align="center">
            <form:form action="" method="post" id="form1"
                       enctype="multipart/form-data">
                <input name="id" value="${gCard.id}" type="hidden">
                <table align="center" class="${tableclass}">
                    <thead>
                    <tr>
                        <th class="no-bg title-th">
                            <span class="title-span">平台营销  ＞
                                <a href="<ls:url address="/admin/gcard/page"/>">福利卡</a>
                                <c:if test="${ not empty gCard.id}"> ＞ <span
                                        style="color:#0e90d2;">查看详情</span></c:if>
                                <c:if test="${empty gCardId.id}"> ＞ <span
                                        style="color:#0e90d2;">添加福利卡</span></c:if>
                            </span>
                        </th>
                    </tr>
                    </thead>
                </table>
                <table border="0" align="center" class="${tableclass} no-border content-table" id="col1">
                    <tr>
                        <td style="width:200px;;">
                            <div align="right">
                                <font color="ff0000">*</font> 福利卡名称：
                            </div>
                        </td>
                        <td align="left" style="padding-left: 2px;"><input type="text" class="${inputclass}"
                                <c:if test="${not empty gCard.id}"> disabled</c:if>
                                                                           style="width: 240px;"
                                                                           name="gCardName" id="gCardName"
                                                                           value="${gCard.gCardName}" maxlength="30"/>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <div align="right">
                                <font color="ff0000">*</font> 有效截至时间：
                            </div>
                        </td>
                        <td align="left" style="padding-left: 2px;"><input
                                <c:if test="${not empty gCard.id}"> disabled</c:if>
                                class="${inputclass}" readonly="readonly" name="endDate"
                                id="endDate" type="text"
                                value='<fmt:formatDate value="${gCard.endDate}" pattern="yyyy-MM-dd HH:mm:ss" />'/>
                            <c:if test="${not empty gCard.id}">
                                <a class="${inputclass}" href="javascript:void(0)" id="updateEndDate" onclick="updateEndDate()">修改截止时间</a>
                            </c:if>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <div align="right">
                                <font color="ff0000">*</font> 发卡方：
                            </div>
                        </td>
                        <td align="left" style="padding-left: 2px;">
                            <input type="hidden" id="cardIssuerId" name="cardIssuerId" value="${gCard.cardIssuerId}"/>
                            <div id="cardIssuerName">${gCard.cardIssuerName}</div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <div align="right">
                                <font color="ff0000">*</font> 结算方式：
                            </div>
                        </td>
                        <td>
                            <div>
                                <label class="radio-wrapper">
                                    <span class="radio-item <c:if test="${not empty gCard.settlementMode && PLATFORM_JOIN eq gCard.settlementMode}">radio-wrapper-checked</c:if>">
                                        <input type="radio" class="radio-input" name="settlementMode" <c:if test="${not empty gCard.id}">disabled</c:if> value="${PLATFORM_JOIN}" id="defaultSettlementModeInput"
                                               <c:if test="${PLATFORM_JOIN eq gCard.settlementMode}">checked='checked'</c:if> onclick="selectSettlementMode(this);">
                                        <span class="radio-inner"></span>
                                    </span>
                                    <span class="radio-txt">平台参与结算</span>
                                </label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <label class="radio-wrapper">
                                    <span class="radio-item <c:if test="${not empty gCard.settlementMode && PLATFORM_NO_JOIN eq gCard.settlementMode}">radio-wrapper-checked</c:if>">
                                        <input type="radio" class="radio-input" name="settlementMode" <c:if test="${not empty gCard.id}">disabled</c:if> value="${PLATFORM_NO_JOIN}"
                                               <c:if test="${PLATFORM_NO_JOIN eq gCard.settlementMode}">checked='checked'</c:if> onclick="selectSettlementMode(this);">
                                        <span class="radio-inner"></span>
                                    </span>
                                    <span class="radio-txt">平台不参与结算</span>
                                </label>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <div align="right">
                                福利卡:
                            </div>
                        </td>
                        <td>
                            <div align="left">
                                <input type="button" onclick="showAddPanel()" value="添加" id="batch-del"
                                       class="batch-btn">
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <div align="right">
                                Excel文件:
                            </div>
                        </td>
                        <td>
                            <div align="left">
                                <input type="file" name="file" accept="application/vnd.ms" style="display: initial">
                                <a href="${contextPath}/system/card.xlsx">下载模板</a>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <div align="right">
                                <font color="ff0000">*</font>附件证明（图片）:
                            </div>
                        </td>
                        <td>
                            <div align="left">
                                <c:if test="${empty gCard.id}">
                                    <input  type="file" id="picFile" name="picFile" style="display: initial">
                                </c:if>
                                <c:if test="${not empty gCard.pic}">
                                    <a target="_blank" href="<ls:photo item='${gCard.pic}'/>">查看</a>
                                </c:if>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <div align="right">
                                领取方式：
                            </div>
                        </td>
                        <td align="left" style="padding-left: 2px;">
                            根据填入的用户手机号直接发放到用户账户中,用户可以在会员中心"我的福利卡"中查看
                        </td>
                    </tr>

                    <tr id="type">
                    </tr>

                    <tr>
                        <td valign="top">
                            <div align="right">
                                 描述：
                            </div>
                        </td>
                        <td align="left" style="padding-left: 2px;"><textarea
                                <c:if test="${not empty gCard.id}"> disabled</c:if>
                                rows="2" cols="5" style="width: 60%; height: 100px;background: #eee;"
                                name="remark" id="remark" maxlength="250">${gCard.remark}</textarea>
                        </td>
                    </tr>
                    <c:if test="${not empty gCard.id}">
                        <tr>
                            <td>
                                <div align="right">
                                    开票：
                                </div>
                            </td>
                            <td align="left">
                                <input class="${btnclass}" type="button" onclick="orderInvoicing(${gCard.id})" value="选择订单开票">
                            </td>
                        </tr>
                    </c:if>
                </table>

                <div align="center">

                    <input class="${btnclass}" type="submit" onsubmit="submit" value="保存"/>

                    <input class="${btnclass}" type="button" value="返回"
                           onclick="window.location='<ls:url address="/admin/gcard/page"/>'"/>
                </div>
            </form:form>

            <div style="height: 100px; float: left; margin-left: 100px; padding: 50px;">
                <%--<c:if test="${not empty gCard.id && fn:length(pageSupport.resultList)>0}">--%>
                <c:if test="${not empty gCard.id}">
                    <form:form id="form2" method="get" action="${contextPath}/admin/gcard/add">
                        <input class="${btnclass}" name="curNo" id="curNo" type="hidden"/>
                        <input class="${btnclass}" value="${gCard.id}" name="id" id="id" type="hidden"/>
                        <div style="text-align: left;margin-bottom: 10px;">
                            <input name="searchParam" id="searchParam" type="text" value="${searchParam}" placeholder="手机号/员工号/姓名"/>
                            <input class="${btnclass}" type="button" value="搜索"
                                   onclick="search()"/>
                            <input class="${btnclass}" type="button" value="导出全部数据"
                                   onclick="window.location='<ls:url address="/admin/gcard/exportGcardDetail/${gCard.id}"/>'"/>
                            <input class="${btnclass}" type="button" value="导出订单数据"
                                   onclick="window.location='<ls:url address="/admin/gcard/exportGcardOrder/${gCard.id}"/>'"/>
                        </div>
                    </form:form>
                </c:if>
                <display:table name="pageSupport.resultList" requestURI="/admin/admin/gcard/add" id="item"
                               export="false"
                               class="${tableclass}"
                               style="width:100%; height:10px;">
                    <%--<display:column style="width:5%" title="
				  	<span>
			           <label class='checkbox-wrapper'>
							<span class='checkbox-item'>
								<input type='checkbox' name='platfromId' value='${item.id}' class='checkbox-input selectAll' onclick='selectAll(this);'/>
								<span class='checkbox-inner' style='margin-left:10px;'></span>
							</span>
					   </label>
					  </span>">
                        <label class="checkbox-wrapper">
						<span class="checkbox-item">
							<input type="checkbox" name='platfromId' value='${item.id}'
                                   class="checkbox-input selectOne" onclick="selectOne(this);"/>
							<span class="checkbox-inner" style="margin-left:10px;"></span>
						</span>
                        </label>
                    </display:column>--%>
                    <display:column title="序号" style="width:5%">${item_rowNum}</display:column>
                    <display:column title="福利卡编号" property="gCardNumber" style="width:14%"></display:column>
                    <display:column title="手机号码" property="phone" style="width:10%"></display:column>
                    <display:column title="福利卡总金额" property="allMoney" style="width:8%"></display:column>
                    <display:column title="余额" style="width:8%">
                        <c:if test="${not empty item.balanceMoney}">
                            ${item.balanceMoney}
                        </c:if>
                        <c:if test="${empty item.balanceMoney}">
                            <span>用户未注册领取</span>
                            <c:if test="${item.enabled eq true}">(正常)</c:if>
                            <c:if test="${item.enabled eq false}">(已作废)</c:if>
                        </c:if>

                    </display:column>
                    <display:column title="公司别" property="company" style="width:10%"></display:column>
                    <display:column title="部门" property="department" style="width:10%"></display:column>
                    <display:column title="员工号" property="staffNo" style="width:10%"></display:column>
                    <display:column title="姓名" property="staffName" style="width:10%"></display:column>
                    <display:column title="状态" style="width:10%">
                        <c:if test="${FORBID eq item.userGCardStatus}">冻结</c:if>
                        <c:if test="${ENABLED eq item.userGCardStatus}">正常</c:if>
                        <c:if test="${USED eq item.userGCardStatus}">已使用</c:if>
                        <c:if test="${empty item.userGCardStatus}">
                            <c:if test="${item.enabled eq true}">正常</c:if>
                            <c:if test="${item.enabled eq false}">作废</c:if>
                        </c:if>
                    </display:column>
                    <display:column title="操作" media="html" style="width:10%">
                        <c:if test="${gCard.status == 0}">
                            <div class="table-btn-group">
                                <div class="am-dropdown" data-am-dropdown>
                                    <button class="tab-btn am-dropdown-toggle" data-am-dropdown-toggle>
                                        <span class="am-icon-cog"></span> <span class="am-icon-caret-down"></span>
                                    </button>
                                    <ul class="am-dropdown-content">
                                        <c:if test="${item.enabled eq true}">
                                            <li>
                                                <a href="javascript:void(0);" onclick="confirmAbolish('${gCard.id}', '${item.id}', '${item.phone}')">作废</a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="changeMobile('${item.id}')">修改手机号</a>
                                            </li>
                                        </c:if>
                                    </ul>
                                </div>
                            </div>
                        </c:if>
                        <c:if test="${gCard.status == 1}">
                            <div class="table-btn-group">
                                <div class="am-dropdown" data-am-dropdown>
                                    <button class="tab-btn am-dropdown-toggle" data-am-dropdown-toggle>
                                        <span class="am-icon-cog"></span> <span class="am-icon-caret-down"></span>
                                    </button>
                                    <ul class="am-dropdown-content">
                                        <c:if test="${empty item.balanceMoney and item.enabled eq true}">
                                            <li>
                                                <a href="javascript:void(0);" onclick="confirmAbolish('${gCard.id}', '${item.id}', '${item.phone}')">作废</a>
                                            <li>
                                        <li>
                                        </c:if>
                                        <c:if test="${(not empty item.balanceMoney and ENABLED eq item.userGCardStatus)}">
                                            <li>
                                                <a href="javascript:void(0);" onclick="changeUserGcardStatus('${item.userGCardId}',0,'是否冻结该福利卡?')">冻结</a>
                                            </li>
                                        </c:if>
                                        <c:if test="${not empty item.balanceMoney and FORBID eq item.userGCardStatus}">
                                            <li>
                                                <a href="javascript:void(0);" onclick="changeUserGcardStatus('${item.userGCardId}',1,'是否解除冻结该福利卡?')">解除冻结</a>
                                            </li>
                                        </c:if>
                                        <c:if test="${empty item.balanceMoney and (item.enabled eq true)}">
                                            <li>
                                                <a href="javascript:void(0);" onclick="changeMobile('${item.id}')">修改手机号</a>
                                            </li>
                                        </c:if>
                                    </ul>
                                </div>
                            </div>
                        </c:if>
                    </display:column>
                </display:table>
                <c:if test="${not empty gCard.id}">
                    <div style="color: #666;margin: 20px 0px;text-align: left">
                        温馨提示：修改手机号后将会根据修改后的手机号查询对应已存在的用户补发福利卡(福利卡发布后才生效).
                    </div>
                </c:if>

                <div class="clearfix">
                    <div class="fl">
                        <%--                    <input type="button" value="批量删除" id="batch-del" class="batch-btn">--%>
                        <%--                    <input type="button" value="批量下线" id="batch-off" class="batch-btn">--%>
                    </div>
                    <div class="fr">
                        <div cla ss="page">
                            <div class="p-wrap">
                                <ls:page pageSize="${pageSupport.pageSize }" total="${pageSupport.total}"
                                         curPageNO="${pageSupport.curPageNO }" type="simple"/>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div style="height: 20px;"></div>
        </div>
    </div>
</div>
</body>

<script type="text/javascript" src="${contextPath}/resources/common/js/jquery.validate.js"></script>
<script type="text/javascript" src='<ls:templateResource item="/resources/common/js/checkImage.js"/>'></script>
<script type="text/javascript" src="${contextPath}/resources/plugins/select2-3.5.2/select2.js"></script>
<script type="text/javascript">
    var gcardId = '${gCard.id}'
    var cardIssuerId = '${gCard.cardIssuerId}';
    var cardIssuerName = '';
    function pager(curPageNO) {
        document.getElementById("curNo").value = curPageNO;
        document.getElementById("form2").submit();
    }

    function search(){
        document.getElementById("form2").submit();
    }

    function selectChild(obj) {
        $("input[name='couponType']").each(function () {
            if (this.checked) {
                $(this).parent().parent().addClass("radio-wrapper-checked");
            } else {
                $(this).parent().parent().removeClass("radio-wrapper-checked");
            }
        });
        var val = $(obj).val();
        if (val == "common") {
            $("#shopListTr").hide();
        } else {
            $("#shopListTr").show();
        }
    }

    function selectSettlementMode(obj) {
        $("input[name='settlementMode']").each(function () {
            if (this.checked) {
                $(this).parent().addClass("radio-wrapper-checked");
            } else {
                $(this).parent().removeClass("radio-wrapper-checked");
            }
        });
    }

    $(document).ready(function () {

        if (!window.panel) {
            window.panel = {};
        }

        if(isEmpty(gcardId)){
            //默认选中平台参与结算
            $("#defaultSettlementModeInput").parent().addClass("radio-wrapper-checked");
            $("#defaultSettlementModeInput").attr("checked","checked");
        }

        if(isEmpty(gcardId)){
            if(cardIssuerName == ''){
                makeSelect2(contextPath + "/admin/cardIssuer/getIssuerSelect2","cardIssuerId","发卡方","value","key",'请选择发卡方', 'cardIssuerName');
            }else{
                makeSelect2(contextPath + "/admin/cardIssuer/getIssuerSelect2","cardIssuerId","发卡方","value","key",cardIssuerName, 'cardIssuerName');
                $("#select2-chosen-1").html(cardIssuerName);
            }
        }

        jQuery("#form1").validate({
            rules: {
                gCardName: "required",
                endDate: "required",
                cardIssuerId: "required",
                settlementMode: "required"
            },
            messages: {
                gCardName: "请输入福利卡名称",
                endDate: "请选择日期",
                cardIssuerId: "请选择发卡方",
                settlementMode: "请选择结算方式"
            },
            submitHandler: function (form) {
                var cardIssuerId = $("#cardIssuerId").val();
                if(isEmpty(cardIssuerId)){
                    layer.msg("请选择发卡方",{icon: 0});
                    return false;
                }
                if(isEmpty(gcardId) && isEmpty($("#picFile").val())){
                    layer.msg("请上传附件证明",{icon: 0});
                    return false;
                }
                var open = layer.confirm('确认无误，创建福利卡？', {
                    icon: 3
                    , btn: ['确定', '取消'] //按钮
                }, function () {
                    var fd = new FormData(form);
                    if (window.panel.details) {
                        window.panel.details.forEach(function (v, i, o) {
                            fd.set("gCardDetails[" + i + "].phone", v.phone);
                            fd.set("gCardDetails[" + i + "].money", v.money);
                        });
                    }
                    var index = layer.load(2);
                    $.ajax({
                        url: "${contextPath}/admin/gcard/add",
                        type: 'post',
                        processData: false,
                        contentType: false,
                        dataType: "json",
                        data: fd,
                        complete: function () {
                            layer.close(index);
                            layer.close(open);
                        },
                        success: function (r) {
                            if (r.success) {
                                window.location.href = "${contextPath}/admin/gcard/add?id=" + r.result;
                            } else {
                                layer.msg(r.msg);
                            }
                        }
                    });
                });

            }
        });

        $(document).on('click', 'input[name="settlementMode"]', function() {
            selectSettlementMode(this);
        });

    });

    function makeSelect2(url,element,text,label,value,holder, textValue){
        $("#"+element).select2({
            placeholder: holder,
            allowClear: true,
            width:'200px',
            ajax: {
                url : url,
                data: function (term,page) {
                    return {
                        q: term,
                        pageSize: 10,    //一次性加载的数据条数
                        currPage: page, //要查询的页码
                    };
                },
                type : "GET",
                dataType : "JSON",
                results: function (data, page, query) {
                    if(page * 5 < data.total){//判断是否还有数据加载
                        data.more = true;
                    }
                    return data;
                },
                cache: true,
                quietMillis: 200//延时
            },
            formatInputTooShort: "请输入" + text,
            formatNoMatches: "没有匹配的" + text,
            formatSearching: "查询中...",
            formatResult: function(row,container) {//选中后select2显示的 内容
                return "<b>"+row.text+"</b>"; //+ "</br>" + row.customText1
            },formatSelection: function(row) { //选择的时候，需要保存选中的id
                $("#" + textValue).val(row.text);
                return row.text;//选择时需要显示的列表内容
            },
        });
    }

    /**
     * 判断是否是空
     * @param value
     */
    function isEmpty(value) {
        if (value == null || value == "" || value == "undefined" || value == undefined || value == "null") {
            return true;
        } else {
            value = (value + "").replace(/\s/g, '');
            if (value == "") {
                return true;
            }
            return false;
        }
    }


    /**
     * 添加面板
     * @param id
     */
    function showAddPanel() {
        if (!window.panel) {
            window.panel = {};
        }

        layer.open({
            title: "批量添加福利卡",
            type: 2,
            content: [contextPath + "/admin/gcard/add/panel", "no"],
            area: ['500px', '400px'],
            btn: ['确认', '格式化', '取消'],
            yes: function (index, layero) {
                window.panel.details = window.panel.serialization();
                layer.close(index);
            },
            btn2: function (index, layero) {
                window.panel.format_button();
                return false;
            },
            cancel: function () {
                //右上角关闭回调
                //return false 开启该代码可禁止点击该按钮关闭
            },
            success: function (layero, index) {
                layer.iframeAuto(index);
            }
        });
    }

    function orderInvoicing(id) {
        if (!window.panel) {
            window.panel = {};
        }

        layer.open({
            title: "选择订单开票",
            type: 2,
            content: [contextPath + "/admin/gcard/order/queryInvoicingOrder?gcard="+id+"&curNo=1", "no"],
            area: ['900px', '400px'],
            btn: ['确认', '取消'],
            yes: function (index, layero) {
                layer.close(index);
            },
            cancel: function () {
                //右上角关闭回调
                //return false 开启该代码可禁止点击该按钮关闭
            },
            success: function (layero, index) {
                layer.iframeAuto(index);
            }
        });
    }


    laydate.render({
        elem: '#endDate',
        type: 'datetime',
        min: '-1',
        calendar: true,
        theme: 'grid'
    });

    function selectFunDiv(obj) {
        if (!obj.checked) {
            $(obj).prop("checked", false);
            $(obj).parent().parent().removeClass("checkbox-wrapper-checked");
        } else {
            $(obj).prop("checked", true);
            $(obj).parent().parent().addClass("checkbox-wrapper-checked");
        }
        applyUser();
    }

    function applyUser() {
        if ($("#companyLabel").hasClass("checkbox-wrapper-checked") && $("#personLabel").hasClass("checkbox-wrapper-checked")) {
            $("#applyUser").val("ALL");
        } else if ($("#companyLabel").hasClass("checkbox-wrapper-checked") && !$("#personLabel").hasClass("checkbox-wrapper-checked")) {
            $("#applyUser").val("COMPANY");
        } else if (!$("#companyLabel").hasClass("checkbox-wrapper-checked") && $("#personLabel").hasClass("checkbox-wrapper-checked")) {
            $("#applyUser").val("PERSON");
        } else {
            $("#applyUser").val("");
        }
    }


    function confirmAbolish(id, detail_id, name) {
        layer.confirm("作废后无法还原,确定要作废[" + name + "]吗？", {icon: 3}, function () {
            jQuery.ajax({
                url: "${contextPath}/admin/gcard/abolish_detail?id=" + id + "&detailId=" + detail_id,
                type: 'post',
                async: false, //默认为true 异步
                dataType: 'json',
                error: function (jqXHR, textStatus, errorThrown) {
                },
                success: function (result) {
                    if (result.success) {
                        layer.msg('作废成功', {icon: 1, time: 1000}, function () {
                            window.location.reload(true);
                        });
                    } else {
                        layer.msg(result.result || result.msg, {icon: 2});
                    }
                }
            });
        });
    }

    function changeMobile(gcardDetailId) {
        layer.prompt({
            formType: 0,
            //value: stocks,
            title: '修改领取手机号',
            area: ['800px', '350px'] //自定义文本域宽高
        }, function(value, index, elem){
            if(isEmpty(value)){
                layer.alert("请输入手机号");
                return;
            }
            //修改手机号
            $.ajax({
                type: "post",
                data:{"gcardDetailId":gcardDetailId,"mobile":value},
                url: "${contextPath}/admin/gcard/changeGcardMobile",
                dataType: "json",
                async : true, //默认为true 异步
                success: function(msg){
                    if(msg=="OK"){
                        layer.msg('更新成功！',{icon:1,time:700},function(){
                            parent.location.reload();
                            layer.close(index);
                        });
                    }else{
                        layer.msg(msg,{icon:2,time:700},function(){
                            parent.location.reload();
                            layer.close(index);
                        });
                    }


                }
            });
            layer.close(index);
        });
    }

    function changeUserGcardStatus(gcardUserId,status,msg) {
        layer.confirm(msg, {
            btn: ['确定', '取消'], //按钮
            icon: 3
        }, function () {
            $.ajax({
                type: "post",
                data:{"gcardUserId":gcardUserId,"status":status},
                url: "${contextPath}/admin/gcard/changeUserGcardStatus",
                dataType: "json",
                async : true, //默认为true 异步
                success: function(msg){
                    if(msg=="OK"){
                        layer.msg('操作成功！',{icon:1,time:700},function(){
                            parent.location.reload();
                        });
                    }else{
                        layer.msg(msg,{icon:2,time:700},function(){
                            parent.location.reload();
                        });
                    }
                }
            });
        })

    }

    $("#picFile").change(function(){
        checkImgType(this);
        checkImgSize(this,5120);
    });

    function updateEndDate(){

        if($("#endDate").prop("disabled")){
            $("#endDate").prop("disabled",false);
            $("#updateEndDate").text("保存时间");
        }else{
            var endDate = $("#endDate").val();
            var id = $("#id").val();
            $.post("/admin/gcard/updateGCardEndDate",{"id":id,"endDate":endDate},function(result){
                result = JSON.parse(result);
                if(result.success){
                    $("#endDate").prop("disabled",true);
                    $("#updateEndDate").text("修改截止时间");
                }else{
                    console.log(result);
                    layer.msg("修改时间失败");
                }
            });
        }
    }

</script>
</html>
