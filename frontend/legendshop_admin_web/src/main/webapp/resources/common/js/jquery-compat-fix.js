// jquery-compat-fix.js
(function($) {
    "use strict";

    // ===== 修复 $.browser 缺失问题 =====
    if (!$.browser) {
        console.info("Creating $.browser polyfill");

        $.browser = {
            msie: false,
            mozilla: false,
            webkit: false,
            safari: false,
            opera: false,
            version: "0"
        };

        var ua = navigator.userAgent.toLowerCase();
        var match = /(chrome|msie|firefox|safari|opr|opera)[\/\s]([\w\.]+)/.exec(ua) ||
            /(trident)(?:.*rv:([\w.]+))?/.exec(ua) || [];

        if (match[1]) {
            switch (match[1]) {
                case "trident":
                    $.browser.msie = true;
                    $.browser.version = match[2] || "11.0";
                    break;
                case "msie":
                    $.browser.msie = true;
                    $.browser.version = match[2];
                    break;
                case "firefox":
                    $.browser.mozilla = true;
                    $.browser.version = match[2];
                    break;
                case "chrome":
                case "safari":
                    $.browser.webkit = true;
                    $.browser.safari = (match[1] === "safari");
                    $.browser.version = match[2];
                    break;
                case "opr":
                case "opera":
                    $.browser.opera = true;
                    $.browser.version = match[2];
                    break;
            }
        }

        // 额外检测WebKit
        if (!$.browser.webkit && ua.indexOf('webkit') > -1) {
            $.browser.webkit = true;
            if (!$.browser.version) {
                match = /webkit\/([\w.]+)/.exec(ua);
                $.browser.version = match ? match[1] : "unknown";
            }
        }
    }

    // ===== 修复弃用的事件方法 =====
    console.info("Patching deprecated jQuery methods");

    // 方法替换映射
    const methodMap = {
        'bind': { type: 'method', replace: 'on' },
        'unbind': { type: 'method', replace: 'off' },
        'delegate': { type: 'method', replace: 'on' },
        'undelegate': { type: 'method', replace: 'off' },
        'size': {
            type: 'property',
            handler: function() {
                // console.warn('jQuery.fn.size() is deprecated, use .length property');
                return this.length;
            }
        },
        'toggle': {
            type: 'method',
            handler: function(fn1, fn2) {
                console.warn('jQuery.fn.toggle() for events is deprecated');
                return this.on('click', function(e) {
                    var handler = (this.tog = !this.tog) ? fn1 : fn2;
                    return handler.apply(this, arguments);
                });
            }
        },
        'live': {
            type: 'method',
            replace: 'on'
        }
    };

    // 快捷事件映射
    const shortcutMap = {
        'hover': function(fnOver, fnOut) {
            return this.on('mouseenter', fnOver).on('mouseleave', fnOut || fnOver);
        }
    };

    // 事件快捷方式
    const events = ['click', 'mouseover', 'mouseout', 'mousedown',
        'mouseup', 'dblclick', 'blur', 'focus',
        'change',       // 处理change事件
        'submit',       // 处理form提交事件
        'input',        // 扩展支持input事件
        'keydown',      // 键盘事件
        'keyup',
        'select',
        'keypress',
        'resize',       // 窗口调整事件
        'scroll'        // 滚动事件
    ];

    // 替换弃用方法
    $.each(methodMap, function(oldMethod, config) {
        if (config.type === 'property') {
            $.fn[oldMethod] = config.handler;
        } else {
            const newMethod = config.replace;
            $.fn[oldMethod] = function() {
                // console.warn(`jQuery.fn.${oldMethod}() is deprecated, using ${newMethod} instead`);
                return $.fn[newMethod].apply(this, arguments);
            };
        }
    });

    // 特殊方法处理
    $.each(shortcutMap, function(method, implementation) {
        $.fn[method] = implementation;
    });

    // 事件快捷方式
    $.each(events, function(_, eventName) {
        var originalMethod = $.fn[eventName];

        $.fn[eventName] = function(fn) {
            if (eventName === 'select') {
                return arguments.length > 0 ?
                    this.on(eventName, fn) :
                    this.trigger(eventName);
            }

            // 特殊处理submit事件
            if (eventName === 'submit') {
                if (arguments.length === 0) {
                    // 无参数时触发原生表单提交
                    return this.each(function() {
                        if (typeof this.submit === 'function') {
                            this.submit();
                        }
                    });
                }
                // 有参数时使用标准事件绑定
                return this.on('submit', null, fn);
            }

            if (eventName === 'change') {
                if (arguments.length === 0) {
                    // 无参数时触发原生change事件
                    return this.each(function() {
                        if (typeof this[eventName] === 'function') {
                            this[eventName]();
                        }
                    });
                }
                // 有参数时使用标准事件绑定
                return this.on(eventName, null, fn);
            }

            // 其他事件保持原有逻辑
            return arguments.length > 0 ?
                this.on(eventName, null, fn) :
                this.trigger(eventName);
        };
    });

    // ===== 添加额外兼容层 =====
    if (!$.fn.andSelf) {
        $.fn.andSelf = function() {
            console.warn("jQuery.fn.andSelf() is deprecated, use .addBack()");
            return this.addBack.apply(this, arguments);
        };
    }

    if (!$.expr[':'] && $.expr.pseudos) {
        console.info("Migrating jQuery.expr[':'] to pseudos");
        $.expr[':'] = $.expr.pseudos;
    } else if ($.expr[':'] && !$.expr.pseudos) {
        // 反向兼容旧版本
        $.expr.pseudos = $.expr[':'];
    }

    // 修复2: jQuery.trim → String.prototype.trim
    if (!$.trim) {
        console.info("Polyfilling jQuery.trim");
        $.trim = function(text) {
            return (text || "").toString().trim();
        };
    }

    // 修复3: jQuery.isFunction → typeof检查
    if (!$.isFunction) {
        console.info("Polyfilling jQuery.isFunction");
        $.isFunction = function(obj) {
            return typeof obj === 'function';
        };
    }

    // ▼▼▼▼▼▼▼▼▼▼ 新增的parseJSON修复 ▼▼▼▼▼▼▼▼▼▼
    // 修复4: jQuery.parseJSON → JSON.parse
    if (!$.parseJSON || (typeof $.parseJSON === 'function' && !$.parseJSON._migrateWrapped)) {
        console.info("Polyfilling jQuery.parseJSON");
        $.parseJSON = function(json) {
            if (!this._migratePatched) {
                console.warn("jQuery.parseJSON() is deprecated; use JSON.parse() instead");
                this._migratePatched = true;
            }
            if (json === null || json === undefined) {
                return null;
            }
            if (typeof json !== 'string') {
                throw new Error("Invalid JSON input");
            }
            json = $.trim(json);
            if (json === '') {
                return null;
            }
            return JSON.parse(json);
        };
        $.parseJSON._migrateWrapped = true;
    }
    // ▲▲▲▲▲▲▲▲▲▲ 新增结束 ▲▲▲▲▲▲▲▲▲▲

    // ===== 添加警告拦截层 =====
    // 修改伪类代理部分代码
    const origPseudo = $.expr.pseudos || $.expr[':'] || {};

    // 创建带方法转发的代理对象
    const pseudoProxy = Object.keys(origPseudo).reduce((proxy, pseudoName) => {
        const original = origPseudo[pseudoName];
        if (typeof original === 'function') {
            proxy[pseudoName] = function() {
                // console.warn(`Deprecated pseudo-class :${pseudoName}`);
                try {
                    // 将arguments转换为数组处理
                    const args = Array.from(arguments);

                    // 如果 args[0] 是 DOM 元素
                    const domDependentPseudos = ['visible', 'hidden', 'selected'];
                    if (!domDependentPseudos.includes(pseudoName)) {
                        if (args[0] instanceof HTMLElement) {
                            // 保留原来的转换逻辑
                            if (args[0].id) {
                                args[0] = `#${args[0].id}`;
                            } else if (args[0].className) {
                                args[0] = `.${args[0].className.split(' ')[0]}`;
                            }
                            args[0] = $(args[0])
                        }
                    }

                    // if(pseudoName === 'selected') {
                    //     if (args[1] instanceof HTMLElement) {
                    //         // 使用元素的 ID 或 tagName 来生成选择器
                    //         if (args[1].id) {
                    //             args[1] = `#${args[1].id}`;  // 根据 ID 创建选择器
                    //         } else if (args[1].className) {
                    //             args[1] = `.${args[1].className.split(' ')[0]}`;  // 使用类名（假设只取第一个类名）
                    //         }
                    //     }
                    // }

                    // 针对 :not 伪类的特殊处理
                    if (pseudoName === 'not') {
                        if (args[0] && args[0].jquery && args[0].selector) {
                            args[0] = args[0].selector;
                        }
                    }
                    // 针对 :has 伪类的特殊处理
                    else if (pseudoName === 'has') {
                        if (args[0] && args[0].jquery && args[0].selector) {
                            args[0] = args[0].selector;
                        }
                    }
                    // 针对 :contains 伪类的特殊处理
                    else if (pseudoName === 'contains') {
                        // 如果参数是一个 jQuery 对象，将其转换为文本内容
                        if (args[0] && args[0].jquery) {
                            args[0] = args[0].text();
                        }
                    }
                    // 针对 :lang 伪类的特殊处理
                    else if (pseudoName === 'lang') {
                        if (args[0]) {
                            args[0] = String(args[0]).toLowerCase(); // 转换为小写
                        }
                    }
                    // 针对 :eq 伪类的特殊处理
                    else if (pseudoName === 'eq') {
                        if (args[0] !== undefined) {
                            args[0] = String(args[0]); // 强制转换为字符串
                        }
                    }
                    // 针对 :lt 和 :gt 伪类的特殊处理
                    else if (pseudoName === 'lt' || pseudoName === 'gt') {
                        if (args[0] !== undefined) {
                            args[0] = String(args[0]); // 强制转换为字符串
                        }
                    }

                    // 转换所有可能包含数值的参数为字符串
                    if (args[3] !== undefined) {
                        args[3] = String(args[3]);  // 强制转换为字符串
                    }
                    return original.apply(this, args);
                } catch (e) {
                    console.error(`Pseudo-class :${pseudoName} error:`, e);
                    return false;
                }
            };
        } else {
            proxy[pseudoName] = original;
        }
        return proxy;
    }, {});
    // 保持原型链继承
    $.expr.pseudos = Object.assign(Object.create(origPseudo), pseudoProxy);
    $.expr[':'] = $.expr.pseudos;

    // ===== 添加:radio伪类支持 =====（移动到这里）
    if (!$.expr.pseudos.radio) {
        $.expr.pseudos.radio = function(elem) {
            // 增强检测逻辑：确保是input元素且type为radio
            return elem.tagName === 'INPUT' && elem.type === 'radio';
        };
    }


    // ===== 兼容旧插件可能的重写 =====
    // 确保所有伪类同时存在于两个属性
    // $.each(['pseudos', ':'], function(_, key) {
    //     if ($.expr[key]) {
    //         $.expr[key].filters = $.expr.filters;
    //         $.expr[key].pseudos = $.expr.pseudos;
    //     }
    // });

})(jQuery);
