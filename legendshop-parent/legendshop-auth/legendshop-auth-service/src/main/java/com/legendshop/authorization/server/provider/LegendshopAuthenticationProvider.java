package com.legendshop.authorization.server.provider;

import cn.legendshop.common.core.enums.AuthTypeEnum;
import cn.legendshop.common.core.enums.UserTypeEnum;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.security.component.LegendShopPreAuthenticationChecks;
import cn.legendshop.common.security.enums.ClientIdEnum;
import cn.legendshop.common.security.token.LegendshopAuthenticationToken;
import cn.legendshop.common.security.utils.CryptographicSignatureUtil;
import cn.legendshop.common.security.utils.UserTokenUtil;
import cn.legendshop.user.api.dto.auth.BasisLoginParamsDTO;
import com.legendshop.authorization.server.service.LegendShopUserDetailsService;
import com.legendshop.authorization.server.service.TokenHandleService;
import com.legendshop.authorization.server.utils.RequestLimiterUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsChecker;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;

public class LegendshopAuthenticationProvider implements AuthenticationProvider {

	@Getter
	@Setter
	protected PasswordEncoder passwordEncoder;

	@Getter
	@Setter
	protected StringRedisTemplate redisTemplate;

	@Getter
	@Setter
	protected RequestLimiterUtil requestLimiterUtil;

	@Getter
	@Setter
	protected LegendShopUserDetailsService userDetailsService;

	@Getter
	@Setter
	protected UserTokenUtil userTokenUtil;

	@Getter
	@Setter
	protected TokenHandleService tokenHandleService;

	private final UserDetailsChecker detailsChecker = new LegendShopPreAuthenticationChecks();

	@Override
	public Authentication authenticate(Authentication authentication) throws AuthenticationException {
		LegendshopAuthenticationToken authenticationToken = (LegendshopAuthenticationToken) authentication;
		// 参数校验
		BasisLoginParamsDTO loginParams = paramVerify(authenticationToken);
		String principal = loginParams.getPrincipal();
		// 查询登录用户
		UserDetails user;

		try {
			user = this.userDetailsService.loadUserByUsername(loginParams);
		} catch (Exception e) {
			String msg = this.requestLimiterUtil.fail(principal);
			 throw new InvalidGrantException(msg);
		}
		this.detailsChecker.check(user);

		//登录前，查看当前帐号是否有登录，如果有登录，会把上一次登录的有效AccessToken清理。（目前只踢移动端用户，商家，平台后台先不限制）
		if(ClientIdEnum.APP.getType().equals(authenticationToken.getClientId())
				|| ClientIdEnum.H5.getType().equals(authenticationToken.getClientId())
				|| ClientIdEnum.MP.getType().equals(authenticationToken.getClientId())
				|| ClientIdEnum.MINI.getType().equals(authenticationToken.getClientId())) {

			String userToken = userTokenUtil.getUserToken(authenticationToken.getClientId(), user.getUsername());
			//如果有AccessToken，则需要删除，把上一次登录的用户踢走。
			if(StringUtils.isNotBlank(userToken)){
				tokenHandleService.delToken(userToken, loginParams.getUserType().getLoginType().trim().trim());
			}

		}

		//重新生成新的AccessToken
		LegendshopAuthenticationToken authenticationResult = new LegendshopAuthenticationToken(user, user.getAuthorities());
		authenticationResult.setDetails(authenticationToken.getDetails());
		this.requestLimiterUtil.success(principal);
		return authenticationResult;
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return LegendshopAuthenticationToken.class.isAssignableFrom(authentication);
	}

	private BasisLoginParamsDTO paramVerify(LegendshopAuthenticationToken authenticationToken) {
		String principal = (String) authenticationToken.getPrincipal();
		String credentials = (String) authenticationToken.getCredentials();
		String thirdPartyIdentifier = authenticationToken.getThirdPartyIdentifier();
		String authType = authenticationToken.getAuthType();
		String userType = authenticationToken.getUserType();
		String extended = authenticationToken.getExtended();
		String userKey = authenticationToken.getUserKey();
		String thirdPartyUserLoginCode = authenticationToken.getThirdPartyUserLoginCode();
		if (StringUtils.isBlank(principal)) {
			throw new InvalidGrantException("用户登录标识不能为空");
		}
		if (StringUtils.isBlank(credentials)) {
			throw new InvalidGrantException("用户登录凭证不能为空");
		}

		AuthTypeEnum authTypeEnum;
		UserTypeEnum userTypeEnum;

		try {
			authTypeEnum = AuthTypeEnum.codeValue(authType);
			userTypeEnum = UserTypeEnum.codeValue(userType);
		} catch (Exception e) {
			throw new InvalidGrantException(e.getMessage());
		}

		// 解密
		principal = CryptographicSignatureUtil.decrypt(principal);
		credentials = CryptographicSignatureUtil.decrypt(credentials);

		// 限制请求次数
		if (authTypeEnum.equals(AuthTypeEnum.PASSWORD)) {
			// 请求次数
			R<String> limiter = this.requestLimiterUtil.limiter(principal);
			if (limiter.getSuccess() == null || !limiter.getSuccess()) {
				throw new InvalidGrantException(limiter.getMsg());
			}
		}

		return new BasisLoginParamsDTO(principal, credentials, thirdPartyIdentifier, authTypeEnum, userTypeEnum, extended, "", "", userKey,thirdPartyUserLoginCode);
	}
}
