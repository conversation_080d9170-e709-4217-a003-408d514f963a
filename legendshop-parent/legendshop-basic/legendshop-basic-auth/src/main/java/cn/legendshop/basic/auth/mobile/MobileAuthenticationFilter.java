/*
 * LegendShop微服务商城系统
 * <p>
 * ©版权所有,并保留所有权利。
 */
package cn.legendshop.basic.auth.mobile;

import cn.legendshop.basic.auth.service.CustomerUserDetailsService;
import com.legendshop.common.data.cache.util.RedisUtil;
import com.legendshop.util.AppUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationEventPublisher;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.security.spec.KeySpec;
import java.util.Base64;

/**
 * 手机号登录验证filter
 */
public class MobileAuthenticationFilter extends AbstractAuthenticationProcessingFilter {
    // 设置请求参数为mobile
    private static final String SPRING_SECURITY_FORM_MOBILE_KEY = "mobile";
    private static final String SPRING_SECURITY_FORM_PASSWORD_KEY = "password";
    private static final String SPRING_SECURITY_FORM_CAPTCHA_KEY = "captcha";
    private static final String SPRING_SECURITY_FORM_CAPTCHA_KEY_KEY = "captchaKey";
    private static final String AES_KEY = "385f33cb91484b04a177828829081ab7";

    @Getter
    @Setter
    private String mobileParameter = SPRING_SECURITY_FORM_MOBILE_KEY;

    @Getter
    @Setter
    private String passwordParameter = SPRING_SECURITY_FORM_PASSWORD_KEY;

    @Getter
    @Setter
    private String captchaParameter = SPRING_SECURITY_FORM_CAPTCHA_KEY;

    @Getter
    @Setter
    private String captchaKeyParameter = SPRING_SECURITY_FORM_CAPTCHA_KEY_KEY;

    // 只进行post请求处理
    @Getter
    @Setter
    private boolean postOnly = true;

    @Getter
    @Setter
    private AuthenticationEventPublisher eventPublisher;

    @Getter
    @Setter
    private AuthenticationEntryPoint authenticationEntryPoint;

    @Getter
    @Setter
    private CustomerUserDetailsService customerUserDetailsService;

    @Getter
    @Setter
    private RedisTemplate<String, String> redisTemplate;

    @Getter
    @Setter
    private RedisUtil redisUtil;

    MobileAuthenticationFilter() {
        // 要拦截的请求
        super(new AntPathRequestMatcher("/oauth/mobileToken", "POST"));
    }

//    private RedisTemplate<String, String> getRedisTemplate(HttpServletRequest request) {
//        if (redisTemplate == null) {
//            ServletContext servletContext = request.getServletContext();
//            redisTemplate = WebApplicationContextUtils.getRequiredWebApplicationContext(servletContext)
//                    .getBean("stringRedisTemplate", RedisTemplate.class);
//        }
//        return redisTemplate;
//    }

//    private RedisUtil getRedisUtil(HttpServletRequest request) {
//        if (redisUtil == null) {
//            ServletContext servletContext = request.getServletContext();
//            redisUtil = WebApplicationContextUtils.getRequiredWebApplicationContext(servletContext)
//                    .getBean(RedisUtil.class);
//        }
//        return redisUtil;
//    }

    @Override
    @SneakyThrows
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) {
        if (postOnly && !request.getMethod().equals(HttpMethod.POST.name())) {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }
        String mobile = obtainMobile(request);
        String password = obtainPassword(request);
        String captcha = obtainCaptcha(request);
        String captchaKey = obtainCaptchaKey(request);

        if (mobile == null) {
            mobile = "";
        }
        mobile = mobile.trim();

        // 验证码校验
        if (StringUtils.isBlank(captcha) || StringUtils.isBlank(captchaKey)) {
            throw new AuthenticationServiceException("验证码不能为空");
        }
        String key ="captcha:" + captchaKey;
        logger.info("校验验证码，redisKey={}"+ key);
        Object object = redisUtil.getObject(key);
        String redisCaptcha="";
        if (AppUtils.isNotBlank(object)){
            redisCaptcha=object.toString();
        }
        logger.info("从Redis获取到的验证码={}"+ redisCaptcha);
        
        if (StringUtils.isBlank(redisCaptcha)) {
            throw new AuthenticationServiceException("验证码已过期");
        }
        
        // 验证码比对
        if (!captcha.equalsIgnoreCase(redisCaptcha)) {
            throw new AuthenticationServiceException("验证码错误");
        }
        
        // 删除验证码
        redisUtil.delKey("captcha:" + captchaKey);

        // 解密密码
        try {
            password = decryptPassword(password);
        } catch (Exception e) {
            throw new AuthenticationServiceException("密码解密失败");
        }

        // 把手机号传进MobileAuthenticationToken
        MobileAuthenticationToken mobileAuthenticationToken = new MobileAuthenticationToken(mobile, password);
        setDetails(request, mobileAuthenticationToken);

        // 调用AuthenticationManager
        Authentication authResult = this.getAuthenticationManager().authenticate(mobileAuthenticationToken);
        logger.info("Authentication success: " + authResult);
        SecurityContextHolder.getContext().setAuthentication(authResult);

        return authResult;
    }

    // 获取手机号
    private String obtainMobile(HttpServletRequest request) {
        return request.getParameter(this.mobileParameter);
    }

    // 获取认证
    private String obtainPassword(HttpServletRequest request) {
        return request.getParameter(this.passwordParameter);
    }

    // 获取验证码
    private String obtainCaptcha(HttpServletRequest request) {
        return request.getParameter(this.captchaParameter);
    }

    // 获取验证码key
    private String obtainCaptchaKey(HttpServletRequest request) {
        return request.getParameter(this.captchaKeyParameter);
    }

    private void setDetails(HttpServletRequest request, MobileAuthenticationToken authRequest) {
        authRequest.setDetails(this.authenticationDetailsSource.buildDetails(request));
    }

    /**
     * AES解密
     */
    private String decryptPassword(String encryptedPassword) throws Exception {
        try {
            byte[] key = AES_KEY.getBytes();
            SecretKey secretKey = new SecretKeySpec(key, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            // 使用Base64解码而不是十六进制解码
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedPassword);
            byte[] plainBytes = cipher.doFinal(encryptedBytes);
            return new String(plainBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("密码解密失败: " + e.getMessage(), e);
            throw e;
        }
    }
}

