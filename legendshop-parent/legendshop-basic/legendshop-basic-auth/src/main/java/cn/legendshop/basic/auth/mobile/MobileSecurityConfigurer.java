/*
 *
 * LegendShop微服务商城系统
 *
 * ©版权所有,并保留所有权利。
 *
 */
package cn.legendshop.basic.auth.mobile;

import cn.legendshop.basic.auth.handler.MobileLoginFailureHandler;
import cn.legendshop.basic.auth.service.CustomerUserDetailsService;
import cn.legendshop.common.rabbitmq.utils.ThirdLogSendMsgUtil;
import cn.legendshop.common.security.component.ResourceAuthExceptionEntryPoint;
import cn.legendshop.user.api.client.UserServiceClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.legendshop.common.data.cache.util.RedisUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationEventPublisher;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 * 手机号登录配置入口
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
public class MobileSecurityConfigurer extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {


	@Autowired
	private TokenStore tokenStore;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private RedisUtil redisUtil;

	@Autowired
	private PasswordEncoder passwordEncoder;

	@Autowired
	private ThirdLogSendMsgUtil thirdLogSendMsgUtil;

	@Autowired
	private MobileLoginFailureHandler mobileLoginFailureHandler;

	@Autowired
	private CustomerUserDetailsService customerUserDetailsService;

	@Autowired
	private AuthenticationSuccessHandler mobileLoginSuccessHandler;

	@Autowired
	private AuthenticationEventPublisher defaultAuthenticationEventPublisher;

	@Autowired
	private UserServiceClient userServiceClient;


	@Override
	public void configure(HttpSecurity http) {
		MobileAuthenticationFilter mobileAuthenticationFilter = new MobileAuthenticationFilter();
		// 设置AuthenticationManager
		mobileAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
		// 设置成功处理器
		mobileAuthenticationFilter.setAuthenticationSuccessHandler(mobileLoginSuccessHandler);
		// 设置异常处理器
		mobileAuthenticationFilter.setAuthenticationFailureHandler(mobileLoginFailureHandler);
		mobileAuthenticationFilter.setCustomerUserDetailsService(customerUserDetailsService);
		mobileAuthenticationFilter.setEventPublisher(defaultAuthenticationEventPublisher);
		mobileAuthenticationFilter.setAuthenticationEntryPoint(new ResourceAuthExceptionEntryPoint(objectMapper));
		// 设置RedisUtil
		mobileAuthenticationFilter.setRedisUtil(redisUtil);

		// 设置provider
		MobileAuthenticationProvider mobileAuthenticationProvider = new MobileAuthenticationProvider();
		mobileAuthenticationProvider.setCustomerUserDetailsService(customerUserDetailsService);
		mobileAuthenticationProvider.setPasswordEncoder(passwordEncoder);
		mobileAuthenticationProvider.setThirdLogSendMsgUtil(thirdLogSendMsgUtil);
		mobileAuthenticationProvider.setRedisTemplate(redisTemplate);
		mobileAuthenticationProvider.setTokenStore(tokenStore);
		mobileAuthenticationProvider.setUserServiceClient(userServiceClient);
		http.authenticationProvider(mobileAuthenticationProvider).addFilterAfter(mobileAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
	}
}
