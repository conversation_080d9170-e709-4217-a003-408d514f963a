package cn.legendshop.obei;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.legendshop.common.core.config.ErpSendDataUrlConfig;
import cn.legendshop.common.core.config.ThirdParameterConfig;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.enums.ErpUrlEnum;
import cn.legendshop.obei.param.AccessControlParam;
import cn.legendshop.obei.param.AccessControlStateParam;
import cn.legendshop.obei.param.BatchUpdateGoodsParam;
import cn.legendshop.url.ErpSendDataUrlConstant;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;


@Slf4j
public class RemoteErpClient {
	private String getToken() {
		// 测试
		// String url = "https://dmzesb.nisco.cn/dmzesb/JRPTWL/BIGPURCHASESYS/services/ProvideToken1?client_code=M4PG0nbmhh6RWTEmuZ1Hg9%2BSOdn32GfRvcYm23lfp0Wp%2B1OC9ubbXEHojImXjQNAa22%2BaI3zvfUOByjfKwpWaj185nc3LgVZ8u1r0g33rnjJTjBuW1bmtq3LFgLnth1%2BDlGEG51aHxdZP%2FWiZtwl15FYIcxCYvMWl4NzLG7B1T0%3D";
		// 生产
		String url = "https://dmzesb.nisco.cn/dmzesb/JRPTWL/BIGPURCHASESYS/services/ExternalControllerGetTokenZS?client_code=UpwnxdTniIVUR6BFqXRuKejeHof66isR6CVyn%2BZK6pPW%2BD%2FbZsBlFkZ%2F8Plqrbg5Tajm0evj%2BYT56P1xPRxwePT006W4Y%2F8TCsxF2csc1g32mcLBYO%2Bxeg18TO%2FXONCpJOecTQL6UAmde3XptalrGAAVQdhrfGhYoD0ZX8uoKnk%3D";
		log.info("get token url:{}",url);
		HttpResponse execute = HttpRequest.get(url).execute();
		if(!execute.isOk()){
			log.info("token请求结果："+execute.body());
			throw new RuntimeException("获取token接口请求失败");
		}
		JSONObject jsonObject = JSONObject.parseObject(execute.body());
        return jsonObject.getString("access_token");
	}

	public  Boolean sendToERP(Object obj,String url, BiConsumer<String, String> consumer){
		String bodyString = null;
		String responseBody = null;
		try {
			bodyString = JSONObject.toJSONString(obj);
			log.info("请求参数  {} ",bodyString);
			HttpRequest post = HttpUtil.createPost(ErpSendDataUrlConstant.ERP_URL + url).body(bodyString);
			HttpResponse execute = post.execute();
			if (execute.isOk()) {
				responseBody  = execute.body();
				JSONObject jsonObject = JSONObject.parseObject(responseBody);
				if (Objects.equals(jsonObject.get("status"),HttpStatus.HTTP_OK)) {
					log.info(execute.body());
					return Boolean.TRUE;
				}else{
					log.error(responseBody);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		} finally {
			if(Objects.nonNull(consumer)){
				consumer.accept(bodyString,responseBody);
			}
		}
		return Boolean.FALSE;
	}

	public  Boolean batchUpdateGoods(String url,BatchUpdateGoodsParam batchUpdateGoodsParam, BiConsumer<String, String> consumer){
		String bodyString = null;
		String responseBody = null;
		try {
			BatchUpdateGoodsParam encodeParam = encode(batchUpdateGoodsParam);
			bodyString = JSONObject.toJSONString(encodeParam);
			log.info("请求参数 batchUpdateGoods {} ",bodyString);

			HttpRequest post = HttpUtil.createPost(url).body(bodyString);
			HttpResponse execute = post.execute();
			if (execute.isOk()) {
				responseBody  = execute.body();
				JSONObject jsonObject = JSONObject.parseObject(responseBody);
				if (Objects.equals(jsonObject.get("status"),HttpStatus.HTTP_OK)) {
					log.info(execute.body());
					return Boolean.TRUE;
				}else{
					String msg = jsonObject.get("msg").toString();
					if(StrUtil.isEmpty(msg)){
						return Boolean.FALSE;
					}
					throw new BusinessException(msg);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		} finally {
			if(Objects.nonNull(consumer)){
				consumer.accept(bodyString,responseBody);
			}
		}
		return Boolean.FALSE;
	}

	public  Boolean addNewGoods(String url,BatchUpdateGoodsParam batchUpdateGoodsParam, BiConsumer<String, String> consumer){
		String bodyString = null;
		String responseBody = null;
		try {
			BatchUpdateGoodsParam encodeParam = encode(batchUpdateGoodsParam);
			bodyString = JSONObject.toJSONString(encodeParam);
			log.info("请求参数 batchUpdateGoods {} ",bodyString);
			HttpRequest post = HttpUtil.createPost(url).body(bodyString);
			HttpResponse execute = post.execute();
			if (execute.isOk()) {
				responseBody  = execute.body();
				JSONObject jsonObject = JSONObject.parseObject(responseBody);
				if (Objects.equals(new Integer((String)jsonObject.get("status")), HttpStatus.HTTP_OK)) {
					log.info(execute.body());
					return Boolean.TRUE;
				} else{
					String msg = jsonObject.get("msg").toString();
					if(StrUtil.isEmpty(msg)){
						return Boolean.FALSE;
					}
					throw new BusinessException(msg);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		} finally {
			if(Objects.nonNull(consumer)){
				consumer.accept(bodyString,responseBody);
			}
		}
		return Boolean.FALSE;
	}

	private BatchUpdateGoodsParam encode(BatchUpdateGoodsParam batchUpdateGoodsParam) throws UnsupportedEncodingException {
		for (BatchUpdateGoodsParam.ProductData productDatum : batchUpdateGoodsParam.getProductData()) {
			String skuName = URLEncoder.encode(productDatum.getSkuName(), CharsetUtil.UTF_8);
			String brandName = URLEncoder.encode(productDatum.getBrandName(), CharsetUtil.UTF_8);
			String imP = URLEncoder.encode(productDatum.getImagePath(), CharsetUtil.UTF_8);

			if (StringUtils.isNotEmpty(productDatum.getSeoModel())) {
				String seoModel = URLEncoder.encode(productDatum.getSeoModel(), CharsetUtil.UTF_8);
				productDatum.setSeoModel(seoModel);
			}
			if (StringUtils.isNotEmpty(productDatum.getSpecificParam())) {
				String specificParam = URLEncoder.encode(productDatum.getSpecificParam(), CharsetUtil.UTF_8);
				productDatum.setSpecificParam(specificParam);
			}
			if (StringUtils.isNotEmpty(productDatum.getSaleUnit())) {
				String saleUnit = URLEncoder.encode(productDatum.getSaleUnit(), CharsetUtil.UTF_8);
				productDatum.setSaleUnit(saleUnit);
			}
			if (StringUtils.isNotEmpty(productDatum.getProductArea())) {
				String productArea = URLEncoder.encode(productDatum.getProductArea(), CharsetUtil.UTF_8);
				productDatum.setProductArea(productArea);
			}
			if (StringUtils.isNotEmpty(productDatum.getWarrantDesc())) {
				String warrantDes = URLEncoder.encode(productDatum.getWarrantDesc(), CharsetUtil.UTF_8);
				productDatum.setWarrantDesc(warrantDes);
			}
			productDatum.setSkuName(skuName);
			productDatum.setBrandName(brandName);
			productDatum.setImagePath(imP);
		}
		return batchUpdateGoodsParam;
	}


	public void accessControlRequest(AccessControlParam accessControlParam){


	}



	public void accessControlstate(AccessControlStateParam accessControlStateParam){

	}

	public Boolean returnMroInformation(String url, BatchUpdateGoodsParam batchUpdateGoodsParam, BiConsumer<String, String> consumer) {
		String bodyString = null;
		String responseBody = null;
		try {
			 // 获取token
			BatchUpdateGoodsParam encodeParam = encode(batchUpdateGoodsParam);
			bodyString = JSONObject.toJSONString(encodeParam);
			log.info("请求参数 batchUpdateGoods {} ",bodyString);
			HttpRequest post = HttpUtil.createPost(url).body(bodyString).header("Authorization", "Bearer " + getToken());
			HttpResponse execute = post.execute();
			if (execute.isOk()) {
				responseBody  = execute.body();
				JSONObject jsonObject = JSONObject.parseObject(responseBody);
				if (Objects.equals(new Integer((String)jsonObject.get("status")), HttpStatus.HTTP_OK)) {
					log.info(execute.body());
					return Boolean.TRUE;
				} else{
					String msg = jsonObject.get("msg").toString();
					if(StrUtil.isEmpty(msg)){
						return Boolean.FALSE;
					}
					throw new BusinessException(msg);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		} finally {
			if(Objects.nonNull(consumer)){
				consumer.accept(bodyString,responseBody);
			}
		}
		return Boolean.FALSE;
	}
}
