package cn.legendshop.data.dao;

import cn.legendshop.data.dto.ServiceBillStatisticsDTO;
import cn.legendshop.data.dto.ThirdIndustrialCountDTO;
import cn.legendshop.data.vo.*;
import cn.legendshop.order.vo.VmSaleItemVo;

import java.util.Date;
import java.util.List;

public interface IndustrialFinanceStatisticsDao {
    List<BillStatisticsVo> thirdIndustrialCount(Date startDate, Date endDate);

    List<AccountPeriodBillStatisticsVo> purchasingEnterpriseBillStatistics(ThirdIndustrialCountDTO countDTO);

    List<AccountPeriodBillStatisticsVo> getAccountPeriodBill(ThirdIndustrialCountDTO countDTO);

    List<ServiceBillStatisticsVo> serviceBillStatistics( ServiceBillStatisticsDTO timeDTO);

    List<ServiceBillExportVo> getThirdService(Date startDate, Date endDate, Integer shopId,Integer accountBookId);

    List<ServiceBillStatisticsVo> intelligentServiceBill(Date startDate, Date endDate);

    List<VmSaleServiceBillExportVo> getVmSaleService(Date startDate, Date endDate, Integer accountBookId);

    List<StationServiceVO> stationServiceStatistics(ServiceBillStatisticsDTO timeDTO);

    List<StationServiceChartRawVO> stationServiceChartByMonth(Date startDate, Date endDate);

    List<StationServiceBarRawVO> stationServiceBarByMonth(Date startDate, Date endDate);
}
