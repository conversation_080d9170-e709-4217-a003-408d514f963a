package cn.legendshop.data.dao.impl;

import cn.legendshop.data.dao.IndustrialFinanceStatisticsDao;
import cn.legendshop.data.dto.ServiceBillStatisticsDTO;
import cn.legendshop.data.dto.ThirdIndustrialCountDTO;
import cn.legendshop.data.vo.*;
import cn.legendshop.order.entity.ReconciliationBill;
import com.legendshop.dao.impl.GenericDaoImpl;
import com.legendshop.model.entity.Sub;
import com.legendshop.util.AppUtils;
import org.springframework.stereotype.Repository;

import java.lang.StringBuilder;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;

@Repository
public class IndustrialFinanceStatisticsDaoImpl extends GenericDaoImpl<ReconciliationBill,Long> implements IndustrialFinanceStatisticsDao  {

    @Override
    public List<BillStatisticsVo> thirdIndustrialCount(Date startDate, Date endDate) {
        String sql ="SELECT " +
                " d.site_name, " +
                " SUM( CASE WHEN b.settlement_status = 40 THEN b.bill_amount ELSE 0 END ) AS settled_amount, " +
                " SUM( CASE WHEN b.settlement_status <> 40 THEN b.bill_amount ELSE 0 END ) AS unsettled_amount, " +
                " SUM( b.bill_amount ) AS total_amount  " +
                "FROM " +
                " ls_reconciliation_bill b JOIN " +
                " ls_shop_detail d ON b.shop_id = d.shop_id  " +
                " where b.begin_time >= ? AND b.end_time <= ?  and b.shop_id <> 4000   " +
                "GROUP BY  " +
                " b.shop_id";
        return this.query(sql, BillStatisticsVo.class,startDate,endDate);
    }

    @Override
    public List<AccountPeriodBillStatisticsVo> purchasingEnterpriseBillStatistics(ThirdIndustrialCountDTO countDTO) {
        String sql ="SELECT " +
                " company_name, " +
                " SUM( CASE WHEN overdue = 1 THEN over_amount ELSE 0 END ) AS overdue_amount, " +
                " SUM( CASE WHEN overdue = 1 THEN 1 ELSE 0 END ) AS overdue, " +
                " sum( IF ( repayment_actual_time IS NULL,IF( DATEDIFF( NOW(), repayment_last_time )>0, DATEDIFF( NOW(), repayment_last_time ),0), IF(DATEDIFF( repayment_actual_time, repayment_last_time )>0,DATEDIFF( repayment_actual_time, repayment_last_time ),0)) ) as overdueDays ,  " +
                " SUM(bill_amount - IFNULL(repayment_amount,0)) AS allUnsettled " +
                "FROM " +
                " ls_account_period_bill  " +
                "WHERE  1=1  " +
                "and bill_amount <> 0  " +
//                " AND company_name <> '南京鑫智链科技信息有限公司'  " +
                "GROUP BY " +
                " company_name";
            return this.query(sql, AccountPeriodBillStatisticsVo.class);
        }

    @Override
    public List<AccountPeriodBillStatisticsVo> getAccountPeriodBill(ThirdIndustrialCountDTO countDTO) {
        String sql = " SELECT company_name,sum(IFNULL(repayment_amount,0)) AS settled_amount,\n" +
                " sum(bill_amount - IFNULL(repayment_amount,0))  AS unsettled_amount,\n" +
                "  sum(bill_amount) AS total_amount,\n" +
                "  Max(IFNULL(overdue,0)) AS isDue,\n" +
                " sum(IF ( repayment_actual_time IS NULL,IF( DATEDIFF( NOW(), repayment_last_time )>0, DATEDIFF( NOW(), repayment_last_time ),0), IF(DATEDIFF( repayment_actual_time, repayment_last_time )>0,DATEDIFF( repayment_actual_time, repayment_last_time ),0)))  AS overdueDay\n" +
                "  FROM `ls_account_period_bill`  \n" +
                "  where 1=1 and bill_amount <> 0 AND end_time  >=  ?  and end_time <=  ? \n" +
                "  AND company_name <> '南京鑫智链科技信息有限公司' group by company_name";
        return this.query(sql, AccountPeriodBillStatisticsVo.class,countDTO.getStartDate(),countDTO.getEndDate());
    }

    @Override
    public List<ServiceBillStatisticsVo> serviceBillStatistics(ServiceBillStatisticsDTO dto) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ")
               .append(" b.shop_id, ")
               .append(" MAX(c.shop_name) as shop_name, ")
               .append(" SUM(b.bill_amount) as total_amount, ")
               .append(" SUM(b.service_fee_amount) as service_charge_amount, ")
               .append(" SUM(CASE WHEN b.service_fee_status = 10 THEN b.service_fee_amount ELSE 0 END) as reconciling_amount, ")
               .append(" SUM(CASE WHEN b.service_fee_status = 20 THEN b.service_fee_amount ELSE 0 END) as invoicing_amount, ")
               .append(" SUM(CASE WHEN b.service_fee_status = 40 THEN b.service_fee_amount ELSE 0 END) as paid_amount ")
               .append("FROM ")
               .append(" ls_service_fee_bill b ")
               .append(" LEFT JOIN ls_industrial_outer_shop_config c ON b.shop_id = c.shop_id ")
               .append(" WHERE 1=1 ");
    
            List<Object> params = new ArrayList<>();
            
            if(dto.getShopId() != null){
                sql.append(" AND b.shop_id = ? ");
                params.add(dto.getShopId());
            }
            if(dto.getAccountBookId() != null){
                sql.append(" AND b.account_book_id = ? ");
                params.add(dto.getAccountBookId());
            }
            if(dto.getStartDate() != null){
                sql.append(" AND b.begin_time >= ? ");
                params.add(dto.getStartDate());
            }
            if(dto.getEndDate() != null){
                sql.append(" AND b.begin_time < ? ");
                params.add(dto.getEndDate());
            }  
            sql.append(" GROUP BY b.shop_id");
            
        return this.query(sql.toString(), ServiceBillStatisticsVo.class, params.toArray());
    }

    @Override
    public List<ServiceBillExportVo> getThirdService(Date startDate, Date endDate, Integer shopId,Integer accountBookId) {
        String sql = "SELECT DISTINCT\n" +
                "    s.sub_number,\n" +
                "    si.third_number,\n" +
                "    si.third_sku_id,\n" +
                "    si.prod_name,\n" +
                "    CASE WHEN s.delivery_type = '20' THEN si.out_count ELSE si.in_count END AS inCount,\n" +
                "    si.cash,\n" +
                "    IFNULL(si.refund_count,0),\n" +
                "    COALESCE(si.refund_count * si.cash, 0) AS \"refund_amount\",\n" +
                "    COALESCE((CASE WHEN s.delivery_type = '20' THEN si.out_count ELSE si.in_count END) * si.cash,0) AS \"order_amount\",\n" +
                "    GREATEST(COALESCE((CASE WHEN s.delivery_type = '20' THEN si.out_count ELSE si.in_count END * si.cash) - (IFNULL(si.refund_count, 0) * si.cash), 0), 0) AS actual_amount," +
                "    s.sub_date AS \"sub_date\",\n" +
                "    s.finally_date AS \"finally_date\",\n" +
                "    s.service_status AS \"service_status\"\n" +
                " FROM ls_sub s\n" +
                " LEFT JOIN ls_sub_item si ON s.sub_number = si.sub_number" +
                " where s.`status` in (30, 40) " +
                " and s.finally_date < ?  " +
                " and s.shop_id = ?  " +
                " and s.account_book_id = ?" +
                " and s.order_source <> 0 " +
                " and( s.service_status is null or s.service_status = 0)";

        return this.query(sql, ServiceBillExportVo.class,endDate,shopId,accountBookId);
    }

    @Override
    public List<ServiceBillStatisticsVo> intelligentServiceBill(Date startDate, Date endDate) {
        String sql = "";
        return this.query(sql, ServiceBillStatisticsVo.class,startDate,endDate);
    }

    @Override
    public List<VmSaleServiceBillExportVo> getVmSaleService(Date startDate, Date endDate, Integer accountBookId) {
        String sql = "SELECT vs.trade_number, vs.vm_code , vs.device_code , vs.vm_time , vsi.trade_num ,\n" +
                "           vsi.sku_code , vsi.sku_name , vsi.tax_price , vsi.no_tax_price ,\n" +
                "           vsi.tax_price*vsi.trade_num as totalTaxAmount ,vsi.tax_status , vsi.unit_name, vsi.pack \n" +
                "    FROM ls_vm_sale vs left join ls_vm_sale_item vsi on vs.trade_number = vsi.trade_number\n" +
                "    where 1=1\n" +
                "      and vs.vm_time <= ?" +
                "      and vs.factory_name = ? " +
                " and (vs.service_status = 0  or vs.service_status is null) ";
        String factoryName ="";
            if (101==accountBookId){
                factoryName="中信泰富特钢集团股份有限公司";
            }else {
                factoryName="南京钢铁股份有限公司";
            }
        return this.query(sql, VmSaleServiceBillExportVo.class,endDate,factoryName);
    }

    @Override
    public List<StationServiceVO> stationServiceStatistics(ServiceBillStatisticsDTO timeDTO) {
        String sql = "SELECT s.shop_name AS supplierName, " +
                "ROUND(SUM(b.bill_amount), 2) AS totalAmount, " +
                "COUNT(1) AS orderCount, " +
                "ROUND(SUM(s.actual_total) * 0.02, 2) AS serviceChargeAmount " +
                "FROM ls_smart_service_charge_bill b " +
                "LEFT JOIN ls_sub s ON b.reconciliation_no = s.smart_bill_sn " +
                "WHERE 1=1 " +
                "AND b.begin_time >= ? " +
                "AND b.begin_time < ? " +
                "GROUP BY s.shop_name;";
        return this.query(sql, StationServiceVO.class, timeDTO.getStartDate(), timeDTO.getEndDate());
    }

    @Override
    public List<StationServiceChartRawVO> stationServiceChartByMonth(Date startDate, Date endDate) {
        String sql = "SELECT " +
                "b.reconciliation_time AS reconciliationTime, " +
                "DATE_FORMAT(t.in_complete_operator_time, '%Y-%m') AS month, " +
                "t.shop_name AS supplierName, " +
                "ROUND(SUM(t.actual_total), 2) AS totalAmount, " +
                "COUNT(1) AS orderCount " +
                "FROM ( " +
                "     SELECT " +
                "         b.id, " +
                "         b.reconciliation_time, " +
                "         s.shop_name, " +
                "         b.bill_amount, " +
                "         s.actual_total, " +
                "         MIN(ls.in_complete_operator_time) AS in_complete_operator_time " +
                "     FROM ls_smart_service_charge_bill b " +
                "          LEFT JOIN ls_sub s ON b.reconciliation_no = s.smart_bill_sn " +
                "          LEFT JOIN ls_shipment ls ON s.sub_number = ls.order_no " +
                "     WHERE ls.in_complete_operator_time IS NOT NULL " +
                "       AND b.begin_time >= ? " +
                "       AND b.begin_time < ? " +
                "     GROUP BY b.id,  s.sub_number " +
                ") t " +
                "LEFT JOIN ls_smart_service_charge_bill b ON t.id = b.id " +
                "GROUP BY b.reconciliation_time, month, supplierName " +
                "ORDER BY b.reconciliation_time, month, supplierName";
        return this.query(sql, StationServiceChartRawVO.class, startDate, endDate);
    }

    @Override
    public List<StationServiceBarRawVO> stationServiceBarByMonth(Date startDate, Date endDate) {
        String sql = "SELECT " +
                "b.reconciliation_time AS reconciliationTime, " +
                "DATE_FORMAT(t.in_complete_operator_time, '%Y-%m') AS month, " +
                "ROUND(SUM(t.actual_total), 2) AS totalAmount, " +
                "COUNT(1) AS orderCount " +
                "FROM ( " +
                "     SELECT " +
                "         b.id, " +
                "         b.reconciliation_time, " +
                "         s.actual_total, " +
                "         MIN(ls.in_complete_operator_time) AS in_complete_operator_time " +
                "     FROM ls_smart_service_charge_bill b " +
                "          LEFT JOIN ls_sub s ON b.reconciliation_no = s.smart_bill_sn " +
                "          LEFT JOIN ls_shipment ls ON s.sub_number = ls.order_no " +
                "     WHERE ls.in_complete_operator_time IS NOT NULL " +
                "       AND b.begin_time >= ? " +
                "       AND b.begin_time < ? " +
                "     GROUP BY b.id,  s.sub_number " +
                ") t " +
                "LEFT JOIN ls_smart_service_charge_bill b ON t.id = b.id " +
                "GROUP BY b.reconciliation_time, month " +
                "ORDER BY b.reconciliation_time, month";
        return this.query(sql, StationServiceBarRawVO.class, startDate, endDate);
    }
}
