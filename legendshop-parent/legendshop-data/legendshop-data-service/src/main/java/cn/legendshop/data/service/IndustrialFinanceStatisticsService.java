package cn.legendshop.data.service;

import cn.legendshop.common.core.utils.R;
import cn.legendshop.data.dto.ServiceBillStatisticsDTO;
import cn.legendshop.data.dto.ServiceBillStatisticsExportDTO;
import cn.legendshop.data.dto.ThirdIndustrialCountDTO;
import cn.legendshop.data.vo.*;

import java.util.List;

public interface IndustrialFinanceStatisticsService {
    R<ThirdIndustrialCountVo> ThirdIndustrialCount(ThirdIndustrialCountDTO countDTO);

    R<EnterpriseBillStatisticsVo> purchasingEnterpriseBillStatistics(ThirdIndustrialCountDTO countDTO);

    R<ThirdServiceBillVo> serviceBillStatistics(ServiceBillStatisticsDTO timeDTO);

    List<ServiceBillExportVo> getThirdService(ServiceBillStatisticsExportDTO timeDTO);

    List<VmSaleServiceBillExportVo> getVmSaleService(ServiceBillStatisticsExportDTO exportDTO);

    R<List<StationServiceVO>> stationServiceStatistics(ServiceBillStatisticsDTO timeDTO);

    R<StationServiceChartVO> stationServiceChart(ServiceBillStatisticsDTO timeDTO);

    R<StationServiceBarVO> stationServiceBar(ServiceBillStatisticsDTO timeDTO);
}
