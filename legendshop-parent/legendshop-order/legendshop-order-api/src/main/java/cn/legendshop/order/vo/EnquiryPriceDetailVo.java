package cn.legendshop.order.vo;

import cn.legendshop.order.dto.EnquirpyFileDTO;
import cn.legendshop.order.entity.EnquiryPriceItem;
import cn.legendshop.user.api.entity.Expert;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class EnquiryPriceDetailVo {
    private Long id;

    /**
     * 委托单名称
     */
    private String epName;

    private Long accountBookId;

    /**
     * 采购单编号
     */
    private String epNo;

    /**
     * 采购类型
     */
    private String purchaseType;

    /**
     * 询价时间(天数）
     */
    private Integer inquiryDays;

    /**
     * 委托人公司
     */
    private String clientCompany;

    /**
     * 委托人公司id
     */
    private Long clientCompanyId;

    /**
     * 委托人
     */
    private String clientName;


    /**
     * 委托人User_id
     */
    private String clientUserId;

    /**
     * 委托人手机号
     */
    private String clientMobile;

    /**
     * 委托人邮箱
     */
    private String clientMail;

    /**
     * 代理机构
     */
    private String agencyCompany;

    /**
     * 代理人ID
     */
    private String agencyId;

    /**
     * 代理人
     */
    private String agencyName;

    /**
     * 代理人联系电话
     */
    private String agencyMobile;

    /**
     * 代理人邮箱
     */
    private String agencyMail;

    /**
     * 项目概算
     */
    private BigDecimal epAmount;

    /**
     * 委托人附件
     */
    private String attachedClient;

    /**
     * 代理人附件
     */
    private String attachedAgency;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收货地址Id
     */
    private Long addressId;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 委托单状态
     */
    private Integer status;

    /**
     * 报价截至日期
     */
    private Date inquiryDeadTime;

    /**
     * 审批人姓名
     */
    private String reviewerName;

    /**
     * 审批人手机号
     */
    private String reviewerMobile;

    /**
     * 报价开始时间
     */
    private Date inquiryBeginTime;

    /**
     * 报价结束时间
     */
    private Date inquiryEndTime;

    /**
     * 评判开始时间
     */
    private Date expertBeginTime;

    /**
     * 评判结束时间
     */
    private Date expertEndTime;

    /**
     * 审批开始时间
     */
    private Date approvalBeginTime;

    /**
     * 审批结束时间
     */
    private Date approvalEndTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private List<EnquirpyFileDTO> innerFile;

    private List<EnquiryPriceItem> enquiryPriceItems;
    private List<Expert> judgeMembers;
    private Map<String,String> attachments;

    /**
     * 附件  延长询价截止时间附件 少于3家附件  重新评审附件 oa会审的附件
     * @return
     */
    private String extensionAtt;
    private String below3OpenAtt;
    private String reReviewAtt;
    private String oaReviewInfoAtt;


    /**
     * 选中供应商名称
     * @return
     */
    private String selectShopNames;

    /**
     * 选中供应商名称
     * @return
     */
    private String isFast;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEpName() {
        return epName;
    }

    public void setEpName(String epName) {
        this.epName = epName;
    }

    public String getEpNo() {
        return epNo;
    }

    public void setEpNo(String epNo) {
        this.epNo = epNo;
    }

    public String getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Integer getInquiryDays() {
        return inquiryDays;
    }

    public void setInquiryDays(Integer inquiryDays) {
        this.inquiryDays = inquiryDays;
    }

    public String getClientCompany() {
        return clientCompany;
    }

    public void setClientCompany(String clientCompany) {
        this.clientCompany = clientCompany;
    }

    public Long getClientCompanyId() {
        return clientCompanyId;
    }

    public void setClientCompanyId(Long clientCompanyId) {
        this.clientCompanyId = clientCompanyId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientMobile() {
        return clientMobile;
    }

    public void setClientMobile(String clientMobile) {
        this.clientMobile = clientMobile;
    }

    public String getClientMail() {
        return clientMail;
    }

    public void setClientMail(String clientMail) {
        this.clientMail = clientMail;
    }

    public String getAgencyCompany() {
        return agencyCompany;
    }

    public void setAgencyCompany(String agencyCompany) {
        this.agencyCompany = agencyCompany;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public String getAgencyMobile() {
        return agencyMobile;
    }

    public void setAgencyMobile(String agencyMobile) {
        this.agencyMobile = agencyMobile;
    }

    public String getAgencyMail() {
        return agencyMail;
    }

    public void setAgencyMail(String agencyMail) {
        this.agencyMail = agencyMail;
    }

    public BigDecimal getEpAmount() {
        return epAmount;
    }

    public void setEpAmount(BigDecimal epAmount) {
        this.epAmount = epAmount;
    }

    public String getAttachedClient() {
        return attachedClient;
    }

    public void setAttachedClient(String attachedClient) {
        this.attachedClient = attachedClient;
    }

    public String getAttachedAgency() {
        return attachedAgency;
    }

    public void setAttachedAgency(String attachedAgency) {
        this.attachedAgency = attachedAgency;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getInquiryDeadTime() {
        return inquiryDeadTime;
    }

    public void setInquiryDeadTime(Date inquiryDeadTime) {
        this.inquiryDeadTime = inquiryDeadTime;
    }

    public String getReviewerName() {
        return reviewerName;
    }

    public void setReviewerName(String reviewerName) {
        this.reviewerName = reviewerName;
    }

    public String getReviewerMobile() {
        return reviewerMobile;
    }

    public void setReviewerMobile(String reviewerMobile) {
        this.reviewerMobile = reviewerMobile;
    }

    public Date getInquiryBeginTime() {
        return inquiryBeginTime;
    }

    public void setInquiryBeginTime(Date inquiryBeginTime) {
        this.inquiryBeginTime = inquiryBeginTime;
    }

    public Date getInquiryEndTime() {
        return inquiryEndTime;
    }

    public void setInquiryEndTime(Date inquiryEndTime) {
        this.inquiryEndTime = inquiryEndTime;
    }

    public Date getExpertBeginTime() {
        return expertBeginTime;
    }

    public void setExpertBeginTime(Date expertBeginTime) {
        this.expertBeginTime = expertBeginTime;
    }

    public Date getExpertEndTime() {
        return expertEndTime;
    }

    public void setExpertEndTime(Date expertEndTime) {
        this.expertEndTime = expertEndTime;
    }

    public Date getApprovalBeginTime() {
        return approvalBeginTime;
    }

    public void setApprovalBeginTime(Date approvalBeginTime) {
        this.approvalBeginTime = approvalBeginTime;
    }

    public Date getApprovalEndTime() {
        return approvalEndTime;
    }

    public void setApprovalEndTime(Date approvalEndTime) {
        this.approvalEndTime = approvalEndTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<EnquiryPriceItem> getEnquiryPriceItems() {
        return enquiryPriceItems;
    }

    public void setEnquiryPriceItems(List<EnquiryPriceItem> enquiryPriceItems) {
        this.enquiryPriceItems = enquiryPriceItems;
    }

    public String getClientUserId() {
        return clientUserId;
    }

    public void setClientUserId(String clientUserId) {
        this.clientUserId = clientUserId;
    }

    public List<Expert> getJudgeMembers() {
        return judgeMembers;
    }

    public void setJudgeMembers(List<Expert> judgeMembers) {
        this.judgeMembers = judgeMembers;
    }


    public Long getAccountBookId() {
        return accountBookId;
    }

    public void setAccountBookId(Long accountBookId) {
        this.accountBookId = accountBookId;
    }

    public Map<String, String> getAttachments() {
        return attachments;
    }

    public void setAttachments(Map<String, String> attachments) {
        this.attachments = attachments;
    }


    public List<EnquirpyFileDTO> getInnerFile() {
        return innerFile;
    }

    public void setInnerFile(List<EnquirpyFileDTO> innerFile) {
        this.innerFile = innerFile;
    }

    public String getExtensionAtt() {
        return extensionAtt;
    }

    public void setExtensionAtt(String extensionAtt) {
        this.extensionAtt = extensionAtt;
    }

    public String getBelow3OpenAtt() {
        return below3OpenAtt;
    }

    public void setBelow3OpenAtt(String below3OpenAtt) {
        this.below3OpenAtt = below3OpenAtt;
    }

    public String getReReviewAtt() {
        return reReviewAtt;
    }

    public void setReReviewAtt(String reReviewAtt) {
        this.reReviewAtt = reReviewAtt;
    }

    public String getOaReviewInfoAtt() {
        return oaReviewInfoAtt;
    }

    public void setOaReviewInfoAtt(String oaReviewInfoAtt) {
        this.oaReviewInfoAtt = oaReviewInfoAtt;
    }

    public String getSelectShopNames() {
        return selectShopNames;
    }

    public void setSelectShopNames(String selectShopNames) {
        this.selectShopNames = selectShopNames;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getIsFast() {
        return isFast;
    }

    public void setIsFast(String isFast) {
        this.isFast = isFast;
    }
}
