package cn.legendshop.order.vo;

import cn.hutool.core.util.NumberUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * 订单项信息
 *
 * @author: jzh
 * @create: 2022-10-09 15:13
 */
@Data
@Accessors(chain = true)
public class SubProductInfoVo {
	@ApiModelProperty(value = "图片")
	private String pic;

	@ApiModelProperty(value = "商品名称")
	private String productName;

	@ApiModelProperty(value = "规格型号")
	private String model;

	@ApiModelProperty(value = "出库数量")
	private BigDecimal outCount;

	@ApiModelProperty(value = "配送完成数量")
	private BigDecimal inCount;

	@ApiModelProperty(value = "退款数量")
	private BigDecimal refundCount;

	@ApiModelProperty(value = "供货价")
	private Double price;

	@ApiModelProperty(value = "下单数量")
	private BigDecimal basketCount;

	@ApiModelProperty(value = "实际数量")
	private BigDecimal actualCount;

	@ApiModelProperty(value = "剩余出库数量")
	private BigDecimal remainOutCount;

	@ApiModelProperty(value = "总价")
	private Double productTotalAmount;

	@ApiModelProperty(value = "订单状态")
	private Integer status;

	@ApiModelProperty(value = "订单号")
	private String subNumber;

	@ApiModelProperty(value = "第三方订单号")
	private String thirdOrderNo;

	@ApiModelProperty(value = "第三方skuId")
	private String thirdSkuId;

	@ApiModelProperty(value = "物料编码")
	private String materialCode;

	@ApiModelProperty(value = "物料单位")
	private String materialUnit;

	@ApiModelProperty(value = "物料描述")
	private String materialInfo;

	@ApiModelProperty(value = "商品ID")
	private Long prodId;

	@ApiModelProperty(value = "订单项ID")
	private Long subItemId;

	@ApiModelProperty(value = "剩余可售后量")
	private BigDecimal remainRefundCount;

	@ApiModelProperty(value = "售后数量")
	private BigDecimal afterRefundCount;

	@ApiModelProperty(value = "已发货售后数量")
	private BigDecimal afterShippedRefundCount;

	@ApiModelProperty(value = "客户要货时间/供应商应当送到时间")
	private Date deliveryDate;

	@ApiModelProperty(value = "南钢ERP要货超期天数")
	private Integer erpOverdueDay;

	@ApiModelProperty(value = "订单行备注/采购要求")
	private String sourcingRequest;

	@ApiModelProperty(value = "取消原因")
	private String cancelReason;

	@ApiModelProperty(value = "取消日期")
	private Date cancelDate;

	@ApiModelProperty(value = "评价ID")
	private Long commentId;

	@ApiModelProperty(value = "追评ID")
	private Long addCommentId;

	@ApiModelProperty(value = "快照ID")
	private Long snapshotId;


	public BigDecimal getRemainOutCount() {
		return NumberUtil.sub(Optional.ofNullable(actualCount).orElse(basketCount), outCount);
	}

	public String getPic() {
		return pic;
	}

	public void setPic(String pic) {
		this.pic = pic;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public BigDecimal getOutCount() {
		return outCount;
	}

	public void setOutCount(BigDecimal outCount) {
		this.outCount = outCount;
	}

	public BigDecimal getInCount() {
		return inCount;
	}

	public void setInCount(BigDecimal inCount) {
		this.inCount = inCount;
	}

	public BigDecimal getRefundCount() {
		return refundCount;
	}

	public void setRefundCount(BigDecimal refundCount) {
		this.refundCount = refundCount;
	}

	public Double getPrice() {
		return price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}

	public BigDecimal getBasketCount() {
		return basketCount;
	}

	public void setBasketCount(BigDecimal basketCount) {
		this.basketCount = basketCount;
	}

	public BigDecimal getActualCount() {
		return actualCount;
	}

	public void setActualCount(BigDecimal actualCount) {
		this.actualCount = actualCount;
	}

	public void setRemainOutCount(BigDecimal remainOutCount) {
		this.remainOutCount = remainOutCount;
	}

	public Double getProductTotalAmount() {
		return productTotalAmount;
	}

	public void setProductTotalAmount(Double productTotalAmount) {
		this.productTotalAmount = productTotalAmount;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getSubNumber() {
		return subNumber;
	}

	public void setSubNumber(String subNumber) {
		this.subNumber = subNumber;
	}

	public String getThirdOrderNo() {
		return thirdOrderNo;
	}

	public void setThirdOrderNo(String thirdOrderNo) {
		this.thirdOrderNo = thirdOrderNo;
	}

	public String getThirdSkuId() {
		return thirdSkuId;
	}

	public void setThirdSkuId(String thirdSkuId) {
		this.thirdSkuId = thirdSkuId;
	}

	public String getMaterialCode() {
		return materialCode;
	}

	public void setMaterialCode(String materialCode) {
		this.materialCode = materialCode;
	}

	public String getMaterialUnit() {
		return materialUnit;
	}

	public void setMaterialUnit(String materialUnit) {
		this.materialUnit = materialUnit;
	}

	public String getMaterialInfo() {
		return materialInfo;
	}

	public void setMaterialInfo(String materialInfo) {
		this.materialInfo = materialInfo;
	}

	public Long getProdId() {
		return prodId;
	}

	public void setProdId(Long prodId) {
		this.prodId = prodId;
	}

	public Long getSubItemId() {
		return subItemId;
	}

	public void setSubItemId(Long subItemId) {
		this.subItemId = subItemId;
	}

	public BigDecimal getRemainRefundCount() {
		return remainRefundCount;
	}

	public void setRemainRefundCount(BigDecimal remainRefundCount) {
		this.remainRefundCount = remainRefundCount;
	}

	public BigDecimal getAfterRefundCount() {
		return afterRefundCount;
	}

	public void setAfterRefundCount(BigDecimal afterRefundCount) {
		this.afterRefundCount = afterRefundCount;
	}

	public BigDecimal getAfterShippedRefundCount() {
		return afterShippedRefundCount;
	}

	public void setAfterShippedRefundCount(BigDecimal afterShippedRefundCount) {
		this.afterShippedRefundCount = afterShippedRefundCount;
	}

	public Date getDeliveryDate() {
		return deliveryDate;
	}

	public void setDeliveryDate(Date deliveryDate) {
		this.deliveryDate = deliveryDate;
	}

	public Integer getErpOverdueDay() {
		return erpOverdueDay;
	}

	public void setErpOverdueDay(Integer erpOverdueDay) {
		this.erpOverdueDay = erpOverdueDay;
	}

	public String getSourcingRequest() {
		return sourcingRequest;
	}

	public void setSourcingRequest(String sourcingRequest) {
		this.sourcingRequest = sourcingRequest;
	}

	public String getCancelReason() {
		return cancelReason;
	}

	public void setCancelReason(String cancelReason) {
		this.cancelReason = cancelReason;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public Long getCommentId() {
		return commentId;
	}

	public void setCommentId(Long commentId) {
		this.commentId = commentId;
	}

	public Long getAddCommentId() {
		return addCommentId;
	}

	public void setAddCommentId(Long addCommentId) {
		this.addCommentId = addCommentId;
	}

	public Long getSnapshotId() {
		return snapshotId;
	}

	public void setSnapshotId(Long snapshotId) {
		this.snapshotId = snapshotId;
	}
}
