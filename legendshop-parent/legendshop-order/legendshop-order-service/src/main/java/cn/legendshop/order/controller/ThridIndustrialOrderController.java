package cn.legendshop.order.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.dao.SystemParameterDao;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.model.entity.SystemParameter;
import cn.legendshop.common.security.annotation.Inner;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.order.dto.UpdateOrderDTO;
import cn.legendshop.order.dto.orderManager.SubmitOrderResultDTO;
import cn.legendshop.order.entity.SubThirdInfo;
import cn.legendshop.order.enums.MallOrderStrategyTypeEnum;
import cn.legendshop.order.event.SubmitOrderEventManger;
import cn.legendshop.order.event.listener.DelayCancelOfferListener;
import cn.legendshop.order.event.listener.ProdSnapshotListener;
import cn.legendshop.order.query.erp.ErpPreOrderParameterDTO;
import cn.legendshop.order.query.erp.ErpPreOrderQuery;
import cn.legendshop.order.service.MallOrderProcessService;
import cn.legendshop.order.service.SubItemService;
import cn.legendshop.order.service.ThridIndustrialOrderService;
import cn.legendshop.order.vo.PurchasedProdVo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.legendshop.model.constant.OrderSourceEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Api(tags = "第三方订单管理")
@Slf4j
@RestController
@RequestMapping("/thridIndustrial")
@AllArgsConstructor
public class ThridIndustrialOrderController {

	private final ThridIndustrialOrderService thridIndustrialOrderService;

    private final MallOrderProcessService mallOrderProcessServiceImpl;

    private final IndustrialOuterShopConfigService industrialOuterShopConfigService;

	private final SubItemService subItemService;
	private final SystemParameterDao systemParameterDao;

	@ApiOperation(value = "ERP -合同预下单接口（预订单）", produces = MediaType.APPLICATION_JSON_VALUE)
	@PostMapping("/erpPreOrderParameter")
	public R<String> erpPreOrderParameter(@Valid @RequestBody ErpPreOrderParameterDTO parameter) throws UnsupportedEncodingException {
		try {
			R orderErp = mallOrderProcessServiceImpl.submitOrder(parameter, MallOrderStrategyTypeEnum.ERP_THIRD_INDUSTRIAL);
			if (!orderErp.isSuccess()) {
				log.info("预下单失败！" + orderErp.getMsg());
				return R.fail(Optional.ofNullable(orderErp.getMsg()).orElse("预下单失败!") );
			}
			String subNum = (String) orderErp.getResult();
			JSONObject jsonObject = JSON.parseObject(subNum);
			log.info("预合同下单成功,  订单号为：{} , MRO对外接口编码: {}", subNum, parameter.getMroApiNo());
			IndustrialOuterShopConfig industrialOuterShopConfig = industrialOuterShopConfigService.getBySupplierCode(parameter.getVendorCode());
			this.thridIndustrialOrderService.recordErpNewOpt(jsonObject.get("orderNumber").toString(), industrialOuterShopConfig.getInnerCode());
			return ResResultManager.setResultSuccess(subNum);
		} catch (Exception e) {
			log.error("ERP预订单下单失败:",e);
			String msg = URLEncoder.encode(e.getMessage(), CharsetUtil.UTF_8);
			return R.fail(msg);
		}
	}


	@ApiOperation(value = "ERP - 合同预订单确认接口", produces = MediaType.APPLICATION_JSON_VALUE)
	@PostMapping("/erpPreOrderConfirm")
	public R<Object> erpPreOrderConfirm(@Valid @RequestBody ErpPreOrderQuery query) {
		// ERP异常订单处理
		SystemParameter errorERPOrder = systemParameterDao.getSystemParameterByName("ERP_ERROR_ORDER_NO");
		if (ObjectUtil.isNotEmpty(errorERPOrder)) {
			// 逗号分隔，拆分成一个list<String>
			String[] errorERPOrders =errorERPOrder.getValue().split(StrUtil.COMMA) ;
			ArrayList<String> errorERPOrderList = new ArrayList(Arrays.asList(errorERPOrders)) ;
			//如果是异常订单 直接返回成功
			if (errorERPOrderList.contains(query.getOrderNumber())){
				return ResResultManager.setResultSuccess(query.getOrderNumber());
			}
		}
//		R orderErp = thridIndustrialOrderService.erpPreOrderConfirm(query);
		UpdateOrderDTO updateOrderDTO = new UpdateOrderDTO();
		updateOrderDTO.setOrderNumber(query.getOrderNumber());
		updateOrderDTO.setOrderSourceEnum(OrderSourceEnum.ERP);
		updateOrderDTO.setErpPreOrderQuery(query);
		R<String> orderErp = mallOrderProcessServiceImpl.confirmOrder(updateOrderDTO);
		if (!orderErp.isSuccess()) {
			log.info("确认预订单失败:{}", orderErp);
			return R.fail("确认订单失败，" + orderErp.getMsg());
		}
		String subNum = query.getOrderNumber();
		log.info("确认预订单成功,  订单号为：{} , MRO对外接口编码: {}", query.getOrderNumber(), query.getMroApiNo());
		//更新订单记录
		this.thridIndustrialOrderService.erpOrderConfirm(query.getOrderNumber());
		//下单成功，保存商品快照
		return ResResultManager.setResultSuccess(subNum);
	}


	@ApiOperation(value = "ERP - 取消预订单接口", produces = MediaType.APPLICATION_JSON_VALUE)
	@PostMapping("/erpPreOrderCancel")
	public R<String> erpPreOrderCancel(@Valid @RequestBody ErpPreOrderQuery query) {
//		R orderErp = thridIndustrialOrderService.erpPreOrderCancel(query);

		UpdateOrderDTO updateOrderDTO = new UpdateOrderDTO();
		updateOrderDTO.setOrderNumber(query.getOrderNumber());
		updateOrderDTO.setOrderSourceEnum(OrderSourceEnum.ERP);
		updateOrderDTO.setErpPreOrderQuery(query);
		updateOrderDTO.setCancelPerson(query.getCancelPerson());
		updateOrderDTO.setCancelDate(DateUtil.parse(query.getCancelDate()));
		updateOrderDTO.setCancelReason(query.getCancelReason());
		R<String> orderErp = mallOrderProcessServiceImpl.cancelOrder(updateOrderDTO);
		if (!orderErp.isSuccess()) {
			log.info("取消预订单失败:{}", orderErp);
			return R.fail("取消订单失败，" + orderErp.getMsg());
		}
		String subNum = orderErp.getResult();
		log.info("取消预订单成功,  订单号为：{} , MRO对外接口编码: {}", query.getOrderNumber(), query.getMroApiNo());
		return ResResultManager.setResultSuccess(subNum);
	}


	@GetMapping("/getSubThirdInfoBySubNumber")
	@Inner
	public R<SubThirdInfo> getSubThirdInfoBySubNumber(@RequestParam("subNumber") String subNumber) {
		return R.ok(thridIndustrialOrderService.getSubThirdInfoBySubNumber(subNumber));
	}

	@GetMapping("/listPurchasedProdByMaterialCode")
	@ApiOperation(value = "【采购商】获取所有已购买商品（订单项中的商品）", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<PurchasedProdVo>> listPurchasedProdByMaterialCode(HttpServletRequest request, @RequestParam("itemId") String materialCode) {
		if(ObjectUtil.isEmpty(materialCode)) {
			return R.fail("物料编码不能为空");
		}
		return R.ok(subItemService.listPurchasedProdByMaterialCode(materialCode, AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));
	}

	@GetMapping("/listPurchasedProdByThirdSkuId")
	@ApiOperation(value = "【采购商】获取所有已购买料号（订单项中的商品）", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<PurchasedProdVo>> listPurchasedProdByThirdSkuId(HttpServletRequest request, @RequestParam("thirdSkuId") String thirdSkuId) {
		if(ObjectUtil.isEmpty(thirdSkuId)) {
			return R.fail("第三方skuId不能为空");
		}

		return R.ok(subItemService.listPurchasedProdByThirdSkuId(thirdSkuId, AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));
	}

}
