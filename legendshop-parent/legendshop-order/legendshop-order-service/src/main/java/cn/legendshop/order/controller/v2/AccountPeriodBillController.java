package cn.legendshop.order.controller.v2;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.common.security.annotation.Inner;
import cn.legendshop.common.security.service.LoginedUserService;
import cn.legendshop.common.utils.CommonServiceUtil;
import cn.legendshop.data.dto.AccountOrderListDto;
import cn.legendshop.data.dto.AccountPeriodBillInfo;
import cn.legendshop.data.dto.FinanceAccountDataDto;
import cn.legendshop.order.dto.AccountPeriodBillInvoiceDTO;
import cn.legendshop.order.dto.BillListDTO;
import cn.legendshop.order.entity.AccountPeriodBill;
import cn.legendshop.order.entity.ReconciliationBill;
import cn.legendshop.order.enums.*;
import cn.legendshop.order.query.AccountPeriodBillQuery;
import cn.legendshop.order.service.AccountPeriodBillService;
import cn.legendshop.order.vo.AccountPeriodBillInfoVo;
import cn.legendshop.order.vo.AccountPeriodBillVo;
import cn.legendshop.user.api.client.AccountPeriodClient;
import cn.legendshop.user.api.vo.AccountPeriodVO;
import com.legendshop.common.excel.util.EasyExcelUtils;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.util.AppUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * erp对账信息表(AccountPeriodBill)表控制层
 *
 * <AUTHOR> wu
 * @since 2022-09-26 16:59:14
 */
@RestController
@RequestMapping("/v2/accountPeriodBill")
@Slf4j
public class AccountPeriodBillController {

	@Autowired
	private LoginedUserService loginedUserService;


	@Autowired
	private AccountPeriodBillService accountPeriodBillService;

	@Autowired
	private AccountPeriodClient accountPeriodClient;




	/**
	 * 搜索账单信息（平台端）
	 *
	 * @return
	 */
	@PostMapping("/pageList")
	@ApiOperation(value = "【平台】查询用户账期账单列表", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<PageSupport<AccountPeriodBillVo>> pageList(@RequestBody AccountPeriodBillQuery accountPeriodBillQuery) {
		PageSupport<AccountPeriodBillVo> billVoPageSupport = accountPeriodBillService.pageList(accountPeriodBillQuery);
		return ResResultManager.setResultSuccess(billVoPageSupport);
	}

	/**
	 * 按照搜索条件导出账单信息（平台端）
	 *
	 * @return
	 */
	@ApiOperation(value = "【平台】导出用户账期账单列表", notes = "【平台】导出用户账期账单列表", produces = MediaType.APPLICATION_JSON_VALUE)
	@PostMapping("/exportPageList")
	public void exportPageList(HttpServletResponse response, @RequestBody AccountPeriodBillQuery accountPeriodBillQuery) throws IOException {
		List<AccountPeriodBillInfo> resultList = accountPeriodBillService.exportPageList(accountPeriodBillQuery);
		if (ObjectUtil.isNotEmpty(resultList)) {
			EasyExcelUtils.exportLocalByExcel(response, resultList, "账期账单明细", AccountPeriodBillInfo.class);
		}
	}

	/**
	 * 搜索账单信息（用户端）
	 *
	 * @return
	 */
	@PostMapping("/pageUserList")
	@ApiOperation(value = "【用户】查询用户账期账单列表", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<PageSupport<AccountPeriodBillVo>> pageUserList(@RequestBody AccountPeriodBillQuery accountPeriodBillQuery) {
		String userId = loginedUserService.getUser().getUserId();
		accountPeriodBillQuery.setUserId(userId);
		PageSupport<AccountPeriodBillVo> billVoPageSupport = accountPeriodBillService.pageList(accountPeriodBillQuery);
		return ResResultManager.setResultSuccess(billVoPageSupport);
	}


	@PostMapping("/drawInvoice")
	@ApiOperation(value = "【平台】开具发票", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<PageSupport<AccountPeriodBillVo>> drawInvoice(MultipartHttpServletRequest request) {
		MultipartFile invoiceVoucherFile = request.getFile("invoiceVoucherFile");
		String invoiceExpressName = request.getParameter("invoiceExpressName");
		String invoiceExpressNo = request.getParameter("invoiceExpressNo");
		String id = request.getParameter("id");
		AccountPeriodBillInvoiceDTO accountPeriodBillInvoiceDTO = new AccountPeriodBillInvoiceDTO();
		accountPeriodBillInvoiceDTO.setId(Long.parseLong(id));
		accountPeriodBillInvoiceDTO.setInvoiceVoucherFile(invoiceVoucherFile);
		accountPeriodBillInvoiceDTO.setInvoiceExpressNo(invoiceExpressNo);
		accountPeriodBillInvoiceDTO.setInvoiceExpressName(invoiceExpressName);
		try {
			accountPeriodBillService.uploadInvoice(accountPeriodBillInvoiceDTO);
			return ResResultManager.setResultSuccess();
		} catch (Exception e) {
			return ResResultManager.setResultError(e.getMessage());
		}
	}


	/**
	 * 获取账单信息
	 *
	 * @return
	 */
	@GetMapping("/accountPeriodInfo/{id}")
	@ApiOperation(value = "获取账单的账期信息", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<AccountPeriodBillInfoVo> accountPeriodInfo(@PathVariable(name = "id") Long id) {
		try {
			AccountPeriodBillInfoVo accountPeriodBillInfo = accountPeriodBillService.getAccountPeriodBillInfo(id);
			return ResResultManager.setResultSuccess(accountPeriodBillInfo);
		} catch (Exception e) {
			log.error("查询失败", e);
			return ResResultManager.setResultError(e.getMessage());
		}
	}

	/**
	 * 获取账单状态
	 *
	 * @return
	 */
	@Inner
	@GetMapping("/accountPeriodBillPayStatus")
	@ApiOperation(value = "获取账单的账期状态", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<Integer> accountPeriodBillPayStatus(@RequestParam(value = "userId") String userId) {
		try {
			AccountPeriodBill accountPeriodBill = accountPeriodBillService.getBillByUserId(userId);
			return ResResultManager.setResultSuccess(accountPeriodBill.getPayStatus());
		} catch (Exception e) {
			log.error("查询失败", e);
			return ResResultManager.setResultError(e.getMessage());
		}
	}


	/**
	 * 确认账单还款
	 *
	 * @return
	 */
	@GetMapping("/repayConfirm/{id}")
	@ApiOperation(value = "【平台】确认账单还款", produces = MediaType.APPLICATION_JSON_VALUE)
	public R buyerConfirm(@PathVariable(name = "id") Long id) {
		accountPeriodBillService.repayConfirm(id);
		return ResResultManager.setResultSuccess();
	}

	/**
	 * 手动创建账单
	 * @param
	 * @return
	 */
	@PostMapping("/createBill")
	@ApiOperation(value = "【用户端】手动创建账单", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> createBill(@RequestBody BillListDTO billListDTO) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		return accountPeriodBillService.createBill(billListDTO.getOrderNumbers(),baseUserDetailDTO.getUserId());
	}

}
