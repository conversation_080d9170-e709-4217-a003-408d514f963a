package cn.legendshop.order.controller.v2;

import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.order.dto.*;
import cn.legendshop.order.entity.EnquiryPriceAttachments;
import cn.legendshop.order.entity.Quotation;
import cn.legendshop.order.service.EnquiryPriceService;
import cn.legendshop.order.service.QuotationService;
import cn.legendshop.order.vo.*;
import com.legendshop.dao.support.PageSupport;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/v2/EnquiryPrice")
@AllArgsConstructor
@Slf4j
public class EnquiryPriceController {
    private final EnquiryPriceService enquiryPriceService;
    private final QuotationService quotationService;


    /**
     * [用户端]
     * @param EnquiryPrice
     * @return
     */
    @ApiOperation(value = "查询当前用户下的所有询价单列表")
    @PostMapping("/queryEnquiryPriceList")
    public R<PageSupport<EnquiryPriceVo>> queryEnquiryPriceList(@RequestBody EnquiryPriceDTO EnquiryPrice) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        EnquiryPrice.setUserId(baseUserDetailDTO.getUserId());
        return enquiryPriceService.queryEnquiryPriceList(EnquiryPrice);
    }

    @ApiOperation(value = "创建询价单初始化信息")
    @PostMapping("/enquiryPriceInit")
    public R<EnquiryPriceDetailVo> enquiryPriceInit() {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return enquiryPriceService.enquiryPriceInit(baseUserDetailDTO.getUserId());
    }

    @ApiOperation(value = "保存询价单")
    @PostMapping("/saveEnquiryPrice")
    public R saveEnquiryPrice(@RequestBody EnquiryPriceDTO enquiryPrice) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        enquiryPrice.setUserId(baseUserDetailDTO.getUserId());
        enquiryPrice.setAccountBookId(baseUserDetailDTO.getAccountBookId());
        enquiryPrice.setUserName(baseUserDetailDTO.getUsername());
        enquiryPriceService.saveEnquiryPrice(enquiryPrice);
        return R.ok();
    }

    @ApiOperation(value = "询价人确认/提交询价单")
    @PostMapping("/submitEnquiryPrice")
    public R confirmEnquiryPrice(@RequestBody EnquiryPriceDTO enquiryPriceDTO) {
        try {
            BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
            enquiryPriceDTO.setUserId(baseUserDetailDTO.getUserId());
            enquiryPriceDTO.setAccountBookId(baseUserDetailDTO.getAccountBookId());
            enquiryPriceDTO.setUserName(baseUserDetailDTO.getUsername());
            enquiryPriceService.confirmEnquiryPrice(enquiryPriceDTO);
            return R.ok("请求已接收，正在处理中");
        } catch (Exception e) {
            log.error("处理询价单时发生错误", e);
            return R.fail("处理询价单时发生错误");
        }
    }

    @ApiOperation(value = "修改委托单  可能去掉")
    @PostMapping("/updateEnquiryPrice")
    public R updateEnquiryPrice(@RequestBody EnquiryPriceDTO enquiryPrice) {
        return enquiryPriceService.updateEnquiryPrice(enquiryPrice);
    }

    @ApiOperation(value = "删除草稿状态的询价单")
    @PostMapping("/deleteDraftEnquiry")
    public R<String> deleteDraftEnquiry(@RequestBody EnquiryPriceDeleteDTO deleteDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        String userId = baseUserDetailDTO.getUserId();
        String userName = baseUserDetailDTO.getUsername();
        
        log.info("接收到删除询价单请求，用户ID: {}, 询价单IDs: {}", userId, deleteDTO.getEpIds());
        try {
            return enquiryPriceService.deleteDraftEnquiryPrice(deleteDTO, userId, userName);
        } catch (Exception e) {
            log.error("删除询价单失败", e);
            return R.fail("删除询价单失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "根据询价单号查询询价单详情")
    @PostMapping("/getEnquiryPriceDetailByEpId")
    public R<EnquiryPriceDetailVo> getEnquiryPriceDetailByEpId(@RequestBody EnquiryPriceDTO enquiryPrice) {
        log.info("开始处理询价单详情请求，询价单号: {}", enquiryPrice.getId());
        try {
            return enquiryPriceService.getEnquiryPriceDetailByEpId(enquiryPrice.getId());
        } catch (Exception e) {
            log.error("处理询价单详情请求时发生错误", e);
            throw e;
        }
    }

    @ApiOperation(value = "根据委托单号查询成功报价商家数量和截至时间")
    @PostMapping("/getEnquiryPriceShipNum")
    public R<EnquiryPriceShopNumVO> getEnquiryPriceShipNum(@RequestBody EnquiryPriceDTO enquiryPrice) {
        return enquiryPriceService.getEnquiryPriceShipNum(enquiryPrice.getEpId());
    }

    // 评价阶段
    @ApiOperation(value = "根据委托单号查询成功报价供应商金额")
    @PostMapping("/getQuotationByEnquiryPriceId")
    public R<List<Quotation>> getQuotationByEnquiryPriceId(@RequestBody EnquiryPriceDTO enquiryPrice) {
        return enquiryPriceService.getQuotationByEnquiryPriceId(enquiryPrice.getId());
    }

    /**
     * [商家端]
     * @param
     * @return
     */
    @ApiOperation(value = "查询当前商家的所有询价单列表")
    @PostMapping("/queryEnquiryPriceListByShopId")
    public R<PageSupport<EnquiryPriceShopVo>> queryEnquiryPriceListByShopId(@RequestBody EnquiryPriceShopDTO enquiryPriceShopDTO) {
        String shopId = UserContextHolder.getInstance().getContext().getShopId();
        enquiryPriceShopDTO.setShopId(shopId);
        return enquiryPriceService.queryEnquiryPriceListByShopId(enquiryPriceShopDTO);
    }

    /**
     *
     * @param
     * @return
     */
    @ApiOperation(value = "导出报价单、询价单、采购结果", notes = "导出报价单、询价单、采购结果", produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping("/export")
    public  R<String> export(HttpServletResponse response, @RequestBody ExportQuotationDTO exportQuotationDTO) throws IOException {
        return  enquiryPriceService.export(response,exportQuotationDTO);
    }




//    @ApiOperation(value = "查询询价单附件")
//    @PostMapping("/queryEPAttachments")
//    public R<List<EnquiryPriceAttachments>> queryEnquiryPriceAttachments(@RequestBody EPAttachmentsDTO epAttachmentsDTO) {
//        return enquiryPriceService.queryEnquiryPriceAttachments(epAttachmentsDTO);
//    }

    @ApiOperation(value = "上传询价单附件")
    @PostMapping("/uploadEnquiryFile")
    public R<String> uploadEnquiryFile(EnquiryPriceAttachments enquiryPriceAttachments) {
        return enquiryPriceService.uploadEnquiryFile(enquiryPriceAttachments);
    }
    /**
     *
     * @param
     * @return
     */
    @ApiOperation(value = "采购结果展示")
    @PostMapping("/enquiryPricerResults")
    public  R<EnquiryPricerResultsVo> enquiryPricerResults(@RequestBody EnquiryPriceShopDTO enquiryPriceShopDTO)  {
        return  enquiryPriceService.enquiryPricerResults(Long.valueOf(enquiryPriceShopDTO.getEpId()));
    }

    @ApiOperation(value = "商家端采购结果展示")
    @PostMapping("/enquiryPricerShopResults")
    public  R<EnquiryPricerShopResultsVo> enquiryPricerShopResults(@RequestBody EnquiryPriceShopDTO enquiryPriceShopDTO)  {
        String shopId = UserContextHolder.getInstance().getContext().getShopId();
        return  enquiryPriceService.enquiryPricerShopResults(Long.valueOf(enquiryPriceShopDTO.getEpId()),shopId);
    }

    @ApiOperation(value = "导出最终结果报告")
    @PostMapping("/exportFinalResultReport")
    public  R<String> exportFinalResultReport(@RequestBody ExportFinalResultReportDTO exportDTO)  {
//        String shopId = UserContextHolder.getInstance().getContext().getShopId();
        return  enquiryPriceService.exportFinalResultReport(exportDTO);
    }

    @ApiOperation(value = "获取oa提交所有附件")
    @GetMapping("/getAllReports")
    public  R<EnquiryOAReportVo> getAllReports(@RequestParam String epId)  {
//        String shopId = UserContextHolder.getInstance().getContext().getShopId();
        return  enquiryPriceService.getAllReports(Long.valueOf(epId));
    }

    @ApiOperation(value = "查看报价单详情")
    @GetMapping("/quotationHall")
    public R<QuotationHallVo> quotationHall(@RequestParam Long epId) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return quotationService.queryQuotationInfoByEpId(epId, baseUserDetailDTO.getUserId());
    }

    @ApiOperation(value = "刷新商品状态")
    @GetMapping("/refreshProductStatus")
    public R refreshProductStatus(@RequestParam Long epId) {
        return enquiryPriceService.refreshProductStatus(epId);
    }
}
