package cn.legendshop.order.controller.v2;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.order.dto.SharedInvoiceSubInfoDTO;
import cn.legendshop.order.dto.SharedJDVOPInvoiceSubInfoDTO;
import cn.legendshop.order.dto.UpdateReconciliationBillDTO;
import cn.legendshop.order.entity.InvoiceRequestApplicant;
import cn.legendshop.order.enums.InvoicingReqCustomerEnum;
import cn.legendshop.order.service.AccountPeriodBillService;
import cn.legendshop.order.service.InvoicingRequestsService;
import cn.legendshop.order.util.SharedFinanceUtil;
import cn.legendshop.order.vo.BillItemVo;
import cn.legendshop.order.vo.BillProdVo;
import cn.legendshop.product.api.entity.OpenMaterialProductRecommend;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.legendshop.common.excel.util.ExcelReadUtil;
import com.legendshop.util.AppUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.legendshop.order.constant.InvoicingRequestConst.SEGMENT_SIZE;

@RestController
@RequestMapping("/v2/financialSharing")
@AllArgsConstructor
@Slf4j
@Api("南钢共享财务共享平台")
public class SharedFinanceController {

    private final InvoicingRequestsService invoicingRequestsService;
    private final AccountPeriodBillService accountPeriodBillService;

    @ApiOperation(value = "开票申请单-订单信息抛送")
    @PostMapping("/invoicingRequests")
    public R invoicingRequests(MultipartHttpServletRequest request, SharedInvoiceSubInfoDTO dto) {
        String msg;
        log.info("进入 南钢财务共享-开票申请单申请");
        dto.setRequest(request);
        Map<String, String> map = invoicingRequestsService.invoicingRequests(dto);
        if (map == null || AppUtils.isNotBlank(map.get("error"))) {
            msg = map.get("error");
            log.info(msg);
            return ResResultManager.setResultError(msg);
        }
        log.info("结束 南钢财务共享-开票申请单申请- map:{}", map);
        if (!"0".equals(map.get("FAIL"))) {
            msg = "发送失败！成功单据数（个）：" + map.get("SUCCESS")  + " 失败单据数（个）：" + map.get("FAIL")  + " 失败原因：" + map.get("MSG");
            return ResResultManager.setResultError(msg);
        } else {
            msg = "发送成功！成功单据数（个）：" + map.get("SUCCESS");
            // 记录最晚还款时间
            Calendar now = Calendar.getInstance();
            now.setTime(new Date());
            //当前时间往后推1月为最晚还款时间
            now.add(Calendar.MONTH, 1);
            Date lastTime = now.getTime();
            accountPeriodBillService.updateLastTime(dto.getId(), lastTime);
        }
        return ResResultManager.setResultSuccess(msg);
    }

    @ApiOperation(value = "开票申请单-临时账单订单信息抛送")
    @PostMapping("/tempInvoicingRequests")
    public R tempInvoicingRequests(@RequestParam Long requestApplicantId, @RequestParam List<Long> shipmentItemIds) {
        String msg = null;
        log.info("进入 南钢财务共享-临时开票申请单");
        Map<String, String> map = invoicingRequestsService.tempInvoicingRequests(requestApplicantId, shipmentItemIds);
        if (map == null || AppUtils.isNotBlank(map.get("error"))) {
            msg = map.get("error");
            log.info(msg);
            return ResResultManager.setResultError(msg);
        }
        log.info("结束 南钢财务共享-临时开票申请单- map:{}", map);
        if (!"0".equals(map.get("FAIL"))) {
            msg = "发送失败！成功单据数：" + map.get("SUCCESS") + " 失败单据数：" + map.get("FAIL") + " 失败原因：" + map.get("MSG");
            return ResResultManager.setResultError(msg);
        } else {
            msg = "发送成功！成功单据数：" + map.get("SUCCESS");
        }
        return ResResultManager.setResultSuccess(msg);
    }

    @ApiOperation(value = "开票申请单-京东VOP员工卡支付订单信息抛送")
    @PostMapping("/JdVopInvoicingRequests")
    public R JdVopInvoicingRequests(MultipartHttpServletRequest request, SharedJDVOPInvoiceSubInfoDTO dto) {
        String msg;
        log.info("进入 南钢财务共享-京东VOP员工卡支付订单信息抛送");
        dto.setRequest(request);
        Map<String, String> map =invoicingRequestsService.JDVopPushBillDisposable(dto);
        if (map == null || AppUtils.isNotBlank(map.get("error"))) {
            msg = map.get("error");
            log.info(msg);
            return ResResultManager.setResultError(msg);
        }
        log.info("结束 南钢财务共享-开票申请单申请- map:{}", map);
        if (!"0".equals(map.get("FAIL"))) {
            msg = "发送失败！成功单据数（个）：" + map.get("SUCCESS")  + " 失败单据数（个）：" + map.get("FAIL")  + " 失败原因：" + map.get("MSG");
            return ResResultManager.setResultError(msg);
        } else {
            msg = "发送成功！成功单据数（个）：" + map.get("SUCCESS");
        }
        return ResResultManager.setResultSuccess(msg);
    }


}
