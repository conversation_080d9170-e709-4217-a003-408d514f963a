package cn.legendshop.order.controller.v2.admin;

import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.order.dto.*;
import cn.legendshop.order.entity.EnquiryPriceItem;
import cn.legendshop.order.service.EnquiryPriceExpertService;
import cn.legendshop.order.service.EnquiryPriceItemService;
import cn.legendshop.order.service.EnquiryPriceService;
import cn.legendshop.order.service.QuotationService;
import cn.legendshop.order.dao.EnquiryPriceDao;
import cn.legendshop.order.vo.EnquiryPriceDetailVo;
import cn.legendshop.order.vo.EnquiryPriceVo;
import cn.legendshop.order.vo.QuotationHallVo;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.common.oa.service.WorkFlowService;
import com.legendshop.common.oa.model.dto.GetStatusXbjWorkFlowDTO;
import com.legendshop.common.oa.model.response.ApprovalFlowStatusResponse;
import cn.legendshop.ngNewIndustryOA.api.client.NgNewIndustryOAClient;
import cn.legendshop.ngNewIndustryOA.api.dto.ApprovalFlowDTO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/v2/admin/EnquiryPrice")
@AllArgsConstructor
@Slf4j
public class EnquiryPriceAdminController {
    private final EnquiryPriceService enquiryPriceService;

    private final EnquiryPriceExpertService enquiryPriceExpertService;

    private final EnquiryPriceItemService enquiryPriceItemService;

    private final QuotationService quotationService;

    private final WorkFlowService workFlowService;

    private final NgNewIndustryOAClient ngNewIndustryOAClient;
    
    private final EnquiryPriceDao enquiryPriceDao;

    @ApiOperation(value = "查询所有委托单列表")
    @PostMapping("/queryEnquiryPriceList")
    public R<PageSupport<EnquiryPriceVo>> queryEnquiryPriceList( @RequestBody EnquiryPriceDTO enquiryPrice) {
        // 增加查询来源 平台端需要过滤用户未提交的草稿状态的订单
        enquiryPrice.setQuerySource("1");
        return enquiryPriceService.queryEnquiryPriceList(enquiryPrice);
    }
    @ApiOperation(value = "退回委托单")
    @GetMapping("/retreatEnquiryPrice")
    public R<String> retreatEnquiryPrice(@RequestParam Long epId) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return enquiryPriceService.retreatEnquiryPrice(epId, baseUserDetailDTO.getUserId(), baseUserDetailDTO.getUsername());
    }

    @ApiOperation(value = "撤销委托单")
    @GetMapping("/cancelEnquiryPrice")
    public R<String> cancelEnquiryPrice(@RequestParam Long epId) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return enquiryPriceService.cancelEnquiryPrice(epId, baseUserDetailDTO.getUserId(), baseUserDetailDTO.getUsername());
    }

    @ApiOperation(value = "后台取消委托单")
    @PostMapping("/adminCancelEnquiryPrice")
    public R<String> adminCancelEnquiryPrice(@RequestBody EnquiryPriceCancelDTO enquiryPriceCancelDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return enquiryPriceService.adminCancelEnquiryPrice(enquiryPriceCancelDTO, baseUserDetailDTO.getUserId(), baseUserDetailDTO.getUsername());
    }



    @ApiOperation(value = "根据询价单号查询询价单详情")
    @PostMapping("/getEnquiryPriceDetailByEpId")
    public R<EnquiryPriceDetailVo> getEnquiryPriceDetailByEpId(@RequestParam("enquiryPriceId") Long enquiryPriceId) {
        return enquiryPriceService.getEnquiryPriceDetailByEpId(enquiryPriceId);
    }


    @ApiOperation(value = "发布询价单")
    @PostMapping("/publishEnquiryPrice")
    public R confirmEnquiryPrice(@RequestBody EnquiryPriceDTO EnquiryPrice) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return enquiryPriceService.publishEnquiryPrice(EnquiryPrice,baseUserDetailDTO);
    }

    @ApiOperation(value = "开启评判")
    @GetMapping("/startComparing")
    public R<String> startComparing(@RequestParam Long epId) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return enquiryPriceService.startEvaluate(epId, baseUserDetailDTO.getUserId(), baseUserDetailDTO.getUsername());
    }

    @ApiOperation(value = "预选成交供应商")
    @PostMapping("/preSelectSupplier")
    public R<String> preSelectSupplier(@Valid @RequestBody EvaluateResultDTO evaluateResultDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        evaluateResultDTO.setUserId(baseUserDetailDTO.getUserId());
        evaluateResultDTO.setUserName(baseUserDetailDTO.getUsername());
        return enquiryPriceService.preSelectSupplier(evaluateResultDTO);
    }


    @ApiOperation(value = "询价时间延长")
    @PostMapping("/extendedEpTime")
    public R<String> extendedEpTime(@Valid @RequestBody ExtendEpTimeDTO extendEpTimeDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return enquiryPriceService.extendedEpTime(extendEpTimeDTO,baseUserDetailDTO.getUserId(), baseUserDetailDTO.getUsername());
    }

    @ApiOperation(value = "更新询价单专家库")
    @PostMapping("/updateEnquiryPriceExperts")
    public R<String> updateEnquiryPriceExperts(@RequestBody EnquiryPriceExpertDTO enquiryPriceExpertDTO) {
//        return enquiryPriceExpertService.saveEnquiryPriceExpert(enquiryPriceExpertDTO.getEnquiryId(),enquiryPriceExpertDTO.getEnquiryExperts());
        return R.fail("现不支持修改专家库");
    }

    @ApiOperation(value = "修改询价单明细")
    @PostMapping("/updateEnquiryPriceItem")
    public R updateEnquiryPriceItem(@RequestBody List<EnquiryPriceItem> enquiryPriceItems) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        enquiryPriceItemService.updateEnquiryPriceItemAfterSubmit(enquiryPriceItems,baseUserDetailDTO.getUserId(), baseUserDetailDTO.getUsername());
        return ResResultManager.setResultSuccess();
    }

    @ApiOperation(value = "查看报价单详情")
    @GetMapping("/quotationHall")
    public R<QuotationHallVo> quotationHall(@RequestParam Long epId) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return quotationService.queryQuotationInfoByEpId(epId, baseUserDetailDTO.getUserId());
    }

    @ApiOperation(value = "专家提交评审结果")
    @PostMapping("/submitEvaluateResult")
    public R<String> submitEvaluateResult(EvaluateResultDTO evaluateResultDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        evaluateResultDTO.setUserId(baseUserDetailDTO.getUserId());
        evaluateResultDTO.setUserName(baseUserDetailDTO.getUsername());
        return quotationService.submitEvaluateResult(evaluateResultDTO);
    }

    @ApiOperation(value = "结束评审")
    @GetMapping("/endEvaluate")
    public R<String> endEvaluate(@RequestParam Long epId) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        return enquiryPriceExpertService.endEvaluate(epId, baseUserDetailDTO.getUserId());
    }

    @ApiOperation(value = "重新评审")
    @PostMapping("/resetEvaluate")
    public R<String> resetEvaluate(@Valid @RequestBody ResetEvaluateDTO resetEvaluateDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        resetEvaluateDTO.setUserId(baseUserDetailDTO.getUserId());
        return enquiryPriceExpertService.resetEvaluate(resetEvaluateDTO);
    }

    @ApiOperation(value = "分项改价")
    @PostMapping("/updateQuotationItemAfterSubmit")
    public R updateQuotationItemAfterSubmit(@RequestBody QuotationDTO quotationItemDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        quotationItemDTO.setUserName(baseUserDetailDTO.getUsername());
        quotationItemDTO.setUserId(baseUserDetailDTO.getUserId());
        enquiryPriceItemService.updateQuotationItemAfterSubmit(quotationItemDTO);
        return ResResultManager.setResultSuccess();
    }

    @ApiOperation(value = "提交最终评审结果")
    @PostMapping("/finshEvaluateAndSubmit")
    public R finshEvaluateAndSubmit(@Valid @RequestBody EnquiryPriceResultSubmitDTO evaluateResultDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        evaluateResultDTO.setUserId(baseUserDetailDTO.getUserId());
        evaluateResultDTO.setUserName(baseUserDetailDTO.getUsername());
        return quotationService.finshEvaluateAndSubmit(evaluateResultDTO);
    }

    @ApiOperation(value = "回退最终评审结果")
    @PostMapping("/rollbackFinalResult")
    public R rollbackFinalResult(@Valid @RequestBody ResetEvaluateDTO evaluateResultDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        evaluateResultDTO.setUserId(baseUserDetailDTO.getUserId());
        return quotationService.rollbackFinalResult(evaluateResultDTO);
    }

    @ApiOperation(value = "提交OA审批")
    @PostMapping("/submitOA")
    public R enquiryPriceOASubmit(EnquiryPriceResultSubmitDTO evaluateResultDTO,@RequestParam("files") List<MultipartFile> files) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        evaluateResultDTO.setUserId(baseUserDetailDTO.getUserId());
        evaluateResultDTO.setUserName(baseUserDetailDTO.getUsername());
//        log.info("请求上传OA,参数:{}", JSON.toJSONString(evaluateResultDTO));
        return quotationService.enquiryPriceOASubmit(evaluateResultDTO,files);
    }

    @ApiOperation(value = "结果上传")
    @PostMapping("/uploadResult")
    public R uploadResult(@RequestBody EnquiryPriceResultSubmitDTO evaluateResultDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        evaluateResultDTO.setUserId(baseUserDetailDTO.getUserId());
        evaluateResultDTO.setUserName(baseUserDetailDTO.getUsername());
        return enquiryPriceService.uploadResult(evaluateResultDTO);
    }

	@ApiOperation(value = "生成最终结果报告")
	@PostMapping("/exportFinalResultReport")
	public R<String> exportFinalResultReport(@RequestBody ExportFinalResultReportDTO exportDTO) {
		return enquiryPriceService.exportFinalResultReport(exportDTO);
	}

    @ApiOperation(value = "生成成交通知书")
    @GetMapping("/createDealNotice")
    public R createDealNotice(@RequestParam Long epId) {
        return enquiryPriceService.createDealNotice(epId);
    }

    @ApiOperation(value = "平台端处理取消下单项")
    @PostMapping("/platformConfirmCancel")
    public R platformConfirmCancel(@RequestBody PlatformConfirmCancelDTO platformConfirmCancelDTO) {
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        platformConfirmCancelDTO.setUserId(baseUserDetailDTO.getUserId());
        return enquiryPriceService.platformConfirmCancel(platformConfirmCancelDTO);
    }

    @ApiOperation(value = "获取新产业OA审批状态")
    @GetMapping("/getOAApprovalStatus")
    public R<ApprovalFlowStatusResponse> getOAApprovalStatus(@RequestParam Long epId) {
        try {
            // 通过epId获取ApprovalFlow对象
            R<List<ApprovalFlowDTO>> approvalFlowResult = ngNewIndustryOAClient.queryApprovalFlowByEpId(epId);
            if (!approvalFlowResult.isSuccess() || approvalFlowResult.getResult() == null || approvalFlowResult.getResult().isEmpty()) {
                return R.fail("未找到对应的审批流程记录");
            }

            // 获取第一个审批流程记录（通常每个epId只有一个审批流程）
            ApprovalFlowDTO approvalFlowDTO = approvalFlowResult.getResult().get(0);
            if (approvalFlowDTO.getApprovalFlowId() == null) {
                return R.fail("审批流程ID为空");
            }

            // 构建GetStatusXbjWorkFlowDTO对象
            GetStatusXbjWorkFlowDTO getStatusXbjWorkFlowDTO = new GetStatusXbjWorkFlowDTO();
            getStatusXbjWorkFlowDTO.setEpId(epId);
            getStatusXbjWorkFlowDTO.setRequestId(Long.valueOf(approvalFlowDTO.getApprovalFlowId()));
            
            // 获取项目经理的新产业OA ID
            String oaUserId = quotationService.getProjectManagerOaIdByEpId(epId);
            if (oaUserId != null) {
                getStatusXbjWorkFlowDTO.setUserid(oaUserId);
                log.info("已设置新产业OA用户ID: {}", oaUserId);
            } else {
                log.warn("未能获取到新产业OA用户ID，将使用默认值");
            }

            // 调用OA接口获取审批状态
            ApprovalFlowStatusResponse statusResponse = workFlowService.getNewIndustryStatus(getStatusXbjWorkFlowDTO);
            return R.ok(statusResponse);
        } catch (Exception e) {
            log.error("获取新产业OA审批状态失败：", e);
            return R.fail("获取新产业OA审批状态失败：" + e.getMessage());
        }
    }
    

}
