package cn.legendshop.order.controller.v2.user;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.order.dto.CartDeleteDTO;
import cn.legendshop.order.dto.CartExportDTO;
import cn.legendshop.order.dto.CartProdDTO;
import cn.legendshop.order.dto.orderManager.CartParam;
import cn.legendshop.order.service.BasketService;
import cn.legendshop.order.vo.ShopCartViewVO;
import com.legendshop.common.excel.util.EasyExcelUtils;
import com.legendshop.util.AppUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
//

/**
 * 用户购物车接口   添加购物车
 */


@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "下单相关", value = "订单相关接口")
@RequestMapping("/v2/p/userCart")
public class UserCartController {


	@Autowired
	private BasketService basketService;


	/**
	 * 查询我的购物车
	 *
	 * @return
	 */
	@GetMapping("/list")
	@ApiOperation(value = "获取用户购物车信息", notes = "获取用户购物车信息")
	public R<ShopCartViewVO> list(@RequestParam(name = "addressId", required = false) Long addressId) {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		Long accountBookId = userDetailDTO.getAccountBookId();
		ShopCartViewVO shopCartViewVO = basketService.getShopCartByUserId(userId, addressId,accountBookId);
		return R.ok(shopCartViewVO);
	}

//	/**
//	 * shopId获取商家可领取的优惠券totalCount
//	 *
//	 * @param shopId
//	 * @return
//	 */
//	@GetMapping("/{shopId}/coupon/list")
//	@ApiOperation(value = "根据商家id获取可领取优惠券列表", notes = "根据商家id获取可领取优惠券列表")
//	public R<List<CouponDTO>> couponList(@PathVariable Long shopId) {
//		return couponClient.listReceivable(CouponQuery.builder().shopId(shopId).build());
//	}

	/**
	 * 获取购物车的数量
	 *
	 * @return
	 */
	@GetMapping("/count")
	@ApiOperation(value = "获取购物车商品数量", notes = "获取所有购物车商品数量")
	public R<Long> productCount(HttpServletRequest request) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		if (AppUtils.isBlank(baseUserDetailDTO)) {
			return R.ok(0L);
		}
		Long shopCartCount = basketService.getShopCartCount();
		return R.ok(shopCartCount);

	}

	/**
	 * 添加购物车
	 *
	 * @param cartParam
	 * @return
	 */
	@PostMapping("/add")
	@ApiOperation(value = "添加购物车", notes = "添加购物车")
	public R add(@Valid @RequestBody CartParam cartParam, HttpServletRequest request) {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		cartParam.setUserId(userId);
		cartParam.setAccountBookId(userDetailDTO.getAccountBookId());
		R addCartGood = basketService.addCartGood(cartParam);
		return addCartGood;
	}


//	@PostMapping("/changePromotion")
//	@ApiOperation(value = "购物车单品修改促销活动", notes = "购物车单品修改促销活动")
//	public R<Boolean> cartChangePromotion(@Valid @RequestBody CartChangePromotionDTO cartChangePromotionDTO) {
//		return R.ok(true);
//	}


	/**
	 * 1.操作购物车
	 * 2.换促销  默认选中状态
	 * 3.加减数量
	 */
	@PostMapping("/change")
	@ApiOperation(value = "新增、修改sku、购物车数量", notes = "修改购物车，传递最终的值，不需要加减。")
	public R change(@Valid @RequestBody CartParam cartParam, HttpServletRequest request) {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		cartParam.setUserId(userId);

		/**
		 * 删除缓存操作在dao层
		 */
		if(StrUtil.isNotBlank(userId)){
			basketService.delCache(userId);
		}else {
			return R.fail();
		}

		return basketService.change(cartParam,userId);
	}


	@PostMapping("/select")
	@ApiOperation(value = "正选、反选、全选购物车", notes = "正选、反选、全选购物车，传递购物车id")
	public R<Boolean> select(@RequestBody List<CartParam> cartParamList) {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		return basketService.select(cartParamList, userId);
	}

	/**
	 * 删除购物车项
	 *
	 * @param CartDeleteDTO
	 * @return
	 */
	@PostMapping("/delete")
	@ApiOperation(value = "删除用户购物车商品", notes = "传递购物车id，删除对应的购物车")
	public R<Boolean> delete(@RequestBody CartDeleteDTO dto) {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		return basketService.delete(userId, dto.getCartIds());
	}

	/**
	 * 删除所有购物车
	 *
	 * @return
	 */
	@GetMapping("/delete/all")
	@ApiOperation(value = "删除用户所有购物车商品", notes = "删除用户所有购物车商品")
	public R<ShopCartViewVO> deleteAll() {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		return basketService.deleteAll(userId);
	}


	@GetMapping("/invalidProduct")
	@ApiOperation(value = "获取购物车失效商品列表", notes = "获取购物车失效商品列表")
	public R<ShopCartViewVO> invalidProduct() {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		return R.ok(basketService.queryInvalidProductList(userId));
	}


	@GetMapping("/cleanInvalidProduct")
	@ApiOperation(value = "清空失效商品", notes = "清空失效商品")
	public R cleanInvalidProduct() {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		basketService.cleanInvalidProduct(userId);
		return R.ok();
	}

	@PostMapping("/exportExcel")
	@ApiOperation(value = "导出购物车", notes = "导出购物车")
	public void exportExcel(HttpServletResponse response,@RequestBody List<CartProdDTO> cartProdDTOList) {
		BaseUserDetailDTO userDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = userDetailDTO.getUserId();
		List<CartExportDTO> cartExportDTOList = basketService.export(cartProdDTOList);
		if(ObjectUtil.isNotEmpty(cartExportDTOList)) {
			try {
				EasyExcelUtils.exportLocalByExcel(response, cartExportDTOList, "购物车商品清单", CartExportDTO.class);
				log.info("下载成功");
			}catch (Exception e){
				log.error("导出失败！");
			}
		}
		log.error("查询商品列表为空");
	}

}
