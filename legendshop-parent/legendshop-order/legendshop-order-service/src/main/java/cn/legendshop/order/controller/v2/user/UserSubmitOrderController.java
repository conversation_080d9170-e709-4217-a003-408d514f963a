package cn.legendshop.order.controller.v2.user;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.order.bo.ConfirmOrderBO;
import cn.legendshop.order.dto.orderManager.ConfirmOrderDTO;
import cn.legendshop.order.dto.orderManager.EngConDTO;
import cn.legendshop.order.dto.orderManager.InvoiceChangeDTO;
import cn.legendshop.order.dto.orderManager.SubmitOrderDTO;
import cn.legendshop.order.dto.SubmitOrderShopDTO;
import cn.legendshop.order.dto.SubmitOrderSkuDTO;
import cn.legendshop.order.enums.MallOrderStrategyTypeEnum;
import cn.legendshop.order.service.*;
import cn.legendshop.order.vo.SubmitOrderSuccessVo;
import cn.legendshop.product.api.client.v2.MroThirdIndustrialProductClient;
import cn.legendshop.user.api.bo.UserInvoiceBO;
import com.legendshop.model.constant.OrderSourceEnum;
import com.legendshop.util.AppUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "下单相关", value = "订单相关接口")
@RequestMapping("/v2/submitOrder")
public class UserSubmitOrderController {


	@Autowired
	private MallOrderProcessService mallOrderProcessService;

	@Autowired
	private OrderCacheService orderCacheService;


	@Autowired
	private OrderUtil orderUtil;

	@Autowired
	private CouponUtil couponUtil;

	@Autowired
	private SubService subService;


	@Autowired
	private MroThirdIndustrialProductClient mroThirdIndustrialProductClient;



	@ApiOperation(value = "【用户】下单检查,组装商品信息", notes = "立即购买、购物车结算以及参与其他营销活动之前的数据校验以及商品数据组装", produces = MediaType.APPLICATION_JSON_VALUE)
	@PostMapping(value = "/check")
	public R<ConfirmOrderBO> orderCheck(@RequestBody ConfirmOrderDTO confirmOrderDTO) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		confirmOrderDTO.setUserId(userId);
		confirmOrderDTO.setSubUserId(baseUserDetailDTO.getSubUserId());
		confirmOrderDTO.setUserName(baseUserDetailDTO.getUsername());
		confirmOrderDTO.setOrderSourceEnum(OrderSourceEnum.MRO);
		confirmOrderDTO.setStrategyType(MallOrderStrategyTypeEnum.MRO_COMMON.getType());
		confirmOrderDTO.setAccountBookId(baseUserDetailDTO.getAccountBookId());
		return mallOrderProcessService.submitOrderProdCheck(confirmOrderDTO);
	}


	@ApiOperation(value = "【用户】获取确认订单信息", notes = "组装收货地址，计算运费，区域限售，计算促销优惠，按预生成订单缓存", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiImplicitParams({
		@ApiImplicitParam(paramType = "query", name = "confirmOrderId", value = "预提交订单ID", dataType = "String", required = true),
		@ApiImplicitParam(paramType = "query", name = "userAddrId", value = "用户选择的收货地址", dataType = "Long", required = true)
	})
	@GetMapping(value = "/confirm/info")
	public R<ConfirmOrderBO> getOrderInfo(String confirmOrderId) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		// 获取预订单缓存
		ConfirmOrderBO confirmOrderBo = orderCacheService.getConfirmOrderInfoCache(userId, confirmOrderId);
		if (ObjectUtil.isNull(confirmOrderBo)) {
			return R.fail("购物信息已失效，请重新下单");
		}
		// 只有金智工程做限制
//		if ("45c7745b-740c-43c4-9961-15b9918a751e".equals(confirmOrderBo.getUserId())) {
//
//		}
		// 提取所有商品名称和SKU信息
		List<String> productNameList = new ArrayList<>();
		if (confirmOrderBo.getShopOrderList() != null) {
			for (SubmitOrderShopDTO shopOrder : confirmOrderBo.getShopOrderList()) {
				if (shopOrder.getSkuList() != null) {
					for (SubmitOrderSkuDTO sku : shopOrder.getSkuList()) {
						productNameList.add(sku.getProductName());
					}
				}
			}
			// 危化品筛选
			List<String> result = mroThirdIndustrialProductClient.hazardousChemical(productNameList).getResult();
			//如果有危化品
			if (AppUtils.isNotBlank(result)) {
				StringBuilder remark = new StringBuilder();
				for (String prodName : result) {
					remark.append(prodName);
					remark.append(" ");
				}
				remark.append("疑似危化品，请核查。");
				confirmOrderBo.setHazardousChemicalRemark(remark.toString());
			}
		}



		return R.ok(confirmOrderBo);
	}


	@ApiOperation(value = "【用户】更新地址信息", notes = "如果用户在确认订单页面切换收货地址，则对当前订单商品重新计算运费", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiImplicitParams({
		@ApiImplicitParam(paramType = "query", name = "confirmOrderId", value = "预提交订单ID", dataType = "String", required = true),
		@ApiImplicitParam(paramType = "query", name = "userAddrId", value = "用户选择的收货地址", dataType = "Long", required = true)
	})
	@GetMapping(value = "/address/change")
	public R<ConfirmOrderBO> addressChange(String confirmOrderId, Long userAddrId) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		// 获取预订单缓存
		ConfirmOrderBO confirmOrderBo = orderCacheService.getConfirmOrderInfoCache(userId, confirmOrderId);
		R<ConfirmOrderBO> handlerDelivery = orderUtil.handlerDelivery(userAddrId, confirmOrderBo);
		if (!handlerDelivery.success()) {
			return R.fail(handlerDelivery.getMsg());
		}
		// 更新预订单缓存
		orderCacheService.putConfirmOrderInfoCacheByUserId(userId, handlerDelivery.getResult());
		return handlerDelivery;
	}


	@ApiOperation(value = "【用户】更新发票信息", notes = "如果是用户开启关闭发票，则无返回数据，如果是切换发票，则返回当前选择的发票信息", produces = MediaType.APPLICATION_JSON_VALUE)
	@PostMapping(value = "/invoice/change")
	public 	R<UserInvoiceBO> invoiceChange(@Valid @RequestBody InvoiceChangeDTO invoiceChangeDTO) {
		// 获取预订单缓存
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		ConfirmOrderBO confirmOrderBo = orderCacheService.getConfirmOrderInfoCache(baseUserDetailDTO.getUserId(), invoiceChangeDTO.getConfirmOrderId());
		if (ObjectUtil.isNull(confirmOrderBo)) {
			return R.fail("购物信息已失效，请重新下单");
		}
		return mallOrderProcessService.invoiceChange(invoiceChangeDTO, confirmOrderBo);
	}

	@PostMapping(value = "/engConMsg")
	public 	R<String> engConMsg(@Valid @RequestBody EngConDTO engConDTO) {
		// 获取预订单缓存
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		ConfirmOrderBO confirmOrderBo = orderCacheService.getConfirmOrderInfoCache(baseUserDetailDTO.getUserId(), engConDTO.getConfirmOrderId());
		if (ObjectUtil.isNull(confirmOrderBo)) {
			return R.fail("购物信息已失效，请重新下单");
		}
		return mallOrderProcessService.changeEngConMsg(engConDTO, confirmOrderBo);
	}

	@ApiOperation(value = "【用户】选择切换优惠券", produces = MediaType.APPLICATION_JSON_VALUE)
	@PostMapping(value = "/select/coupon")
	public R<ConfirmOrderBO> selectCoupon(@RequestBody List<Long> couponIds, Long shopId, @RequestParam String confirmOrderId) {
		return null;
	}


	@ApiOperation(value = "【用户】选择平台优惠券，默认选择最优平台优惠券", produces = MediaType.APPLICATION_JSON_VALUE)
	@PostMapping(value = "/use/platformCoupon")
	public R<ConfirmOrderBO> usePlatformCoupon(@RequestBody Long couponId, @RequestParam String confirmOrderId) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		ConfirmOrderBO confirmOrderBo = orderCacheService.getConfirmOrderInfoCache(userId, confirmOrderId);
		// 处理选择平台优惠券
		R<ConfirmOrderBO> confirmOrderBOR = couponUtil.handleSelectPlatformCoupon(confirmOrderBo, couponId);
		if (!confirmOrderBOR.success()) {
			return R.fail(confirmOrderBOR.getMsg());
		}
		// 更新预订单缓存
		orderCacheService.putConfirmOrderInfoCacheByUserId(userId, confirmOrderBOR.getResult());
		return confirmOrderBOR;
	}


	@ApiOperation(value = "【用户】提交订单", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiImplicitParams({
		@ApiImplicitParam(paramType = "query", name = "confirmOrderId", value = "预提交订单ID", dataType = "String", required = true),
		@ApiImplicitParam(paramType = "query", name = "shopOrderMessageList", value = "店铺订单信息列表，包含店铺备注和商品项次备注", dataType = "List", required = true),
		@ApiImplicitParam(paramType = "query", name = "pointId", value = "自提点id", dataType = "Long"),
		@ApiImplicitParam(paramType = "query", name = "deliveryType", value = "配送类型", dataType = "Integer", required = true),
	})
	@PostMapping(value = "/submit/order")
	public R submitOrder(@RequestBody SubmitOrderDTO submitOrderDTO) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		// 获取预订单缓存
		ConfirmOrderBO confirmOrderBo = orderCacheService.getConfirmOrderInfoCache(userId, submitOrderDTO.getConfirmOrderId());
		if (ObjectUtil.isNull(confirmOrderBo)) {
			return R.fail("购物信息已失效, 请刷新页面重试!");
		}
		confirmOrderBo.setOrderSourceEnum(OrderSourceEnum.MRO);
		
		// 更新店铺备注和商品项次采购要求
		updateShopOrderMessageAndSkuSourcingRequest(confirmOrderBo, submitOrderDTO.getShopOrderMessageList());
		
		return mallOrderProcessService.mroSubmitOrder(confirmOrderBo);
	}



	/**
	 * 普通订单成功订单详情
	 */
	@ApiOperation(value = "普通订单成功订单详情", httpMethod = "POST", notes = "普通订单成功订单详情", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@ApiImplicitParams({
		@ApiImplicitParam(paramType = "query", name = "subSettlementSn", value = "清算单据号", required = true, dataType = "String"),
		@ApiImplicitParam(paramType = "query", name = "subSettlementType", value = "单据类型", required = true, dataType = "String")
	})
	@GetMapping(value = "/successOrderDetail")
	public R<SubmitOrderSuccessVo> successOrderDetail(@RequestParam(name ="subSettlementSn" ) String subSettlementSn, @RequestParam(name ="subSettlementType",required = false ) String subSettlementType) {
		try {
			BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
			String userId = baseUserDetailDTO.getUserId();
			if (AppUtils.isBlank(userId)) {
				return R.fail(-1, "请先登录");
			}
			return  mallOrderProcessService.submitOrderSuccessDetail(subSettlementSn, userId);
		} catch (Exception e) {
			log.error("获取提交成功的订单详情异常!", e);
			return R.fail();
		}
	}

	/**
	 * 更新店铺备注和商品项次采购要求
	 */
	private void updateShopOrderMessageAndSkuSourcingRequest(ConfirmOrderBO confirmOrderBo, List<SubmitOrderDTO.ShopOrderMessageDTO> shopOrderMessageList) {
		if (CollUtil.isEmpty(shopOrderMessageList) || CollUtil.isEmpty(confirmOrderBo.getShopOrderList())) {
			return;
		}
		
		// 构建店铺ID到备注信息的映射
		Map<Long, SubmitOrderDTO.ShopOrderMessageDTO> shopMessageMap = shopOrderMessageList.stream()
			.collect(Collectors.toMap(
				SubmitOrderDTO.ShopOrderMessageDTO::getShopId,
				Function.identity(),
				(existing, replacement) -> replacement
			));
		
		// 更新ConfirmOrderBO中的店铺备注和商品项次采购要求
		confirmOrderBo.getShopOrderList().forEach(shopOrder -> {
			Long shopId = shopOrder.getShopId();
			SubmitOrderDTO.ShopOrderMessageDTO shopMessageDTO = shopMessageMap.get(shopId);
			
			if (shopMessageDTO != null) {
				// 更新店铺备注
				shopOrder.setRemark(shopMessageDTO.getMessage());
				
				// 更新商品项次采购要求
				if (CollUtil.isNotEmpty(shopMessageDTO.getSkuSourcingRequestList()) && CollUtil.isNotEmpty(shopOrder.getSkuList())) {
					Map<Long, String> skuSourcingRequestMap = shopMessageDTO.getSkuSourcingRequestList().stream()
						.collect(Collectors.toMap(
							SubmitOrderDTO.SkuSourcingRequestDTO::getSkuId,
							SubmitOrderDTO.SkuSourcingRequestDTO::getSourcingRequest,
							(existing, replacement) -> replacement
						));
					
					shopOrder.getSkuList().forEach(sku -> {
						String sourcingRequest = skuSourcingRequestMap.get(sku.getSkuId());
						if (StrUtil.isNotBlank(sourcingRequest)) {
							sku.setSourcingRequest(sourcingRequest);
						}
					});
				}
			}
		});
	}




}

