package cn.legendshop.order.dao.impl;

import cn.legendshop.XinghuoBlockChain.api.dto.EnquiryPriceBlockChainPushDTO;
import cn.legendshop.bestsign.api.dto.EnquiryPriceResultDTO;
import cn.legendshop.bestsign.api.dto.QuotationResultDTO;
import cn.legendshop.order.dao.EnquiryPriceDao;
import cn.legendshop.order.dto.EnquiryPriceCancelDTO;
import cn.legendshop.order.dto.EnquiryPriceDTO;
import cn.legendshop.order.dto.EnquiryPriceShopDTO;
import cn.legendshop.order.entity.EnquiryPrice;
import cn.legendshop.order.vo.EnquiryPriceExportVo;
import cn.legendshop.order.vo.EnquiryPriceShopVo;
import cn.legendshop.order.vo.EnquiryPriceVo;
import com.legendshop.dao.criterion.MatchMode;
import com.legendshop.dao.impl.GenericDaoImpl;
import com.legendshop.dao.sql.ConfigCode;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.QueryMap;
import com.legendshop.dao.support.SimpleSqlQuery;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Repository
public class EnquiryPriceDaoImpl  extends GenericDaoImpl<EnquiryPrice, Long>  implements EnquiryPriceDao {
    @Override
    public PageSupport<EnquiryPriceVo> queryEnquiryPriceList(EnquiryPriceDTO powerOfAttorney) {
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(EnquiryPriceVo.class, powerOfAttorney.getPageSize(), powerOfAttorney.getCurPage());
        QueryMap queryMap = new QueryMap();
        queryMap.put("userId",powerOfAttorney.getUserId());
        queryMap.like("epNo",powerOfAttorney.getEpNo());
        queryMap.like("epName",powerOfAttorney.getEpName());
        queryMap.put("status",powerOfAttorney.getStatus());
        if(StringUtils.isNotEmpty(powerOfAttorney.getClientCompany())) {
            queryMap.like("clientCompany", powerOfAttorney.getClientCompany(), MatchMode.ANYWHERE);
        }
        if(StringUtils.isNotEmpty(powerOfAttorney.getAgencyName())) {
            queryMap.like("agencyName", powerOfAttorney.getAgencyName(), MatchMode.ANYWHERE);
        }
        // 如果是查询询价单来源为平台端，则过滤掉用户未提交的草稿询价单
        if(powerOfAttorney.getQuerySource() != null && "1".equals(powerOfAttorney.getQuerySource())){
            queryMap.put("StatusSql"," AND ep.status not in ('0')");
        }
        simpleSqlQuery.setSqlAndParameter("enquiryPrice.queryEpList", queryMap);
        return querySimplePage(simpleSqlQuery);
    }

    @Override
    public Integer updateEnquiryPriceStatusById(Long enquiryPriceId, Integer status) {
        String sql = "update ls_enquiry_price set status=? where id=?";
        return update(sql,status,enquiryPriceId);
    }

    @Override
    public Integer cancelEnquiryPrice(EnquiryPriceCancelDTO enquiryPriceCancelDTO, Integer status) {
        String sql = "update ls_enquiry_price set status = ? , cancel_reason = ? , cancel_time = ? where id=?";
        return update(sql,status,enquiryPriceCancelDTO.getCancelReason(),enquiryPriceCancelDTO.getCancelDate(),enquiryPriceCancelDTO.getEpId());
    }

    @Override
    public  String getAgencyMobileByUserId(String userId) {
        String sql = "select mobile from ls_expert where user_id = ?";
        return get(sql,String.class,userId);
    }

    @Override
    public  String getJobNumByEpId(Long epId) {
        String sql = "select au.name from ls_admin_user au left join ls_enquiry_price_expert epe on au.id = epe.user_id where epe.ep_id = ?";
        return get(sql, String.class, epId);
    }

    @Override
    public EnquiryPrice getByEpNo(String epNo) {
        return this.getByProperties(new EntityCriterion().eq("epNo",epNo));
    }

    @Override
    public Integer updateStatusByEpNo(String epNo, Integer status) {
        String sql = "update ls_enquiry_price set status= ?  where ep_no = ?";
        return update(sql,status,epNo);
    }

    @Override
    public Integer updateRemarkById(Long epId, String remark) {
        String sql = "update ls_enquiry_price set remark= ?  where id = ?";
        return update(sql,remark,epId);
    }

    @Override
    public Integer updateEnquiryDeadTime(String epNo, String selectShopIds ,Date enquiryDeadTime) {
        String sql = "update ls_enquiry_price set inquiry_dead_time = ? , select_shop_ids = ? where ep_no = ?";
        return update(sql,enquiryDeadTime,selectShopIds,epNo);
    }

    @Override
    public PageSupport<EnquiryPriceShopVo> queryEnquiryPriceListByShopId(EnquiryPriceShopDTO enquiryPriceShopDTO) {
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(EnquiryPriceShopVo.class, enquiryPriceShopDTO.getPageSize(), enquiryPriceShopDTO.getCurPage());
        QueryMap queryMap = new QueryMap();
        queryMap.put("shopId",enquiryPriceShopDTO.getShopId());
        queryMap.put("epNo",enquiryPriceShopDTO.getEpNo());
        queryMap.put("epName",enquiryPriceShopDTO.getEpName());
        queryMap.put("status",enquiryPriceShopDTO.getStatus());
        queryMap.put("createStartTime",enquiryPriceShopDTO.getCreateStartTime());
        queryMap.put("createEndTime",enquiryPriceShopDTO.getCreateEndTime());
        simpleSqlQuery.setSqlAndParameter("enquiryPrice.queryEnquiryPriceListByShopId", queryMap);
        return querySimplePage(simpleSqlQuery);
    }

    @Override
    public void updateStatusEnquiryPriceListDeadTime(Date date, Integer status) {
        String sql = "update ls_enquiry_price set status = 30 where inquiry_dead_time < ? and status = ? ";
        this.update(sql,date,status);
    }

    /**
     *
     * 获取最终结果报告
     * @param epId
     * @return EnquiryPriceExportVo
     */
    @Override
    public EnquiryPriceExportVo getFinalReportByEpId(Long epId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("epId",epId);
        String sql = ConfigCode.getInstance().getCode("enquiryPrice.getFinalReportByEpId", queryMap);
        return this.get(sql, EnquiryPriceExportVo.class, epId);
    }

    @Override
    public EnquiryPriceResultDTO enquiryPriceResult(Long epId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("epId",epId);
        String sql = ConfigCode.getInstance().getCode("enquiryPrice.getEnquiryPriceResult", queryMap);
        return this.get(sql, EnquiryPriceResultDTO.class, epId);
    }

    @Override
    public List<QuotationResultDTO> enquiryPriceResultProdList(Long epId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("epId",epId);
        String sql = ConfigCode.getInstance().getCode("enquiryPrice.queryEnquiryPriceResultProdList", queryMap);
        return this.query(sql, QuotationResultDTO.class, epId);
    }

    @Override
    public EnquiryPriceBlockChainPushDTO getEnquiryPriceBlockChainPushDTO(Long epId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("epId",epId);
        String sql = ConfigCode.getInstance().getCode("enquiryPrice.getEnquiryPriceBlockChainPushDTO", queryMap);
        return this.get(sql, EnquiryPriceBlockChainPushDTO.class, epId);
    }
}
