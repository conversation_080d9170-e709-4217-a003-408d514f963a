package cn.legendshop.order.dao.impl;

import cn.legendshop.order.dao.QuotationitemDao;
import cn.legendshop.order.dto.QuotationItemInfo;
import cn.legendshop.order.dto.QuotationItemWithEpItemDTO;
import cn.legendshop.order.entity.QuotationItem;
import cn.legendshop.order.vo.DealQuotationItemVo;
import cn.legendshop.order.vo.EnquiryPriceQuotationItemVo;
import cn.legendshop.order.vo.PreSelectQuotationItemVo;
import cn.legendshop.order.vo.SupplierQuotationItemVo;
import com.legendshop.dao.impl.GenericDaoImpl;
import com.legendshop.dao.sql.ConfigCode;
import com.legendshop.dao.support.QueryMap;
import com.legendshop.dao.support.update.LambdaDelete;
import com.legendshop.util.AppUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Repository
public class QuotaionitemDaoImpl extends GenericDaoImpl<QuotationItem, Long> implements QuotationitemDao {
    @Override
    public List<EnquiryPriceQuotationItemVo> queryByQuotationId(Long epId,Long quotationId) {
        //如果为空不查询报价单信息,只查询询价单信息
        if (AppUtils.isBlank(quotationId)){
            quotationId= -1L;
        }
        QueryMap queryMap = new QueryMap();
        queryMap.put("quotationId", quotationId);
        queryMap.put("epId",epId);
        String sql = ConfigCode.getInstance().getCode("quotation.queryEpQuotationItemByEpId", queryMap);
        return this.query(sql, EnquiryPriceQuotationItemVo.class, queryMap.toArray());
    }

    @Override
    public void deleteByQuotationId(Long enquiryPriceId) {
        LambdaDelete<QuotationItem> lambdaDelete = new LambdaDelete<>(QuotationItem.class);
        lambdaDelete.eq(QuotationItem::getQuotationId,enquiryPriceId);
        this.deleteByProperties(lambdaDelete);
    }

    @Override
    public List<QuotationItemInfo> queryQuotationItemInfo(Long quotationId) {
        String sql = "select * from ls_quotation_item where quotation_id= ? ";
        return query(sql, QuotationItemInfo.class, quotationId);
    }

    @Override
    public List<DealQuotationItemVo> queryUsefulItemByEnquiryPriceId(Long enquiryPriceId) {
        String sql = "select lqi.id,\n" +
                "       lqi.quotation_id,\n" +
                "       lqi.ep_item_id,\n" +
                "       lqi.product_name,\n" +
                "       lqi.product_sku,\n" +
                "       lqi.status,\n" +
                "       lqi.product_price,\n" +
                "       lqi.product_delivery,\n" +
                "       lqi.total_price,\n" +
                "       lqi.remark,\n" +
                "       lqi.is_deal,\n" +
                "       epi.product_unit as productUnit,\n" +
                "       epi.product_quantity as productQuantity,\n" +
                "       epi.product_model as productModel,\n" +
                "       lqi.product_brand as productBrand,lq.shop_id as shopId " +
                "       from ls_quotation_item lqi " +
                "       left join ls_quotation lq on lq.id = lqi.quotation_id\n" +
                "       left join ls_enquiry_price_item epi on lqi.ep_item_id = epi.id " +
                " where lq.ep_id = ? and lqi.is_deal = ?";
        return query(sql,DealQuotationItemVo.class,enquiryPriceId,Boolean.TRUE);
    }

    @Override
    public List<DealQuotationItemVo> queryUnlistedItemsByEnquiryPriceId(Long enquiryPriceId) {
        String sql = "select lqi.id,\n" +
                "       lqi.quotation_id,\n" +
                "       lqi.ep_item_id,\n" +
                "       lqi.product_name,\n" +
                "       lqi.product_sku,\n" +
                "       lqi.status,\n" +
                "       lqi.product_price,\n" +
                "       lqi.product_delivery,\n" +
                "       lqi.total_price,\n" +
                "       lqi.remark,\n" +
                "       lqi.is_deal,\n" +
                "       epi.product_unit as productUnit,\n" +
                "       epi.product_quantity as productQuantity,\n" +
                "       epi.product_model as productModel,\n" +
                "       lqi.product_brand as productBrand,lq.shop_id as shopId " +
                "       from ls_quotation_item lqi " +
                "       left join ls_quotation lq on lq.id = lqi.quotation_id\n" +
                "       left join ls_enquiry_price_item epi on lqi.ep_item_id = epi.id " +
                " where lq.ep_id = ? and lqi.is_deal = ?" +
                " and (lqi.status = 0 or lqi.status is null)";
        return query(sql,DealQuotationItemVo.class,enquiryPriceId,Boolean.TRUE);
    }

    @Override
    public List<DealQuotationItemVo> queryUsefulItemByQuotationItemIds(List<Long> quotationItemIds){
        if (quotationItemIds == null || quotationItemIds.isEmpty()) {
            return Collections.emptyList();
        }

        String placeholders = String.join(",", Collections.nCopies(quotationItemIds.size(), "?"));
        String sql = "select lqi.`id`, lqi.quotation_id, lqi.ep_item_id, lqi.product_name, lqi.product_sku, lqi.`status`, lqi.product_price, lqi.product_delivery, lqi.total_price, lqi.`remark`, lqi.is_deal, epi.product_unit as productUnit, epi.product_quantity as productQuantity, epi.product_model as productModel, lqi.product_brand as productBrand,lq.shop_id as shopId " +
                "from ls_quotation_item lqi " +
                "left join ls_quotation lq on lq.`id` = lqi.quotation_id " +
                "left join ls_enquiry_price_item epi on lqi.ep_item_id = epi.`id` " +
                "where lqi.is_deal = 1 " +
                "AND lqi.`id` IN (" + placeholders + ")";

        return this.query(sql, DealQuotationItemVo.class, quotationItemIds.toArray());
    }

    @Override
    public List<DealQuotationItemVo> queryUselessQuotationIdsByEpId(Long enquiryPriceId){
        String sql = "select lqi.*,lq.shop_id as shopId from ls_quotation_item lqi left join ls_quotation lq on lq.id = lqi.quotation_id where lq.ep_id = ? and lqi.is_deal != ?";
        return query(sql,DealQuotationItemVo.class,enquiryPriceId,Boolean.TRUE);
    };

    @Override
    public void updateStatusBySkuId(String skuId, int status) {
        String sql = "update ls_quotation_item set status = ? where product_sku = ? ";
        update(sql,status,skuId);
    }

    @Override
    public void updateStatusByQuotationItemIds(List<Long> quotationItemIds, int status) {
        String sql = "update ls_quotation_item set order_status = ? where id in (" +
                quotationItemIds.stream().map(id -> "?").collect(Collectors.joining(",")) + ")";
        List<Object> params = new ArrayList<>();
        params.add(status);
        params.addAll(quotationItemIds);
        update(sql, params.toArray());
    }

    @Override
    public void rollBackQuotationItemsDealStatus(List<Long> quotationItemIds) {
        String sql = "update ls_quotation_item set is_deal = ? where id in (" +
                quotationItemIds.stream().map(id -> "?").collect(Collectors.joining(",")) + ")";
        List<Object> params = new ArrayList<>();
        params.add(0);
        params.addAll(quotationItemIds);
        update(sql, params.toArray());
    }

    @Override
    public void updatePrice(QuotationItem quotationItem) {
        String sql = "update ls_quotation_item set product_price = ? , total_price = ?  , remark = ? where id = ? ";
        update(sql,quotationItem.getProductPrice(),quotationItem.getTotalPrice(),quotationItem.getRemark(),quotationItem.getId());
    }

    @Override
    public void updateSkuById(String skuId, Date pushCompletedTime, Long quotationItemId) {
        String sql = "update ls_quotation_item set product_sku = ? , push_completed_time =? where id = ? ";
        update(sql,skuId,pushCompletedTime,quotationItemId);
    }

    @Override
    public void updatePushCompletedTime(Date pushCompletedTime, Long quotationItemId) {
        String sql = "update ls_quotation_item set push_completed_time =? where id = ? ";
        update(sql,pushCompletedTime,quotationItemId);
    }

    @Override
    public void updateQuotationItemCancelInfo(QuotationItem quotationItem) {
        String sql = "update ls_quotation_item set order_status = ?, cancel_reason =? ,cancel_time = ? where id = ? ";
        update(sql,quotationItem.getOrderStatus(),quotationItem.getCancelReason(),new Date(),quotationItem.getId());
    }

    @Override
    public void updateSupplierConfirmCancelInfo(QuotationItem quotationItem) {
        String sql = "update ls_quotation_item set order_status = ?, supplier_deal_time =? ,supplier_reject_reason = ? where id = ? ";
        update(sql,quotationItem.getOrderStatus(),quotationItem.getSupplierDealTime(),quotationItem.getSupplierRejectReason(),quotationItem.getId());
    }

    @Override
    public void platformConfirmCancel(QuotationItem quotationItem) {
        String sql = "update ls_quotation_item set order_status = ?, platform_deal_time =? ,platform_reject_reason = ? where id = ? ";
        update(sql,quotationItem.getOrderStatus(),quotationItem.getPlatformDealTime(),quotationItem.getPlatformRejectReason(),quotationItem.getId());
    }

    @Override
    public String isAllInquiriesOrdered(Long quotationItemId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("quotationItemId",quotationItemId);
        String sql = ConfigCode.getInstance().getCode("quotation.isAllInquiriesOrdered", queryMap);
        return this.get(sql, String.class, queryMap.toArray());
    }

    @Override
    public List<QuotationItemWithEpItemDTO> queryQuotationItemWithEpItem(List<Long> quotationItemIds) {
        if (quotationItemIds == null || quotationItemIds.isEmpty()) {
            return Collections.emptyList();
        }

        String placeholders = String.join(",", Collections.nCopies(quotationItemIds.size(), "?"));
        String sql = "SELECT " +
                    "qi.*, " +
                    "epi.product_unit as ep_product_unit, " +
                    "epi.product_quantity as ep_product_quantity " +
                    "FROM ls_quotation_item qi " +
                    "LEFT JOIN ls_enquiry_price_item epi ON qi.ep_item_id = epi.id " +
                    "WHERE qi.id IN (" + placeholders + ")";

        return this.query(sql, QuotationItemWithEpItemDTO.class, quotationItemIds.toArray());
    }

    @Override
    public List<SupplierQuotationItemVo> querySupplierQuotationItemVoList(Long epId, Long quotationId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("epId",epId);
        queryMap.put("quotationId",quotationId);
        String sql = ConfigCode.getInstance().getCode("quotation.querySupplierQuotationItemVoList", queryMap);
        return this.query(sql, SupplierQuotationItemVo.class, queryMap.toArray());
    }

    @Override
    public List<Long> queryUnConfirmOrderItemsByEpId(Long epId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("epId",epId);
        String sql = ConfigCode.getInstance().getCode("quotation.queryUnConfirmOrderItemsByEpId", queryMap);
        return this.query(sql, Long.class, queryMap.toArray());
    }

    @Override
    public List<PreSelectQuotationItemVo> queryPreSelectQuotationItemVoList(Long epId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("epId",epId);
        String sql = ConfigCode.getInstance().getCode("quotation.queryPreSelectQuotationItemVoList", queryMap);
        return this.query(sql, PreSelectQuotationItemVo.class, queryMap.toArray());
    }

    @Override
    public Integer getUnorderedQuotationItemCount(Long epId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("epId",epId);
        String sql = ConfigCode.getInstance().getCode("quotation.getUnorderedQuotationItemCount", queryMap);
        return this.get(sql, Integer.class, queryMap.toArray());
    }

    @Override
    public void updateStatusBySkuIds(List<String> skuIds) {
        if (skuIds == null || skuIds.isEmpty()) {
            return; // 如果 skuIds 为空或为空列表，直接返回
        }

        // 创建占位符字符串，例如 (?, ?, ?)
        String placeholders = String.join(",", Collections.nCopies(skuIds.size(), "?"));
        String sql = "update ls_quotation_item set status = ? where product_sku in (" + placeholders + ")";

        // 将 status 和 skuIds 组合成参数列表
        List<Object> params = new ArrayList<>();
        params.add(10); // status
        params.addAll(skuIds); // skuIds

        // 执行更新操作
        update(sql, params.toArray());
    }
}
