/*
 *
 * LegendShop 多用户商城系统
 *
 *  版权所有,并保留所有权利。
 *
 */
package cn.legendshop.order.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.enums.IndustrialTypeEnum;
import cn.legendshop.common.core.enums.ThirdConsumerEnum;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.SqlUtil;
import cn.legendshop.common.dao.OuterShopConfigDao;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.utils.QuarterDateUtil;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.data.entity.DataV;
import cn.legendshop.open.request.bill.OpenTenderBillRequest;
import cn.legendshop.order.dao.SubDao;
import cn.legendshop.order.dto.*;
import cn.legendshop.order.model.VmSale;
import cn.legendshop.order.vo.OpenTenderBillVo;
import cn.legendshop.order.vo.SubItemDetailVos;
import cn.legendshop.order.vo.SubItemInfoListVo;
import com.legendshop.dao.SQLOperation;
import com.legendshop.dao.criterion.MatchMode;
import com.legendshop.dao.impl.GenericDaoImpl;
import com.legendshop.dao.sql.ConfigCode;
import com.legendshop.dao.support.*;
import com.legendshop.dao.support.lambda.LambdaCriteriaQuery;
import com.legendshop.dao.support.lambda.LambdaEntityCriterion;
import com.legendshop.dao.support.update.LambdaUpdate;
import com.legendshop.model.constant.*;
import com.legendshop.model.dto.MergeGroupSubDto;
import com.legendshop.model.dto.OrderExprotDto;
import com.legendshop.model.dto.SubCountsDto;
import com.legendshop.model.dto.SubDto;
import com.legendshop.model.dto.order.*;
import com.legendshop.model.entity.*;
import com.legendshop.model.entity.presell.PresellSubEntity;
import com.legendshop.model.entity.store.StoreProd;
import com.legendshop.util.AppUtils;
import com.legendshop.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 订单Dao
 */
@Repository("subDao")
public class SubDaoImpl extends GenericDaoImpl<Sub, Long> implements SubDao {

    //c端：支付了
    private final static String COMMOM_C_PAY_SQL = "\nFROM ls_sub s WHERE s.purchase_type = 1 AND s.status > 3 and is_payed =1 \n";
    //B端 款到发货 ：支付了
    private final static String COMMOM_B_ONLINE_PAY_SQL = "\nFROM ls_sub s WHERE s.purchase_type = 2 AND s.status > 3 AND s.pay_manner = 2 and is_payed =1 \n";
    //B端 预付款,货到付款 ：合同签署完成
    private final static String COMMOM_B_NO_ONLINE_PAY_SQL = "\nFROM ls_sub s WHERE s.purchase_type = 2 AND s.status > 2 AND s.status < 50 AND s.pay_manner != 2\n";

    //生活品金额统计
    private final static String C_PAY_SQL = "\nfrom ls_sub s where s.purchase_type = '1' and s.`status` not in (0, 3, 50) \n";
    //第三方工业品采购金额
    private final static String THIRD_INDUSTRIAL_SQL = "\nfrom ls_sub s join ls_industrial_outer_shop_config as sc on s.shop_id = sc.shop_id where s.purchase_type = '2' and s.`status` not in (0, 3, 50)\n";
    //招标金额统计
    private final static String BIDS_SQL = "\nfrom ls_sub s where s.purchase_type = '2' and s.order_source = 2 and s.`status` not in (0, 3, 50) and s.shop_id not in ( select shop_id from ls_industrial_outer_shop_config) and s.sub_type != 'CONCRETE'\n";
    //混凝土金额统计
    private final static String CONCRETE_SQL = "\nfrom ls_sub s where s.sub_type = 'CONCRETE' and s.`status` not in (50)\n";
    //零碳金额统计
    private final static String ZERO_CARBON_SQL = "\nfrom ls_sub s where s.shop_id = 4001 and s.`status` not in (0, 3, 50)\n";
    //有分，小羊等电商金额统计
    private final static String EXTERNAL_SUB_SQL = "\nfrom  ls_external_sub lex where 1=1\n";
    

    @Autowired
    private OuterShopConfigDao outerShopConfigDao;


    /*
     * (non-Javadoc)
     *
     * @see
     * com.legendshop.business.dao.impl.SubDao#saveSub(com.legendshop.model.
     * entity.Sub)
     */
    @Override
    public Long saveSub(Sub sub) {
        return save(sub);
    }

    @Override
    public Long saveSub(Sub sub, Long subId) {
        return save(sub, subId);
    }


    /*
     * (non-Javadoc)
     *
     * @see com.legendshop.business.dao.impl.SubDao#getSubById(java.lang.Long)
     */
    @Override
    public Sub getSubById(Long subId) {
        return getById(subId);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.legendshop.business.dao.impl.SubDao#findSubBySubNumber(java.lang.
     * String)
     */
    @Override
    public Sub getSubBySubNumber(String subNumber) {
        if (AppUtils.isBlank(subNumber)) {
            return null;
        }
        return getByProperties(new EntityCriterion().eq("subNumber", subNumber));
    }


    /*
     * (non-Javadoc)
     *
     * @see com.legendshop.business.dao.impl.SubDao#finishUnPay(int,
     * java.util.Date)
     */
    @Override
    public List<Sub> getFinishUnPayThridIndustrialOrder(List<Long> shopIds) {
        QueryMap queryMap = new QueryMap();
        queryMap.in("shopIds", shopIds);
        SQLOperation sqlOperation = ConfigCode.getInstance().getCodeParameter("order.getFinishUnPayThridIndustrialOrder", queryMap);
        return queryLimit(sqlOperation.getSql(), Sub.class, 0, 100, sqlOperation.getParams());
    }

    /*
     * (non-Javadoc)
     *
     * @see com.legendshop.business.dao.impl.SubDao#findUnAcklodgeSub(int,
     * java.util.Date)
     */
    @Override
    public List<Sub> getUnAcklodgeSub(int maxNum, Date expireDate) {
        String sql = ConfigCode.getInstance().getCode("order.getUnAcklodgeSub");
        return queryLimit(sql, Sub.class, 0, maxNum, expireDate, OrderStatusEnum.CONSIGNMENT.value());
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.legendshop.business.dao.impl.SubDao#removeOverTimeBasket(java.util
     * .Date)
     */
    @Override
    public void deleteOverTimeBasket(Date date) {
        String sql = ConfigCode.getInstance().getCode("order.deleteOverTimeBasket");
        update(sql, Constants.TRUE_INDICATOR, date);
    }

    @Override
    public void updateSub(Sub sub) {
        update(sub);
    }

    /**
     * 所有正在处理的订单
     */
    @Override
    public Long getTotalProcessingOrder(Long shopId) {
        String sql = ConfigCode.getInstance().getCode("order.getTotalProcessingOrder");
        return getLongResult(sql, Constants.FALSE_INDICATOR, shopId);
    }

    /**
     * 判断用户是否已经订购有效某个产品
     */
    @Override
    public boolean isUserOrderProduct(Long prodId, String userName) {
        String sql = ConfigCode.getInstance().getCode("order.isUserOrderProduct");
        long result = getLongResult(sql, Long.class, prodId, userName);
        return result > 0;
    }

    /**
     * 判断用户是否有订购过有效的订单
     *
     * @param userName
     */
    public boolean isUserOrderProduct(String userName) {
        String sql = ConfigCode.getInstance().getCode("order.isUserOrderProduct2");
        long result = getLongResult(sql, userName);
        return result > 0;
    }

    /**
     * 删除订单
     */
    @Override
    public void deleteSub(String userId, String userName) {
        update(ConfigCode.getInstance().getCode("order.deleteSubHis"), userId); // 删除用户订单项目
        update(ConfigCode.getInstance().getCode("order.deleteSubItem"), userId); // 删除用户订单项目
        update(ConfigCode.getInstance().getCode("order.deleteSub"), userId); // 删除用户订单项目
    }

    @Override
    public Long getSubId() {
        return createId();
    }

    @Override
    public Sub getSubBySubNumberByUserId(String subNumber, String userId) {
        if (AppUtils.isBlank(subNumber) && AppUtils.isBlank(userId)) {
            return null;
        }
        return getByProperties(new EntityCriterion().eq("subNumber", subNumber).eq("userId", userId));
    }


    @Override
    public List<Sub> getSubBySubNumberByUserId(List<String> subNumbers, String userId) {
        EntityCriterion entityCriterion = new EntityCriterion().eq("userId", userId);
        entityCriterion.in("subNumber", subNumbers.toArray());
        return queryByProperties(entityCriterion);
    }


    @Override
    public Sub getSubBySubNumberByShopId(String subNumber, Long ShopId) {
        if (AppUtils.isBlank(subNumber) && AppUtils.isBlank(ShopId)) {
            return null;
        }
        return getByProperties(new EntityCriterion().eq("subNumber", subNumber).eq("shopId", ShopId));
    }

    @Override
    public List<Sub> getSubByIds(String[] subIds, String userId, Integer status) {
        QueryMap map = new QueryMap();
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        for (String subId : subIds) {
            sql.append("?,");
            params.add(subId);
        }
        sql.setLength(sql.length() - 1);
        map.put("subByIds", sql.toString());
        params.add(userId);
        params.add(status);
        String query = ConfigCode.getInstance().getCode("order.getSubByIds", map);
        return query(query, Sub.class, params.toArray());
    }

    @Override
    public int updateSubForPay(Sub sub, Integer currentStatus) {
        String query = ConfigCode.getInstance().getCode("order.updateSubForPay");
        return update(query, sub.getPayId(), sub.getPayTypeId(), sub.getPayTypeName(), sub.getPayDate(), sub.getUpdateDate(), sub.getStatus(), sub.isPayed(), sub.getFlowTradeNo(), sub.getSubSettlementSn(), sub.getSubId(), currentStatus);
    }

    @Override
    public List<OrderSub> getOutPutExcelOrder(Long[] subIds) {
        QueryMap map = new QueryMap();
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        for (Long subId : subIds) {
            sql.append("?,");
            params.add(subId);
        }
        sql.setLength(sql.length() - 1);
        map.put("subByIds", sql.toString());
        String query = ConfigCode.getInstance().getCode("order.getOutPutExcelOrder", map);
        return query(query, OrderSub.class, params.toArray());
    }

    @Override
    public List<SubCountsDto> querySubCounts(Long subUserId, String userId, String industrialFlag) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("userId", userId);
        queryMap.put("industrialType", industrialFlag);
        queryMap.put("subUserId", subUserId);
        SQLOperation sqlOperation = ConfigCode.getInstance().getCodeParameter("order.querySubCounts", queryMap);
        return query(sqlOperation.getSql(), SubCountsDto.class, sqlOperation.getParams());
    }

    /**
     * 查询在endDate之前已经完成的订单
     */
    @Override
    public List<Sub> getBillFinishOrders(Long shopId, Date endDate, Integer status) {
        String sql = ConfigCode.getInstance().getCode("order.getBillFinishOrders");
        return this.query(sql, Sub.class, shopId, status, endDate);
    }

    @Override
    @Caching(evict = {@CacheEvict(value = "OrderDtoList", key = "#userId + '.' + #deleteStatus")})
    public void cleanOrderCache(String userId, Integer deleteStatus) {

    }

    @Override
    @Caching(evict = {@CacheEvict(value = "UnpayOrderCount", key = "#userId")})
    public void cleanUnpayOrderCount(String userId) {

    }

    @Override
    @Caching(evict = {@CacheEvict(value = "ConsignmentOrderCount", key = "#userId")})
    public void cleanConsignmentOrderCount(String userId) {

    }

    class MySubDtoRowMapper implements RowMapper<OrderDetailDto> {

        private Map<String, MySubDto> map = null;

        private boolean config; // 是否查询其他订单信息

        public MySubDtoRowMapper(Map<String, MySubDto> map, boolean config) {
            this.map = map;
            this.config = config;
        }

        @Override
        public OrderDetailDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            String subNumber = rs.getString("subNumber");
            if (map.containsKey(subNumber)) { // 存在
                MySubDto mySubDto = map.get(subNumber);
                SubOrderItemDto mySubItemDto = buildSubItem(rs);
                mySubDto.addItem(mySubItemDto);
            } else {
                MySubDto dto = buildSub(rs);
                SubOrderItemDto mySubItemDto = buildSubItem(rs);
                dto.addItem(mySubItemDto);
                if (config) {
                    // 载入用户发票信息
                    if (AppUtils.isNotBlank(dto.getInvoiceSubId()) && dto.getInvoiceSubId() != 0) {
                        InvoiceSub invoiceSub = new InvoiceSub();
                        invoiceSub.setTypeId(rs.getInt("invTypeId"));
                        invoiceSub.setTitleId(rs.getInt("invTitleId"));
                        invoiceSub.setCompany(rs.getString("invCompany"));
                        invoiceSub.setInvoiceHumNumber(rs.getString("invoiceHumNumber"));
                        dto.setInvoiceSub(invoiceSub);
                    }
                    // 载入用户地址信息
                    if (AppUtils.isNotBlank(dto.getAddrOrderId())) {
                        UserAddressSub userAddressSub = new UserAddressSub();
                        userAddressSub.setReceiver(rs.getString("receiver"));
                        userAddressSub.setTelphone(rs.getString("telphone"));
                        userAddressSub.setSubPost(rs.getString("subPost"));
                        userAddressSub.setMobile(rs.getString("mobile"));
                        userAddressSub.setDetailAddress(rs.getString("detailAddress"));
                        dto.setUserAddressSub(userAddressSub);
                    }

                }

                // 载入订单物流公司信息
                if (AppUtils.isNotBlank(dto.getDvyTypeId())) {
                    DeliveryDto delivery = new DeliveryDto();
                    delivery.setDelName(rs.getString("companyName"));
                    delivery.setDelUrl(rs.getString("companyHomeUrl"));
                    delivery.setQueryUrl(rs.getString("queryUrl"));
                    delivery.setDvyFlowId(dto.getDvyFlowId());
                    delivery.setDvyTypeId(dto.getDvyTypeId());
                    dto.setDelivery(delivery);
                }

                map.put(subNumber, dto);
            }
            return null;
        }

        public MySubDto buildSub(ResultSet rs) throws SQLException {
            MySubDto dto = new MySubDto();
            dto.setSubId(rs.getLong("subId"));
            dto.setAmountType(rs.getInt("amountType"));
            dto.setSubNum(rs.getString("subNumber"));
            dto.setProdName(rs.getString("prodName"));
            dto.setUserId(rs.getString("userId"));
            dto.setUserName(rs.getString("userName"));
            dto.setSubType(rs.getString("subType"));
            dto.setTotal(rs.getDouble("total"));
            dto.setActualTotal(rs.getDouble("actualTotal"));
            dto.setPayManner(rs.getInt("payManner"));
            dto.setPayTypeId(rs.getString("payTypeId"));
            dto.setPayTypeName(rs.getString("payTypeName"));
            dto.setPayDate(rs.getTimestamp("payDate"));
            dto.setOther(rs.getString("other"));
            dto.setShopId(rs.getLong("shopId"));
            dto.setShopName(rs.getString("shopName"));
            dto.setStatus(rs.getInt("status"));
            dto.setScore(rs.getInt("score"));
            dto.setDvyType(rs.getString("dvyType"));
            dto.setDvyTypeId(rs.getLong("dvyTypeId"));
            dto.setDvyFlowId(rs.getString("dvyFlowId"));
            dto.setFreightAmount(rs.getDouble("freightAmount"));
            dto.setInvoiceSubId(rs.getLong("invoiceSubId"));
            dto.setOrderRemark(rs.getString("orderRemark"));
            dto.setAddrOrderId(rs.getLong("addrOrderId"));
            dto.setIsNeedInvoice(rs.getInt("isNeedInvoice") == 1);
            dto.setWeight(rs.getDouble("weight"));
            dto.setVolume(rs.getDouble("volume"));
            dto.setProductNums(rs.getInt("productNums"));
            dto.setSubDate(rs.getTimestamp("subDate"));
            dto.setDvyDate(rs.getTimestamp("dvyDate"));
            dto.setFinallyDate(rs.getTimestamp("finallyDate"));
            dto.setUpdateDate(rs.getTimestamp("updateDate"));
            dto.setIspay(rs.getInt("status") != 1);
            dto.setIsCod(rs.getInt("isCod") != 1);
            dto.setDeleteStatus(rs.getInt("deleteStatus"));
            dto.setDiscountPrice(rs.getDouble("discountPrice"));
            dto.setDistCommisAmount(rs.getDouble("distCommisAmount"));
            dto.setCouponOffPrice(rs.getDouble("couponOffPrice"));
            dto.setRefundState(rs.getLong("refundState"));
            dto.setPredepositAmount(rs.getDouble("predepositAmount"));
            dto.setPredPayType(rs.getString("predPayType"));
            dto.setRedpackOffPrice(rs.getDouble("redpackOffPrice"));
            dto.setRemindDelivery(rs.getBoolean("remindDelivery"));
            dto.setRemindDeliveryTime(rs.getDate("remindDeliveryTime"));
            dto.setSubSettlement(rs.getString("subSettlement"));
            dto.setFlowTradeNo(rs.getString("flowTradeNo"));
            dto.setCancelReason(rs.getString("cancelReason"));
            //备注
            dto.setIsShopRemarked(rs.getInt("isShopRemarked") == 1);
            dto.setShopRemark(rs.getString("shopRemark"));
            dto.setShopRemarkDate(rs.getTimestamp("shopRemarkDate"));
            dto.setActiveId(rs.getLong("activeId"));
            dto.setGroupStatus(rs.getInt("groupStatus"));
            dto.setMergeGroupStatus((Integer) rs.getObject("mergeGroupStatus"));
            dto.setAddNumber(rs.getString("addNumber"));

            //2018-12-18  新增 预售订单的信息
            dto.setPreDepositPrice(rs.getBigDecimal("preDepositPrice"));
            dto.setIsPayDeposit(rs.getInt("isPayDeposit"));
            dto.setDepositPayName(rs.getString("depositPayName"));
            dto.setDepositTradeNo(rs.getString("depositTradeNo"));
            dto.setFinalPayName(rs.getString("finalPayName"));
            dto.setFinalTradeNo(rs.getString("finalTradeNo"));
            dto.setFinalPrice(rs.getBigDecimal("finalPrice"));
            dto.setPayPctType(rs.getInt("payPctType"));
            dto.setFinalMStart(rs.getTimestamp("finalMStart"));
            dto.setFinalMEnd(rs.getTimestamp("finalMEnd"));
            dto.setIsPayFinal(rs.getInt("isPayFinal"));
            dto.setDepositPayTime(rs.getTimestamp("depositPayTime"));
            dto.setFinalPayTime(rs.getTimestamp("finalPayTime"));

            return dto;
        }

        private SubOrderItemDto buildSubItem(ResultSet rs) throws SQLException {
            SubOrderItemDto subOrderItemDto = new SubOrderItemDto();
            subOrderItemDto.setSubItemId(rs.getLong("subItemId"));
            subOrderItemDto.setSubItemNumber(rs.getString("subItemNumber"));
            subOrderItemDto.setProdId(rs.getLong("prodId"));
            subOrderItemDto.setSkuId(rs.getLong("skuId"));
            subOrderItemDto.setSnapshotId(rs.getLong("snapshotId"));
            subOrderItemDto.setBasketCount(rs.getBigDecimal("basketCount"));
            subOrderItemDto.setProdName(rs.getString("itemProdName"));
            subOrderItemDto.setAttribute(rs.getString("attribute"));
            subOrderItemDto.setPic(rs.getString("pic"));
            subOrderItemDto.setCash(rs.getDouble("cash"));
            subOrderItemDto.setPrice(rs.getDouble("price"));
            subOrderItemDto.setProductTotalAmount(rs.getDouble("productTotalAmount"));
            subOrderItemDto.setObtainIntegral(rs.getDouble("obtainIntegral"));
            subOrderItemDto.setWeight(rs.getDouble("itemWeight"));
            subOrderItemDto.setVolume(rs.getDouble("itemVolume"));
            subOrderItemDto.setDistUserName(rs.getString("distUserName"));
            subOrderItemDto.setDistCommisCash(rs.getDouble("distCommisCash"));
            subOrderItemDto.setPromotionInfo(rs.getString("promotionInfo"));
            subOrderItemDto.setCommSts(rs.getInt("commSts"));
            subOrderItemDto.setRefundId(rs.getLong("refundId"));
            subOrderItemDto.setRefundState(rs.getLong("itemRefundState"));
            subOrderItemDto.setRefundAmount(rs.getDouble("refundAmount"));
            subOrderItemDto.setRefundType(rs.getLong("refundType"));
            subOrderItemDto.setRefundCount(rs.getBigDecimal("refundCount"));
            return subOrderItemDto;
        }
    }


    @Override
    public MySubDto findOrderDetail(String subNumber) {
        QueryMap queryMap = new QueryMap();
        String sql = ConfigCode.getInstance().getCode("order.findOrderDetail", queryMap);
        Map<String, MySubDto> map = new HashMap<>();
        super.get(sql, new Object[]{subNumber}, new MySubDtoRowMapper(map, true));
        if (AppUtils.isBlank(map)) return null;
        return map.get(subNumber);
    }

    @Override
    public MySubDto findOrderDetail(String subNumber, Long shopId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("shopId", shopId);
        String sql = ConfigCode.getInstance().getCode("order.findOrderDetail", queryMap);
        Map<String, MySubDto> map = new HashMap<>();
        super.get(sql, new Object[]{subNumber, shopId}, new MySubDtoRowMapper(map, true));
        if (AppUtils.isBlank(map)) return null;
        return map.get(subNumber);

    }

    @Override
    public MySubDto findOrderDetail(String subNumber, String userId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("userId", userId);
        String sql = ConfigCode.getInstance().getCode("order.findOrderDetail", queryMap);
        Map<String, MySubDto> map = new HashMap<>();
        super.get(sql, new Object[]{subNumber, userId}, new MySubDtoRowMapper(map, true));
        if (AppUtils.isBlank(map)) return null;
        return map.get(subNumber);
    }

    /**
     * 查询订单列表信息
     */
    @Override
    public PageSupport<MySubDto> getOrders(QueryMap queryMap, List<Object> args, int curPageNO, int pageSize, OrderSearchParamDto paramDto) {
        String dialectType = this.getDialect().getDialectType();
        String countSql = ConfigCode.getInstance().getCode(dialectType + ".getOrdersCount", queryMap);
        String subSQl = ConfigCode.getInstance().getCode(dialectType + ".getOrders", queryMap);
        long total = getLongResult(countSql, args.toArray());
        List<MySubDto> mySubDtos = getMySubDtos(subSQl, args);
        return initPageProvider(total, curPageNO, pageSize, mySubDtos);
    }

    private PageSupport<MySubDto> initPageProvider(long total, Integer curPageNO, Integer pageSize, List<MySubDto> resultList) {
        DefaultPagerProvider pageProvider = new DefaultPagerProvider(total, curPageNO, pageSize);
        return new PageSupport<>(resultList, pageProvider);
    }

    private List<MySubDto> getMySubDtos(String sql, List<Object> args) {
        Map<String, MySubDto> map = new HashMap<>();
        super.query(sql, args.toArray(), new MySubDtoListRowMapper(map));
        if (AppUtils.isBlank(map)) return null;
        List<MySubDto> subDtos = new ArrayList<>();
        for (Entry<String, MySubDto> entry : map.entrySet()) {
            MySubDto mySubDto = entry.getValue();
            subDtos.add(mySubDto);
        }
        subDtos.sort((o1, o2) -> o2.getSubDate().compareTo(o1.getSubDate()));
        return subDtos;
    }


    class MySubDtoListRowMapper implements RowMapper<OrderDetailDto> {

        private Map<String, MySubDto> map = null;

        public MySubDtoListRowMapper(Map<String, MySubDto> map) {
            this.map = map;
        }

        @Override
        public OrderDetailDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            String subNumber = rs.getString("subNumber");
            if (map.containsKey(subNumber)) { // 存在
                MySubDto mySubDto = map.get(subNumber);
                SubOrderItemDto mySubItemDto = buildSubItem(rs);
                mySubDto.addItem(mySubItemDto);
            } else {
                MySubDto dto = buildSub(rs);
                SubOrderItemDto mySubItemDto = buildSubItem(rs);

                dto.addItem(mySubItemDto);

                map.put(subNumber, dto);
            }
            return null;
        }

        public MySubDto buildSub(ResultSet rs) throws SQLException {
            MySubDto dto = new MySubDto();
            dto.setSubId(rs.getLong("subId"));
            dto.setSubNum(rs.getString("subNumber"));
            dto.setUserId(rs.getString("userId"));
            dto.setUserName(rs.getString("userName"));
            dto.setReciverName(rs.getString("receiver"));
            dto.setReciverMobile(rs.getString("mobile"));
            dto.setSubType(rs.getString("subType"));
            dto.setActualTotal(rs.getDouble("actualTotal"));
            dto.setPayManner(rs.getInt("payManner"));
            dto.setShopId(rs.getLong("shopId"));
            dto.setShopName(rs.getString("shopName"));
            dto.setStatus(rs.getInt("status"));
            dto.setFreightAmount(rs.getDouble("freightAmount"));
            dto.setSubDate(rs.getTimestamp("subDate"));
            dto.setRefundState(rs.getLong("refundState"));
            dto.setDvyTypeId(rs.getLong("dvyTypeId"));
            dto.setSubSettlement(rs.getString("subSettlement"));
            dto.setFlowTradeNo(rs.getString("flowTradeNo"));
            //备注
            dto.setIsShopRemarked(rs.getInt("isShopRemarked") == 1);
            dto.setShopRemark(rs.getString("shopRemark"));
            dto.setShopRemarkDate(rs.getTimestamp("shopRemarkDate"));
            dto.setActiveId(rs.getLong("activeId"));
            dto.setGroupStatus(rs.getInt("groupStatus"));
            dto.setMergeGroupStatus((Integer) rs.getObject("mergeGroupStatus"));
            dto.setAddNumber(rs.getString("addNumber"));
            dto.setHasInvoice(rs.getInt("hasInvoice"));
            dto.setIsNeedInvoice(rs.getInt("isNeedInvoice") == 1);
            dto.setPayTypeId(rs.getString("payTypeId"));
            dto.setPayTypeName(rs.getString("payTypeName"));
            dto.setIspay(rs.getBoolean("ispay"));
            dto.setUpdateDate(rs.getTimestamp("updateDate"));
            dto.setProductNums(rs.getInt("productNums"));

            //2018-12-14  新增 预售订单的信息
            dto.setPreDepositPrice(rs.getBigDecimal("preDepositPrice"));
            dto.setIsPayDeposit(rs.getInt("isPayDeposit"));
            dto.setDepositPayName(rs.getString("depositPayName"));
            dto.setDepositTradeNo(rs.getString("depositTradeNo"));
            dto.setFinalPayName(rs.getString("finalPayName"));
            dto.setFinalTradeNo(rs.getString("finalTradeNo"));
            dto.setFinalPrice(rs.getBigDecimal("finalPrice"));
            dto.setPayPctType(rs.getInt("payPctType"));
            dto.setFinalMStart(rs.getTimestamp("finalMStart"));
            dto.setFinalMEnd(rs.getTimestamp("finalMEnd"));
            dto.setIsPayFinal(rs.getInt("isPayFinal"));
            return dto;
        }

        private SubOrderItemDto buildSubItem(ResultSet rs) throws SQLException {
            SubOrderItemDto subOrderItemDto = new SubOrderItemDto();
            subOrderItemDto.setSubItemId(rs.getLong("subItemId"));
            subOrderItemDto.setProdId(rs.getLong("prodId"));
            subOrderItemDto.setSnapshotId(rs.getLong("snapshotId"));
            subOrderItemDto.setBasketCount(rs.getBigDecimal("basketCount"));
            subOrderItemDto.setProdName(rs.getString("itemProdName"));
            subOrderItemDto.setAttribute(rs.getString("attribute"));
            subOrderItemDto.setPic(rs.getString("pic"));
            subOrderItemDto.setCash(rs.getDouble("cash"));
            subOrderItemDto.setProductTotalAmount(rs.getDouble("productTotalAmount"));
            subOrderItemDto.setCommSts(rs.getInt("commSts"));
            subOrderItemDto.setRefundId(rs.getLong("refundId"));
            subOrderItemDto.setRefundType(rs.getLong("refundType"));
            subOrderItemDto.setRefundState(rs.getLong("itemRefundState"));
            subOrderItemDto.setRefundAmount(rs.getDouble("refundAmount"));
            return subOrderItemDto;
        }
    }


    /**
     * 查找用户曾经下过的团购单
     */
    @Override
    public Integer findUserOrderGroup(String userId, Long productId, Long skuId, Long groupId) {
        return get(
                /*"SELECT SUM(basket_count) FROM ls_sub LEFT JOIN ls_sub_item ON ls_sub.sub_number=ls_sub_item.sub_number WHERE ls_sub.user_id=? AND  ls_sub.sub_type='GROUP' AND  ls_sub_item.prod_id=?  AND ls_sub.active_id=?",*/
                "SELECT SUM(basket_count) FROM ls_sub LEFT JOIN ls_sub_item ON ls_sub.sub_number=ls_sub_item.sub_number WHERE ls_sub.user_id=? AND  ls_sub.sub_type='GROUP' AND  ls_sub_item.prod_id=? AND ls_sub.active_id=? AND ls_sub.status <>5", Integer.class, userId, productId, groupId);
    }

    @Override
    public PageSupport<PresellSubDto> getPresellOrdersList(QueryMap queryMap, List<Object> args, int curPageNO, int pageSize, OrderSearchParamDto paramDto) {
        String querySQL = ConfigCode.getInstance().getCode(this.getDialect().getDialectType() + ".getPresellOrders", queryMap);
        String queryCountSQL = ConfigCode.getInstance().getCode(this.getDialect().getDialectType() + ".getPresellOrdersCount", queryMap);
        long total = getLongResult(queryCountSQL, args.toArray());
        List<PresellSubDto> presellSubDtos = getPresellSubDtos(querySQL, args);
        return initPageProviderPresellOrders(total, curPageNO, pageSize, presellSubDtos);
    }

    private PageSupport<PresellSubDto> initPageProviderPresellOrders(long total, Integer curPageNO, Integer pageSize, List<PresellSubDto> resultList) {
        DefaultPagerProvider pageProvider = new DefaultPagerProvider(total, curPageNO, pageSize);
        return new PageSupport<>(resultList, pageProvider);
    }

    /**
     * @param querySQL
     * @param args
     * @return
     */
    private List<PresellSubDto> getPresellSubDtos(String querySQL, List<Object> args) {
        return this.query(querySQL, args.toArray(), new PresellSubDtoRowMapper(false));
    }

    @Override
    public PresellSubDto findPresellOrderDetail(String subNumber) {
        QueryMap queryMap = new QueryMap();
        String sql = ConfigCode.getInstance().getCode("order.findPresellOrderDetail", queryMap);
        return super.get(sql, new Object[]{subNumber}, new PresellSubDtoRowMapper(true));
    }

    @Override
    public PresellSubDto findPresellOrderDetail(String subNumber, String userId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("userId", userId);
        String sql = ConfigCode.getInstance().getCode("order.findPresellOrderDetail", queryMap);
        return super.get(sql, new Object[]{subNumber, userId}, new PresellSubDtoRowMapper(true));
    }

    @Override
    public PresellSubEntity getPreSubBySubNoAndUserId(String subNumber, String userId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("userId", userId);
        String sql = ConfigCode.getInstance().getCode("order.getPreSubBySubNoAndOther", queryMap);
        return this.get(sql, PresellSubEntity.class, subNumber, userId);
    }

    @Override
    public PresellSubEntity getPreSubBySubNoAndShopId(String subNumber, Long shopId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("shopId", shopId);
        String sql = ConfigCode.getInstance().getCode("order.getPreSubBySubNoAndOther", queryMap);
        return this.get(sql, PresellSubEntity.class, subNumber, shopId);
    }

    @Override
    public PresellSubEntity getPreSubBySubNo(String subNumber) {
        QueryMap queryMap = new QueryMap();
        String sql = ConfigCode.getInstance().getCode("order.getPreSubBySubNoAndOther", queryMap);
        return this.get(sql, PresellSubEntity.class, subNumber);
    }

    /**
     * 预售订单Dto的结果集映射
     *
     * <AUTHOR>
     * @Description
     */
    class PresellSubDtoRowMapper implements RowMapper<PresellSubDto> {
        private boolean isFindDetail = false;// 是否查询订单详情
        private boolean isTheFirst = true;// 是否第一次

        PresellSubDtoRowMapper(boolean isFindDetail) {
            this.isFindDetail = isFindDetail;
        }

        @Override
        public PresellSubDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            if (isTheFirst) {// 如果是第一次
                rs.beforeFirst();// 使指针回到表头
                if (!rs.next()) return null;// 探测有没有记录
                isTheFirst = false;
            }

            Long subId = rs.getLong("subId");// 记住

            // 封装预售订单
            PresellSubDto presellSubDto = new PresellSubDto();
            presellSubDto.setSubId(subId);
            presellSubDto.setSubNo(rs.getString("subNo"));
            presellSubDto.setShopId(rs.getLong("shopId"));
            presellSubDto.setShopName(rs.getString("shopName"));
            presellSubDto.setUserId(rs.getString("userId"));
            presellSubDto.setUserName(rs.getString("userName"));
            presellSubDto.setSubCreateTime(rs.getTimestamp("subCreateTime"));
            presellSubDto.setTotalPrice(rs.getDouble("totalPrice"));
            presellSubDto.setActualTotalPrice(rs.getDouble("actualTotalPrice"));
            presellSubDto.setFreightAmount(rs.getDouble("freightAmount"));
            presellSubDto.setBasketCount(rs.getLong("basketCount"));
            presellSubDto.setDepositPrice(rs.getDouble("depositPrice"));
            presellSubDto.setDepositPayName(rs.getString("depositPayName"));
            presellSubDto.setDepositTradeNo(rs.getString("depositTradeNo"));
            presellSubDto.setFinalPayName(rs.getString("finalPayName"));
            presellSubDto.setFinalTradeNo(rs.getString("finalTradeNo"));
            presellSubDto.setIsPayDeposit(rs.getLong("isPayDeposit"));
            presellSubDto.setFinalPrice(rs.getDouble("finalPrice"));
            presellSubDto.setFinalMStart(rs.getTimestamp("finalMStart"));
            presellSubDto.setFinalMEnd(rs.getTimestamp("finalMEnd"));
            presellSubDto.setIsPayFinal(rs.getLong("isPayFinal"));
            presellSubDto.setStatus(rs.getLong("status"));
            presellSubDto.setIsPayed(rs.getLong("isPayed"));
            presellSubDto.setPayDate(rs.getTimestamp("payDate"));
            presellSubDto.setPayTypeName(rs.getString("payTypeName"));
            presellSubDto.setAddrOrderId(rs.getLong("addrOrderId"));
            presellSubDto.setInvoiceSubId(rs.getLong("invoiceSubId"));
            presellSubDto.setDvyTypeId(rs.getLong("dvyTypeId"));
            presellSubDto.setDvyFlowId(rs.getString("dvyFlowId"));
            presellSubDto.setDvyDate(rs.getDate("dvyDate"));
            presellSubDto.setFinallyDate(rs.getTimestamp("finallyDate"));
            presellSubDto.setDvyType(rs.getString("dvyType"));
            presellSubDto.setOrderRemark(rs.getString("orderRemark"));
            presellSubDto.setPayPctType(rs.getInt("payPctType"));
            presellSubDto.setRefundState(rs.getLong("refundState"));

            // 如果查询的是详情页面
            if (isFindDetail) {
                // 载入用户发票信息
                if (AppUtils.isNotBlank(presellSubDto.getInvoiceSubId()) && presellSubDto.getInvoiceSubId() != 0) {
                    InvoiceSub invoiceSub = new InvoiceSub();
                    invoiceSub.setTypeId(rs.getInt("invTypeId"));
                    invoiceSub.setTitleId(rs.getInt("invTitleId"));
                    invoiceSub.setCompany(rs.getString("invCompany"));
                    presellSubDto.setInvoiceSub(invoiceSub);
                }
                // 载入用户地址信息
                if (AppUtils.isNotBlank(presellSubDto.getAddrOrderId())) {
                    UserAddressSub userAddressSub = new UserAddressSub();
                    userAddressSub.setReceiver(rs.getString("receiver"));
                    userAddressSub.setTelphone(rs.getString("telphone"));
                    userAddressSub.setSubPost(rs.getString("subPost"));
                    userAddressSub.setMobile(rs.getString("mobile"));
                    userAddressSub.setDetailAddress(rs.getString("detailAddress"));
                    presellSubDto.setUserAddressSub(userAddressSub);
                }
                // 载入订单物流公司信息
                if (AppUtils.isNotBlank(presellSubDto.getDvyTypeId())) {
                    DeliveryDto delivery = new DeliveryDto();
                    delivery.setDelName(rs.getString("companyName"));
                    delivery.setDelUrl(rs.getString("companyHomeUrl"));
                    delivery.setQueryUrl(rs.getString("queryUrl"));
                    delivery.setDvyFlowId(presellSubDto.getDvyFlowId());
                    delivery.setDvyTypeId(presellSubDto.getDvyTypeId());
                    presellSubDto.setDelivery(delivery);
                }
            }

            // 封装预售订单项
            PresellSubItemDto presellSubItemDto = new PresellSubItemDto();
            presellSubItemDto.setProdId(rs.getLong("prodId"));
            presellSubItemDto.setSkuId(rs.getLong("skuId"));
            presellSubItemDto.setProdName(rs.getString("prodName"));
            presellSubItemDto.setProdPrice(rs.getDouble("prodPrice"));
            presellSubItemDto.setProdCash(rs.getDouble("prodCash"));
            presellSubItemDto.setProdPic(rs.getString("prodPic"));
            presellSubItemDto.setAttribute(rs.getString("attribute"));
            presellSubItemDto.setCommSts(rs.getLong("commsts"));
            presellSubItemDto.setBasketCount(rs.getInt("basketCount"));
            presellSubItemDto.setProductTotalAmount(rs.getDouble("productTotalAmount"));

            List<PresellSubItemDto> orderItems = new ArrayList<>();
            orderItems.add(presellSubItemDto);

            // 继续往下走
            while (rs.next()) {
                if (!subId.equals(rs.getLong("subId"))) {
                    rs.previous();
                    break;
                }

                // 封装预售订单项
                presellSubItemDto = new PresellSubItemDto();
                presellSubItemDto.setProdId(rs.getLong("prodId"));
                presellSubItemDto.setSkuId(rs.getLong("skuId"));
                presellSubItemDto.setProdName(rs.getString("prodName"));
                presellSubItemDto.setProdPrice(rs.getDouble("prodPrice"));
                presellSubItemDto.setProdCash(rs.getDouble("prodCash"));
                presellSubItemDto.setProdPic(rs.getString("prodPic"));
                presellSubItemDto.setAttribute(rs.getString("attribute"));
                presellSubItemDto.setCommSts(rs.getLong("commsts"));
                presellSubItemDto.setBasketCount(rs.getInt("basketCount"));
                presellSubItemDto.setProductTotalAmount(rs.getDouble("productTotalAmount"));

                orderItems.add(presellSubItemDto);
            }

            presellSubDto.setOrderItems(orderItems);
            return presellSubDto;
        }
    }

    @Override
    public int updateRefundState(Long subId, Integer refundState) {
        String sql = "UPDATE ls_sub SET refund_state = ? WHERE sub_id = ?";
        return this.update(sql, refundState, subId);
    }

    @Override
    public int closeSub(Long subId) {
        String sql = "UPDATE ls_sub SET update_date=?,status = ? WHERE sub_id = ?";
        return this.update(sql, new Date(), OrderStatusEnum.CLOSE.value(), subId);
    }

    @Override
    public int finalySub(Long subId) {
        Date timeDate = new Date();
        String sql = "UPDATE ls_sub SET update_date=?,finally_date=?,status = ? WHERE sub_id = ?";
        return this.update(sql, timeDate, timeDate, OrderStatusEnum.SUCCESS.value(), subId);
    }

    @Override
    public Integer getTodaySubCount(Long shopId) {
        return this.get("SELECT COUNT(s.sub_id) FROM ls_sub s WHERE s.shop_id=? AND s.status=?", Integer.class, shopId, OrderStatusEnum.PAID.value());
    }

    @Override
    public List<OrderExprotDto> exportOrder(String string, Object[] array) {
        return this.query(string, OrderExprotDto.class, array);
    }


    @Override
    public int remindDelivery(Long subId, String userId) {
        String sql = "UPDATE ls_sub SET remind_delivery = 1, remind_delivery_time = ? WHERE sub_id = ? and user_id = ? ";
        return this.update(sql, new Date(), subId, userId);
    }

    @Override
    public List<Sub> findCancelOrders() {
        return this.query("select sub_id as subId,sub_number as subNumber,sub_date as subDate,user_id as userId,sub_type as subType from ls_sub where status=? order by sub_date asc ", Sub.class, OrderStatusEnum.UNPAID.value());
    }

    @Override
    public void updateBuys(long buys, Long prodId) {
        update("update ls_prod set buys = ? where prod_id = ?", buys, prodId);
    }

    @Override
    public Sub getSubBySubItemId(Long subItemId) {
        String sql = "SELECT sub.* FROM ls_sub sub, ls_sub_item item " + "WHERE sub.sub_number = item.sub_number " + "AND item.sub_item_id = ? ";
        return this.get(sql, Sub.class, subItemId);
    }

    @Override
    public int remindDeliveryBySN(String subNumber, String userId) {
        String sql = "UPDATE ls_sub SET remind_delivery = 1, remind_delivery_time = ? WHERE sub_number = ? and user_id = ? ";
        return this.update(sql, new Date(), subNumber, userId);
    }

    @Override
    public PageSupport<Sub> getSubListPage(String curPageNO, ShopOrderBill shopOrderBill, String subNumber) {
        CriteriaQuery cq = new CriteriaQuery(Sub.class, curPageNO);
        cq.setPageSize(10);
        cq.eq("shopId", shopOrderBill.getShopId());
        cq.eq("status", OrderStatusEnum.SUCCESS.value());
        cq.eq("billSn", shopOrderBill.getSn());
        cq.eq("subNumber", subNumber);
        return queryPage(cq);
    }

    @Override
    public PageSupport getOrdersPage(OrderSearchParamDto paramDto, int pageSize) {
        QueryMap param = new QueryMap();
        param.put("userName", paramDto.getUserName());

        if (AppUtils.isNotBlank(paramDto.getSubNumber())) {
            param.put("subNumber", paramDto.getSubNumber());
        }
        if (AppUtils.isNotBlank(paramDto.getStatus())) {
            // 子订单状态
            param.put("status", paramDto.getStatus());
        }

        if (AppUtils.isNotBlank(paramDto.getEndDate())) {
            paramDto.setEndDate(getEndMonment(paramDto.getEndDate()));// 结束日期更新为最后一天
        }

        if (AppUtils.isNotBlank(paramDto.getStartDate())) {
            param.put("startDate", paramDto.getStartDate());
        }
        if (AppUtils.isNotBlank(paramDto.getEndDate())) {
            param.put("endDate", paramDto.getEndDate());
        }

        param.put("removeStatus", paramDto.getRemoveStatus());
        String queryAllSQL = ConfigCode.getInstance().getCode("prod.getAdminCountOrder", param);// 查询订单数量
        String querySQL = ConfigCode.getInstance().getCode("prod.getAdminOrder", param);// 查询订单对象List
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(OrderSub.class, pageSize, paramDto.getCurPageNO());
        simpleSqlQuery.setAllCountString(queryAllSQL);
        simpleSqlQuery.setQueryString(querySQL);
        simpleSqlQuery.setParam(param.toArray());
        return querySimplePage(simpleSqlQuery);
    }

    private Date getEndMonment(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime();
    }

    @Override
    public PageSupport<Sub> getSubListPage(String curPageNO, Long shopId, String subNumber, ShopOrderBill shopOrderBill) {
        CriteriaQuery cq = new CriteriaQuery(Sub.class, curPageNO);
        cq.setPageSize(10);
        cq.eq("shopId", shopId);
        cq.eq("status", OrderStatusEnum.SUCCESS.value());
        cq.eq("subNumber", subNumber);
        cq.eq("billSn", shopOrderBill.getSn());
        return queryPage(cq);
    }

    @Override
    public PageSupport<Sub> getOrder(String curPageNO, String userName) {
        CriteriaQuery cq = new CriteriaQuery(Sub.class, curPageNO);
        cq.setPageSize(15);
        cq.eq("userName", userName);
        cq.eq("deleteStatus", OrderDeleteStatusEnum.NORMAL.value());
        cq.gt("subDate", DateUtil.getTimeMonthsAgo(1));
        cq.addOrder("desc", "subDate");
        return queryPage(cq);
    }

    @Override
    public PageSupport<InvoiceSubDto> getInvoiceOrder(String curPageNO, Long shopId) {

        String sql = "SELECT ls.sub_number AS subNumber,li.title_id AS titleId,li.company AS company,li.create_time AS createTime,li.invoice_hum_number AS invoiceHumNumber,ls.user_name AS userName,ls.sub_date AS buyDate,ls.has_invoice AS hasInvoice FROM ls_sub ls LEFT JOIN ls_invoice li ON ls.invoice_sub_id = li.id WHERE ls.invoice_sub_id IS NOT NULL AND ls.is_need_invoice=1 AND ls.shop_id=? order by li.create_time desc";
        String countSql = "SELECT count(1) FROM ls_sub ls LEFT JOIN ls_invoice li ON ls.invoice_sub_id = li.id WHERE ls.invoice_sub_id IS NOT NULL AND ls.is_need_invoice=1 AND ls.shop_id=?";
        QueryMap map = new QueryMap();
        map.put("shopId", shopId);
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(InvoiceSubDto.class, 10, curPageNO);
        simpleSqlQuery.setAllCountString(countSql);
        simpleSqlQuery.setQueryString(sql);
        simpleSqlQuery.setParam(map.toArray());
        return querySimplePage(simpleSqlQuery);
    }

    @Override
    public PageSupport<InvoiceSubDto> getInvoiceOrder(String curPageNO, Long shopId, InvoiceSubDto invoiceSubDto) {
        QueryMap map = new QueryMap();
        map.put("shopId", shopId);
        if (AppUtils.isNotBlank(invoiceSubDto.getSubNumber())) {
            map.like("subNumber", invoiceSubDto.getSubNumber());
        }
        if (AppUtils.isNotBlank(invoiceSubDto.getHasInvoice())) {
            map.put("hasInvoice", invoiceSubDto.getHasInvoice());
        }
        String queryAllSQL = ConfigCode.getInstance().getCode("uc.invoiceSub.queryInvoiceSubCount", map);// 查询订单数量
        String querySQL = ConfigCode.getInstance().getCode("uc.invoiceSub.queryInvoiceSub", map);// 查询订单对象List
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(InvoiceSubDto.class, 10, curPageNO);
        simpleSqlQuery.setAllCountString(queryAllSQL);
        simpleSqlQuery.setQueryString(querySQL);
        simpleSqlQuery.setParam(map.toArray());
        return querySimplePage(simpleSqlQuery);
    }

    @Override
    public List<Sub> getSubBySubNums(String[] subNums, String userId, Integer orderStatus) {
        QueryMap map = new QueryMap();
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        for (String subNum : subNums) {
            sql.append("?,");
            params.add(subNum);
        }
        sql.setLength(sql.length() - 1);
        map.put("subBySubNums", sql.toString());
        params.add(userId);
        params.add(orderStatus);
        String query = ConfigCode.getInstance().getCode("order.getSubBySubNums", map);
        return query(query, Sub.class, params.toArray());
    }


    @Override
    public StoreProd getStoreProdByShopIdAndProdId(Long shopId, Long prodId) {
        return get("SELECT * FROM ls_store_prod AS sp WHERE sp.shop_id = ? AND sp.prod_id = ?", StoreProd.class, shopId, prodId);
    }

    @Override
    public List<Sub> findHaveInGroupSub(Long groupId, Integer groupStatus) {
        return queryByProperties(new EntityCriterion().eq("activeId", groupId).eq("groupStatus", groupStatus).eq("subType", SubTypeEnum.GROUP.value()));
    }

    @Override
    public void updateSubGroupStatusByGroup(Long activeId, Integer status) {
        String sql = "UPDATE ls_sub SET group_status = ? WHERE active_id = ? AND sub_type = ?";
        update(sql, status, activeId, SubTypeEnum.GROUP.value());
    }

    @Override
    public void updateMergeGroupAddStatusByAddNumber(String addNumber) {
        String sql = "update ls_sub set merge_group_status=1 where add_number=?";
        this.update(sql, addNumber);

    }

    @Override
    public MergeGroupSubDto getMergeGroupSubDtoBySettleSn(String subSettlementSn) {
        String sql = "SELECT s.sub_id as subId,s.sub_number as subNumber,s.prod_name as prodName,s.sub_type as subType,o.id as operateId,s.add_number as addNumber,o.status,o.create_time as createTime," + "g.is_head as isHead,m.count,p.merge_price as mergePrice,k.cn_properties as cnProperties,m.people_number as peopleNumber,m.id as mergeId,k.pic,o.number,m.end_time as endTime  " + "FROM ls_sub s LEFT JOIN ls_sub_item i ON s.sub_number=i.sub_number " + "LEFT JOIN ls_merge_group_operate o ON s.add_number=o.add_number " + "LEFT JOIN ls_merge_group_add g ON g.operate_id=o.id AND s.user_id=g.user_id " + "LEFT JOIN ls_merge_group m ON o.merge_id = m.id " + "LEFT JOIN ls_merge_product p ON i.sku_id=p.sku_id AND p.merge_id=g.merge_id " + "LEFT JOIN ls_sku k ON i.sku_id=k.sku_id WHERE s.sub_settlement_sn=?";
        MergeGroupSubDto mergeGroupSubDto = this.get(sql, MergeGroupSubDto.class, subSettlementSn);
        if (AppUtils.isBlank(mergeGroupSubDto)) {
            return null;
        }
        return mergeGroupSubDto;
    }

    @Override
    public List<SubDto> exportSubOrder(String string, Object[] array) {
        return this.query(string, SubDto.class, array);
    }

    @Override
    public MySubDto getMySubDtoBySubNumber(String subNumber) {
        String sql = ConfigCode.getInstance().getCode("order.getMySubDtoBySubNumber");
        return get(sql, MySubDto.class, subNumber);
    }

    /*
     * 查询退款的总平台优惠券金额
     */
    @Override
    public Double getReturnRedpackOffPrice(Set<String> subNumbers) {
        StringBuilder sql = new StringBuilder("SELECT SUM(ROUND(redpack_off_price*cash/product_total_amount,2)) FROM ls_sub_item WHERE refund_state = 2 AND sub_number in (");

        for (int i = 0; i < subNumbers.size() - 1; i++) {
            sql.append("?, ");
        }
        sql.append("? )");

        return get(sql.toString(), Double.class, subNumbers.toArray());
    }

    @Override
    public Integer getSeckillSubBuyCount(Long seckillId, Long skuId) {
        String sql = "SELECT SUM(basket_count) FROM ls_sub_item item LEFT JOIN ls_sub sub ON sub.sub_number = item.sub_number WHERE sub.sub_type = 'SECKILL' AND sub.active_id = ? AND item.sku_id = ? AND sub.STATUS <> 1";
        return this.get(sql, Integer.class, seckillId, skuId);
    }

    @Override
    public List<Sub> getUnpaymentseckillOrder(Long seckillId) {
        String sql = "SELECT shop_id as shopId,sub_id AS subId,user_name AS userName,user_id AS userId,sub_number AS subNumber,total AS total,actual_total AS actualTotal,province_id as provinceId,active_id AS activeId " + "FROM ls_sub WHERE sub_type = 'SECKILL' AND active_id = ? AND STATUS = 1";
        return this.query(sql, Sub.class, seckillId);
    }


    @Override
    public MySubDto findOrderDetailBySubSettlementSn(String subSettlementSn, String userId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("subSettlementSn", subSettlementSn);
        queryMap.put("userId", userId);
        String sql = ConfigCode.getInstance().getCode("order.findSuccessOrderDetail", queryMap);
        return get(sql, MySubDto.class, subSettlementSn, userId);
    }

    @Override
    public List<MySubDto> queryOrderDetailBySubSettlementSn(String subSettlementSn, String userId) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("subSettlementSn", subSettlementSn);
        queryMap.put("userId", userId);
        String sql = ConfigCode.getInstance().getCode("order.findSuccessOrderDetailV2", queryMap);
        return query(sql, MySubDto.class, subSettlementSn, userId);
    }

    @Override
    public List<Sub> getSubBySubSettlementSn(String subSettlementSn, String userId) {

        String sql = " SELECT sub.* " + " FROM ls_sub sub INNER JOIN ls_sub_settlement settlement ON sub.sub_settlement_sn = settlement.sub_settlement_sn" + " WHERE  settlement.sub_settlement_sn = ? AND settlement.user_id = ? ";

        return this.query(sql, Sub.class, subSettlementSn, userId);
    }

    @Override
    public List<Sub> getSubBySubSettlementSn(String subSettlementSn) {
        String sql = " SELECT sub.* " + " FROM ls_sub sub INNER JOIN ls_sub_settlement settlement ON sub.sub_settlement_sn = settlement.sub_settlement_sn" + " WHERE  settlement.sub_settlement_sn = ? ";

        return this.query(sql, Sub.class, subSettlementSn);
    }

    public Sub findOrderDetailByContractNumber(String contractNumber) {
        return getByProperties(new EntityCriterion().eq("contractNum", contractNumber).notEq("status", OrderStatusEnum.CLOSE.value()));
    }

    public Sub findOrderDetailBySubNumber(String subNumber) {
        return getByProperties(new EntityCriterion().eq("subNumber", subNumber));
    }

    @Override
    public void updateStatus(Long subId, Integer status) {
        String sql = "UPDATE ls_sub SET status = ? WHERE sub_id = ?";
        update(sql, status, subId);
    }

    @Override
    public void updateOrderStatusToCancel(Long subId, String cancelReason) {
        String sql = "UPDATE ls_sub SET status = ? , cancel_reason = ? WHERE sub_id = ?";
        update(sql, OrderStatusEnum.CLOSE.value(), cancelReason, subId);
    }

    @Override
    public Boolean updateStatus(String subNumber, Integer status) {
        String sql = "UPDATE ls_sub SET status = ? WHERE sub_number = ?";
        return SqlUtil.retBool(update(sql, status, subNumber));
    }

    @Override
    public List<Sub> getSubBySubNumbers(String[] numbers) {
        return this.queryByProperties(new EntityCriterion().in("subNumber", numbers));
    }

    @Override
    public List<Sub> getSubBySubNumbersByUnPay(String[] numbers) {
        return this.queryByProperties(new EntityCriterion().in("subNumber", numbers).eq("status", OrderStatusEnum.UNPAID.value()));
    }

    @Override
    public Sub getSubBySubNumberByUnPay(String subNumber) {
        return this.getByProperties(new EntityCriterion().eq("subNumber", subNumber).eq("status", OrderStatusEnum.UNPAID.value()));
    }

    @Override
    public Sub getSubBySubNumberByClearing(String subNumber) {
        return this.getByProperties(new EntityCriterion().eq("subNumber", subNumber));
    }

    @Override
    public List<Sub> querySubBySubNumbersByClearing(List<String> subNumber) {
        return this.queryByProperties(new EntityCriterion().in("subNumber", subNumber).eq("status", OrderStatusEnum.CLEAN_PAYMENT.value()));
    }

    @Override
    public List<Sub> getSubBySubNumbersByIsPayed(String[] numbers) {
        return this.queryByProperties(new EntityCriterion().in("subNumber", numbers).eq("payed", 0));
    }

    @Override
    public Sub getSubBySubNumberByIsPayed(String subNumber) {
        return this.getByProperties(new EntityCriterion().eq("subNumber", subNumber).eq("payed", 0));
    }

    @Override
    public SubContractDTO getSubByContractNo(String contractNo) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("contractNo", contractNo);
        queryMap.put("status", OrderStatusEnum.CLOSE.value());
        String sql = ConfigCode.getInstance().getCode("order.getSubByContractNo", queryMap);
        return get(sql, SubContractDTO.class, queryMap.toArray());
    }

    @Override
    public SubContractDTO getSubByContract(String contract) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("contractNo", contract);
        queryMap.put("status", OrderStatusEnum.CLOSE.value());
        String sql = ConfigCode.getInstance().getCode("order.getSubByContract", queryMap);
        return get(sql, SubContractDTO.class, queryMap.toArray());
    }

    @Override
    public Sub getSubByContractNumber(String contractNo) {
        LambdaEntityCriterion<Sub> lambdaEntityCriterion = new LambdaEntityCriterion(Sub.class);
        lambdaEntityCriterion.eq(Sub::getContractNum, contractNo);
        return this.getByProperties(lambdaEntityCriterion);
    }

    @Override
    public ErpOrderCountDTO erpOrderCount(String userNo, String startDate, String endDate) {
        QueryMap queryMap = new QueryMap();
        queryMap.put("userNo", userNo);
        queryMap.put("startDate", startDate);
        queryMap.put("endDate", endDate);
        String sql = ConfigCode.getInstance().getCode("order.erpOrderCount", queryMap);
        return get(sql, ErpOrderCountDTO.class, queryMap.toArray());
    }

    @Override
    public Integer getSubStatusBySubNumber(String subNumber) {
        return get("select status from ls_sub where sub_number = ?;", Integer.class, subNumber);
    }

    @Override
    public Integer todaySubCountData() {
        log.info("查询时间：{}", cn.hutool.core.date.DateUtil.beginOfDay(new Date()));
        //生活品金额统计
        String cPaySql = "SELECT COUNT(*)" + C_PAY_SQL + "AND sub_date >= ? ";
        //第三方工业品金额统计
        String thirdIndustrialSql = "SELECT COUNT(*)" + THIRD_INDUSTRIAL_SQL + "AND sub_date >= ? ";
        //招标金额统计
        String bidsSql = "SELECT COUNT(*)" + BIDS_SQL + "AND sub_date >= ? ";
        //混凝土统计
        String concreteSql = "SELECT COUNT(*)" + CONCRETE_SQL + "AND sub_date >= ? ";
        //零碳数据统计
        String zeroCarbonSql = "SELECT COUNT(*)" + ZERO_CARBON_SQL + "AND sub_date >= ? ";
        //小羊有分商城等金额统计
        String externalSubSql = "SELECT COUNT(*)" + EXTERNAL_SUB_SQL + "AND order_date >= ?  ";
        return this.get(cPaySql, Integer.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date())) + this.get(thirdIndustrialSql, Integer.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date())) + this.get(bidsSql, Integer.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()))
        + this.get(concreteSql, Integer.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()))+this.get(zeroCarbonSql, Integer.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()))+this.get(externalSubSql, Integer.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()));
    }

    @Override
    public Double todayPayAmountData() {
        log.info("查询时间：{}", cn.hutool.core.date.DateUtil.beginOfDay(new Date()));
        //生活品金额统计
        String cPaySql = "SELECT SUM(actual_total)" + C_PAY_SQL + "AND sub_date >= ? ";
        //第三方工业品金额统计
        String thirdIndustrialSql = "SELECT SUM(actual_total)" + THIRD_INDUSTRIAL_SQL + "AND sub_date >= ? ";
        //招标金额统计
        String bidsSql = "SELECT SUM(actual_total)" + BIDS_SQL + "AND sub_date >= ? ";
        //混凝土统计
        String concreteSql = "SELECT SUM(actual_total)" + CONCRETE_SQL + "AND sub_date >= ? ";
        //零碳数据统计
        String zeroCarbonSql = "SELECT SUM(actual_total)" + ZERO_CARBON_SQL + "AND sub_date >= ? ";
        //小羊有分商城等金额统计
        String externalSubSql = "SELECT SUM(actual_total)" + EXTERNAL_SUB_SQL + "AND order_date >= ? ";
        return this.get(cPaySql, Double.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date())) + this.get(thirdIndustrialSql, Double.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date())) + this.get(bidsSql, Double.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()))
                + this.get(concreteSql, Double.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()))+this.get(zeroCarbonSql, Double.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()))+this.get(externalSubSql, Double.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()));
    }

    @Override
    public Integer getSubCountByDay() {
        //c端：支付了
        String cPaySql = "SELECT COUNT(*)" + COMMOM_C_PAY_SQL + "AND sub_date >= ?";
        //B端 款到发货 ：支付了
        String bOnlinePaySql = "SELECT COUNT(*)" + COMMOM_B_ONLINE_PAY_SQL + "AND sub_date >= ?";
        //B端 预付款,货到付款 ：合同签署完成
        String bOtherSql = "SELECT COUNT(*)" + COMMOM_B_NO_ONLINE_PAY_SQL + "AND sub_date >= ?";
        DateTime dateTime = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        return this.get(cPaySql, Integer.class, dateTime) + this.get(bOnlinePaySql, Integer.class, dateTime) + this.get(bOtherSql, Integer.class, dateTime);
    }

    @Override
    public Integer getSubCountByDayAndConsumer() {
        //c端：支付了
        String cPaySql = "SELECT COUNT(*)" + COMMOM_C_PAY_SQL + "AND sub_date >= ?";
        DateTime dateTime = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        return this.get(cPaySql, Integer.class, dateTime);
    }

    @Override
    public Double getSubAmountByDay() {
        //c端：支付了
        String cPaySql = "SELECT sum(actual_total)" + COMMOM_C_PAY_SQL + "AND sub_date >= ?";
        //B端 款到发货 ：支付了
        String bOnlinePaySql = "SELECT sum(actual_total)" + COMMOM_B_ONLINE_PAY_SQL + "AND sub_date >= ?";
        //B端 预付款,货到付款 ：合同签署完成
        String bOtherSql = "SELECT sum(actual_total)" + COMMOM_B_NO_ONLINE_PAY_SQL + "AND sub_date >= ?";
        DateTime dateTime = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        return get(cPaySql, Double.class, dateTime) + get(bOnlinePaySql, Double.class, dateTime) + get(bOtherSql, Double.class, dateTime);
    }

    @Override
    public Double getSubAmountByDayAndConsumer() {
        //c端：支付了
        String cPaySql = "SELECT sum(actual_total)" + COMMOM_C_PAY_SQL + "AND sub_date >= ?";
        return get(cPaySql, Double.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()));
    }

    @Override
    public List<VendorSubAmountDTO> getSubAmountTopByDay() {
        String sql = "SELECT SUM(actualTotal) AS `value`,userName AS content FROM (\n" + "\tSELECT s.actual_total AS actualTotal,s.sub_date AS `subdate`,s.user_name AS userName,s.user_id AS userId" + COMMOM_C_PAY_SQL + "\tUNION ALL\n" + "\tSELECT s.actual_total AS actualTotal,s.sub_date AS `subdate`,s.user_name AS userName,s.user_id AS userId" + COMMOM_B_ONLINE_PAY_SQL + "\tUNION ALL\n" + "\tSELECT s.actual_total AS actualTotal,s.sub_date AS `subdate`,s.user_name AS userName,s.user_id AS userId" + COMMOM_B_NO_ONLINE_PAY_SQL + ")o WHERE `subdate` >= ? GROUP BY userId ORDER BY VALUE DESC";
        return query(sql, VendorSubAmountDTO.class, cn.hutool.core.date.DateUtil.beginOfDay(new Date()));
    }

    @Override
    public List<Sub> getSubByConsignmentAndFirstNumber(Integer number) {
        return this.query("select * from ls_sub where status = ? order by sub_date desc limit ?", Sub.class, OrderStatusEnum.CONSIGNMENT.value(), number);
    }

    @Override
    public List<DataV> getVendorSalesRanking() {
        return this.query("SELECT shop_name AS content,shop_id as name, SUM(actual_total) AS value FROM ls_sub GROUP BY shop_id ORDER BY value DESC LIMIT 15", DataV.class);
    }

    @Override
    public DataV orderQuarterlySales() {
        return this.get("SELECT SUM(actual_total) AS value FROM ls_sub where sub_date >= ? and sub_date < ?", DataV.class, QuarterDateUtil.currentQuarterStartDate(), QuarterDateUtil.currentQuarterEndDate());
    }

    @Override
    public List<String> findSubNumberByShopIdAndBillSn(Long shopId, String billSn) {
        return this.query("select s.sub_number FROM ls_sub s where s.bill_sn = ? and s.shop_id = ?;", String.class, billSn, shopId);
    }

    @Override
    public boolean isSupportErpBankNotesPay(String subNum) {
        String sql = "SELECT COUNT(*) FROM ls_sub_amount_info WHERE sub_number = ?";
        return getLongResult(sql, subNum) > 0;
    }

    @Override
    public List<Sub> findSubByDvyFlowId(String dvyNumber) {
        return this.queryByProperties(new EntityCriterion().eq("dvyFlowId", dvyNumber));
    }

    @Override
    public List<String> getUntreatedNewOrder(Date startDate, Date endDate) {
        String sql = "SELECT CONCAT(s.shop_id,'-',COUNT(*)) FROM ls_sub s WHERE (CASE WHEN s.purchase_type = 2 THEN s.status = ? or (s.order_source = ? AND s.status= ?) ELSE s.status = ? END) AND s.update_date >= ? AND s.update_date  <= ? group by shop_id";
        return this.query(sql, String.class, OrderStatusEnum.TO_BE_CONFIRMED.value(), OrderSourceEnum.TENDER.value(), OrderStatusEnum.ADVANCE_ORDER.value(), OrderStatusEnum.PAID.value(), startDate, endDate);
    }

    @Override
    public List<Sub> findByContract(ErpOrderRequestParams erpOrderRequestParams) {
        StringBuilder sql = new StringBuilder("SELECT s.* FROM ls_sub s INNER JOIN ls_contract_sub_information csi ON csi.contract_number = s.contract_num WHERE 1 = 1");
        List<Object> params = new ArrayList<>();
        if (null != erpOrderRequestParams.getStatus()) {
            sql.append(" AND s.`status` = ? ");
            params.add(erpOrderRequestParams.getStatus());
        }
        if (!CollectionUtils.isEmpty(erpOrderRequestParams.getContractList())) {
            sql.append(" AND csi.contract IN ( ");
            for (int i = 0; i < erpOrderRequestParams.getContractList().size(); i++) {
                if (i != 0) {
                    sql.append(",");
                }
                sql.append("?");
            }
            sql.append(")");
            params.addAll(erpOrderRequestParams.getContractList());
        }
        return super.query(sql.toString(), Sub.class, params.toArray());
    }

    @Override
    public List<OrderBlackChainDTO> findSubBySubNum(String subNum) {
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT t.sub_number number, t.status statue, t.actual_total totalPrice, t.freight_amount freightAmount,");
        sb.append("t.coupon_off_price couponPrice, t.redpack_off_price redpackPrice, t.pay_type_name payTypeName, ");
        sb.append("t.user_name userName, t.order_remark remark,t.prod_name prodName,t.basket_count prodCount,t.price price, t.cn_properties attr, ");
        sb.append("t.sub_date createDate,t.sub_date subDate,t.finally_date finallyDate, t.shop_name shopName, t.party_code partyCode, ");
        sb.append("t.is_payed isPayed, t.refundSts1, t.refundSts2, t.receiver, t.mobile, t.detail_address addr, t.sub_type subType, ");
        sb.append("t.pay_date orderPayDate, t.dvy_date  deliveryDate, inv.company company, t.dvy_type dvyType, t.pay_manner payManner, ");
        sb.append("t.recommended_by as recommendedBy,t.annual_sales_amount as annualSalesAmount,t.enterprise_type AS enterpriseType,t.business_category AS businessCategory,");
        sb.append("t.pick_up_name AS pickUpName,t.pick_up_phone AS pickUpPhone,t.gcard_amount as gcardAmount,t.pre_deposit_amount as preDepositAmount,");
        sb.append("t.contract AS contract,");
        sb.append("t.platform_profit_sharing_amount AS shopProfitSharingAmount,( IFNULL(t.platform_profit_sharing_amount,0) + IFNULL(t.wallet_profit_sharing_amount,0) ) AS platformProfitSharingAmount,t.commission_rate AS profitSharingRate, t.purchase_type AS orderUserType ,");
        sb.append("t.dist_user_name AS distUserName,t.dist_second_name AS distSecondName,t.dist_user_commis AS distUserCommis,t.dist_second_commis AS distSecondCommis , t.user_mobile AS userMobile ");
        sb.append(",t.clearing_date AS profitSharingTime ,IFNULL(t.pay_amount,0) AS onlineAmount ");
        sb.append("FROM ( ");
        sb.append("SELECT s.sub_number, s.status, s.actual_total, s.freight_amount, si.coupon_off_price, s.redpack_off_price,");
        sb.append("s.pay_type_name, s.addr_order_id, u.user_name, s.order_remark ,si.prod_name, si.basket_count, ");
        sb.append("sk.cn_properties, s.sub_date,s.finally_date, s.shop_name , si.price,sk.model_id ,sk.party_code , s.is_payed, ");
        sb.append("r.is_handle_success refundSts2, rr.is_handle_success refundSts1, s.pay_date, s.dvy_date, s.invoice_sub_id, ");
        sb.append("a.receiver receiver, a.mobile mobile, a.detail_address, s.dvy_type, s.pay_manner, s.sub_type, ");
        sb.append("s.pre_deposit_amount,s.gcard_amount,");
        sb.append("csi.contract,u.user_mobile,");
        sb.append("sd.recommended_by,sd.annual_sales_amount,sd.enterprise_type,sd.business_category,");
        sb.append("sto.pick_up_name,sto.pick_up_phone,s.platform_profit_sharing_amount,s.wallet_profit_sharing_amount,sd.commission_rate ,s.purchase_type,");
        sb.append("si.dist_user_name,si.dist_second_name,si.dist_user_commis,si.dist_second_commis ");
        sb.append(",p.clearing_date,p.pay_amount ");
        sb.append("FROM ls_sub s ");
        sb.append("LEFT JOIN ls_presell_sub ps ON ps.sub_number = s.sub_number ");
        sb.append("INNER JOIN ls_usr_detail u ON s.user_id = u.user_id ");
        sb.append("INNER JOIN ls_sub_item si ON s.sub_number = si.sub_number ");
        sb.append("INNER JOIN ls_usr_addr_sub a ON s.addr_order_id = a.addr_order_id ");
        sb.append("LEFT JOIN ls_contract_sub_information csi ON s.sub_number = csi.sub_number ");
        sb.append("LEFT JOIN (SELECT p.sub_numbers,SUM(p.pay_amount) as pay_amount,p.payment_status,max(p.clearing_date) as clearing_date FROM ls_payment_transaction p WHERE p.payment_status = 1 ");
        if (AppUtils.isNotBlank(subNum)) {
            sb.append("AND p.sub_numbers = '" + subNum + "' ");
        }
        sb.append("GROUP BY p.sub_numbers) p ON p.sub_numbers = s.sub_number ");
        sb.append("INNER JOIN ls_shop_detail sd ON sd.shop_id = s.shop_id ");
        sb.append("LEFT JOIN ls_sku sk ON si.sku_id=sk.sku_id ");
        sb.append("LEFT JOIN ls_courier_station_order sto ON sto.sub_number = s.sub_number ");
        sb.append("LEFT JOIN ls_sub_refund_return r ON r.sub_item_id=si.sub_item_id AND (r.is_handle_success = 1  or r.seller_state = 1) "); //去掉 AND r.seller_state=2
        sb.append("LEFT JOIN ls_sub_refund_return rr ON rr.sub_number=si.sub_number AND rr.sub_item_id=0 AND (rr.is_handle_success = 1  or rr.seller_state = 1) "); //去掉  AND rr.seller_state=2
        sb.append("WHERE s.sub_number=si.sub_number AND s.user_id=u.user_id ");
        if (AppUtils.isNotBlank(subNum)) {
            sb.append("AND s.sub_number = ? ");
        }
        sb.append("	ORDER BY s.sub_date DESC ) t ");
        sb.append(" LEFT JOIN ls_invoice_sub inv ON t.invoice_sub_id = inv.id ");
        List<OrderBlackChainDTO> query = this.query(sb.toString(), OrderBlackChainDTO.class, subNum);
        return query;
    }

    @Override
    public List<Sub> findByContractDis(ErpOrderRequestParams erpOrderRequestParams) {
        StringBuilder sql = new StringBuilder("SELECT DISTINCT s.* FROM ls_sub s INNER JOIN ls_contract_sub_information csi ON csi.contract_number = s.contract_num WHERE 1 = 1");
        List<Object> params = new ArrayList<>();
        if (null != erpOrderRequestParams.getStatus()) {
            sql.append(" AND s.`status` = ? ");
            params.add(erpOrderRequestParams.getStatus());
        }
        if (!CollectionUtils.isEmpty(erpOrderRequestParams.getContractList())) {
            sql.append(" AND csi.contract IN ( ");
            for (int i = 0; i < erpOrderRequestParams.getContractList().size(); i++) {
                if (i != 0) {
                    sql.append(",");
                }
                sql.append("?");
            }
            sql.append(")");
            params.addAll(erpOrderRequestParams.getContractList());
        }
        return super.query(sql.toString(), Sub.class, params.toArray());
    }

    /**
     * 根据订单状态查出需要平台审核的订单
     *
     * @param status
     * @return
     */
    @Override
    public List<Sub> findAdminAuditSub() {
        String sql = "SELECT * FROM ls_sub WHERE status = 6 or status = 7";
        return this.query(sql, Sub.class);
    }

    /**
     * 查询取消的erp订单信息
     *
     * @param subNumber   订单号
     * @param orderStatus 订单状态
     * @return
     */
    @Override
    public List<ErpOrderCancelDTO> queryCancelErpOrder(String subNumber, Integer orderStatus) {
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT s.sub_number AS subNumber,lc.contract_number AS contractNum,lc.contract_version AS contractVersion FROM ls_sub s");
        sql.append(" LEFT JOIN ls_contract_sub_information lc ON s.sub_number = lc.sub_number WHERE s.order_source = 2");
        if (null != orderStatus) {
            sql.append(" AND s.status = ?");
            params.add(orderStatus);
        }
        if (null != subNumber) {
            sql.append(" AND s.sub_number = ?");
            params.add(subNumber);
        }
        return this.query(sql.toString(), ErpOrderCancelDTO.class, params.toArray());
    }

    @Override
    public Double gmvSubTotal() {
        String sql = "SELECT SUM(actual_total) from ls_sub";
        return this.get(sql, Double.class);
    }

    @Override
    public Integer cleanPlatformOrderDate() {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ls_sub SET platform_audit_pay_time = NULL ,platform_audit_dvy_time = NULL WHERE platform_audit_pay_time IS NOT NULL AND sub_number NOT IN(SELECT his.sub_number FROM ls_gcard_user_history his LEFT JOIN ls_gcard lg ON his.gcard_id = lg.id WHERE lg.settlement_mode = 'PLATFORM_JOIN') ");

        return this.update(sql.toString());
    }

    @Override
    public Integer querySubStatusBySubNumber(String subNumber) {
        String sql = "SELECT `status` FROM ls_sub WHERE sub_number = ?";
        return this.get(sql, Integer.class, subNumber);
    }


    @Override
    public List<Sub> querySubNumber(List<String> orderNumbers) {
        if (CollUtil.isEmpty(orderNumbers)) {
            return Collections.emptyList();
        }
        return queryByProperties(new LambdaEntityCriterion<>(Sub.class).in(Sub::getSubNumber, orderNumbers));
    }

    @Override
    public Boolean batchUpdateStatus(List<Long> ids, Integer value) {
        List<Long> args = new ArrayList<>();
        args.add(Long.valueOf(value));
        StringBuffer sb = new StringBuffer("update ls_sub s set s.status=? where s.sub_id in (");
        for (Long id : ids) {
            args.add(id);
            sb.append("?,");
        }
        sb.setLength(sb.length() - 1);
        sb.append(")");
        return SqlUtil.retBool(update(sb.toString(), args.toArray()));
    }

    @Override
    public PageSupport<SubV2ListDto> getThirdIndustrialSubInfo(OrderListQueryTermDTO orderListQueryTermDTO, Long shopId) {
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(SubV2ListDto.class, orderListQueryTermDTO.getPageSize(), orderListQueryTermDTO.getCurPage());
        QueryMap map = new QueryMap();

        if (shopId == null) {
            return null;
        }

        map.put("shopId", shopId);


        if (StrUtil.isNotBlank(orderListQueryTermDTO.getSubNumber())) {
            map.like("subNum", orderListQueryTermDTO.getSubNumber(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getProductName())) {
            map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getThirdSubNum())) {
            map.like("thirdSubNum", orderListQueryTermDTO.getThirdSubNum(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getContractNum())) {
            map.put("contractNum", orderListQueryTermDTO.getContractNum());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getOrderSource())) {
            map.put("orderSource", orderListQueryTermDTO.getOrderSource());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getDate())) {

            String[] split = orderListQueryTermDTO.getDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getFinallyDate())) {

            String[] split = orderListQueryTermDTO.getFinallyDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeFinallyDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterFinallyDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getBuyerName())) {
            map.like("buyerName", orderListQueryTermDTO.getBuyerName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getPhone())) {
            map.like("phone", orderListQueryTermDTO.getPhone(), MatchMode.ANYWHERE);
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getStatus())) {
            //如果100的话，是超15天订单
            if ("100".equals(orderListQueryTermDTO.getStatus())){
                map.put("overdueDays",15);
                map.in("status", Arrays.stream("4,7,5,10,15".split(",")).map(String::trim).collect(Collectors.toList()));
            }else{
                map.in("status", Arrays.stream(orderListQueryTermDTO.getStatus().split(",")).map(String::trim).collect(Collectors.toList()));
            }
        }

        if (ObjectUtil.isNotEmpty(orderListQueryTermDTO.getAccountBookId())){
            map.put("accountBookId",orderListQueryTermDTO.getAccountBookId());
        }

        if (AppUtils.isNotBlank(orderListQueryTermDTO.getDeliveryType()) ){
            map.put("deliveryType",orderListQueryTermDTO.getDeliveryType());
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getBuyerName())) {
            map.like("buyerName", orderListQueryTermDTO.getBuyerName(), MatchMode.ANYWHERE);
        }

        simpleSqlQuery.setSqlAndParameter("order.getOrderList", map);
        return querySimplePage(simpleSqlQuery);
    }


    @Override
    public PageSupport<SubV2ListDto> getThirdIndustrialSubInfo(OrderListQueryTermDTO orderListQueryTermDTO) {
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(SubV2ListDto.class, orderListQueryTermDTO.getPageSize(), orderListQueryTermDTO.getCurPage());
        QueryMap map = new QueryMap();
        map.put("userId", orderListQueryTermDTO.getUserId());
        map.put("subUserId", orderListQueryTermDTO.getSubUserId());
        map.put("payManner", orderListQueryTermDTO.getPayManner());
        map.put("audit", orderListQueryTermDTO.getAudit());
        if (AppUtils.isNotBlank(orderListQueryTermDTO.getSupplier())) {
            ThirdIndustrialEnum industrialEnum = ThirdIndustrialEnum.findByCode(orderListQueryTermDTO.getSupplier());
            Long shopId = null;
            if (AppUtils.isBlank(industrialEnum)) {
                IndustrialOuterShopConfig industrialOuterShopConfig = outerShopConfigDao.getByInnerCode((orderListQueryTermDTO.getSupplier()));
                if (AppUtils.isBlank(industrialOuterShopConfig)) {
                    throw new BusinessException("不存在的第三方供应商！");
                }
                shopId = industrialOuterShopConfig.getShopId();
            } else {
                shopId = industrialEnum.getShopId();
            }
            map.put("shopId", shopId);
        } else if (AppUtils.isNotBlank(orderListQueryTermDTO.getConsumerSupplier())) {
            ThirdConsumerEnum consumerEnum = ThirdConsumerEnum.findByCode(orderListQueryTermDTO.getConsumerSupplier());
            if (AppUtils.isBlank(consumerEnum)) {
                throw new BusinessException("不存在的第三方供应商！");
            }
            map.put("shopId", consumerEnum.getShopId());
        } else {
//			List<Long> shopIds = Arrays.stream(ThirdIndustrialEnum.values()).map(ThirdIndustrialEnum::getShopId).collect(Collectors.toList());
//			shopIds.add(ThirdConsumerEnum.JDVOP.getShopId());
//			List<IndustrialOuterShopConfig> industrialOuterShopConfigs = outerShopConfigDao.queryAllConfig();
//			List<Long> openPlatformShopIds = industrialOuterShopConfigs.stream().map(IndustrialOuterShopConfig::getShopId).collect(Collectors.toList());
//			shopIds.addAll(openPlatformShopIds);
//			map.in("shopIds", shopIds);

            map.put("industrialType", IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getFlag());

        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getSubNumber())) {
            map.like("subNum", orderListQueryTermDTO.getSubNumber(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getFrameContractNo())) {
            map.like("frameContractNo", orderListQueryTermDTO.getFrameContractNo(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getProductName())) {
            map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getMaterialCode())) {
            map.put("materialCode", orderListQueryTermDTO.getMaterialCode());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getThirdSkuId())) {
            map.put("thirdSkuId", orderListQueryTermDTO.getThirdSkuId());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getShopName())) {
            map.like("shopName", orderListQueryTermDTO.getShopName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getThirdSubNum())) {
            map.like("thirdSubNum", orderListQueryTermDTO.getThirdSubNum(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getContractNum())) {
            map.put("contractNum", orderListQueryTermDTO.getContractNum());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getOrderSource())) {
            map.put("orderSource", orderListQueryTermDTO.getOrderSource());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getDate())) {

            String[] split = orderListQueryTermDTO.getDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getFinallyDate())) {

            String[] split = orderListQueryTermDTO.getFinallyDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeFinallyDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterFinallyDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getBuyerName())) {
            map.like("buyerName", orderListQueryTermDTO.getBuyerName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getPhone())) {
            map.like("phone", orderListQueryTermDTO.getPhone(), MatchMode.ANYWHERE);
        }



        if (StrUtil.isNotBlank(orderListQueryTermDTO.getStatus())) {
            List<Integer> status = Arrays.stream(orderListQueryTermDTO.getStatus().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            map.in("status", status);
        }
        if (ObjectUtil.isNotEmpty(orderListQueryTermDTO.getAccountBookId())){
            map.put("accountBookId",orderListQueryTermDTO.getAccountBookId());
        }

        if (orderListQueryTermDTO.getShopId() != null){
            map.put("shopId",orderListQueryTermDTO.getShopId());
        }
        if (AppUtils.isNotBlank(orderListQueryTermDTO.getDeliveryType()) ){
            map.put("deliveryType",orderListQueryTermDTO.getDeliveryType());
        }

        if (AppUtils.isNotBlank(orderListQueryTermDTO.getEpId()) ){
            map.put("epId",orderListQueryTermDTO.getEpId());
        }

        if(!StringUtils.isBlank(orderListQueryTermDTO.getOrderType()) && SubTypeEnum.findByValue(orderListQueryTermDTO.getOrderType())!=null) {
            map.put("orderType",SubTypeEnum.findByValue(orderListQueryTermDTO.getOrderType()).value());
        }

        if(ObjectUtil.isNotEmpty(orderListQueryTermDTO.getShopIdList())) {
            if("INDUSTRIAL".equals(orderListQueryTermDTO.getOrderType())) {
                map.in("shopIds",orderListQueryTermDTO.getShopIdList());
            }
            if("TENDER".equals(orderListQueryTermDTO.getOrderType())) {
                map.in("noShopIds",orderListQueryTermDTO.getShopIdList());
                map.put("firstOrderSources", OrderSourceEnum.ERP.value());
                map.put("secondOrderSources", OrderSourceEnum.TENDER.value());
            }
        }
        simpleSqlQuery.setSqlAndParameter("order.getOrderList", map);
        return querySimplePage(simpleSqlQuery);

    }

    @Override
    public PageSupport<SubV2ListDto> getPageSubInfo(OrderListQueryTermDTO orderListQueryTermDTO) {
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(SubV2ListDto.class, orderListQueryTermDTO.getPageSize(), orderListQueryTermDTO.getCurPage());
        QueryMap map = new QueryMap();
        map.put("userId", orderListQueryTermDTO.getUserId());
        map.put("subUserId", orderListQueryTermDTO.getSubUserId());
        map.put("payManner", orderListQueryTermDTO.getPayManner());
        map.put("audit", orderListQueryTermDTO.getAudit());
        if (AppUtils.isNotBlank(orderListQueryTermDTO.getSupplier())) {
            ThirdIndustrialEnum industrialEnum = ThirdIndustrialEnum.findByCode(orderListQueryTermDTO.getSupplier());
            Long shopId = null;
            if (AppUtils.isBlank(industrialEnum)) {
                IndustrialOuterShopConfig industrialOuterShopConfig = outerShopConfigDao.getByInnerCode((orderListQueryTermDTO.getSupplier()));
                if (AppUtils.isBlank(industrialOuterShopConfig)) {
                    throw new BusinessException("不存在的第三方供应商！");
                }
                shopId = industrialOuterShopConfig.getShopId();
            } else {
                shopId = industrialEnum.getShopId();
            }
            map.put("shopId", shopId);
        } else if (AppUtils.isNotBlank(orderListQueryTermDTO.getConsumerSupplier())) {
            ThirdConsumerEnum consumerEnum = ThirdConsumerEnum.findByCode(orderListQueryTermDTO.getConsumerSupplier());
            if (AppUtils.isBlank(consumerEnum)) {
                throw new BusinessException("不存在的第三方供应商！");
            }
            map.put("shopId", consumerEnum.getShopId());
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getSubNumber())) {
            map.like("subNum", orderListQueryTermDTO.getSubNumber(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getProductName())) {
            map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getShopName())) {
            map.like("shopName", orderListQueryTermDTO.getShopName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getThirdSubNum())) {
            map.like("thirdSubNum", orderListQueryTermDTO.getThirdSubNum(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getContractNum())) {
            map.put("contractNum", orderListQueryTermDTO.getContractNum());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getOrderSource())) {
            map.put("orderSource", orderListQueryTermDTO.getOrderSource());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getDate())) {

            String[] split = orderListQueryTermDTO.getDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getFinallyDate())) {

            String[] split = orderListQueryTermDTO.getFinallyDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeFinallyDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterFinallyDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getBuyerName())) {
            map.like("buyerName", orderListQueryTermDTO.getBuyerName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getPhone())) {
            map.like("phone", orderListQueryTermDTO.getPhone(), MatchMode.ANYWHERE);
        }


        if (StrUtil.isNotBlank(orderListQueryTermDTO.getStatus())) {
            List<Integer> status = Arrays.stream(orderListQueryTermDTO.getStatus().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            map.in("status", status);
        }
        if (ObjectUtil.isNotEmpty(orderListQueryTermDTO.getAccountBookId())){
            map.put("accountBookId",orderListQueryTermDTO.getAccountBookId());
        }

        if (orderListQueryTermDTO.getShopId() != null){
            map.put("shopId",orderListQueryTermDTO.getShopId());
        }
        if (AppUtils.isNotBlank(orderListQueryTermDTO.getDeliveryType()) ){
            map.put("deliveryType",orderListQueryTermDTO.getDeliveryType());
        }
        simpleSqlQuery.setSqlAndParameter("order.getOrderList", map);
        return querySimplePage(simpleSqlQuery);

    }

    @Override
    public Sub getByShopIdBySubNumber(Long shopId, String subNumber) {
        if (StrUtil.isEmpty(subNumber)) {
            return null;
        }
        LambdaEntityCriterion<Sub> lambdaEntityCriterion = new LambdaEntityCriterion<>(Sub.class);
        lambdaEntityCriterion.eq(Sub::getSubNumber, subNumber);
        if (AppUtils.isNotBlank(shopId)) {
            lambdaEntityCriterion.eq(Sub::getShopId, shopId);
        }
        List<Sub> subList = queryByProperties(lambdaEntityCriterion);
        if (subList.size() > 0) {
            return subList.get(0);
        }
        return null;
    }

    @Override
    public ErpOrderDetailDTO getErpOrderDetail(String subNumber) {
        if (StrUtil.isEmpty(subNumber)) {
            return null;
        }
        QueryMap map = new QueryMap();
        map.put("subNumber", subNumber);
        SQLOperation operation = this.getSQLAndParams("order.getErpOrderDetail", map);
        return get(operation.getSql(), operation.getParams(), new BeanPropertyRowMapper<>(ErpOrderDetailDTO.class));
    }

    @Override
    public List<Sub> queryErrorStatusByOrderNo(List<String> orderNos) {
        StringBuffer sql = new StringBuffer("select s.* from (SELECT sub_number, sum( remain_count ) AS remain_count FROM ls_sub_item GROUP BY sub_number ) T1  INNER JOIN ls_sub s on T1.sub_number = s.sub_number " + "INNER JOIN (SELECT order_no, max( out_operator_time ) AS out_operator_time, min( status ) AS shipmentStatus FROM ls_shipment GROUP BY order_no) T2 on T1.sub_number = T2.order_no " + "WHERE T1.remain_count = 0 AND T2.shipmentStatus >= 40 AND s.sub_number in ( ");
        for (String orderNo : orderNos) {
            sql.append("?");
            sql.append(",");
        }
        sql.setLength(sql.length() - 1);
        sql.append(" )");
        return query(sql.toString(), Sub.class, orderNos.toArray());
    }

    @Override
    public List<Sub> querySubByShopIdAndDontHaveThirdNumber(Long shopId) {
        if (shopId == null) {
            return null;
        }
        QueryMap map = new QueryMap();
        map.put("shopId", shopId);
        SQLOperation operation = this.getSQLAndParams("order.querySubByshopIdAndThirdNumber", map);
        return query(operation.getSql(), Sub.class, operation.getParams());
    }

    @Override
    public void updateSubCancelReasonBySubNumber(String subNumber, String cancelReason) {
        String sql = "UPDATE ls_sub SET cancel_reason = ? WHERE sub_number = ?";
        update(sql, cancelReason, subNumber);
    }

    @Override
    public int updateSubInvoiceStatus(InvoiceSubDTO invoiceSubDTO) {
        LambdaUpdate<Sub> lambdaUpdate = new LambdaUpdate<>(Sub.class);
        lambdaUpdate.set(Sub::getHasInvoice, invoiceSubDTO.getHasInvoice());
        lambdaUpdate.in(Sub::getSubNumber, invoiceSubDTO.getSubNumberList());
        return updateProperties(lambdaUpdate);
    }

    @Override
    public List<SubItemInfoListVo> queryThirdIndustrialSubInfoList(OrderListQueryTermDTO orderListQueryTermDTO, Long shopId) {
        QueryMap map = new QueryMap();

        if (shopId == null) {
            return null;
        }
        map.put("shopId", shopId);
		map.put("deliveryType",orderListQueryTermDTO.getDeliveryType());



        if (StrUtil.isNotBlank(orderListQueryTermDTO.getSubNumber())) {
            map.like("subNum", orderListQueryTermDTO.getSubNumber(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getProductName())) {
            map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getThirdSubNum())) {
            map.like("thirdSubNum", orderListQueryTermDTO.getThirdSubNum(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getDate())) {

            String[] split = orderListQueryTermDTO.getDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getFinallyDate())) {

            String[] split = orderListQueryTermDTO.getFinallyDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeFinallyDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterFinallyDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getBuyerName())) {
            map.like("buyerName", orderListQueryTermDTO.getBuyerName(), MatchMode.ANYWHERE);
        }
//        if (StrUtil.isNotBlank(orderListQueryTermDTO.getPhone())) {
//            map.like("phone", orderListQueryTermDTO.getPhone(), MatchMode.ANYWHERE);
//        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getStatus())) {
            map.in("status", Arrays.stream(orderListQueryTermDTO.getStatus().split(",")).map(String::trim).collect(Collectors.toList()));
        }
        SQLOperation sqlAndParams = getSQLAndParams("order.exportOrderList2", map);
        return query(sqlAndParams.getSql(), SubItemInfoListVo.class, sqlAndParams.getParams());
    }

	@Override
	public List<SubV2ListExportDto> queryThirdIndustrialSubInfoList(OrderListQueryTermDTO orderListQueryTermDTO) {
		QueryMap map = new QueryMap();
        if(!Objects.isNull(orderListQueryTermDTO.getSupplier()) && AppUtils.isNotBlank(orderListQueryTermDTO.getSupplier())){
            map.put("shopId", outerShopConfigDao.getByInnerCode(orderListQueryTermDTO.getSupplier()).getShopId());
        }
		if (CollUtil.isNotEmpty(orderListQueryTermDTO.getShopIds())){
			map.put("shopIds", orderListQueryTermDTO.getShopIds());
		}

        if (AppUtils.isNotBlank(orderListQueryTermDTO.getDeliveryType()) ){
            map.put("deliveryType",orderListQueryTermDTO.getDeliveryType());
        }

        if (ObjectUtil.isNotEmpty(orderListQueryTermDTO.getAccountBookId())){
            map.put("accountBookId",orderListQueryTermDTO.getAccountBookId());
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getSubNumber())) {
			map.like("subNum", orderListQueryTermDTO.getSubNumber(), MatchMode.ANYWHERE);
		}
		if (StrUtil.isNotBlank(orderListQueryTermDTO.getProductName())) {
			map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
		}
		if (StrUtil.isNotBlank(orderListQueryTermDTO.getThirdSubNum())) {
			map.like("thirdSubNum", orderListQueryTermDTO.getThirdSubNum(), MatchMode.ANYWHERE);
		}
		if (StrUtil.isNotBlank(orderListQueryTermDTO.getDate())) {

			String[] split = orderListQueryTermDTO.getDate().split(StrUtil.COMMA);

			if (StrUtil.isNotEmpty(split[0])) {
				map.put("beforeDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
			}
			if (StrUtil.isNotEmpty(split[1])) {
				map.put("afterDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
			}
		}

		if (StrUtil.isNotBlank(orderListQueryTermDTO.getFinallyDate())) {

			String[] split = orderListQueryTermDTO.getFinallyDate().split(StrUtil.COMMA);

			if (StrUtil.isNotEmpty(split[0])) {
				map.put("beforeFinallyDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
			}
			if (StrUtil.isNotEmpty(split[1])) {
				map.put("afterFinallyDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
			}
		}

		if (StrUtil.isNotBlank(orderListQueryTermDTO.getBuyerName())) {
			map.like("buyerName", orderListQueryTermDTO.getBuyerName(), MatchMode.ANYWHERE);
		}
		if (StrUtil.isNotBlank(orderListQueryTermDTO.getPhone())) {
			map.like("phone", orderListQueryTermDTO.getPhone(), MatchMode.ANYWHERE);
		}

		if (StrUtil.isNotBlank(orderListQueryTermDTO.getStatus())) {
			map.in("status", Arrays.stream(orderListQueryTermDTO.getStatus().split(",")).map(String::trim).collect(Collectors.toList()));
		}

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getUserId())) {
            map.put("userId", orderListQueryTermDTO.getUserId());
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getMaterialCode())) {
            map.put("materialCode", orderListQueryTermDTO.getMaterialCode());
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getThirdSkuId())) {
            map.put("thirdSkuId", orderListQueryTermDTO.getThirdSkuId());
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getShopName())) {
            map.like("shopName", orderListQueryTermDTO.getShopName(), MatchMode.ANYWHERE);
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getContractNum())) {
            map.put("contractNum", orderListQueryTermDTO.getContractNum());
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getOrderSource())) {
            map.put("orderSource", orderListQueryTermDTO.getOrderSource());
        }

        if(!StringUtils.isBlank(orderListQueryTermDTO.getOrderType()) && SubTypeEnum.findByValue(orderListQueryTermDTO.getOrderType())!=null) {
            map.put("orderType",SubTypeEnum.findByValue(orderListQueryTermDTO.getOrderType()).value());
        }

		SQLOperation sqlAndParams = getSQLAndParams("order.exportOrderList", map);
		return query(sqlAndParams.getSql(), SubV2ListExportDto.class, sqlAndParams.getParams());
	}

	@Override
    public Sub getByShipmentId(Long shipmentId) {
        if (shipmentId == null) {
            return null;
        }
        String sql = "SELECT * FROM ls_sub s left join ls_shipment sh on sh.order_no = s.sub_number where sh.shipment_id = ? group by s.sub_id";
        return get(sql, Sub.class, shipmentId);
    }

    @Override
    public Sub getByThirdSubNumber(String thirdSubNumber) {

        if (thirdSubNumber == null) {
            return null;
        }
        QueryMap map = new QueryMap();
        map.put("thirdSubNumber", thirdSubNumber);
        SQLOperation operation = this.getSQLAndParams("order.getByThirdOrderNo", map);
        return get(operation.getSql(), Sub.class, operation.getParams());

    }

    @Override
    public List<SubItemV2ListDto> querySubItemV2ListDto(OrderListQueryTermDTO orderListQueryTermDTO) {
        QueryMap map = new QueryMap();
        map.put("userId", orderListQueryTermDTO.getUserId());
        map.put("subUserId", orderListQueryTermDTO.getSubUserId());
        map.put("payManner", orderListQueryTermDTO.getPayManner());
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getSubNumber())) {
            map.like("subNum", orderListQueryTermDTO.getSubNumber(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getProductName())) {
            map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getDate())) {
            String[] split = orderListQueryTermDTO.getDate().split(StrUtil.COMMA);
            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getStatus())) {
            map.in("status", Arrays.stream(orderListQueryTermDTO.getStatus().split(",")).map(String::trim).collect(Collectors.toList()));
        }
        SQLOperation sqlAndParams = getSQLAndParams("order.querySubItemV2List", map);
        return query(sqlAndParams.getSql(), SubItemV2ListDto.class, sqlAndParams.getParams());

    }

    @Override
    public PageSupport<SubV2ListDto> getCompanyThirdIndustrialSubInfo(OrderListQueryTermDTO orderListQueryTermDTO) {
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(SubV2ListDto.class, orderListQueryTermDTO.getPageSize(), orderListQueryTermDTO.getCurPage());
        QueryMap map = new QueryMap();
        map.put("payManner", orderListQueryTermDTO.getPayManner());
        map.put("audit", orderListQueryTermDTO.getAudit());
        if (AppUtils.isNotBlank(orderListQueryTermDTO.getSupplier())) {
            ThirdIndustrialEnum industrialEnum = ThirdIndustrialEnum.findByCode(orderListQueryTermDTO.getSupplier());
            Long shopId = null;
            if (AppUtils.isBlank(industrialEnum)) {
                IndustrialOuterShopConfig industrialOuterShopConfig = outerShopConfigDao.getByInnerCode((orderListQueryTermDTO.getSupplier()));
                if (AppUtils.isBlank(industrialOuterShopConfig)) {
                    throw new BusinessException("不存在的第三方供应商！");
                }
                shopId = industrialOuterShopConfig.getShopId();
            } else {
                shopId = industrialEnum.getShopId();
            }
            map.put("shopId", shopId);
        } else if (AppUtils.isNotBlank(orderListQueryTermDTO.getConsumerSupplier())) {
            ThirdConsumerEnum consumerEnum = ThirdConsumerEnum.findByCode(orderListQueryTermDTO.getConsumerSupplier());
            if (AppUtils.isBlank(consumerEnum)) {
                throw new BusinessException("不存在的第三方供应商！");
            }
            map.put("shopId", consumerEnum.getShopId());
        } else {
//			List<Long> shopIds = Arrays.stream(ThirdIndustrialEnum.values()).map(ThirdIndustrialEnum::getShopId).collect(Collectors.toList());
//			shopIds.add(ThirdConsumerEnum.JDVOP.getShopId());
//			List<IndustrialOuterShopConfig> industrialOuterShopConfigs = outerShopConfigDao.queryAllConfig();
//			List<Long> openPlatformShopIds = industrialOuterShopConfigs.stream().map(IndustrialOuterShopConfig::getShopId).collect(Collectors.toList());
//			shopIds.addAll(openPlatformShopIds);
//			map.in("shopIds", shopIds);

            map.put("industrialType", IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getFlag());

        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getSubNumber())) {
            map.like("subNum", orderListQueryTermDTO.getSubNumber(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getProductName())) {
            map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getShopName())) {
            map.like("shopName", orderListQueryTermDTO.getShopName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getThirdSubNum())) {
            map.like("thirdSubNum", orderListQueryTermDTO.getThirdSubNum(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getContractNum())) {
            map.put("contractNum", orderListQueryTermDTO.getContractNum());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getOrderSource())) {
            map.put("orderSource", orderListQueryTermDTO.getOrderSource());
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getDate())) {

            String[] split = orderListQueryTermDTO.getDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getFinallyDate())) {

            String[] split = orderListQueryTermDTO.getFinallyDate().split(StrUtil.COMMA);

            if (StrUtil.isNotEmpty(split[0])) {
                map.put("beforeFinallyDate", cn.hutool.core.date.DateUtil.parse(split[0], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
            if (StrUtil.isNotEmpty(split[1])) {
                map.put("afterFinallyDate", cn.hutool.core.date.DateUtil.parse(split[1], DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            }
        }

        if (StrUtil.isNotBlank(orderListQueryTermDTO.getBuyerName())) {
            map.like("buyerName", orderListQueryTermDTO.getBuyerName(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getPhone())) {
            map.like("phone", orderListQueryTermDTO.getPhone(), MatchMode.ANYWHERE);
        }
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getStatus())) {
            List<Integer> status = Arrays.stream(orderListQueryTermDTO.getStatus().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            map.in("status", status);
        }
        if (ObjectUtil.isNotEmpty(orderListQueryTermDTO.getAccountBookId())){
            map.put("accountBookId",orderListQueryTermDTO.getAccountBookId());
        }
        map.in("userIdList", orderListQueryTermDTO.getUserIds());
        if (StrUtil.isNotBlank(orderListQueryTermDTO.getAccountBookCompany())){
            map.put("accountBookCompany",orderListQueryTermDTO.getAccountBookCompany());
        }
        simpleSqlQuery.setSqlAndParameter("order.getCompanyOrderList", map);
        return querySimplePage(simpleSqlQuery);
    }

    @Override
    public List<SubV2ListDto> queryCompanyByOrderNos(List<String> orderNos) {
        QueryMap map = new QueryMap();
        map.in("orderNos",orderNos);
        SQLOperation sqlAndParams = getSQLAndParams("order.queryCompanyByOrderNos", map);
        return query(sqlAndParams.getSql(), SubV2ListDto.class, sqlAndParams.getParams());
    }

    @Override
    public List<Sub> pageQueryByOrderSource(Integer orderSource, Integer orderStatus) {
        LambdaEntityCriterion<Sub> lambdaCriteriaQuery = new LambdaEntityCriterion<>(Sub.class);
        if (AppUtils.isNotBlank(orderSource)){
            lambdaCriteriaQuery.eq(Sub::getOrderSource, orderSource);
        }

        if (AppUtils.isNotBlank(orderStatus)){
            lambdaCriteriaQuery.eq(Sub::getStatus, orderStatus);
        }

        return queryByProperties(lambdaCriteriaQuery);
    }

    @Override
    public List<Sub> SmartLockerBill(SmartLockerBillDTO smartLockerBillDTO) {
        QueryMap map = new QueryMap();
        map.put("deliveryType",smartLockerBillDTO.getDeliveryType());
//        map.put("status","30");
        map.put("startDate",smartLockerBillDTO.getBeginDate());
        map.put("endDate",smartLockerBillDTO.getEndDate());
        SQLOperation sqlAndParams = getSQLAndParams("order.SmartLockerBill", map);
        return query(sqlAndParams.getSql(), Sub.class, sqlAndParams.getParams());

    }

    @Override
    public String getAddressBySubNumber(String subNumber) {
        String sql = "SELECT uas.detail_address FROM `ls_sub`  s left join ls_usr_addr_sub uas on  s.addr_order_id  = uas.addr_order_id where s.sub_number= ? ";
       return get(sql,String.class,subNumber);
    }

    @Override
    public List<SubItemDetailVos> getSubItemDetailBySubNumber(String subNumber) {
        String sql = "select cash as price, sub_item_id as id,sku_id as skuId, pic ,prod_name as skuName ,actual_count as allCount ,remain_count as unShipmentCount from ls_sub_item where sub_number = ?";
        return query(sql,SubItemDetailVos.class,subNumber);
    }

    @Override
    public PageSupport<Sub> pageQueryByAmountType(PageParams pageParams, Integer amountType, Integer orderStatus) {
        LambdaCriteriaQuery<Sub> lambdaCriteriaQuery = new LambdaCriteriaQuery<>(Sub.class,pageParams.getPageSize(), pageParams.getCurPage());
        if (AppUtils.isNotBlank(amountType)){
            lambdaCriteriaQuery.eq(Sub::getAmountType, amountType);
        }

        if (AppUtils.isNotBlank(orderStatus)){
            lambdaCriteriaQuery.eq(Sub::getStatus, orderStatus);
        }

        return queryDTOPage(lambdaCriteriaQuery);
    }

    @Override
    public Integer finshContractAndChangeStatus(String subNumber,Integer status) {
        String sql = "update ls_sub s set s.status = ? where s.sub_number = ? and s.status = ?";
        return update(sql,status,subNumber,OrderStatusEnum.TO_BE_AUDITED.value());
    }

    @Override
    public PageSupport<OpenTenderBillVo> queryPayableSubList(OpenTenderBillRequest billRequest) {
        SimpleSqlQuery simpleSqlQuery = new SimpleSqlQuery(OpenTenderBillVo.class, billRequest.getPageSize(), billRequest.getCurPage());
        QueryMap map = new QueryMap();
        List<Integer> status = new ArrayList<>();
        status.add(OrderStatusEnum.CLEAN_PAYMENT.value());
        status.add(OrderStatusEnum.SUCCESS.value());
        map.put("uniformNo",billRequest.getUniformNo());
        map.put("status", status);
        map.put("startDate",billRequest.getBeginDate());
        map.put("endDate",billRequest.getEndDate());
        simpleSqlQuery.setSqlAndParameter("order.queryPayableSubList", map);
        return querySimplePage(simpleSqlQuery);
    }

    @Override
    public Integer cleanSmartBillSn(String reconciliationNo) {
        String sql = "update ls_sub s set s.smart_bill_sn = null where s.smart_bill_sn = ? ";
        return update(sql,reconciliationNo);
    }

    @Override
    public Sub findOldNotConfirmOrder(String contractNo) {
        String sql = "select s.* from ls_sub s where s.wait_erp_comfirm is null and s.status = 1 and s.order_source = 2 and s.contract_num = ?";
        return get(sql,Sub.class,contractNo);
    }

    @Override
    public void updateSubServiceCharge(List<String> subNumbers) {
        String sql = "update ls_sub s set s.service_status = 60 where s.sub_number in  ('";
        for (int i = 0; i < subNumbers.size(); i++) {
            sql += (subNumbers.get(i)+"','") ;
        }
        sql = sql.substring(0,sql.length()-2);
        sql+=")";
        update(sql);
    }

    @Override
    public void updateServiceStatus(String reconciliationNo, List<String> orderNos) {
        if (orderNos == null || orderNos.isEmpty()) {
            return;
        }
        StringBuilder sql = new StringBuilder("update ls_sub s set s.service_status = ? where s.sub_number in (");
        for (int i = 0; i < orderNos.size(); i++) {
            if (i > 0) {
                sql.append(",");
            }
            sql.append("?");
        }
        sql.append(")");
        
        List<Object> params = new ArrayList<>();
        params.add(reconciliationNo);
        params.addAll(orderNos);
        
        update(sql.toString(), params.toArray());
    }

    @Override
    public void resetServiceStatus(String reconciliationNo) {
        LambdaUpdate<Sub> lambdaUpdate = new LambdaUpdate<>(Sub.class);
        lambdaUpdate.set(Sub::getServiceStatus, "0");
        lambdaUpdate.eq(Sub::getServiceStatus, reconciliationNo);
        updateProperties(lambdaUpdate);
    }
}
