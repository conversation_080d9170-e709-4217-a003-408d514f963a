/*
 *
 * LegendShop 多用户商城系统
 *
 *  版权所有,并保留所有权利。
 *
 */
package cn.legendshop.order.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.SqlUtil;
import cn.legendshop.order.dao.SubItemDao;
import cn.legendshop.order.dto.OrderListQueryTermDTO;
import cn.legendshop.order.dto.ShipmentItemQueryParamDTO;
import cn.legendshop.order.dto.SubInfoDTO;
import cn.legendshop.order.vo.SubProductCommVo;
import cn.legendshop.order.vo.PurchasedProdVo;
import cn.legendshop.order.vo.SubProductInfoVo;
import com.legendshop.dao.SQLOperation;
import com.legendshop.dao.criterion.MatchMode;
import com.legendshop.dao.impl.GenericDaoImpl;
import com.legendshop.dao.sql.ConfigCode;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.QueryMap;
import com.legendshop.dao.support.SimpleSqlQuery;
import com.legendshop.dao.support.lambda.LambdaEntityCriterion;
import com.legendshop.dao.support.update.LambdaUpdate;
import com.legendshop.model.constant.OrderStatusEnum;
import com.legendshop.model.dto.SubItemDTO;
import com.legendshop.model.entity.SubItem;
import com.legendshop.util.AppUtils;
import com.legendshop.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 订单项服务
 */
@Repository("subItemDao")
public class SubItemDaoImpl extends GenericDaoImpl<SubItem, Long> implements SubItemDao {

	//获取分销的订单详情，用于分佣金结算
	private static String getDistSubItemListSql = "SELECT si.sub_item_id AS subItemId,si.sub_item_number as sub_item_number,si.sub_number AS subNumber,si.prod_id AS prodId,si.sku_id AS skuId,si.refund_id as refundId,si.refund_state as refundState,si.refund_amount as refundAmount,si.refund_type as refundType,si.refund_count as refundCount," +
		" si.dist_user_name AS distUserName,si.dist_commis_cash AS distCommisCash,si.dist_second_name AS distSecondName, " +
		" si.dist_second_commis AS distSecondCommis,si.dist_third_name AS distThirdName,si.dist_third_commis AS distThirdCommis FROM ls_sub_item si,ls_sub s WHERE si.sub_number =s.sub_number AND si.has_dist = 1  AND si.commis_settle_sts = 0 AND s.refund_state != 2 AND s.refund_state!=1  " +
		" AND s.status=? AND si.sub_item_id>=? AND si.sub_item_id<? AND s.finally_date< ? ";

	//获取分销的订单详情，用于分佣金结算（过了退货时间才能结算）
	private static String getDistSubItemListAfterReturnSql = "SELECT si.sub_item_id AS subItemId,si.sub_item_number as sub_item_number,si.sub_number AS subNumber,si.prod_id AS prodId,si.sku_id AS skuId,si.refund_id as refundId,si.refund_state as refundState,si.refund_amount as refundAmount,si.refund_type as refundType,si.refund_count as refundCount," +
		" si.dist_user_name AS distUserName,si.dist_commis_cash AS distCommisCash,si.dist_second_name AS distSecondName,si.dist_second_commis AS distSecondCommis,si.dist_third_name AS distThirdName,si.dist_third_commis AS distThirdCommis,c.return_valid_period AS returnValidPeriod " +
		" FROM ls_sub_item si,ls_sub s,ls_prod p,ls_category c WHERE si.sub_number =s.sub_number AND si.prod_id = p.prod_id AND c.id = p.category_id AND si.has_dist = 1  AND si.commis_settle_sts = 0 AND s.refund_state != 2 AND s.refund_state!=1 " +
		" AND s.status=? AND si.sub_item_id>=? AND si.sub_item_id<? AND NOW() > DATE_ADD(s.finally_date, INTERVAL IF(c.return_valid_period > 0,c.return_valid_period,?) DAY) ";

	//获取第一个分销的订单号，尚未结算的，用于分佣金结算
	private static String getFirstDistSubItemId = "select min(si.sub_item_id) from ls_sub_item si, ls_sub s where si.sub_number =s.sub_number AND si.has_dist = 1  AND si.commis_settle_sts = 0 AND s.refund_state != 2 AND s.refund_state!=1 " +
		" and s.status=? AND s.finally_date< ? ";

	//获取最后一个分销的订单号，尚未结算的，用于分佣金结算
	private static String getLastDistSubItemId = "select max(si.sub_item_id) from ls_sub_item si, ls_sub s where si.sub_number =s.sub_number AND si.has_dist = 1  AND si.commis_settle_sts = 0 AND s.refund_state != 2 AND s.refund_state!=1 " +
		" and s.status=? AND s.finally_date< ? ";

	//查询当前期 所有的分销单，用于商家结算.    refund_state: 退换货状态  0:默认,1:在处理,2:处理完成
	private static String getShopDistSubItemListSql = "SELECT si.sub_item_id AS subItemId,si.sub_item_number as sub_item_number,si.sub_number AS subNumber,si.prod_id AS prodId,si.sku_id AS skuId,si.refund_id as refundId,si.refund_state as refundState,si.refund_amount as refundAmount,si.refund_type as refundType,si.refund_count as refundCount," +
		"si.dist_user_name AS distUserName,si.dist_user_commis AS distUserCommis,si.dist_commis_cash AS distCommisCash,si.dist_second_name AS distSecondName," +
		"si.dist_second_commis AS distSecondCommis,si.dist_third_name AS distThirdName,si.dist_third_commis AS distThirdCommis FROM ls_sub_item si,ls_sub s WHERE si.sub_number =s.sub_number AND s.is_bill = 0 AND si.has_dist = 1 AND s.refund_state!=2 AND s.refund_state!=1  " +
		" AND s.status=? AND s.shop_id=? AND s.finally_date<? ";

	//查询获取账单的所有订单详情.
	private static String getSubItemSql = "select si.sub_item_id as subItemId, si.sub_number as subNumber, si.sub_item_number as subItemNumber, si.prod_id as prodId, si.sku_id as skuId,si.third_sku_id as thirdSkuId ,si.snapshot_id as snapshotId , si.basket_count as basketCount , si.actual_count as actualCount , si.prod_name as prodName , " +
			"si.unit as unit ,si.attribute as attribute ,si.pic as pic ,si.price as price ,si.cash as cash ,si.user_id as userId ,si.item_status as itemStatus , si.product_total_amount as productTotalAmount ,si.obtain_integral as obtainIntegral ,si.sub_item_date as subItemDate ,si.weight as weight , si.volume as volume , si.dist_commis_cash as distCommisCash , " +
			"si.dist_user_name as distUserName , si.dist_second_name as distSecondName, si.dist_third_name as distThirdName,  si.dist_user_commis as distUserCommis, si.dist_second_commis as distSecondCommis,  si.dist_third_commis as distThirdCommis,  si.promotion_info as promotionInfo,  si.has_dist as hasDist," +
			"si.commis_settle_sts as commisSettleSts ,si.out_count as outCount , si.remain_count as remainCount ,si.in_count as inCount , si.third_number as thirdNumber , si.material_code as materialCode,si.material_unit as materialUnit , si.material_info as materialInfo , si.refund_count as refundCount,si.cancel_reason as cancelReason,si.cancel_date as cancelDate from ls_sub_item si  where 1=1 AND si.sub_number = ?  group  by  si.sub_item_id "
			;

	//查询当前已完成，未评论的订单项
	private static String getNoCommonSubItemListSql = "select si.* from ls_sub s left join ls_sub_item si on s.sub_number = si.sub_number where s.status = 4 and si.comm_sts = 0 and finally_date <= ?";

	public List<SubItem> getSubItem(String subNumber, String userId) {
		return this.queryByProperties(new EntityCriterion().eq("subNumber", subNumber).eq("userId", userId));
	}

	public SubItem getSubItem(Long id) {
		return getById(id);
	}

	public int deleteSubItem(SubItem subItem) {
		return delete(subItem);
	}

	public Long saveSubItem(SubItem subItem) {
		return save(subItem);
	}

	public int updateSubItem(SubItem subItem) {
		return update(subItem);
	}

	@Override
	public List<Long> saveSubItem(List<SubItem> subItems) {
		return this.save(subItems);
	}

	@Override
	public List<Long> saveSubItemWithId(List<SubItem> subItems) {
		return this.saveWithId(subItems);
	}

	@Override
	public List<SubItem> getSubItem(String subNumber) {
		return this.queryByProperties(new EntityCriterion().eq("subNumber", subNumber));
	}

	@Override
	public List<SubItem> getSubItemWithRefundCount(String subNumber) {
		return this.query(getSubItemSql,SubItem.class, subNumber);
	}

	@Override
	public void updateSnapshotIdById(Long snapshotId, Long subItemId) {
		this.update("update ls_sub_item set snapshot_id = ? where sub_item_id = ?", snapshotId, subItemId);
	}

	@Override
	public Long getFirstDistSubItemId(Date date) {
		return this.get(getFirstDistSubItemId, Long.class, OrderStatusEnum.SUCCESS.value(), date);
	}

	@Override
	public Long getLastDistSubItemId(Date date) {
		return this.get(getLastDistSubItemId, Long.class, OrderStatusEnum.SUCCESS.value(), date);
	}

	/**
	 * 获取分销的订单详情
	 */
	@Override
	public List<SubItem> getDistSubItemList(Long firstSubItemId, Long toSubItemId, Date date) {
		return this.query(getDistSubItemListSql, SubItem.class, OrderStatusEnum.SUCCESS.value(), firstSubItemId, toSubItemId, date);
	}


	/**
	 * 查询当前期 所有的分销单
	 */
	@Override
	public List<SubItem> getShopDistSubItemList(Long shopId, Date endDate) {
		return this.query(getShopDistSubItemListSql, SubItem.class, OrderStatusEnum.SUCCESS.value(), shopId, endDate);
	}

	@Override
	public void updateSubItemCommSts(Long subItemId) {
		this.update("update ls_sub_item set comm_sts=1 where sub_item_id=?", subItemId);
	}

	@Override
	public int updateSubItemCommSts(Long subItemId, String userId) {
		return this.update("update ls_sub_item set comm_sts = 1 where sub_item_id = ? AND user_id = ?", subItemId, userId);
	}

	@Override
	public Integer getUnCommProdCount(String userId) {
		return this.get("select count(i.sub_item_id) from ls_sub_item i, ls_sub s where i.sub_number=s.sub_number and s.status=? and  i.comm_sts=0 and i.user_id=?", Integer.class, OrderStatusEnum.SUCCESS.value(), userId);
	}

	@Override
	public void updateRefundState(Long subItemId, Integer refundState) {
		this.update("UPDATE ls_sub_item SET refund_state = ? WHERE sub_item_id = ?", refundState, subItemId);
	}

	@Override
	public Long queryRefundItemCount(String subNumber, Integer refundState) {
		String sql = "SELECT COUNT(sub_item_id) FROM ls_sub_item WHERE sub_number = ? AND  refund_state = ? ";
		return this.getLongResult(sql, subNumber, refundState);
	}

	@Override
	public boolean existNoRefunditem(String subNumber) {
		String sql = "SELECT COUNT(sub_item_id) FROM ls_sub_item WHERE sub_number = ? AND  ( refund_state = 0 or refund_state=-1)";
		return this.getLongResult(sql, subNumber) > 0;
	}

	@Override
	public void updateRefundInfoBySubId(String subNumber, Long refundId) {
		//退款金额原封不动的set到订单项的退款金额
		String sql = "UPDATE ls_sub_item SET refund_id = ?, refund_state = 1, refund_type = 1, refund_amount = actual_amount WHERE sub_number = ?";
		this.update(sql, refundId, subNumber);
	}

	@Override
	public void updateRefundStateBySubNumber(String subNumber, Integer refundState) {
		String sql = "UPDATE ls_sub_item SET refund_state = ? WHERE sub_number = ?";
		this.update(sql, refundState, subNumber);
	}


	private static String getSubItemBySubIdSql = "SELECT si.*,s.freight_amount as freightAmount FROM ls_sub_item si, ls_sub s WHERE si.sub_number =s.sub_number AND s.sub_id=? ";

	@Override
	public List<SubItem> getSubItemBySubId(Long subId) {
		return this.query(getSubItemBySubIdSql, SubItem.class, subId);
	}

	private static String getSubItemByParam = "SELECT si.*,s.freight_amount as freightAmount FROM ls_sub_item si, ls_sub s WHERE si.sub_number =s.sub_number AND s.sub_id=? ";

	@Override
	public List<SubItem> getSubItemByParam(ShipmentItemQueryParamDTO param) {
		StringBuilder sqlsb = new StringBuilder(getSubItemByParam);
		List<Object> args = new ArrayList<>();
		args.add(param.getSubId());
		if (null != param.getShopId()) {
			sqlsb.append(" AND ").append("s.shop_id=?");
			args.add(param.getShopId());
		}
		if (StringUtil.isNotBlank(param.getBuyerId())) {
			sqlsb.append(" AND ").append("s.user_id=?");
			args.add(param.getBuyerId());
		}
		if (StringUtil.isNotBlank(param.getProdName())) {
			sqlsb.append(" AND ").append("si.prod_name LIKE ?");
			args.add('%' + param.getProdName() + '%');
		}
		return this.query(sqlsb.toString(), SubItem.class, args.toArray());
	}

	private static String getContractNumByParam = "SELECT csi.contract_number FROM ls_contract_sub_information csi where 1=1 and sub_number  = ? limit 1";

	@Override
	public String getContractNumByParam(String subNumber) {
		return this.get(getContractNumByParam, String.class, subNumber);
	}

	@Override
	public void updateCommissionSettleStatus(Long subItemId, int status) {
		this.update("update ls_sub_item set commis_settle_sts = ? where sub_item_id = ?", status, subItemId);
	}

	@Override
	public void updateProdSnapshot(Long subItemId, Long prodSnapshotId) {
		LambdaUpdate<SubItem> lambdaUpdate = new LambdaUpdate<>(SubItem.class);
		lambdaUpdate.eq(SubItem::getSubItemId,subItemId);
		lambdaUpdate.set(SubItem::getSnapshotId,prodSnapshotId);
		this.update("update ls_sub_item set snapshot_id = ? where sub_item_id = ?", prodSnapshotId, subItemId);
	}

	@Override
	public PageSupport<SubItem> querySubItems(String curPageNO, Long shopId, String distUserName, Integer status,
											  String subItemNum, Date fromDate, Date toDate) {
		SimpleSqlQuery query = new SimpleSqlQuery(SubItem.class, 10, curPageNO);
		QueryMap map = new QueryMap();
		map.put("shopId", shopId);
		if (AppUtils.isNotBlank(distUserName)) {
			map.like("distUserName", distUserName.trim());
		}
		map.put("status", status);
		if (AppUtils.isNotBlank(subItemNum)) {
			map.like("subItemNum", subItemNum.trim());
		}
		map.put("startDate", fromDate);
		map.put("endDate", toDate);
		String queryAllSQL = ConfigCode.getInstance().getCode("shop.getDistSubItemsCount", map);
		String querySQL = ConfigCode.getInstance().getCode("shop.getDistSubItems", map);
		query.setAllCountString(queryAllSQL);
		query.setQueryString(querySQL);
		query.setParam(map.toArray());
		return querySimplePage(query);
	}

	@Override
	public PageSupport<SubItem> querySubItems(String curPageNO, String userName, Integer status, String subItemNum, Date fromDate, Date toDate) {
		SimpleSqlQuery query = new SimpleSqlQuery(SubItem.class, 10, curPageNO);
		QueryMap map = new QueryMap();
		map.put("distUserName", userName);
		map.put("status", status);
		map.put("subItemNum", subItemNum);
		map.put("startDate", fromDate);
		map.put("endDate", toDate);
		String queryAllSQL = ConfigCode.getInstance().getCode("uc.order.getDistSubItemsCount", map);
		String querySQL = ConfigCode.getInstance().getCode("uc.order.getDistSubItems", map);
		query.setAllCountString(queryAllSQL);
		query.setQueryString(querySQL);
		query.setParam(map.toArray());
		return querySimplePage(query);
	}

	@Override
	public List<SubItem> getDistSubAfterReturnItemList(Long firstSubItemId, Long toSubItemId, String autoProductReturn) {
		return this.query(getDistSubItemListAfterReturnSql, SubItem.class, OrderStatusEnum.SUCCESS.value(), firstSubItemId, toSubItemId, autoProductReturn);
	}

	@Override
	public List<SubItem> getDistSubItemBySubNumber(String subNumber) {
		return this.queryByProperties(new EntityCriterion().eq("subNumber", subNumber).eq("hasDist", 1));
	}

	@Override
	public List<SubItem> getNoCommonSubItemList(Date time) {
		return this.query(getNoCommonSubItemListSql, SubItem.class, time);
	}

	@Override
	public void batchUpdate(List<SubItem> updateSubItemList) {
		update(updateSubItemList);
	}

	@Override
	public SubItem getSubItemBySubNumberAndSkuId(String subNumber, Long skuId) {
		return this.getByProperties(new EntityCriterion().eq("subNumber", subNumber).eq("skuId", skuId));
	}

	@Override
	public SubItem getSubItemBySubNumberAndSkuId(String subNumber) {
		return this.getByProperties(new EntityCriterion().eq("subNumber", subNumber));
	}

	@Override
	public List<SubItem> querySubItemBySubNumber(List<String> subNumbers) {
		return CollUtil.isEmpty(subNumbers) ? Collections.emptyList() : this.queryByProperties(new LambdaEntityCriterion<>(SubItem.class).in(SubItem::getSubNumber, subNumbers));
	}

	@Override
	public List<SubItem> querySubItemBySubNumber(String subNumber) {
		return this.queryByProperties(new LambdaEntityCriterion<>(SubItem.class).eq(SubItem::getSubNumber, subNumber));
	}

	@Override
	public Boolean updateThirdIndustrialNumber(String thirdNumber, String oyeRowCode, String subItemNumber) {
		return SqlUtil.retBool(this.updateProperties(new LambdaUpdate<>(SubItem.class).set(SubItem::getThirdNumber, thirdNumber).set(SubItem::getThirdItemNumber, oyeRowCode).eq(SubItem::getSubItemNumber, subItemNumber)));
	}

	@Override
	public Boolean updateThirdIndustrialNumber(String oyeOrderCode, String subNumber) {
		return SqlUtil.retBool(this.updateProperties(new LambdaUpdate<>(SubItem.class).set(SubItem::getThirdNumber, oyeOrderCode).eq(SubItem::getSubNumber, subNumber)));
	}

	@Override
	public List<SubItem> queryBySubNumberAndMaterialCodeList(String orderNumber, Set<String> materialCodes) {
//		QueryMap queryMap = new QueryMap();
//		queryMap.put("subNumber",orderNumber);
//		queryMap.in("materialCode",keySet);
//		SQLOperation sqlAndParams = getSQLAndParams("order.querySubItemInfo", queryMap);
//		return query(sqlAndParams.getSql(),SubItem.class,sqlAndParams.getParams());
		if (StrUtil.isEmpty(orderNumber) && CollUtil.isEmpty(materialCodes)) {
			return Collections.emptyList();
		}
		return queryByProperties(new LambdaEntityCriterion<>(SubItem.class).eq(SubItem::getSubNumber, orderNumber).in(SubItem::getMaterialCode, materialCodes));
	}

	@Override
	public SubItem getBySubItemNumber(String subItemNumber) {
		if (StrUtil.isEmpty(subItemNumber)) {
			return null;
		}
		return getByProperties(new LambdaEntityCriterion<>(SubItem.class).eq(SubItem::getSubItemNumber, subItemNumber));
	}

	@Override
	public SubItem getByThirdItemNumber(String thirdItemNumber) {
		if (StrUtil.isEmpty(thirdItemNumber)) {
			return null;
		}
		return getByProperties(new LambdaEntityCriterion<>(SubItem.class).eq(SubItem::getThirdItemNumber, thirdItemNumber));
	}

	@Override
	public List<SubItem> queryByThirdNumber(String thirdNumber) {
		return StrUtil.isEmpty(thirdNumber) ? Collections.emptyList() : queryByProperties(new LambdaEntityCriterion<>(SubItem.class).eq(SubItem::getThirdNumber, thirdNumber));
	}

	@Override
	public List<SubItem> queryByLikeThirdNumber(String thirdNumber) {
		return StrUtil.isEmpty(thirdNumber) ? Collections.emptyList() : queryByProperties(new LambdaEntityCriterion<>(SubItem.class).like(SubItem::getThirdNumber, thirdNumber));
	}

	@Override
	public List<SubItem> queryByThirdNumber(List<String> thirdNumbers) {
		return CollUtil.isEmpty(thirdNumbers) ? Collections.emptyList() : queryByProperties(new LambdaEntityCriterion<>(SubItem.class).in(SubItem::getThirdNumber, thirdNumbers));
	}

	@Override
	public List<SubItemDTO> queryByThirdNumberAndOuterId(String thirdNumber) {
		String sql = "SELECT item.*,item.third_sku_id AS outer_id FROM ls_sub_item item WHERE item.third_number = ?";
		return query(sql, SubItemDTO.class, thirdNumber);
	}

	@Override
	public Boolean updateItemStatusBySubNums(List<String> orderNumbers) {
		StringBuffer sb = new StringBuffer("update ls_sub_item s set s.item_status=30 where (s.item_status is null or s.item_status <=30) and s.sub_number in(");
		for (String id : orderNumbers) {
			sb.append("?,");
		}
		sb.setLength(sb.length() - 1);
		sb.append(")");
		return SqlUtil.retBool(update(sb.toString(), orderNumbers.toArray()));
	}

	@Override
	public List<SubInfoDTO> getBySubNumber(String orderNum, OrderListQueryTermDTO orderListQueryTermDTO) {
		QueryMap map = new QueryMap();
		if (StrUtil.isNotEmpty(orderListQueryTermDTO.getProductName())) {
			map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
		}

		if (StrUtil.isNotEmpty(orderListQueryTermDTO.getThirdSubNum())) {
			map.like("thirdSubNum", orderListQueryTermDTO.getThirdSubNum(), MatchMode.ANYWHERE);
		}

		if (StrUtil.isEmpty(orderNum)) {
			throw new BusinessException("订单编号为空");
		}
		map.put("orderNo", orderNum);

		SQLOperation sqlAndParams = getSQLAndParams("order.getInfo", map);
		return query(sqlAndParams.getSql(), SubInfoDTO.class, sqlAndParams.getParams());
	}

	@Override
	public List<SubInfoDTO> queryBySubNumber(List<String> orderNum, OrderListQueryTermDTO orderListQueryTermDTO) {
		QueryMap map = new QueryMap();
		if (StrUtil.isNotEmpty(orderListQueryTermDTO.getProductName())) {
			map.like("productName", orderListQueryTermDTO.getProductName(), MatchMode.ANYWHERE);
		}

		if (StrUtil.isNotEmpty(orderListQueryTermDTO.getThirdSubNum())) {
			map.like("thirdSubNum", orderListQueryTermDTO.getThirdSubNum(), MatchMode.ANYWHERE);
		}

		if (StrUtil.isNotEmpty(orderListQueryTermDTO.getMaterialCode())) {
			map.like("materialCode", orderListQueryTermDTO.getMaterialCode(), MatchMode.ANYWHERE);
		}

		if (CollUtil.isEmpty(orderNum)) {
			throw new BusinessException("订单编号为空");
		}

		map.put("orderNoList", orderNum);

		SQLOperation sqlAndParams = getSQLAndParams("order.getInfo", map);
		return query(sqlAndParams.getSql(), SubInfoDTO.class, sqlAndParams.getParams());
	}

	@Override
	public List<SubInfoDTO> getBySubNumber(String orderNum) {
		QueryMap map = new QueryMap();
		if (StrUtil.isEmpty(orderNum)) {
			throw new BusinessException("订单编号为空");
		}
		map.put("orderNo", orderNum);

		SQLOperation sqlAndParams = getSQLAndParams("order.getInfo", map);
		return query(sqlAndParams.getSql(), SubInfoDTO.class, sqlAndParams.getParams());
	}

	@Override
	public List<SubItem> queryBySubNumber(String orderNumber) {
		return StrUtil.isEmpty(orderNumber) ? Collections.emptyList() : queryByProperties(new LambdaEntityCriterion<>(SubItem.class).in(SubItem::getSubNumber, orderNumber));
	}

	@Override
	public List<PurchasedProdVo> listPurchasedProdByMaterialCode(String materialCode, Long accountBookId) {
		QueryMap map = new QueryMap();
		map.put("materialCode", materialCode);
		map.put("accountBookId", accountBookId);
		SQLOperation sqlAndParams = getSQLAndParams("order.listPurchasedProdByMaterialCode", map);
		return query(sqlAndParams.getSql(), PurchasedProdVo.class, sqlAndParams.getParams());
	}

	@Override
	public List<PurchasedProdVo> listPurchasedProdByThirdSkuId(String thirdSkuId, Long accountBookId) {
		QueryMap map = new QueryMap();
		map.put("thirdSkuId", thirdSkuId);
		map.put("accountBookId", accountBookId);
		SQLOperation sqlAndParams = getSQLAndParams("order.listPurchasedProdByThirdSkuId", map);
		return query(sqlAndParams.getSql(), PurchasedProdVo.class, sqlAndParams.getParams());
	}

	@Override
	public List<SubProductCommVo> querySubItemComm(List<Long> subItemIds) {
		String sql = "select lpc.sub_item_id as subItemId,lpc.id as prodCommId,lpac.id as prodAddCommId from ls_prod_comm lpc left join ls_prod_add_comm lpac on lpc.id = lpac.prod_comm_id where 1=1";
		if (subItemIds != null && !subItemIds.isEmpty()) {
			StringBuilder sb = new StringBuilder();
			for (Long subItemId : subItemIds) {
				sb.append(subItemId).append(StrUtil.COMMA);
			}
			sb.setLength(sb.length() - StrUtil.COMMA.length());
			sql += " and lpc.sub_item_id in (" + sb.toString() + ")";
		} else {
			return Collections.emptyList();
		}
		return query(sql,SubProductCommVo.class);
	}

	@Override
	public List<SubProductInfoVo> QuerySubItemOverdueDay(String subNumber) {
		String sql ="SELECT\n" +
				"    si.third_sku_id as thirdSkuId,\n" +
				"    TIMESTAMPDIFF(DAY, si.delivery_date, COALESCE(sh.in_complete_operator_time, NOW())) AS erpOverdueDay\n" +
				" from   ls_sub_item si " +
				"    left join ls_shipment_item shi on si.sub_item_id = shi.sub_item_id\n" +
				"    left join ls_shipment sh on shi.batch_no = sh.batch_no\n" +
				"where si.sub_number = ? ";
		return query(sql,SubProductInfoVo.class,subNumber);
	}
}
