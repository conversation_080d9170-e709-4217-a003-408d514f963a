package cn.legendshop.order.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 原本该DTO用于上传最终结果及上传OA审批一个动作，最后拆成两个动作，两边都会用
 */

@Data
public class EnquiryPriceResultSubmitDTO {


    @NotNull(message = "询价单ID不能为空")
    private Long epId;

    private String title;

    private Boolean isResubmit;

    private String content;
    /**
     * 用于上传最终结果文件
     */
    private MultipartFile file;

    /**
     * 用于上传OA审批文件
     */
//    private List<MultipartFile> files;
    private String userId;
    private String userName;
    private String remark;

    @NotNull(message = "询价单明细不能为空")
    private List<EvaluateItemDTO> itemList;

    @Data
    public static class EvaluateItemDTO {
        private Long epItemId;
        private Long quItemId;
    }
}
