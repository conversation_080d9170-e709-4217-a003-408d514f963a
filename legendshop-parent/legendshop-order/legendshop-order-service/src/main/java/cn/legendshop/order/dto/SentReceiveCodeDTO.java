package cn.legendshop.order.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class SentReceiveCodeDTO {
    //compId	合同公司别
    private String compId;
    //poNo	合同号
    private String poNo;
    //poItemNo	   合同项次
    private String poItemNo;
    //matrlno	料号
    private String matrlno;
    //receiveCode	取件码
    private String receiveCode;

    private String arrivalDate;

}
