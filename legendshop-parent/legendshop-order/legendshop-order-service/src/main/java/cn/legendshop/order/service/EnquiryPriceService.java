package cn.legendshop.order.service;

import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.order.bo.ConfirmOrderBO;
import cn.legendshop.order.dto.*;
import cn.legendshop.order.entity.EnquiryPriceAttachments;
import cn.legendshop.order.entity.Quotation;
import cn.legendshop.order.vo.*;
import com.legendshop.dao.support.PageParams;
import com.legendshop.dao.support.PageSupport;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

public interface EnquiryPriceService {
//    R<PageSupport<EnquiryPriceVo>> querAllEnquiryPrice(EnquiryPriceDTO enquiryPrice);

    Long saveEnquiryPrice(EnquiryPriceDTO EnquiryPrice);

    R updateEnquiryPrice(EnquiryPriceDTO EnquiryPrice);

    R confirmEnquiryPrice(EnquiryPriceDTO EnquiryPrice);

    R publishEnquiryPrice(EnquiryPriceDTO EnquiryPrice,BaseUserDetailDTO baseUserDetailDTO);

    R<EnquiryPriceDetailVo> getEnquiryPriceDetailByEpId(Long enquiryPriceId);

    R<PageSupport<EnquiryPriceVo>> queryEnquiryPriceList(EnquiryPriceDTO EnquiryPrice);

    R<EnquiryPriceShopNumVO> getEnquiryPriceShipNum(Long epId);

    R<List<Quotation>> getQuotationByEnquiryPriceId(Long enquiryPriceNo);

    R<EnquiryPriceDetailVo> enquiryPriceInit(String userId);

    R<String> cancelEnquiryPrice(Long enquiryPriceId,String userId,String userName);

    R<String> startEvaluate(Long enquiryPriceId,String userId,String userName);

    R<String> extendedEpTime(ExtendEpTimeDTO extendEpTimeDTO, String userId, String userName);

    R<PageSupport<EnquiryPriceShopVo>> queryEnquiryPriceListByShopId(EnquiryPriceShopDTO enquiryPriceShopDTO);

    R<ConfirmOrderBO> checkEnquiryPriceItem(EnquiryOrderDTO enquiryOrderDTO);

    Integer changeEnquiryPriceStatus(Long enquiryPriceId, Integer status,String userId,String userName,List<String> subNumber);

    R<String> export(HttpServletResponse response, ExportQuotationDTO exportQuotationDTO) throws IOException;

    R<List<EnquiryPriceAttachments>> queryEnquiryPriceAttachments(EPAttachmentsDTO epAttachmentsDTO);

    void updateStatusEnquiryPriceListDeadTime(Date date, Integer status);

    R<String> uploadEnquiryFile(EnquiryPriceAttachments enquiryPriceAttachments);

    R uploadResult(EnquiryPriceResultSubmitDTO evaluateResultDTO);

    R platformConfirmCancel(PlatformConfirmCancelDTO platformConfirmCancelDTO);

    R<EnquiryPricerResultsVo> enquiryPricerResults(Long epId);

    R<EnquiryPricerShopResultsVo> enquiryPricerShopResults(Long aLong, String shopId);

    R<String> exportFinalResultReport(ExportFinalResultReportDTO exportDTO);

    R refreshProductStatus(Long epId);

    R<String> adminCancelEnquiryPrice(EnquiryPriceCancelDTO enquiryPriceCancelDTO, String userId, String username);

    EnquiryPriceVo getEpVoById(Long epId);

    R<EnquiryOAReportVo> getAllReports(Long epId);

    void getHash(int i);

    R<String> preSelectSupplier(EvaluateResultDTO evaluateResultDTO);

    R createDealNotice(Long epId);

    R<String> retreatEnquiryPrice(Long epId, String userId, String username);
    
    /**
     * 删除草稿状态的询价单
     * @param deleteDTO 询价单ID列表
     * @param userId 用户ID
     * @param userName 用户名称
     * @return 删除结果
     */
    R<String> deleteDraftEnquiryPrice(EnquiryPriceDeleteDTO deleteDTO, String userId, String userName);
}
