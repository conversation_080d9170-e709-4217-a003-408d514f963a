package cn.legendshop.order.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.utils.CommonServiceUtil;
import cn.legendshop.order.dao.ReconciliationBillDao;
import cn.legendshop.order.dao.ReconciliationBillItemDao;
import cn.legendshop.order.dto.SharedInvoiceSubInfoDTO;
import cn.legendshop.order.dto.SharedJDVOPInvoiceSubInfoDTO;
import cn.legendshop.order.dto.UpdateReconciliationBillDTO;
import cn.legendshop.order.entity.InvoiceRequestApplicant;
import cn.legendshop.order.entity.ReconciliationBill;
import cn.legendshop.order.entity.ReconciliationBillItem;
import cn.legendshop.order.enums.InvoicingReqCustomerEnum;
import cn.legendshop.order.enums.ReconciliationTypeEnum;
import cn.legendshop.order.query.ReconciliationBillQuery;
import cn.legendshop.order.service.InvoicingRequestsService;
import cn.legendshop.order.strategy.finance.InvoicingRequestStrategy;
import cn.legendshop.order.strategy.finance.InvoicingRequestStrategyContext;
import cn.legendshop.order.strategy.finance.impl.AccountPeriodInvoicingRequestStrategy;
import cn.legendshop.order.util.SharedFinanceUtil;
import cn.legendshop.order.vo.BillItemVo;
import cn.legendshop.order.vo.BillProdVo;
import cn.legendshop.order.vo.JDVopProdVo;
import com.alibaba.excel.EasyExcel;
import com.legendshop.util.AppUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.legendshop.order.constant.InvoicingRequestConst.SEGMENT_SIZE;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class InvoicingRequestsServiceImpl implements InvoicingRequestsService {

    private final InvoicingRequestStrategyContext invoicingRequestStrategyContext;

    private final AccountPeriodInvoicingRequestStrategy accountPeriodInvoicingRequestStrategy;

    private final SharedFinanceUtil sharedFinanceUtil;

    private final ReconciliationBillDao reconciliationBillDao;

    private final ReconciliationBillItemDao reconciliationBillItemDao;


    @Override
    public HashMap<String, String> invoicingRequests(SharedInvoiceSubInfoDTO dto) {
        if (AppUtils.isBlank(dto) || AppUtils.isBlank(dto.getApplicationType())) {
            HashMap<String, String> returnMap = new HashMap<>();
            returnMap.put("error", "参数错误，请检查传参是否完整");
            return returnMap;
        }
        return invoicingRequestStrategyContext.getStrategy(dto.getApplicationType()).pushBill(dto);
    }

    @Override
    public HashMap<String, String> tempInvoicingRequests(Long requestApplicantId, List<Long> shipmentItemIds) {
        return accountPeriodInvoicingRequestStrategy.pushTempShipmentItem(requestApplicantId, shipmentItemIds);
    }

    @Override
    public Map<String, String> JDVopPushBillDisposable(SharedJDVOPInvoiceSubInfoDTO dto) {
        // 生成临时的账单
        ReconciliationBillQuery query  = new ReconciliationBillQuery();
        query.setReconciliationTime("********");
        ReconciliationBill bill = reconciliationBillDao.getBill(query);
        //如果不存在新增
        if (AppUtils.isBlank(bill)) {
            bill = new ReconciliationBill();
            bill.setBeginTime(new DateTime("2023-11-18 21:44:08"));
            bill.setEndTime(new DateTime("2025-06-10 09:41:14"));
            bill.setShopId(4000L);
            bill.setSupplierName("上海圆迈贸易有限公司");
            LocalDateTime localDate = LocalDateTime.now();
            bill.setReconciliationTime("********");
            bill.setReconciliationNo(CommonServiceUtil.getRandomSn());
            bill.setType(ReconciliationTypeEnum.VOP.getType());
        }else{
            //清除之前统计的 重新统计
            reconciliationBillItemDao.deleteItem(bill.getId());
        }
        Long billId = reconciliationBillDao.saveOrUpdate(bill);
        bill.setId(billId);
        // 1.解析excel
        List<JDVopProdVo> billProdVoList = getBillProdVoList(dto);
        BigDecimal billAmount = new BigDecimal(0);
        // 2.生成账单
        for (JDVopProdVo jdVopProdVo : billProdVoList) {
            ReconciliationBillItem reconciliationBillItem = new ReconciliationBillItem();
            reconciliationBillItem.setBillAmount(jdVopProdVo.getAmount());
            reconciliationBillItem.setOrderNo(jdVopProdVo.getOrderNo());
            reconciliationBillItem.setReconciliationId(billId);
            reconciliationBillItem.setIcardAmount(jdVopProdVo.getAmount());
            reconciliationBillItem.setInvoiceStatus(1);
            reconciliationBillItem.setThirdItemNo(jdVopProdVo.getSku());
            reconciliationBillItem.setShipmenItemBillNum(jdVopProdVo.getShipmentCount());
            reconciliationBillItemDao.save(reconciliationBillItem);
            billAmount = billAmount.add(jdVopProdVo.getAmount());
        }
        bill.setBillAmount(billAmount);
        reconciliationBillDao.update(bill);
        // 3.抛送财务共享
        HashMap<String, String> returnMap = new HashMap<>();
        StringBuilder errorMsg = new StringBuilder();

        InvoiceRequestApplicant invoiceRequestApplicant =new InvoiceRequestApplicant();
        invoiceRequestApplicant.setContractCode("PCL002025060002"); // 合同编号 1  PCL002024080003 阳采
        invoiceRequestApplicant.setContractName("京东实物销售协议"); // 合同名称 1  鑫采商城-阳采集团平台战略采购框架协议
        invoiceRequestApplicant.setAccountingSubjectCode("L00"); // 核算主体 1 L00
        invoiceRequestApplicant.setCustomerSubjectCode("SH002426"); // 供应商  SH002426
        invoiceRequestApplicant.setSaleSettlementType("DIM_EXP_KCSP_001"); // 货物类型  DIM_EXP_KCSP_001
        invoiceRequestApplicant.setUrl("http://fssc.nisco.cn/fssc/api/rest/jhxmysLedger.do");
        // 获取账单明细信息
        JSONObject supplierRequest = sharedFinanceUtil.createSupplierRequest(bill, invoiceRequestApplicant);
        // 开票申请单申请——调用南钢财务共享接口
        int successCount = 0;
        int failCount = 0;
        try {
            //调用南钢财务共享接口
            String response = sharedFinanceUtil.getDataFromGX(supplierRequest, invoiceRequestApplicant.getUrl());
            if (AppUtils.isBlank(response)) {
                throw new RuntimeException("共享平台返回数据异常，请联系管理员");
            } else {
                JSONObject jsonObj = JSONUtil.parseObj(response);
                Boolean status = jsonObj.getBool("status");
                if (!status) {
                    throw new RuntimeException("共享财务系统通信正常，生成结算单异常:" + jsonObj.getStr("msg"));
                }
                //记录开票信息
                errorMsg.append("成功");
                successCount++;
            }
        } catch (Exception e) {
            failCount++;
            errorMsg.append(e.getMessage());
            log.error("共享财务系统通信异常，抛送结算单异常: ", e);
        }
        log.info("财务共享结算单处理结果:" + "单据成功数：" + successCount + " 单据失败数：" + failCount + " 异常信息：" + errorMsg);
        returnMap.put("SUCCESS", String.valueOf(successCount));
        returnMap.put("FAIL", String.valueOf(failCount));
        returnMap.put("MSG", errorMsg.toString());
        return returnMap;


    }


    private List<JDVopProdVo> getBillProdVoList(SharedJDVOPInvoiceSubInfoDTO dto) {

        MultipartFile file = Optional.ofNullable(dto.getRequest()).map(f -> f.getFile("importFile")).orElse(null);
        // 读取本地文件测试用
//        File file = new File("C:\\Users\\<USER>\\Desktop\\金瀚环保3月开票.xlsx");


        // 判断文件类型
        assert file != null;
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw new BusinessException("导入列表请上传excel类型文件");
        }
        List<JDVopProdVo> readBillProdsList;
        try {
            readBillProdsList = EasyExcel.read(file.getInputStream()).head(JDVopProdVo.class).doReadAllSync();
        } catch (IOException e) {
            throw new BusinessException("读取excel文件失败");
        }


        return readBillProdsList;
    }
}
