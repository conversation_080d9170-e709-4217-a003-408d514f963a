package cn.legendshop.order.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.OpenPlatformEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.common.rabbitmq.constants.ThridIndustrialProductMQConstant;
import cn.legendshop.common.rabbitmq.utils.AmqpSendMsgUtil;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.invoice.client.InvoiceAddressServiceClient;
import cn.legendshop.invoice.client.InvoiceServiceClient;
import cn.legendshop.invoice.dto.InvoiceDTO;
import cn.legendshop.invoice.enums.InvoiceTypeEnum;
import cn.legendshop.invoice.model.InvoiceAddress;
import cn.legendshop.open.request.order.CancelPreOrderRequest;
import cn.legendshop.order.bo.ConfirmOrderBO;
import cn.legendshop.order.bo.ThirdIndustrialConfirmOrderBO;
import cn.legendshop.order.dao.SubDao;
import cn.legendshop.order.dao.SubItemDao;
import cn.legendshop.order.dto.SubmitOrderShopDTO;
import cn.legendshop.order.dto.UpdateOrderDTO;
import cn.legendshop.order.dto.orderManager.ConfirmOrderDTO;
import cn.legendshop.order.dto.orderManager.EngConDTO;
import cn.legendshop.order.dto.orderManager.InvoiceChangeDTO;
import cn.legendshop.order.dto.orderManager.SubmitOrderResultDTO;
import cn.legendshop.order.enums.MallOrderStrategyTypeEnum;
import cn.legendshop.order.event.SubmitOrderEventManger;
import cn.legendshop.order.event.listener.DelayCancelOfferListener;
import cn.legendshop.order.event.listener.ProdSnapshotListener;
import cn.legendshop.order.query.erp.ErpPreOrderParameterDTO;
import cn.legendshop.order.query.purchaser.ChangeOrderDeliveryDateParam;
import cn.legendshop.order.query.purchaser.ConfirmReceiptParam;
import cn.legendshop.order.query.purchaser.PurchaserPreOrderParamDTO;
import cn.legendshop.order.query.purchaser.ReviewSubItemHistDTO;
import cn.legendshop.order.query.supplier.ApplySubItemHistDTO;
import cn.legendshop.order.service.MallOrderProcessService;
import cn.legendshop.order.service.MallShipmentProcessService;
import cn.legendshop.order.service.MessagePoolOrderService;
import cn.legendshop.order.service.OrderCacheService;
import cn.legendshop.order.strategy.InternalSubmit.SubmitOrderStrategy;
import cn.legendshop.order.strategy.InternalSubmit.SubmitOrderStrategyContext;
import cn.legendshop.order.vo.SubProductInfoVo;
import cn.legendshop.order.vo.SubVo;
import cn.legendshop.order.vo.SubmitOrderSuccessVo;
import cn.legendshop.payment.client.PaymentTransactionClient;
import cn.legendshop.payment.entity.PaymentTransaction;
import cn.legendshop.product.api.dto.BuysDTO;
import cn.legendshop.user.api.bo.UserInvoiceBO;
import com.google.common.collect.Lists;
import com.legendshop.dao.util.StringUtils;
import com.legendshop.model.constant.OrderSourceEnum;
import com.legendshop.model.dto.order.MySubDto;
import com.legendshop.model.entity.Sub;
import com.legendshop.model.entity.SubItem;
import com.legendshop.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MallOrderProcessServiceImpl implements MallOrderProcessService {


    @Autowired
    private SubmitOrderStrategyContext submitOrderStrategyContext;

    @Autowired
    private OrderCacheService orderCacheService;

    @Autowired
    private MallShipmentProcessService mallShipmentProcessService;

    //构建订单提交后观察者监听
    private SubmitOrderEventManger submitOrderEventManger;

    @Autowired
    private InvoiceServiceClient invoiceServiceClient;

    @Autowired
    private InvoiceAddressServiceClient invoiceAddressServiceClient;

    @Autowired
    private SubDao subDao;

    @Autowired
    private SubItemDao subItemDao;

    @Autowired
    private IndustrialOuterShopConfigService industrialOuterShopConfigService;

    @Autowired
    private AmqpSendMsgUtil amqpSendMsgUtil;

    @Autowired
    private PaymentTransactionClient paymentTransactionClient;

    @Autowired
    protected MessagePoolOrderService messagePoolOrderService;

    public MallOrderProcessServiceImpl(ProdSnapshotListener prodSnapshotListener, DelayCancelOfferListener delayCancelOfferListener) {
        submitOrderEventManger = new SubmitOrderEventManger(SubmitOrderEventManger.EventType.MQ, SubmitOrderEventManger.EventType.Message);
        submitOrderEventManger.subscribe(SubmitOrderEventManger.EventType.MQ, prodSnapshotListener);
        submitOrderEventManger.subscribe(SubmitOrderEventManger.EventType.Message, delayCancelOfferListener);
    }

    public String getOrderStrategyType(String subNumber) {
        //1. 查询对应的订单信息 根据对应的订单类型做判断
        Sub sub = subDao.getSubBySubNumber(subNumber);
        if (AppUtils.isBlank(sub)) {
            throw new BusinessException("订单不存在!");
        }

        if (OrderSourceEnum.MRO.value().equals(sub.getOrderSource())) {
            return MallOrderStrategyTypeEnum.MRO_COMMON.getType();
        }
        if (OrderSourceEnum.ERP.value().equals(sub.getOrderSource())) {
            // 鑫智链账套，就是南钢ERP的订单
            if (ObjectUtil.isEmpty(sub.getAccountBookId()) || AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId().equals(sub.getAccountBookId())) {
                return MallOrderStrategyTypeEnum.ERP_THIRD_INDUSTRIAL.getType();
            } else {
                // 采购商账套
                return MallOrderStrategyTypeEnum.PURCHASER_THIRD_INDUSTRIAL.getType();
            }
        }

        if (OrderSourceEnum.TENDER.value().equals(sub.getOrderSource())) {
            return MallOrderStrategyTypeEnum.PURCHASER_TENDER.getType();
        }
        return null;
    }

    @Override
    public R<ConfirmOrderBO> submitOrderProdCheck(ConfirmOrderDTO confirmOrderDTO) {
        //1.组装商品信息
        R<ConfirmOrderBO> confirmOrderBOR = submitOrderStrategyContext.executeCheckStrategy(confirmOrderDTO);
        if (!confirmOrderBOR.getSuccess()) {
            return confirmOrderBOR;
        }
        //2.组装订单信息
        SubmitOrderStrategy strategy = submitOrderStrategyContext.getStrategy(confirmOrderDTO.getStrategyType());
        R<ConfirmOrderBO> orderBOR = strategy.confirm(confirmOrderBOR.getResult());
        return orderBOR;
    }

    @Override
    public R<UserInvoiceBO> invoiceChange(InvoiceChangeDTO invoiceChangeDTO, ConfirmOrderBO confirmOrderBo) {
        log.info("###### 更新发票信息 ##### ");
        UserInvoiceBO userInvoiceBo = null;
        List<SubmitOrderShopDTO> shopOrderList = confirmOrderBo.getShopOrderList();
        for (SubmitOrderShopDTO shop : shopOrderList) {
            // 只修改对应的商家订单信息
            if (!shop.getShopId().equals(invoiceChangeDTO.getShopId())) {
                continue;
            }
            // 用户选择的开启关闭发票标识
            shop.setUserInvoiceFlag(invoiceChangeDTO.getInvoiceFlag());
//			//如果用户所选发票ID不为空， 则切换为用户所选的发票信息
//			if (!ObjectUtil.isNotNull(invoiceChangeDTO.getInvoiceId())) {
//				continue;
//			}

            R<InvoiceDTO> userInvoice = invoiceServiceClient.getInvoiceListByInvoiceIdAndUserId(invoiceChangeDTO.getInvoiceId(), confirmOrderBo.getUserId(), SecurityConstants.FROM_IN);
            InvoiceDTO userInfoviceResult = userInvoice.getResult();
            if (!userInvoice.getSuccess()) {
                throw new BusinessException(userInvoice.getMsg());
            }

            // 如果选中的是非第三方平台则判断选中的发票是否为增值税发票
            if (!OpenPlatformEnum.MRO_PLATFORM.getPlatformId().equals(shop.getPlatformId())) {
                if (!invoiceChangeDTO.getInvoiceFlag()) {
                    return R.fail("第三方平台自营商品,需要填写增值税发票信息！");
                }
                if (!InvoiceTypeEnum.VAT.value().equals(userInfoviceResult.getTypeId())) {
                    return R.fail("第三方平台自营商品,需要填写增值税发票信息！");
                }
            }
            if (userInvoice.getResult() != null) {
                UserInvoiceBO userInvoiceBO = new UserInvoiceBO();
                userInvoiceBO.setId(userInfoviceResult.getId());
                userInvoiceBO.setCompany(userInfoviceResult.getCompany());
                userInvoiceBO.setType(userInfoviceResult.getTypeId());
                userInvoiceBO.setInvoiceHumNumber(userInfoviceResult.getInvoiceHumNumber());
                userInvoiceBO.setTitleType(userInfoviceResult.getTitleId());
                userInvoiceBO.setRegisterAddr(userInfoviceResult.getRegsAddress());
                userInvoiceBO.setBankAccountNum(userInfoviceResult.getBankAccount());
                userInvoiceBO.setRegisterPhone(userInfoviceResult.getRegsPhone());
                userInvoiceBO.setDepositBank(userInfoviceResult.getOpeningBank());
                userInvoiceBO.setContentId(userInfoviceResult.getContentId());

                R<InvoiceAddress> invoiceAddressR = invoiceAddressServiceClient.findByInvoiceId(userInfoviceResult.getId(), SecurityConstants.FROM_IN);
                InvoiceAddress invoiceAddress = invoiceAddressR.getResult();
                userInvoiceBO.setName(invoiceAddress.getName());
                userInvoiceBO.setPhone(invoiceAddress.getPhone());
                userInvoiceBO.setAddress(invoiceAddress.getAddress());
                userInvoiceBO.setMail(invoiceAddress.getMail());


                shop.setUserInvoiceBo(userInvoiceBO);
                userInvoiceBo = userInvoiceBO;
            }
        }
        // 更新预订单缓存
        orderCacheService.putConfirmOrderInfoCacheByUserId(confirmOrderBo.getUserId(), confirmOrderBo);
        log.info("###### 更新发票信息完成 ，更新预订单缓存信息{} ##### ", JSONUtil.toJsonStr(confirmOrderBo));
        return R.ok(userInvoiceBo);
    }

	@Override
	public R<String> changeEngConMsg(EngConDTO engConDTO, ConfirmOrderBO confirmOrderBo) {
		confirmOrderBo.setEngId(engConDTO.getEngId());
		confirmOrderBo.setConId(engConDTO.getConId());
		confirmOrderBo.setArrivelDate(engConDTO.getArrivelDate());
		confirmOrderBo.setAccess(engConDTO.getAccess());
		orderCacheService.putConfirmOrderInfoCacheByUserId(confirmOrderBo.getUserId(), confirmOrderBo);
		log.info("###### 更新订单工程合同信息完成 ，更新预订单缓存信息{} ##### ", JSONUtil.toJsonStr(confirmOrderBo));
		return R.ok();
	}


    @Override
    public <T> R<Object> submitOrder(T orderDTO, MallOrderStrategyTypeEnum strategyType) {
        ConfirmOrderDTO confirmOrderDTO = buildConfirmOrderDTO(orderDTO, strategyType);
        if (confirmOrderDTO == null) {
            return R.fail("不支持的订单类型！");
        }

        // 1. 校验订单
        R<ConfirmOrderBO> confirmOrderBoR = submitOrderStrategyContext.executeCheckStrategy(confirmOrderDTO);
        if (!confirmOrderBoR.isSuccess()) {
            return R.fail(confirmOrderBoR.getMsg());
        }

        // 2. 订单提交
        ConfirmOrderBO confirmOrderBO = confirmOrderBoR.getResult();
        return submitOrderStrategyContext.executeStrategy(confirmOrderBO);
    }

    /**
     * 根据订单类型构建 ConfirmOrderDTO
     */
    private <T> ConfirmOrderDTO buildConfirmOrderDTO(T orderDTO, MallOrderStrategyTypeEnum strategyType) {
        ConfirmOrderDTO confirmOrderDTO = new ConfirmOrderDTO();

        if (orderDTO instanceof ErpPreOrderParameterDTO) {
            ThirdIndustrialConfirmOrderBO thirdIndustrialConfirmOrderBO = new ThirdIndustrialConfirmOrderBO();
            thirdIndustrialConfirmOrderBO.setErpPreOrderParameter((ErpPreOrderParameterDTO) orderDTO);
            confirmOrderDTO.setThirdIndustrialConfirmOrderBO(thirdIndustrialConfirmOrderBO);
        } else if (orderDTO instanceof PurchaserPreOrderParamDTO) {
            PurchaserPreOrderParamDTO preOrderParamDTO = (PurchaserPreOrderParamDTO) orderDTO;
            confirmOrderDTO.setPurchaserPreOrderParamDTO(preOrderParamDTO);
        } else {
            return null;
        }

        confirmOrderDTO.setStrategyType(strategyType.getType());
        return confirmOrderDTO;
    }

    /**
     * 异步处理逻辑，如发送消息或记录日志
     */
    private void asyncOrderProcessing(ConfirmOrderBO confirmOrderBO) {
        // 异步处理订单提交后相关逻辑（如发送MQ、记录日志等）
    }

    @Override
    public R mroSubmitOrder(ConfirmOrderBO confirmOrderBO) {
        SubmitOrderStrategy submitOrderStrategy = submitOrderStrategyContext.getStrategy(MallOrderStrategyTypeEnum.MRO_COMMON.getType());
        R submit = submitOrderStrategy.submit(confirmOrderBO);
        if (submit.isSuccess()) {
            SubmitOrderResultDTO submitResult = (SubmitOrderResultDTO) submit.getResult();
            //处理订单成功 异步提交后的信息
            log.info("开始记录商品快照:{}",JSONUtil.toJsonStr(submitResult));
            submitOrderEventManger.notify(SubmitOrderEventManger.EventType.MQ, submitResult);
            submitOrderEventManger.notify(SubmitOrderEventManger.EventType.Message, submitResult);
        }
        return submit;
    }

    @Override
    public R<SubmitOrderSuccessVo> submitOrderSuccessDetail(String subSettlementSn, String subSettlementType) {
        SubmitOrderSuccessVo submitOrderSuccessVo = new SubmitOrderSuccessVo();
        BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
        String userId = baseUserDetailDTO.getUserId();
        List<MySubDto> mySubDtos = subDao.queryOrderDetailBySubSettlementSn(subSettlementSn, userId);
        if (AppUtils.isBlank(mySubDtos)) {
            return R.fail("订单尚未完成支付！");
        }
        List<Map<String, Object>> subPayMethod = getSubPayMethod(mySubDtos);
        List<String> subNumberList = mySubDtos.stream().map(MySubDto::getSubNum).collect(Collectors.toList());
        R<PaymentTransaction> paymentTransactionClientByPaySn = paymentTransactionClient.findByPaySn(subSettlementSn);
        if (!paymentTransactionClientByPaySn.getSuccess()) {
            return R.fail("支付单查询失败！");
        }
        PaymentTransaction paymentTransaction = paymentTransactionClientByPaySn.getResult();
        submitOrderSuccessVo.setPayMethod(subPayMethod);
        submitOrderSuccessVo.setSubNumberList(subNumberList);
        submitOrderSuccessVo.setPayAmount(new BigDecimal(paymentTransaction.getPayAmount()));
        return R.ok(submitOrderSuccessVo);
    }


    private List<Map<String, Object>> getSubPayMethod(List<MySubDto> mySubDto) {

        List<Map<String, Object>> list = Lists.newArrayList();
        Map<String, Object> map = new HashMap<>();
        for (MySubDto myOrder : mySubDto) {
            BigDecimal onlineAmount = BigDecimal.valueOf(0.0);
            if (null != myOrder.getUserAccountDepositAmount() && myOrder.getUserAccountDepositAmount() > 0) {
                BigDecimal bigDecimal = NumberUtil.add(BigDecimal.valueOf(myOrder.getUserAccountDepositAmount()), new BigDecimal(map.getOrDefault("预付款支付", BigDecimal.ZERO).toString()));
                map.put("预付款支付", bigDecimal);
            }
            if (BigDecimal.valueOf(myOrder.getActualTotal()).compareTo(onlineAmount) == 1 && StrUtil.isNotBlank(myOrder.getPayTypeName())) {
                map.put(myOrder.getPayTypeName(), NumberUtil.add(BigDecimal.valueOf(myOrder.getActualTotal()).subtract(onlineAmount), new BigDecimal(map.getOrDefault(myOrder.getPayTypeName(), BigDecimal.ZERO).toString())));
            }
        }
        for (String payType : map.keySet()) {
            Map<String, Object> payMap = new HashMap<>();
            Object o = map.get(payType);
            payMap.put("method", payType);
            payMap.put("amount", o);
            list.add(payMap);
        }
        return list;
    }

    @Override
    public R<String> confirmOrder(UpdateOrderDTO updateOrderDTO) {
        R<String> confirmOrderR = submitOrderStrategyContext.getStrategy(getOrderStrategyType(updateOrderDTO.getOrderNumber())).confirmOrder(updateOrderDTO);
        // 增加商品销量buys
        addProdBuys(updateOrderDTO.getOrderNumber());
        // 记录
        return confirmOrderR;
    }

    @Override
    public R<String> applyCancelPreOrder(UpdateOrderDTO updateOrderDTO) {
        return submitOrderStrategyContext.getStrategy(getOrderStrategyType(updateOrderDTO.getOrderNumber())).applyCancelPreOrder(updateOrderDTO);
    }

    @Override
    public R<String> cancelOrder(UpdateOrderDTO updateOrderDTO) {
        return submitOrderStrategyContext.getStrategy(getOrderStrategyType(updateOrderDTO.getOrderNumber())).cancelOrder(updateOrderDTO);
    }

    @Override
    public R<String> cancelPaidOrder(UpdateOrderDTO updateOrderDTO) {
        return submitOrderStrategyContext.getStrategy(getOrderStrategyType(updateOrderDTO.getOrderNumber())).cancelPaidOrder(updateOrderDTO);
    }

    @Override
    public R<String> cancelOrderBeforeDelivering(UpdateOrderDTO updateOrderDTO) {
        return submitOrderStrategyContext.getStrategy(getOrderStrategyType(updateOrderDTO.getOrderNumber())).cancelOrderBeforeDelivering(updateOrderDTO);
    }

    //判断订单项是否是整单取消
    boolean isOrderCancel(SubVo subVo) {
        List<SubProductInfoVo> productInfoVos = subVo.getSubItems();
        if (productInfoVos == null || productInfoVos.isEmpty()) {
            // 根据业务逻辑，空列表可以返回 true 或 false
            // 这里假设对于空列表不进行任何比较，直接返回 true
            return true;
        }

        // 遍历列表中的每个元素，检查 afterRefundCount 是否等于 remainRefundCount
        for (SubProductInfoVo subProductInfoVo : productInfoVos) {
            BigDecimal afterRefundCount = subProductInfoVo.getAfterRefundCount();
            BigDecimal remainRefundCount = subProductInfoVo.getRemainRefundCount();

            // 考虑分项取消的情况 对已发货的进行判断
            if (afterRefundCount.compareTo(BigDecimal.ZERO) == 0) {
                return false;
            }

            // 检查两个值是否都为null，或者它们是否相等
            if ((!Objects.equals(afterRefundCount, remainRefundCount))) {
                // 如果发现不一致，立即返回 false
                return false;
            }
        }

        // 如果所有元素的 afterRefundCount 都等于 remainRefundCount，返回 true
        return true;
    }

    @Override
    public R<String> cancelPaidOrderItem(SubVo subVo) {
        return submitOrderStrategyContext.getStrategy(getOrderStrategyType(subVo.getSubNumber())).cancelPaidOrderItem(subVo);
    }

    @Override
    public R<String> thirdCancelOrder(CancelPreOrderRequest cancelPreOrderRequest) {
        Sub sub = subDao.getSubBySubNumber(cancelPreOrderRequest.getOrderNo());
        if (AppUtils.isBlank(sub)) {
            return R.fail("该订单不存在!");
        }
        UpdateOrderDTO updateOrderDTO = new UpdateOrderDTO();
        updateOrderDTO.setOrderNumber(cancelPreOrderRequest.getOrderNo());
        updateOrderDTO.setCancelReason(cancelPreOrderRequest.getReason());


        //查询对应的订单信息 根据对应的订单类型做判断
        SubmitOrderStrategy strategy;
        if (OrderSourceEnum.ERP.value().equals(sub.getOrderSource())) {
            if (AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId().equals(sub.getAccountBookId())) {
                strategy = submitOrderStrategyContext.getStrategy(MallOrderStrategyTypeEnum.ERP_THIRD_INDUSTRIAL.getType());
            } else {
                strategy = submitOrderStrategyContext.getStrategy(MallOrderStrategyTypeEnum.PURCHASER_THIRD_INDUSTRIAL.getType());
            }
        } else {
            strategy = submitOrderStrategyContext.getStrategy(MallOrderStrategyTypeEnum.MRO_COMMON.getType());
        }
        return strategy.thirdCancelOrder(updateOrderDTO);
    }

    public void addProdBuys(String subNumber) {
        Sub sub = subDao.getSubBySubNumber(subNumber);
        Long shopId = sub.getShopId();
        Optional.ofNullable(industrialOuterShopConfigService.getByShopId(shopId)).ifPresent(shopConfig -> {
            List<SubItem> subItemList = subItemDao.queryBySubNumber(subNumber);
            for (SubItem subItem : subItemList) {
                BuysDTO buysDTO = new BuysDTO(subItem.getProdId(), subItem.getBasketCount().longValue(), shopId);
                this.amqpSendMsgUtil.convertAndSend(ThridIndustrialProductMQConstant.INDUSTRIAL_CHAIN_EXCHANGE, ThridIndustrialProductMQConstant.PRODUCT_BUYS_ADD_KEY, buysDTO);
            }
        });

    }

    // 提取未发货的售后项
    @Override
    public List<SubProductInfoVo> extractUnshippedItems(List<SubProductInfoVo> productInfoVos) {
        return productInfoVos.stream()
                .filter(item -> item.getAfterRefundCount() != null && item.getAfterRefundCount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
    }

    // 提取已发货的售后项
    @Override
    public List<SubProductInfoVo> extractShippedItems(List<SubProductInfoVo> productInfoVos) {
        return productInfoVos.stream()
                .filter(item -> item.getAfterShippedRefundCount() != null && item.getAfterShippedRefundCount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
    }


    // 处理仅未发货的售后项
    @Override
    public R<String> handleUnshippedItems(SubVo subVo, List<SubProductInfoVo> productInfoVos) {
        String cancelReason = productInfoVos.stream()
                .map(SubProductInfoVo::getCancelReason)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(","));

        if (isOrderCancel(subVo)) {
            UpdateOrderDTO updateOrderDTO = createUpdateOrderDTO(subVo, cancelReason);
            return cancelPaidOrder(updateOrderDTO);
        } else {
            subVo.setSubItems(productInfoVos);
            subVo.setCancelPerson(UserContextHolder.getInstance().getContext().getUsername());
            return cancelPaidOrderItem(subVo);
        }
    }

    // 创建更新订单DTO
     UpdateOrderDTO createUpdateOrderDTO(SubVo subVo, String cancelReason) {
        UpdateOrderDTO updateOrderDTO = new UpdateOrderDTO();
        updateOrderDTO.setOrderNumber(subVo.getSubNumber());
        updateOrderDTO.setOrderSourceEnum(OrderSourceEnum.ERP);
        updateOrderDTO.setCancelReason(cancelReason);
        updateOrderDTO.setCancelPerson(UserContextHolder.getInstance().getContext().getUsername());
        updateOrderDTO.setCancelDate(new Date());
        return updateOrderDTO;
    }

    // 处理仅已发货的售后项
    public R<String> handleShippedItems(SubVo subVo, List<SubProductInfoVo> shippedItems) {
        subVo.setSubItems(shippedItems);
        List<ConfirmReceiptParam> confirmReceiptParams = mallShipmentProcessService.getAfterRefundShipmentBySub(subVo);
        for (ConfirmReceiptParam confirmReceiptParam : confirmReceiptParams) {
            mallShipmentProcessService.confirmReceipt(confirmReceiptParam, OrderSourceEnum.PURCHASER);
        }
        Sub sub = subDao.getSubBySubNumber(subVo.getSubNumber());
        for (SubProductInfoVo subProductInfoVo : shippedItems) {
             messagePoolOrderService.pushCancelOrderItem(sub, subProductInfoVo);
        }
        return R.ok();
    }

    @Override
    public R<String> applySubItemHist(ApplySubItemHistDTO applySubItemHistDTO) {
        return submitOrderStrategyContext.getStrategy(getOrderStrategyType(applySubItemHistDTO.getSubNumber())).applySubItemHist(applySubItemHistDTO);
    }

    @Override
    public R<String> reviewSubItemHist(ReviewSubItemHistDTO reviewSubItemHistDTO) {
        return submitOrderStrategyContext.getStrategy(getOrderStrategyType(reviewSubItemHistDTO.getSubNumber())).reviewSubItemHist(reviewSubItemHistDTO);
    }
}
