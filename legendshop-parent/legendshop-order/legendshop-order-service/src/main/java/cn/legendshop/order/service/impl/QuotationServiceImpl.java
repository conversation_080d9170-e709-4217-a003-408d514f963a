package cn.legendshop.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.legendshop.XinghuoBlockChain.api.client.BlockChainClient;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.RSAUtils;
import cn.legendshop.common.rabbitmq.constants.WorkFlowMQConstant;
import cn.legendshop.common.rabbitmq.utils.AmqpSendMsgUtil;
import cn.legendshop.common.service.SystemParameterService;
import cn.legendshop.ngNewIndustryOA.api.client.NgNewIndustryOAClient;
import cn.legendshop.ngNewIndustryOA.api.dto.ApprovalFlowDTO;
import cn.legendshop.order.enums.QuotationItemStatusOrderEnum;
import cn.legendshop.order.service.EnquiryPriceExpertService;
import cn.legendshop.user.api.client.v2.ExpertClient;
import cn.legendshop.user.api.entity.Expert;
import com.legendshop.common.oa.model.dto.ApprovalFlowFileDTO;
import cn.legendshop.order.dao.EnquiryPriceDao;
import cn.legendshop.order.dao.QuotationDao;
import cn.legendshop.order.dao.*;
import cn.legendshop.order.dto.*;
import cn.legendshop.order.entity.EnquiryPrice;
import cn.legendshop.order.entity.Quotation;
import cn.legendshop.order.entity.QuotationItem;
import cn.legendshop.order.enums.EnquiryPriceAttachmentsTypeEnum;
import cn.legendshop.order.enums.EnquiryPriceEnum;
import cn.legendshop.order.entity.*;
import cn.legendshop.order.enums.QuotationStatusEnum;
import cn.legendshop.order.service.QuotationService;
import cn.legendshop.order.service.convert.QuotationConverter;
import cn.legendshop.order.vo.*;
import cn.legendshop.product.api.client.ThirdIndustrialProductClient;
import cn.legendshop.product.api.entity.IndustrialProduct;
import cn.legendshop.smsmail.api.constant.SmsSystemParamterConstant;
import cn.legendshop.upload.api.client.UploadClient;
import cn.legendshop.user.api.client.ShopDetailServiceClient;
import cn.legendshop.workFlow.api.dto.WorkFlowDTO;
import cn.legendshop.workFlow.api.enums.XBJWorkFlowTypeEnum;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.legendshop.common.oa.model.constant.NgOaParamterConstant;
import com.legendshop.common.oa.model.dto.CreateXbjWorkFlowDTO;
import com.legendshop.common.oa.model.dto.GetUserXbjWorkFlowDTO;
import com.legendshop.common.oa.model.enums.ApprovalFlowFeildEnum;
import com.legendshop.common.oa.model.enums.ResponseCodeEnum;
import com.legendshop.common.oa.model.request.ApprovalFlowCreateRequest;
import com.legendshop.common.oa.model.request.UploadFileRequest;
import com.legendshop.common.oa.model.response.ApprovalFlowCreateResponse;
import com.legendshop.common.oa.model.response.ApprovalFlowUserResponse;
import com.legendshop.common.oa.model.response.UploadFileResponse;
import com.legendshop.common.oa.service.WorkFlowService;
import com.legendshop.dao.support.EntityCriterion;
import cn.legendshop.order.vo.EnquiryPriceQuotationItemVo;
import cn.legendshop.order.vo.EnquiryPriceShopVo;
import com.legendshop.model.entity.ShopDetail;
import com.legendshop.util.AppUtils;
import com.legendshop.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class QuotationServiceImpl implements QuotationService {
    private final EnquiryPriceDao enquiryPriceDao;
    private final EnquiryPriceItemDao enquiryPriceItemDao;
    private final QuotationDao quotationDao;
    private final QuotationitemDao quotationItemDao;
    private final QuotationConverter quotationConverter;
    private final SystemParameterService systemParameterService;
    private final EnquiryPriceExpertDao enquiryPriceExpertDao;
    private final UploadClient uploadClient;
    private final AmqpSendMsgUtil amqpSendMsgUtil;
    private final ExpertSelectProdDao expertSelectProdDao;
    private final EnquiryPriceAttachmentsDao enquiryPriceAttachmentsDao;
    private final ShopDetailServiceClient shopDetailServiceClient;
    private final NgNewIndustryOAClient ngNewIndustryOAClient;
    private final WorkFlowService workFlowService;
    private final EnquiryPriceExpertService enquiryPriceExpertService;

    private final ThirdIndustrialProductClient thirdIndustrialProductClient;
    private final ExpertClient expertClient;

    private final BlockChainClient blockChainClient;

    private static final String iv = "ab3456ha89012346";

    // 正则表达式：匹配两位小数的数字
    private static final Pattern DECIMAL_PATTERN = Pattern.compile("^\\d+(\\.\\d{1,2})?$");

    private String getEncode(String param,String key) {
        try {
            return RSAUtils.encryptByPublicKey(key,param);
        } catch (Exception e) {
            log.info("加密失败！！！");
            throw new RuntimeException(e);
        }
    }

    private void saveWorkFlow(String type,String flowName,Long epId,String userId,String userName,String remark,String other) {
        WorkFlowDTO workFlowDTO = new WorkFlowDTO();
        workFlowDTO.setType(type);
        workFlowDTO.setFlowName(flowName);
        workFlowDTO.setCreateTime(new Date());
        workFlowDTO.setEpId(epId);
        workFlowDTO.setUserId(userId);
        workFlowDTO.setUserName(userName);
        workFlowDTO.setRemark(remark);
        workFlowDTO.setOther(other);
        this.amqpSendMsgUtil.convertAndSend(WorkFlowMQConstant.WORK_FLOW_EXCHANGE, WorkFlowMQConstant.WORK_FLOW_CREATE_KEY, JSONObject.toJSONString(workFlowDTO));
    }


    @Override
    public Long saveQuotation(EnquiryPriceShopVo enquiryPriceShopVo) {
        String publicKey = systemParameterService.getSystemParameterByName(SmsSystemParamterConstant.ENQUIRY_PRICE_PUBLIC_KEY, String.class);
        Quotation quotation = quotationDao.getById(enquiryPriceShopVo.getQuotationId());

        ExecutorService executorService = Executors.newFixedThreadPool(20);

        // 异步获取供应商名称
        CompletableFuture<ShopDetail> shopDetailFuture = CompletableFuture.supplyAsync(() ->
                shopDetailServiceClient.getShopDetailByShopId(enquiryPriceShopVo.getShopId()).getResult(), executorService);

        // 保存报价单
        if (AppUtils.isNotBlank(quotation)) {
        } else {
            quotation = new Quotation();
            quotation.setRound(1);
            quotation.setShopId(enquiryPriceShopVo.getShopId());
            quotation.setEpId(enquiryPriceShopVo.getEpId());
            quotation.setEpNo(enquiryPriceShopVo.getEpNo());
        }

        // 等待供应商名称获取完成
        ShopDetail shop = shopDetailFuture.join();
        String shopName = shop.getSiteName();
        enquiryPriceShopVo.setShopName(shopName);
        quotation.setShopName(shopName);

        quotation.setRemark(enquiryPriceShopVo.getQuotationRemark());
        quotation.setFilePath(enquiryPriceShopVo.getQuotationFilePath());
        String quotationTotalPriceEncode = getEncode(enquiryPriceShopVo.getQuotationTotalPrice(), publicKey);
        quotation.setTotalPrice(quotationTotalPriceEncode);
        quotation.setStatus(QuotationStatusEnum.UN_QUOTATION.getStatus());
        quotation.setSubmitTime(new Date());

        // 保存报价单
        Long quotationId = quotationDao.saveOrUpdate(quotation);

        // 先清理之前的报价单明细
        quotationItemDao.deleteByQuotationId(quotationId);

        List<EnquiryPriceQuotationItemVo> enquiryPriceQuotationItems = enquiryPriceShopVo.getEnquiryPriceQuotationItems();
        List<QuotationItem> quotationItemsSave = new ArrayList<>();

        // 异步获取商品信息，并过滤掉productSku为null的部分
        List<String> searchSkuIds = enquiryPriceQuotationItems.stream()
                .map(EnquiryPriceQuotationItemVo::getProductSku)
                .filter(Objects::nonNull)  // 过滤掉productSku为null的部分
                .filter(s -> !s.isEmpty()) // 过滤掉productSku为空字符串的部分
                .collect(Collectors.toList());
        // 仅在sku不为null时处理
        List<String> skuIds = new ArrayList<>();
        if (AppUtils.isNotBlank(searchSkuIds)) {
            CompletableFuture<List<IndustrialProduct>> industrialProductsFuture = CompletableFuture.supplyAsync(() ->
                    thirdIndustrialProductClient.getProdByShopIdAndSkuIdS(enquiryPriceShopVo.getShopId(), searchSkuIds, SecurityConstants.FROM_IN).getResult(), executorService);

            // 等待商品信息获取完成
            List<IndustrialProduct> industrialProducts = industrialProductsFuture.join();

            skuIds = industrialProducts.stream()
                    .map(IndustrialProduct::getSkuId)
                    .collect(Collectors.toList());
        }

        // 多线程处理报价单明细
        List<String> finalSkuIds = skuIds;
        List<CompletableFuture<Void>> futures = enquiryPriceQuotationItems.stream()
                .map(item -> CompletableFuture.runAsync(() -> {
                    QuotationItem quotationItem = new QuotationItem();
                    quotationItem.setQuotationId(quotationId);
                    quotationItem.setEpItemId(item.getEpItemId());
                    quotationItem.setProductName(item.getQuotationProductName());
                    quotationItem.setRemark(item.getQuotationRemark());
                    quotationItem.setProductModel(item.getQuotationProductModel());
                    quotationItem.setProductBrand(item.getQuotationProductBrand());
                    quotationItem.setProductDelivery(Integer.valueOf(item.getQuotationProductDelivery()));
                    quotationItem.setValidUntil(item.getValidUntil());

                    String encode = getEncode(item.getQuotationProductPrice(), publicKey);
                    quotationItem.setProductPrice(encode);
                    quotationItem.setProductSku(item.getProductSku());
                    quotationItem.setProductUnit(item.getQuotationProductUnit());
                    quotationItem.setProductQuantity(item.getQuotationProductQuantity());
                    String totalPriceEncode = getEncode(item.getQuotationTotalPrice(), publicKey);
                    quotationItem.setTotalPrice(totalPriceEncode);
                    if (AppUtils.isNotBlank(finalSkuIds) && finalSkuIds.contains(item.getProductSku())) {
                        // 直接修改状态为上架
                        quotationItem.setStatus(10);
                    }
                    synchronized (quotationItemsSave) {
                        quotationItemsSave.add(quotationItem);
                    }
                }, executorService))
                .collect(Collectors.toList());

        // 等待所有报价单明细处理完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 批量保存报价单明细
        quotationItemDao.save(quotationItemsSave);
        return quotationId;
    }

    // 保存sku方法
    @Override
    public Long saveSku(EnquiryPriceShopVo enquiryPriceShopVo) {
        List<EnquiryPriceQuotationItemVo> enquiryPriceQuotationItems = enquiryPriceShopVo.getEnquiryPriceQuotationItems();
        if (AppUtils.isNotBlank(enquiryPriceQuotationItems)) {
            // 创建一个固定大小的线程池
            ExecutorService executorService = Executors.newFixedThreadPool(20);

            // 创建一个CompletableFuture数组来存储所有的异步任务
            CompletableFuture<?>[] futures = enquiryPriceQuotationItems.stream()
                    .map(enquiryPriceQuotationItemVo -> CompletableFuture.runAsync(
                            () -> quotationItemDao.updateSkuById(enquiryPriceQuotationItemVo.getProductSku(), new Date(),enquiryPriceQuotationItemVo.getQuotationItemId()),
                            executorService
                    ))
                    .toArray(CompletableFuture[]::new);

            // 等待所有任务完成
            CompletableFuture.allOf(futures).join();

            // 关闭线程池
            executorService.shutdown();
        }
        return enquiryPriceShopVo.getQuotationId();
    }

    @Override
    public R supplierConfirmCancel(SupplierConfirmCancelDTO supplierConfirmCancelDTO){
        Long quotationItemId = supplierConfirmCancelDTO.getQuotationItemId();
        QuotationItem quotationItem = quotationItemDao.getById(quotationItemId);
        //判断状态
        if(QuotationItemStatusOrderEnum.MERCHANT_CONFIRM.getStatus() != quotationItem.getOrderStatus()){
            return R.fail("当前报价项未提出取消申请");
        }
        // 0为驳回 驳回需要给出驳回原因
        if (supplierConfirmCancelDTO.getConfirmResult() == 0){
            quotationItem.setOrderStatus(QuotationItemStatusOrderEnum.NOT_ORDERED.getStatus());
            quotationItem.setSupplierRejectReason(supplierConfirmCancelDTO.getSupplierRejectReason());
            quotationItem.setSupplierDealTime(new Date());
        } else{
            quotationItem.setOrderStatus(QuotationItemStatusOrderEnum.PLATFORM_CONFIRM.getStatus());
            quotationItem.setSupplierDealTime(new Date());
        }
        quotationItemDao.updateSupplierConfirmCancelInfo(quotationItem);
        return R.ok();
    };

    @Override
    public R<EnquiryPriceShopVo> getQuotationDetailByEpId(EnquiryPriceShopDTO enquiryPriceShopDTO) {
        EnquiryPrice enquiryPrice = enquiryPriceDao.getById(Long.valueOf(enquiryPriceShopDTO.getEpId()));
        //查询询价单报价单详情
        EnquiryPriceShopVo result = quotationDao.getQuotationDetailByEpId(enquiryPriceShopDTO);
        String privateKey = systemParameterService.getSystemParameterByName(SmsSystemParamterConstant.ENQUIRY_PRICE_PRIVATE_KEY, String.class);
        // 修复报价单中看不到询价单文件的问题
        List<EnquiryPriceAttachments> enquiryPriceAttachments = enquiryPriceAttachmentsDao.queryByType(Long.valueOf(enquiryPriceShopDTO.getEpId()),EnquiryPriceAttachmentsTypeEnum.Inquiry_Att.getCode());
        if (AppUtils.isNotBlank(enquiryPriceAttachments)){
            List<EnquirpyFileDTO> innerFiles = new ArrayList<>();
            for (EnquiryPriceAttachments fileItem : enquiryPriceAttachments) {
                EnquirpyFileDTO innerFile = new EnquirpyFileDTO();
                innerFile.setName(fileItem.getName());
                innerFile.setPath(fileItem.getFilePath());
                innerFiles.add(innerFile);
            }
            result.setInnerFile(innerFiles);
        }
        if (AppUtils.isNotBlank(result.getQuotationId())){
            List<EnquiryPriceAttachments> supplierAttachments = enquiryPriceAttachmentsDao.queryQuotationFilesByType(Long.valueOf(enquiryPriceShopDTO.getEpId()), result.getQuotationId(),EnquiryPriceAttachmentsTypeEnum.Quotation_Att.getCode());
            if (AppUtils.isNotBlank(supplierAttachments)){
                List<EnquirpyFileDTO> supplierFiles = new ArrayList<>();
                for (EnquiryPriceAttachments fileItem : supplierAttachments) {
                    EnquirpyFileDTO supplierFile = new EnquirpyFileDTO();
                    supplierFile.setName(fileItem.getName());
                    supplierFile.setPath(fileItem.getFilePath());
                    supplierFiles.add(supplierFile);
                }
                result.setSupplierFile(supplierFiles);
            }

            String totalPrice = result.getQuotationTotalPrice();
            // 判断 totalPrice 是否能转换成金额类型（两位小数的数字）
            if (AppUtils.isNotBlank(totalPrice) && !isValidAmount(totalPrice)) {
                totalPrice = decryptPrice(privateKey,totalPrice);
                result.setQuotationTotalPrice(totalPrice);
            }
        }

        //查询询价单详情
        List<EnquiryPriceQuotationItemVo> quotationItems = quotationItemDao.queryByQuotationId(result.getEpId(), result.getQuotationId());
        if(AppUtils.isNotBlank(quotationItems) ) {
            // 如果询价单状态在开启评判前并且已经生成报价单，才需要解密
            if (EnquiryPriceEnum.START_EVALUATE.getStatus()>enquiryPrice.getStatus() && result.getQuotationId() != null){
//            for (EnquiryPriceQuotationItemVo item : quotationItems) {
//                String productPrice = item.getQuotationProductPrice();
//                String itemTotalPrice = item.getQuotationTotalPrice();
//
//                // 判断 quotationProductPrice 是否能转换成金额类型（两位小数的数字）
//                if (AppUtils.isNotBlank(productPrice) && !isValidAmount(productPrice)) {
//                    productPrice = decryptPrice(productPrice);
//                    item.setQuotationProductPrice(productPrice);
//                }
//
//                // 判断 quotationTotalPrice 是否能转换成金额类型（两位小数的数字）
//                if (AppUtils.isNotBlank(itemTotalPrice) && !isValidAmount(itemTotalPrice)) {
//                    itemTotalPrice = decryptPrice(itemTotalPrice);
//                    item.setQuotationTotalPrice(itemTotalPrice);
//                }
//            }
                // 使用线程池执行异步任务
                ExecutorService executor = Executors.newFixedThreadPool(20);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                for (EnquiryPriceQuotationItemVo item : quotationItems) {
                    futures.add(CompletableFuture.runAsync(() -> {
                        String productPrice = item.getQuotationProductPrice();
                        if (AppUtils.isNotBlank(productPrice) && !isValidAmount(productPrice)) {
                            item.setQuotationProductPrice(decryptPrice(privateKey,productPrice));
                        }
                        String itemTotalPrice = item.getQuotationTotalPrice();
                        if (AppUtils.isNotBlank(itemTotalPrice) && !isValidAmount(itemTotalPrice)) {
                            item.setQuotationTotalPrice(decryptPrice(privateKey,itemTotalPrice));
                        }
                    }, executor));
                }
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            }
        }

        result.setEnquiryPriceQuotationItems(quotationItems);
        return ResResultManager.setResultSuccess(result);
    }

    // 假设的方法，用于判断字符串是否是有效的金额格式（两位小数的数字）
    private boolean isValidAmount(String amount) {
        // 使用正则表达式验证金额格式
        return amount.matches("\\d+(\\.\\d{2})?");
    }

    @Override
    public R<QuotationHallVo> queryQuotationInfoByEpId(Long epId, String userId) {
        if (StringUtil.isBlank(userId)) {
            return R.fail("用户未登录！");
        }

        EnquiryPrice enquiryPrice = enquiryPriceDao.getById(epId);
        if (ObjectUtil.isNull(enquiryPrice)) {
            return R.fail("未查询到询价单！");
        }
        List<EnquiryPriceExpert> enquiryPriceExperts =enquiryPriceExpertDao.queryByProperties(new EntityCriterion().eq("epId", epId));
        if (CollectionUtil.isEmpty(enquiryPriceExperts)) {
            return R.fail("该询价单没有评审小组！");
        }

        // 构建 QuotationHallVo 基础信息
        QuotationHallVo quotationHallVo = buildQuotationHallVo(enquiryPrice, enquiryPriceExperts, userId);
        // 构建供应商报价信息列表
        buildSupplierQuotationInfoVoList(quotationHallVo, enquiryPrice, userId);
        // 预评审阶段结束后 查询专家评审信息以及预中选供应商报价信息
        if (quotationHallVo.getEnquiryStatus() >= EnquiryPriceEnum.PRE_EVALUATE.getStatus() ) {
            // 查询与中选供应商报价信息
            buildPreSelectQuotationItemInfoVoList(quotationHallVo, epId);
            // 构建专家评审信息列表
            buildExpertEvaluateResultVoList(quotationHallVo, enquiryPriceExperts, userId);
        }
        return ResResultManager.setResultSuccess(quotationHallVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> submitEvaluateResult(EvaluateResultDTO evaluateResultDTO) {
        EnquiryPrice enquiryPrice = enquiryPriceDao.getById(Long.valueOf(evaluateResultDTO.getEpId()));
        if(ObjectUtil.isNull(enquiryPrice)) {
            return R.fail("未查询到询价单！");
        }
        // 询价单状态异常
        if(EnquiryPriceEnum.PRE_EVALUATE.getStatus() != enquiryPrice.getStatus()) {
            return R.fail("询价单状态异常，请联系管理员查看！");
        }
        EnquiryPriceExpert enquiryPriceExpert = enquiryPriceExpertDao.getByProperties(new EntityCriterion().eq("epId",Long.valueOf(evaluateResultDTO.getEpId())).eq("userId",evaluateResultDTO.getUserId()));
        if(ObjectUtil.isNull(enquiryPriceExpert)) {
            return R.fail("该账号不在此询价单的评审小组内！");
        }
        if(enquiryPriceExpert.getComplete()==10) {
            return R.fail("已提交评审结果！");
        }
        enquiryPriceExpert.setReviewTime(new Date());
        String remark = enquiryPriceExpert.getName()+(enquiryPriceExpert.getComplete()==20?"重新":"")+"提交评审结果";
        if(evaluateResultDTO.getFilePath() != null) {
            //todo 专家传文件
            //上传保存附件
//            saveEnquiryPriceAttachments(evaluateResultDTO.getEpId(),evaluateResultDTO.getFile(), EnquiryPriceAttachmentsTypeEnum.Review_Info_Att.getCode());
//            enquiryPriceExpert.setFilePath(uploadClient.upload(evaluateResultDTO.getFile(), SecurityConstants.FROM_IN).getResult());
            enquiryPriceExpert.setFilePath(evaluateResultDTO.getFilePath());
        }
        enquiryPriceExpert.setRemark(evaluateResultDTO.getRemark());
        enquiryPriceExpert.setSelectOpinion(evaluateResultDTO.getOpinion());
        enquiryPriceExpert.setComplete(10);
        List<ExpertSelectProd> expertSelectProds = new ArrayList<>();
        List<EvaluateResultDTO.EvaluateItemDTO> evaluateItemDTOS = JSONArray.parseArray(evaluateResultDTO.getItemList(), EvaluateResultDTO.EvaluateItemDTO.class);
        for(EvaluateResultDTO.EvaluateItemDTO evaluateItemDTO:evaluateItemDTOS) {
            ExpertSelectProd expertSelectProd = new ExpertSelectProd();
            expertSelectProd.setEpItemId(evaluateItemDTO.getEpItemId());
            expertSelectProd.setEpExpertId(enquiryPriceExpert.getExpertId());
            expertSelectProd.setEpId(Long.valueOf(evaluateResultDTO.getEpId()));
            expertSelectProd.setQuotationItemId(evaluateItemDTO.getQuItemId());
            expertSelectProds.add(expertSelectProd);
        }
        expertSelectProdDao.save(expertSelectProds);
        enquiryPriceExpertDao.update(enquiryPriceExpert);
        saveWorkFlow(XBJWorkFlowTypeEnum.SUBMIT_EVALUATE.getCode(), enquiryPrice.getEpName(), Long.valueOf(evaluateResultDTO.getEpId()), evaluateResultDTO.getUserId(), enquiryPriceExpert.getName(),remark,null);
        return R.ok();
    }

    @Override
    public R submitQuotation(EnquiryPriceShopVo enquiryPriceShopVo) {
        EnquiryPrice enquiryPrice = enquiryPriceDao.getById(enquiryPriceShopVo.getEpId());
        // 如果 询价结束时间小于当前时间， 状态大于开始报价 不能提交
        if ( enquiryPrice.getInquiryDeadTime().before(new Date()) || enquiryPrice.getStatus() > EnquiryPriceEnum.START_QUOTE.getStatus()  ){
            return R.fail("报价单超过截至时间,不能再次提交");
        }

        ShopDetail shop = shopDetailServiceClient.getShopDetailByShopId(enquiryPriceShopVo.getShopId()).getResult();
        // 删除原来的报价信息
        List<Quotation> quotationList = quotationDao.getQuotationByEpIdAndShopId(enquiryPriceShopVo.getEpId(),enquiryPriceShopVo.getShopId());
        if (CollectionUtil.isNotEmpty(quotationList)) {
            quotationDao.delete(quotationList);
           quotationList.forEach(
                   quotation -> {
                      quotationItemDao.deleteByQuotationId(quotation.getId());
                   }
           );
        }

        Long quotationId;
        // 如果之前没有保存，保存一下
        quotationId = saveQuotation(enquiryPriceShopVo);

        //保存附件
        if (AppUtils.isNotBlank(enquiryPriceShopVo.getSupplierFile())){
            saveQuotationAttachmentsList(enquiryPriceShopVo.getEpId(),quotationId,enquiryPriceShopVo.getSupplierFile(),EnquiryPriceAttachmentsTypeEnum.Quotation_Att.getCode());
        }

        quotationDao.updateStatusByQuotationId(quotationId,QuotationStatusEnum.HAS_QUOTATION.getStatus());
        // 添加日志
        saveWorkFlow(XBJWorkFlowTypeEnum.SUPPLIER_QUOTE.getCode(),enquiryPrice.getEpName(),enquiryPrice.getId(),shop.getUserId(),shop.getSiteName(),shop.getSiteName()+"提交报价单",null);
        return ResResultManager.setResultSuccess();

    }

    @Override
    public List<QuotationItemInfo> queryQuotationInfoByQuotation(Long quotationId) {
        return quotationDao.queryQuotationInfoByQuotation(quotationId);

    }


    @Override
    public R finshEvaluateAndSubmit(EnquiryPriceResultSubmitDTO evaluateResultDTO) {
        // 如果还有专家没有评审的，不能提交
        List<EnquiryPriceExpert> enquiryPriceExperts = enquiryPriceExpertDao.queryByProperties(new EntityCriterion().eq("epId",Long.valueOf(evaluateResultDTO.getEpId())));
        for (EnquiryPriceExpert enquiryPriceExpert: enquiryPriceExperts) {
            if(10 !=enquiryPriceExpert.getComplete()) {
                return R.fail("还有专家没有提交评审结果！，请检查之后重试");
            }
        }

        //1.生成询价结果
        log.info("开始保存询价结果！");
        EnquiryPrice enquiryPrice = enquiryPriceDao.getById(Long.valueOf(evaluateResultDTO.getEpId()));
        List<Long> selectedItems = evaluateResultDTO.getItemList().stream().map(EnquiryPriceResultSubmitDTO.EvaluateItemDTO::getQuItemId).collect(Collectors.toList());
        List<QuotationItem> quotationItemList = quotationItemDao.queryAllByIds(selectedItems);
        Map<Long, EnquiryPriceResultSubmitDTO.EvaluateItemDTO> selectedItemMap =  evaluateResultDTO.getItemList().stream().collect(Collectors.toMap(EnquiryPriceResultSubmitDTO.EvaluateItemDTO::getQuItemId, Function.identity()));
        //检查结果是否正常
        if (quotationItemList.size() != evaluateResultDTO.getItemList().size()){
            return R.fail("询价单结果项次与原项次不一致！");
        }else {
            for ( QuotationItem quotationItem : quotationItemList) {
                if(!Objects.equals(selectedItemMap.get(quotationItem.getId()).getEpItemId(), quotationItem.getEpItemId())) {
                    return R.fail("报价单项次异常！");
                }
                quotationItem.setIsDeal(Boolean.TRUE);
            }
        }
        enquiryPrice.setStatus(EnquiryPriceEnum.EVALUATE_PASS.getStatus());
        // 提交最终评审后更新最终结束时间
        enquiryPrice.setInquiryEndTime(new Date());
//        List<Long> quoIds = quotationItemList.stream().map(QuotationItem::getQuotationId).distinct().collect(Collectors.toList());
//        List<Quotation> quotations = quotationDao.queryAllByIds(quoIds);
//        List<String> shopNameList = quotations.stream().map(Quotation::getShopName).collect(Collectors.toList());
//        String winningSuppliers = String.join(",",shopNameList);
        quotationItemDao.update(quotationItemList);
        enquiryPriceDao.update(enquiryPrice);
        //2.保存日志
        Map<String, Object> params = new HashMap<>();
        params.put("epId", Long.valueOf(evaluateResultDTO.getEpId()));
        params.put("userId", evaluateResultDTO.getUserId());
        Expert expert = expertClient.queryExpertListByEpId(params, SecurityConstants.FROM_IN).getResult();
        saveWorkFlow(XBJWorkFlowTypeEnum.SUBMIT_FINAL_RESULT.getCode(), enquiryPrice.getEpName(), enquiryPrice.getId(), evaluateResultDTO.getUserId(), expert.getName(),expert.getName()+"提交最终评审结果",null);



        return R.ok();
    }

    /**
     * 回滚最终评审结果
     *
     * 该函数用于处理询价单的最终评审结果回滚操作。首先检查所有专家是否已经提交评审结果，如果还有未提交的专家，则返回失败。
     * 然后生成询价结果，并检查询价单结果项次是否与原项次一致。如果一致，则更新询价单状态和最终结束时间，并保存日志。
     *
     * @param evaluateResultDTO 包含询价单ID、用户ID和评审项次列表的DTO对象
     * @return 返回操作结果，成功返回R.ok()，失败返回R.fail()并附带错误信息
     */
    @Override
    public R rollbackFinalResult(ResetEvaluateDTO evaluateResultDTO) {
        // 检查所有专家是否已经提交评审结果
        List<EnquiryPriceExpert> enquiryPriceExperts = enquiryPriceExpertDao.queryByProperties(new EntityCriterion().eq("epId", Long.valueOf(evaluateResultDTO.getEpId())));
        for (EnquiryPriceExpert enquiryPriceExpert : enquiryPriceExperts) {
            if (10 != enquiryPriceExpert.getComplete()) {
                return R.fail("还有专家没有提交评审结果！，请检查之后重试");
            }
        }

        // 1. 回滚询价结果
        log.info("开始回滚询价结果！");
        EnquiryPrice enquiryPrice = enquiryPriceDao.getById(evaluateResultDTO.getEpId());
        List<ApprovalFlowDTO> approvalFlowDTOList = ngNewIndustryOAClient.queryApprovalFlowByEpId(evaluateResultDTO.getEpId()).getResult();
        if(ObjectUtil.isNotEmpty(approvalFlowDTOList) || !approvalFlowDTOList.isEmpty()) {
            return R.fail("该询价单已提交至OA评审，请勿重复提交！");
        }

        List<DealQuotationItemVo> quotationItemList = quotationItemDao.queryUsefulItemByEnquiryPriceId(evaluateResultDTO.getEpId());
        if (quotationItemList  == null){
            return R.fail("询价单结果项次为空！");
        }
        List<Long> quotationItemIds = quotationItemList.stream().map(DealQuotationItemVo::getId).collect(Collectors.toList());



        // 回滚询价单状态
        enquiryPrice.setStatus(EnquiryPriceEnum.START_EVALUATE.getStatus()); // 回滚到预评审状态
        enquiryPrice.setInquiryEndTime(null); // 清空最终结束时间

        // 更新询价单和报价单项
        quotationItemDao.rollBackQuotationItemsDealStatus(quotationItemIds);
        enquiryPriceDao.update(enquiryPrice);

        // 2.清除最终结果报告日志
        enquiryPriceAttachmentsDao.deleteByType(evaluateResultDTO.getEpId(),EnquiryPriceAttachmentsTypeEnum.Final_Result_Att.getCode());

        // 3. 保存日志
        Map<String, Object> params = new HashMap<>();
        params.put("epId", evaluateResultDTO.getEpId());
        params.put("userId", evaluateResultDTO.getUserId());
        Expert expert = expertClient.queryExpertListByEpId(params, SecurityConstants.FROM_IN).getResult();
        saveWorkFlow(XBJWorkFlowTypeEnum.ROLLBACK_FINAL_RESULT.getCode(), enquiryPrice.getEpName(), enquiryPrice.getId(), evaluateResultDTO.getUserId(), expert.getName(), expert.getName() + "回滚最终评审结果", null);

        // 4.回退初始状态
        resetEvaluate(evaluateResultDTO);

        return R.ok();
    }

    public R<String> resetEvaluate(ResetEvaluateDTO resetEvaluateDTO) {
        if(ObjectUtil.isEmpty(resetEvaluateDTO)){
            return R.fail("发起重新审批参数异常！");
        }
        Long epId = resetEvaluateDTO.getEpId();
        String userId = resetEvaluateDTO.getUserId();
        String reason = resetEvaluateDTO.getReason();
        //查询该询价单是否有专家审批
        List<Expert> experts = enquiryPriceExpertDao.queryJudgeMembersByEpId(epId);
        if(ObjectUtil.isEmpty(experts)) {
            return R.fail("当前询价单未添加评审员！");
        }
        EnquiryPrice enquiryPrice = enquiryPriceDao.getById(epId);
        log.info("重置{}询价单评审状态状态！", epId);

        List<ExpertSelectProd> oldExpertSelectProds = expertSelectProdDao.queryByProperties(new EntityCriterion().eq("epId",epId));
        List<EnquiryPriceExpert> oldEnquiryPriceExperts = enquiryPriceExpertDao.queryByProperties(new EntityCriterion().eq("epId",epId));
        // 如果是管理员发起，则判断是否已经添加专家审批
        if (ObjectUtil.isEmpty(oldEnquiryPriceExperts) && "admin".equals(resetEvaluateDTO.getType())){
            return R.fail("当前询价单暂无专家审批！");
        }
        if (ObjectUtil.isEmpty(oldExpertSelectProds) && "admin".equals(resetEvaluateDTO.getType())){
            return R.fail("当前询价单暂无专家审批！");
        }
        // 如果为个人发起，则判断当前账号是否在评审小组内
        Optional<EnquiryPriceExpert> optionalExpert = oldEnquiryPriceExperts.stream()
                .filter(expert -> expert.getUserId().equals(resetEvaluateDTO.getUserId()))
                .findFirst();

        if (!optionalExpert.isPresent() && "user".equals(resetEvaluateDTO.getType())) {
            return R.fail("当前账号不在此询价单的评审小组内！");
        }

        // 先清除专家选择记录
        int deleteNums = expertSelectProdDao.delete(oldExpertSelectProds);
        // 再清除专家绑定记录
        oldEnquiryPriceExperts.forEach(
                epExpert -> {
                    epExpert.setComplete(20);
                    epExpert.setSelectOpinion(null);
                    epExpert.setReviewTime(null);
                    epExpert.setRemark(null);
                }
        );
        enquiryPriceExpertDao.update(oldEnquiryPriceExperts) ;

        // 上传文件
        if (resetEvaluateDTO.getFileList() != null && !resetEvaluateDTO.getFileList().isEmpty()) {
            resetEvaluateDTO.getFileList().forEach(file -> saveEnquiryPriceAttachments(epId,uploadClient.upload(file, SecurityConstants.FROM_IN).getResult(),file.getName(),EnquiryPriceAttachmentsTypeEnum.Re_Review_Att.getCode()));
        }

        return ResResultManager.setResultSuccess();
    }


    @Override
    public void updateQuotationProduct(String skuId) {
        quotationItemDao.updateStatusBySkuId(skuId,10);
    }

    @Override
    public R enquiryPriceOASubmit(EnquiryPriceResultSubmitDTO evaluateResultDTO,List<MultipartFile> files) {

        List<ApprovalFlowDTO> approvalFlowDTOList = ngNewIndustryOAClient.queryApprovalFlowByEpId(Long.valueOf(evaluateResultDTO.getEpId())).getResult();
        if((ObjectUtil.isNotEmpty(approvalFlowDTOList)) && !evaluateResultDTO.getIsResubmit()) {
            return R.fail("该询价单已提交至OA评审，请勿重复提交！");
        }

        EnquiryPrice enquiryPrice= enquiryPriceDao.getById(evaluateResultDTO.getEpId());

        //1.保存审批单记录
        ApprovalFlowDTO approvalFlowDTO = new ApprovalFlowDTO();
        approvalFlowDTO.setApprovalTime(new Date());
        approvalFlowDTO.setEpId(evaluateResultDTO.getEpId());
        approvalFlowDTO.setEpNo(enquiryPrice.getEpNo());
//        approvalFlowDTO.setRealName();
        approvalFlowDTO.setStatus(10);
//        String content = "本次电商询价项目"+enquiryPrice.getEpNo()+enquiryPrice.getEpNo()+"，经评议小组综合评议，供应商 "+winningSuppliers+" 在技术和商务方面均满足本次询价要求，本次报价价格在合理范围（报价详情见附件文件），故拟定为成交供应商。\n" +
//                "\n" +
//                "特此报告！妥否？请审批。";
        approvalFlowDTO.setContent(evaluateResultDTO.getContent());
        approvalFlowDTO.setTitle(evaluateResultDTO.getTitle());
        approvalFlowDTO.setOaRemark(evaluateResultDTO.getRemark());
        approvalFlowDTO.setEpName(enquiryPrice.getEpName());
        approvalFlowDTO.setUserId(evaluateResultDTO.getUserId());
        approvalFlowDTO.setEpUserName(enquiryPrice.getClientNickName());
        approvalFlowDTO.setRealName(evaluateResultDTO.getUserName());
        approvalFlowDTO.setApprovalTime(new Date());
        approvalFlowDTO.setUpdateTime(new Date());


        //3.提交OA审批
        log.info("提交OA审批，审批单内容：{}",approvalFlowDTO);

        CreateXbjWorkFlowDTO createXbjWorkFlowDTO = new CreateXbjWorkFlowDTO();
        createXbjWorkFlowDTO.setTitle(evaluateResultDTO.getTitle());
        createXbjWorkFlowDTO.setContent(evaluateResultDTO.getContent());
        List<ApprovalFlowFileDTO> approvalFlowFileDTOS = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(files)) {
            for (MultipartFile file : files) {
                UploadFileRequest uploadFileRequest = systemParameterService.getSystemParameterByName(NgOaParamterConstant.NG_NEW_INDUSTRY_OA_UPLOAD_FILE_PARAMS, UploadFileRequest.class);
                uploadFileRequest.setName(file.getOriginalFilename());
                uploadFileRequest.setFile(file);
                UploadFileResponse uploadFileResponse = workFlowService.uploadFile(uploadFileRequest);
                if (!ResponseCodeEnum.SUCCESS.equals(ResponseCodeEnum.findByCode(uploadFileResponse.getMessage().getErrcode()))) {
                    log.info("OA上传文件失败！失败原因：" + uploadFileResponse.getMessage().getErrmsg() + "  错误码：" + uploadFileResponse.getMessage().getErrcode());
                    return R.fail("OA上传文件失败！失败原因：" + uploadFileResponse.getMessage().getErrmsg());
                }
                ApprovalFlowFileDTO approvalFlowFileDTO = new ApprovalFlowFileDTO();
                approvalFlowFileDTO.setFileName(uploadFileResponse.getData().getName());
                approvalFlowFileDTO.setFileType(uploadFileResponse.getData().getType());
                approvalFlowFileDTO.setFileId(uploadFileResponse.getData().getFileid());
                approvalFlowFileDTOS.add(approvalFlowFileDTO);
                createXbjWorkFlowDTO.setCreatorRealName(uploadFileResponse.getData().getUploadUserName());
            }
            String fileStr = JSONObject.toJSONString(approvalFlowFileDTOS);
            createXbjWorkFlowDTO.setFiles(approvalFlowFileDTOS);
            approvalFlowDTO.setOaFiles(fileStr);
        }
        EnquiryPriceExpert enquiryPriceExpert = enquiryPriceExpertDao.getByProperties(new EntityCriterion().eq("epId",enquiryPrice.getId()).eq("agency",1));
        createXbjWorkFlowDTO.setProjNo(enquiryPrice.getEpNo());
        createXbjWorkFlowDTO.setProjManager(enquiryPriceExpert.getName());
        createXbjWorkFlowDTO.setProjName(enquiryPrice.getEpName());
        createXbjWorkFlowDTO.setProjTotalPrice(enquiryPrice.getEpAmount());
        createXbjWorkFlowDTO.setXbjdw(enquiryPrice.getClientCompany());
        createXbjWorkFlowDTO.setZxbz(evaluateResultDTO.getRemark());

        List<DealQuotationItemVo> quotationItemVoList = quotationItemDao.queryUsefulItemByEnquiryPriceId(enquiryPrice.getId());
        List<ApprovalFlowCreateRequest.DataDetail> dataDetails = new ArrayList<>();
        String newDataDetailStr = systemParameterService.getSystemParameterByName(NgOaParamterConstant.NG_NEW_INDUSTRY_OA_CREATE_WORKFLOW_TABLE, String.class);
        List<ApprovalFlowCreateRequest.DataDetail> newDataDetails = JSONObject.parseArray(newDataDetailStr, ApprovalFlowCreateRequest.DataDetail.class);
        List<EnquiryPriceItem> enquiryPriceItems = enquiryPriceItemDao.queryByProperties(new EntityCriterion().eq("epId",enquiryPrice.getId()));
        Map<Long,EnquiryPriceItem> enquiryPriceItemMap = enquiryPriceItems.stream().collect(Collectors.toMap(EnquiryPriceItem::getId, Function.identity()));

        BigDecimal totalPrice = BigDecimal.ZERO;
        List<String> winners = new ArrayList<>();
        Long i = 1L;
        for (DealQuotationItemVo dealQuotationItemVo : quotationItemVoList) {
            List<ApprovalFlowCreateRequest.DataDetail> colmnDataDetail = new ArrayList<>();
            String winnerShopName = "";

            // 检查 dealQuotationItemVo 是否为空
            if (dealQuotationItemVo == null) {
                log.error("dealQuotationItemVo is null, skipping this item.");
                continue;
            }

            for (ApprovalFlowCreateRequest.DataDetail dataDetail : newDataDetails) {
                ApprovalFlowCreateRequest.DataDetail newDataDetail = new ApprovalFlowCreateRequest.DataDetail();
                newDataDetail.setDataKey(dataDetail.getDataKey());
                newDataDetail.setDataIndex(i);
                newDataDetail.setSubFormId(dataDetail.getSubFormId());

                // 检查 dataDetail.getDataKey() 是否为空
                if (dataDetail.getDataKey() == null) {
                    log.error("dataDetail.getDataKey() is null, skipping this dataDetail.");
                    continue;
                }

                // 获取枚举值并进行 null 检查
                ApprovalFlowFeildEnum flowFeildEnum = ApprovalFlowFeildEnum.getEnumByValue(dataDetail.getDataKey());
                if (flowFeildEnum == null) {
                    log.error("No enum value found for dataKey: " + dataDetail.getDataKey());
                    continue;
                }

                switch (flowFeildEnum) {
                    case SPM:
                        // 检查 enquiryPriceItemMap 和 dealQuotationItemVo.getEpItemId() 是否为空
                        if (enquiryPriceItemMap == null || dealQuotationItemVo.getEpItemId() == null) {
                            log.error("enquiryPriceItemMap or dealQuotationItemVo.getEpItemId() is null.");
                            break;
                        }
                        newDataDetail.setContent(enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getProductName());
                        break;
                    case XJGGXH:
                        if (enquiryPriceItemMap == null || dealQuotationItemVo.getEpItemId() == null) {
                            log.error("enquiryPriceItemMap or dealQuotationItemVo.getEpItemId() is null.");
                            break;
                        }
                        newDataDetail.setContent(enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getProductModel());
                        break;
                    case YGDJY:
                        if (enquiryPriceItemMap == null || dealQuotationItemVo.getEpItemId() == null) {
                            log.error("enquiryPriceItemMap or dealQuotationItemVo.getEpItemId() is null.");
                            break;
                        }
                        newDataDetail.setContent(enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getProductPrice() == null ? "无" : enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getProductPrice().toString());
                        break;
                    case SL:
                        if (enquiryPriceItemMap == null || dealQuotationItemVo.getEpItemId() == null) {
                            log.error("enquiryPriceItemMap or dealQuotationItemVo.getEpItemId() is null.");
                            break;
                        }
                        newDataDetail.setContent(enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getProductQuantity().toString());
                        break;
                    case JLDW:
                        if (enquiryPriceItemMap == null || dealQuotationItemVo.getEpItemId() == null) {
                            log.error("enquiryPriceItemMap or dealQuotationItemVo.getEpItemId() is null.");
                            break;
                        }
                        newDataDetail.setContent(enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getProductUnit());
                        break;
                    case ZXDWMC:
                        // 检查 dealQuotationItemVo.getShopId() 是否为空
                        if (dealQuotationItemVo.getShopId() == null) {
                            log.error("dealQuotationItemVo.getShopId() is null.");
                            break;
                        }
                        ShopDetail shopDetail = shopDetailServiceClient.getShopDetailByShopId(dealQuotationItemVo.getShopId()).getResult();
                        if (shopDetail == null) {
                            log.error("shopDetail is null for shopId: {}", dealQuotationItemVo.getShopId());
                            break;
                        }
                        winnerShopName = shopDetail.getSiteName();
                        newDataDetail.setContent(winnerShopName);
                        break;
                    case ZXGGXH:
                        newDataDetail.setContent(dealQuotationItemVo.getProductModel());
                        break;
                    case ZXDJY:
                        newDataDetail.setContent(dealQuotationItemVo.getProductPrice());
                        break;
                    case ZXCPPP:
                        newDataDetail.setContent(dealQuotationItemVo.getProductBrand());
                        break;
                    case ZXSL:
                        if (enquiryPriceItemMap == null || dealQuotationItemVo.getEpItemId() == null) {
                            log.error("enquiryPriceItemMap or dealQuotationItemVo.getEpItemId() is null.");
                            break;
                        }
                        newDataDetail.setContent(enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getProductQuantity().toString());
                        break;
                    case ZXJLDW:
                        newDataDetail.setContent(dealQuotationItemVo.getProductUnit());
                        break;
                    case BZ:
                        newDataDetail.setContent(StringUtil.isBlank(dealQuotationItemVo.getRemark()) ? "无" : dealQuotationItemVo.getRemark());
                        break;
                    case XBJZJY:
                        if (enquiryPriceItemMap == null || dealQuotationItemVo.getEpItemId() == null) {
                            log.error("enquiryPriceItemMap or dealQuotationItemVo.getEpItemId() is null.");
                            break;
                        }
                        newDataDetail.setContent(enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getTotalPrice() == null ? "无" : enquiryPriceItemMap.get(dealQuotationItemVo.getEpItemId()).getTotalPrice().toString());
                        break;
                    case ZXZJY:
                        newDataDetail.setContent(dealQuotationItemVo.getTotalPrice());
                        break;
                    default:
                        log.info("有新字段！请添加新字段2！");
                        throw new RuntimeException("有新字段！请添加新字段！！");
                }
                colmnDataDetail.add(newDataDetail);
            }

            // 检查 dealQuotationItemVo.getTotalPrice() 是否为空
            if (dealQuotationItemVo.getTotalPrice() == null) {
                log.error("dealQuotationItemVo.getTotalPrice() is null.");
                continue;
            }

            totalPrice = totalPrice.add(new BigDecimal(dealQuotationItemVo.getTotalPrice()));
            if (ObjectUtil.isEmpty(winners) || !winners.contains(winnerShopName)) {
                winners.add(winnerShopName);
            }
            // 打印 colmnDataDetail 为 JSON 格式
            String jsonStr = JSONObject.toJSONString(colmnDataDetail, true); // true 表示格式化输出
            log.info("colmnDataDetail: {}", jsonStr);
            dataDetails.addAll(colmnDataDetail);
            i++;
        }
        createXbjWorkFlowDTO.setWinnerTotalPrice(totalPrice.divide(new BigDecimal(10000),6,BigDecimal.ROUND_HALF_UP));
        createXbjWorkFlowDTO.setWinner(String.join(",", winners));
        createXbjWorkFlowDTO.setDataDetails(dataDetails);

        ApprovalFlowCreateRequest approvalFlowCreateRequest = systemParameterService.getSystemParameterByName(NgOaParamterConstant.NG_NEW_INDUSTRY_OA_CREATE_WORKFLOW_PARAMS, ApprovalFlowCreateRequest.class);
        
        // 获取新产业OA的用户ID
        try {
            approvalFlowCreateRequest.setUserid(getProjectManagerOaIdByEpId(evaluateResultDTO.getEpId()));
        } catch (Exception e) {
            log.error("获取新产业OA用户ID异常，使用原来的方法：", e);
        }

        ApprovalFlowCreateResponse approvalFlowCreateResponse = workFlowService.createApprovalFlow(createXbjWorkFlowDTO,approvalFlowCreateRequest);
        if(!ResponseCodeEnum.SUCCESS.equals(ResponseCodeEnum.findByCode(approvalFlowCreateResponse.getMessage().getErrcode()))) {
            log.info("OA创建审批流失败！失败原因："+approvalFlowCreateResponse.getMessage().getErrmsg()+"  错误码："+approvalFlowCreateResponse.getMessage().getErrcode());
            return R.fail("OA创建审批流失败！失败原因："+approvalFlowCreateResponse.getMessage().getErrmsg());
        }
        approvalFlowDTO.setApprovalFlowId(approvalFlowCreateResponse.getMessage().getRequestId());
        approvalFlowDTO.setStatus(20);
        R<Long> approvalFlowRe = ngNewIndustryOAClient.createApprovalFlow(approvalFlowDTO);
        if(!approvalFlowRe.isSuccess()) {
            return R.fail("提交OA审批失败！");
        }
        // 提交oa会审后增加中间状态
        enquiryPrice.setStatus(EnquiryPriceEnum.OA_REVIEW.getStatus());
        // 提交最终评审后更新最终结束时间
        enquiryPriceDao.update(enquiryPrice);

        saveWorkFlow(XBJWorkFlowTypeEnum.SUBMIT_OA_APPROVAL_FLOW.getCode(), enquiryPrice.getEpName(), enquiryPrice.getId(), evaluateResultDTO.getUserId(), evaluateResultDTO.getUserName(),evaluateResultDTO.getUserName()+"提交OA审批",null);
        return R.ok();
    }

    // 解密
    private String decryptPrice(String privateKey,String param) {
        try {
            return RSAUtils.decryptByPrivateKey(privateKey,param);
        } catch (Exception e) {
            log.info("解密失败！！！");
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建报价大厅基础信息
     *
     * @param enquiryPrice 询价单信息
     * @param enquiryPriceExperts 询价单专家列表
     * @param userId 当前用户ID
     * @return 返回构建好的QuotationHallVo对象
     */
    private QuotationHallVo buildQuotationHallVo(EnquiryPrice enquiryPrice, List<EnquiryPriceExpert> enquiryPriceExperts, String userId) {
        QuotationHallVo quotationHallVo = new QuotationHallVo();
        quotationHallVo.setEnquiryId(enquiryPrice.getId());
        quotationHallVo.setEnquiryNo(enquiryPrice.getEpNo());
        quotationHallVo.setEnquiryName(enquiryPrice.getEpName());
        quotationHallVo.setEnquiryTotalAmount(enquiryPrice.getEpAmount());
        quotationHallVo.setEnquiryStatus(enquiryPrice.getStatus());

        Date now = new Date();
        boolean showPrice = (enquiryPrice.getInquiryEndTime() != null && !enquiryPrice.getInquiryEndTime().after(now) && EnquiryPriceEnum.START_EVALUATE.getStatus() <= enquiryPrice.getStatus());
        quotationHallVo.setShowPrice(showPrice);

        //针对用户端 根据询询价单状态获取当前专家的评审信息
        if (EnquiryPriceEnum.PRE_EVALUATE.getStatus().equals(enquiryPrice.getStatus())) {
            // 查看当前登录人是否是评审专家
            EnquiryPriceExpert myExpertRole = enquiryPriceExperts.stream()
                    .filter(expert -> userId.equals(expert.getUserId()))
                    .findFirst()
                    .orElse(null);;
            if (ObjectUtil.isNull(myExpertRole)) {
                quotationHallVo.setIsExpertRole(Boolean.FALSE);
            } else {
                quotationHallVo.setIsExpertRole(Boolean.TRUE);
                //当前登录人如果是专家的话 获取当前登录人的评审结果
                MyEvaluateResultVo myEvaluateResultVo = buildMyEvaluateResultVo(myExpertRole, enquiryPrice.getStatus(), enquiryPrice.getId());
                quotationHallVo.setMyEvaluateResultVo(myEvaluateResultVo);
            }
        }
        return quotationHallVo;
    }

    /**
     * 构建我的评价结果视图对象
     *
     * @param myExpertRole 询价专家角色信息，用于获取专家的完成状态和选择的意见等
     * @param enquiryStatus 询价状态，用于判断是否可以进行评价
     * @param epId 询价专家ID，用于查询专家选择的产品信息
     * @return 返回一个构建好的MyEvaluateResultVo对象，包含专家的评价信息和选择的产品信息
     */
    private MyEvaluateResultVo buildMyEvaluateResultVo(EnquiryPriceExpert myExpertRole, Integer enquiryStatus, Long epId) {
        MyEvaluateResultVo myEvaluateResultVo = new MyEvaluateResultVo();
        boolean canJudge = EnquiryPriceEnum.PRE_EVALUATE.getStatus() == enquiryStatus && (myExpertRole.getComplete() == 0 || myExpertRole.getComplete() == 20);
        Integer opinion = myExpertRole.getSelectOpinion();

        if (!canJudge) {
            opinion = Optional.ofNullable(opinion)
                    .filter(op -> op == 10 || op == 20)
                    .orElse(0);
        }

        List<ExpertSelectProd> expertSelectProds = expertSelectProdDao.queryByProperties(new EntityCriterion().eq("epId", epId).eq("epExpertId", myExpertRole.getExpertId()));
        if (CollectionUtil.isNotEmpty(expertSelectProds)) {
            List<MyEvaluateResultVo.EvaluateItemDTO> chooseList = expertSelectProds.stream()
                    .map(expertSelectProd -> {
                        MyEvaluateResultVo.EvaluateItemDTO evaluateItemDTO = new MyEvaluateResultVo.EvaluateItemDTO();
                        evaluateItemDTO.setQuoItemId(expertSelectProd.getQuotationItemId());
                        evaluateItemDTO.setEpItemId(expertSelectProd.getEpItemId());
                        return evaluateItemDTO;
                    })
                    .collect(Collectors.toList());
            myEvaluateResultVo.setChooseList(chooseList);
        }
        myEvaluateResultVo.setCanJudge(canJudge);
        myEvaluateResultVo.setOpinion(opinion);
        myEvaluateResultVo.setFile(myExpertRole.getFilePath());
        myEvaluateResultVo.setRemark(myExpertRole.getRemark());
        return myEvaluateResultVo;
    }


    /**
     * 构建供应商报价信息列表
     *
     * 根据询价单和报价信息，构建供应商报价信息视图列表，并根据是否显示价格的标志决定是否隐藏价格信息
     *
     * @param quotationHallVo 询价大厅视图对象，包含是否显示价格的标志
     * @param enquiryPrice 询价单对象，用于获取询价单项和报价信息
     * @param userId 用户ID，目前未使用，但保留以备将来可能的需求
     */
    private void buildSupplierQuotationInfoVoList(QuotationHallVo quotationHallVo, EnquiryPrice enquiryPrice, String userId) {
        Long epId = enquiryPrice.getId();
        List<EnquiryPriceItem> enquiryPriceItems = enquiryPriceItemDao.queryByProperties(new EntityCriterion().eq("epId", epId));
        List<Quotation> quotationList = quotationDao.queryByProperties(new EntityCriterion().eq("epId", epId).notEq("status", QuotationStatusEnum.REVIEWING.getStatus()));
        // 查看报价时 有可能会出现报价未解密的情况
        // 筛选出 quotationList 中 totalPrice 未加密的部分
        List<Quotation> unencryptedQuotationList = quotationList.stream()
                .filter(quotation -> !isUnencrypted(quotation.getTotalPrice()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(unencryptedQuotationList)){
            String privateKey = systemParameterService.getSystemParameterByName(SmsSystemParamterConstant.ENQUIRY_PRICE_PRIVATE_KEY, String.class);
            for(Quotation quotation : unencryptedQuotationList){
                quotation.setTotalPrice(decryptPrice(privateKey,quotation.getTotalPrice()));
                quotationDao.updateTotalPriceByQuotationId(quotation.getId(),quotation.getTotalPrice());
            }
            List<QuotationItem> quotationItemList = quotationItemDao.queryByProperties(new EntityCriterion().in("quotationId", quotationList.stream().map(Quotation::getId).collect(Collectors.toList())));
            List<QuotationItem> unencryptedQuotationItemList = quotationItemList.stream()
                    .filter(quotationItem -> !isUnencrypted(quotationItem.getTotalPrice()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(unencryptedQuotationItemList)){
                for(QuotationItem quotationItem : unencryptedQuotationItemList){
                    quotationItem.setProductPrice(decryptPrice(privateKey,quotationItem.getProductPrice()));
                    quotationItem.setTotalPrice(decryptPrice(privateKey,quotationItem.getTotalPrice()));
                    quotationItemDao.update(quotationItem);
                }
            }
        }

        List<SupplierQuotationInfoVo> supplierQuotationInfoVoList = quotationConverter.to1List(quotationList);

        for (SupplierQuotationInfoVo vo : supplierQuotationInfoVoList) {
            boolean showPrice = quotationHallVo.getShowPrice();
            if (!showPrice) {
                vo.setTotalAmount(null);
            }

            List<SupplierQuotationItemVo> supplierQuotationItemVoList = buildSupplierQuotationItemVoList(epId, vo.getQuotationId(), showPrice);
            vo.setItemVoList(supplierQuotationItemVoList);
            List<EnquiryPriceAttachments> quotationAttachments = enquiryPriceAttachmentsDao.queryQuotationFilesByType(epId,vo.getQuotationId(),EnquiryPriceAttachmentsTypeEnum.Quotation_Att.getCode());
            if (AppUtils.isNotBlank(quotationAttachments)){
                List<EnquirpyFileDTO> supplierFiles = new ArrayList<>();
                for (EnquiryPriceAttachments fileItem : quotationAttachments) {
                    EnquirpyFileDTO supplierFile = new EnquirpyFileDTO();
                    supplierFile.setName(fileItem.getName());
                    supplierFile.setPath(fileItem.getFilePath());
                    supplierFiles.add(supplierFile);
                }
                vo.setSupplierFile(supplierFiles);
            }
        }

        quotationHallVo.setSupplierQuotationInfoVoList(supplierQuotationInfoVoList);
    }

    /**
     * 构建供应商报价单项列表
     *
     * 该方法用于根据询价单明细和报价单明细构建供应商报价单项视图列表它首先检查这两个列表的大小是否相同，
     * 如果不同，则记录一条信息表明报价单明细不全然后，它将报价单明细列表转换为一个映射，以便通过询价单明细的ID快速查找对应的报价单明细
     * 最后，它遍历询价单明细列表，为每个询价单明细创建一个供应商报价单项视图对象，并根据showPrice参数决定是否设置价格信息
     *
     * @param showPrice 是否显示价格信息
     * @return 供应商报价单项视图列表
     */
    private List<SupplierQuotationItemVo> buildSupplierQuotationItemVoList(Long epId, Long quotationId, boolean showPrice) {
        // 直接通过SQL查询获取数据
        List<SupplierQuotationItemVo> items = quotationItemDao.querySupplierQuotationItemVoList(epId, quotationId);
        
        // 如果不显示价格，则清空价格相关字段
        if (!showPrice) {
            items.forEach(item -> {
                item.setSinglePrice(null);
                item.setAmount(null);
            });
        }
        
        return items;
    }

    /**
     * 构建专家评估结果视图列表
     *
     *
     * @param quotationHallVo 询价大厅视图对象，包含询价相关信息
     * @param enquiryPriceExperts 询价专家列表，包含每个专家的评估信息
     * @param userId 当前用户ID，用于排除当前用户自身的专家信息
     */
    private void buildExpertEvaluateResultVoList(QuotationHallVo quotationHallVo, List<EnquiryPriceExpert> enquiryPriceExperts, String userId) {
            List<ExpertEvaluateResultVo> expertEvaluateResultVoList = new ArrayList<>();
            for (EnquiryPriceExpert enquiryPriceExpert : enquiryPriceExperts) {
				// 不查看代理人评审信息 但是可以查看自己的评审信息
                if (!enquiryPriceExpert.getAgency().equals(1)) {
                    ExpertEvaluateResultVo expertEvaluateResultVo = new ExpertEvaluateResultVo();
                    expertEvaluateResultVo.setEvaluateTime(enquiryPriceExpert.getReviewTime());
                    expertEvaluateResultVo.setExpertOption(enquiryPriceExpert.getSelectOpinion());
                    expertEvaluateResultVo.setExpertRemark(enquiryPriceExpert.getRemark());
                    expertEvaluateResultVo.setRealName(enquiryPriceExpert.getName());
                    expertEvaluateResultVo.setPhone(enquiryPriceExpert.getMobile());
                    expertEvaluateResultVo.setExpertEvaluateStatus(enquiryPriceExpert.getComplete());
                    expertEvaluateResultVoList.add(expertEvaluateResultVo);
                }
            }
            quotationHallVo.setExpertEvaluateResultVoList(expertEvaluateResultVoList);
    }

    private void buildPreSelectQuotationItemInfoVoList(QuotationHallVo quotationHallVo, Long epId){
        List<PreSelectQuotationItemVo> preSelectQuotationItemVoList = new ArrayList<>();
        preSelectQuotationItemVoList = quotationItemDao.queryPreSelectQuotationItemVoList(epId);
        quotationHallVo.setPreSelectQuotationItemVoList(preSelectQuotationItemVoList);
    }

    /**
     * 保存附件表 多个文件走这个
     * @param enquiryPriceId
     * @param fileList
     * @param type
     */
    public  void saveQuotationAttachmentsList(Long enquiryPriceId,Long quotationId,List<EnquirpyFileDTO> fileList ,String type){
        List<EnquiryPriceAttachments> files =  enquiryPriceAttachmentsDao.queryQuotationFilesByType(enquiryPriceId,quotationId,type);
        //如果之前已经保存了，清理掉，并新增。
        if (!files.isEmpty()) {
            enquiryPriceAttachmentsDao.delete(files);
        }
        List<EnquiryPriceAttachments> saveFile = new ArrayList<>();
        for (EnquirpyFileDTO item : fileList) {
            EnquiryPriceAttachments file = new EnquiryPriceAttachments();
            file.setEpId(enquiryPriceId);
            file.setQuotationId(quotationId);
            file.setFilePath(item.getPath());
            file.setName(item.getName());
            file.setType(type);
            file.setStatus(10);
            saveFile.add(file);
        }
        enquiryPriceAttachmentsDao.save(saveFile);
    }

    public static boolean isUnencrypted(String totalPrice) {
        if (totalPrice == null || totalPrice.isEmpty()) {
            return false;
        }
        // 使用正则表达式判断是否为合法的两位小数数字
        return DECIMAL_PATTERN.matcher(totalPrice).matches();
    }

    // 上传附件通用方法
    void saveEnquiryPriceAttachments(Long enquiryPriceId,String innerFilePath,String innerFileName,String type){
        List<EnquiryPriceAttachments> files =  enquiryPriceAttachmentsDao.queryByType(enquiryPriceId,type);
        //如果之前已经保存了，清理掉，并新增。
        if (!files.isEmpty()) {
            enquiryPriceAttachmentsDao.delete(files);
        }
        EnquiryPriceAttachments file = new EnquiryPriceAttachments();
        file.setEpId(enquiryPriceId);
        file.setFilePath(innerFilePath);
        file.setName(innerFileName);
        file.setType(type);
        file.setStatus(10);
        enquiryPriceAttachmentsDao.save(file);
    }


    /**
     * 通过epId获取项目经理的新产业OA ID
     * @param epId 专家ID
     * @return 项目经理的新产业OA ID
     */
    public String getProjectManagerOaIdByEpId(Long epId) {
        try {
            GetUserXbjWorkFlowDTO getUserXbjWorkFlowDTO = new GetUserXbjWorkFlowDTO();
            // 通过询价单id获取项目经理工号
            getUserXbjWorkFlowDTO.setJobNum(enquiryPriceDao.getJobNumByEpId(epId));
            
            ApprovalFlowUserResponse userResponse = workFlowService.getNewIndustryUserId(getUserXbjWorkFlowDTO);
            if (!ResponseCodeEnum.SUCCESS.equals(ResponseCodeEnum.findByCode(userResponse.getMessage().getErrcode()))) {
                log.info("获取新产业OA用户ID失败！失败原因：" + userResponse.getMessage().getErrmsg() + "  错误码：" + userResponse.getMessage().getErrcode());
                return null;
            }
            
            if (userResponse.getData() != null && !userResponse.getData().isEmpty()) {
                // 使用获取到的第一个用户的userid
                return userResponse.getData().get(0).getUserid().toString();
            }
        } catch (Exception e) {
            log.error("获取新产业OA用户ID异常：", e);
        }
        return null;
    }
}
