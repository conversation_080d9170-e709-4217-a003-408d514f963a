package cn.legendshop.order.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.*;
import cn.hutool.http.*;
import cn.hutool.json.JSONUtil;
import cn.legendshop.client.DiyiClient;
import cn.legendshop.common.core.config.ErpSendDataUrlConfig;
import cn.legendshop.common.core.config.ThirdParameterConfig;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.FileUtil;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.model.entity.Area;
import cn.legendshop.common.model.entity.City;
import cn.legendshop.common.model.entity.Province;
import cn.legendshop.common.rabbitmq.constants.ErpOrderMQConstant;
import cn.legendshop.common.rabbitmq.constants.SubTOBlockChainConstant;
import cn.legendshop.common.rabbitmq.constants.ThridIndustrialOrderMQConstant;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyConstant;
import cn.legendshop.common.rabbitmq.utils.AmqpSendMsgUtil;
import cn.legendshop.common.service.LocationService;
import cn.legendshop.common.service.SystemParameterService;
import cn.legendshop.common.utils.CommonServiceUtil;
import cn.legendshop.enums.ObeiSendDataEnum;
import cn.legendshop.jd.iop.sdk.client.OrderClient;
import cn.legendshop.jd.iop.sdk.enums.JdCustomerExpectEnum;
import cn.legendshop.jd.iop.sdk.model.info.OrderTrack;
import cn.legendshop.jd.iop.sdk.model.request.OrderTrackRequest;
import cn.legendshop.jd.iop.sdk.model.response.CommonResponse;
import cn.legendshop.jd.iop.sdk.model.response.OrderTrackResponse;
import cn.legendshop.model.DiyiResponse;
import cn.legendshop.model.request.AsyncOrderRequest;
import cn.legendshop.obei.RemoteObeiClient;
import cn.legendshop.obei.param.ReceiveDeliveryInfoParam;
import cn.legendshop.obei.param.ReceiveDeliveryPurchaserInfoParam;
import cn.legendshop.obei.param.applyInfo;
import cn.legendshop.obei.result.DeliveryDetail;
import cn.legendshop.obei.result.DeliveryDetailItem;
import cn.legendshop.obei.result.GateApplyResult;
import cn.legendshop.order.config.ShipmentConfig;
import cn.legendshop.order.constant.DispatchingCarsStatusEnum;
import cn.legendshop.order.constant.*;
import cn.legendshop.order.constants.RedissonConstants;
import cn.legendshop.order.dao.*;
import cn.legendshop.order.dto.*;
import cn.legendshop.order.dto.afterSales.AfterSaleShipmentDTO;
import cn.legendshop.order.entity.ErpArriveSite;
import cn.legendshop.order.entity.SmartLockerReceive;
import cn.legendshop.order.entity.SubThirdInfo;
import cn.legendshop.order.enums.*;
import cn.legendshop.order.exception.*;
import cn.legendshop.order.model.*;
import cn.legendshop.order.mq.producer.OrderProducerService;
import cn.legendshop.order.service.MessagePoolShipmentService;
import cn.legendshop.order.service.ShipmentService;
import cn.legendshop.order.service.StockService;
import cn.legendshop.order.service.SubService;
import cn.legendshop.order.service.convert.*;
import cn.legendshop.order.vo.*;
import cn.legendshop.payment.enums.ContractPayTypeEnum;
import cn.legendshop.product.api.client.OpenTenderSkuClient;
import cn.legendshop.product.api.query.tender.OpenTenderSkuQuery;
import cn.legendshop.product.api.vo.tender.OpenTenderSkuVO;
import cn.legendshop.smsmail.api.client.AliCloudSMSServiceClient;
import cn.legendshop.smsmail.api.client.ShopSmsClient;
import cn.legendshop.smsmail.api.constant.AliDayuFieldEnum;
import cn.legendshop.smsmail.api.constant.ShopSmsParticularTypeEnum;
import cn.legendshop.smsmail.api.constant.SmsSystemParamterConstant;
import cn.legendshop.tender.api.client.UserCompanyVendorInfoClient;
import cn.legendshop.tender.api.dto.UserCompanyVendorInfoDTO;
import cn.legendshop.user.api.client.GCardAdminClient;
import cn.legendshop.user.api.client.NgCompanyPrincipalQualificationClient;
import cn.legendshop.user.api.client.ShopDetailServiceClient;
import cn.legendshop.user.api.dto.ErpReportDataDTO;
import cn.legendshop.user.api.dto.NgCompanyPrincipalQualificationDTO;
import cn.legendshop.user.api.enums.CardSettlementModeEnum;
import cn.legendshop.util.ErpSendDataUtils;
import cn.legendshop.zczy.api.LogisticsTypeEnum;
import cn.legendshop.zczy.api.client.LogisticsClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.PascalNameFilter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageParams;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.lambda.LambdaEntityCriterion;
import com.legendshop.model.constant.*;
import com.legendshop.model.dto.order.MySubBaseDto;
import com.legendshop.model.entity.ShopDetail;
import com.legendshop.model.entity.Sub;
import com.legendshop.model.entity.SubHistory;
import com.legendshop.model.entity.SubItem;
import com.legendshop.model.entity.gcard.GCard;
import com.legendshop.model.entity.orderreturn.SubRefundReturn;
import com.legendshop.model.entity.orderreturn.SubRefundReturnItem;
import com.legendshop.model.entity.orderreturn.SubRefundReturnRenewShipment;
import com.legendshop.util.AppUtils;
import com.legendshop.util.StringUtil;
import com.xxl.job.core.log.XxlJobLogger;
import io.seata.tm.api.GlobalTransactionContext;
import io.seata.tm.api.transaction.TransactionHookAdapter;
import io.seata.tm.api.transaction.TransactionHookManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.LinkedMap;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import springfox.documentation.annotations.Cacheable;

import java.io.File;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.legendshop.open.constants.ShipmentServiceConstant.OPEN_SHIPMENT_CREATE_LOCK_KEY;
import static cn.legendshop.url.ErpSendDataUrlConstant.ESB_URL;

@Slf4j
@Service
@AllArgsConstructor
public class ShipmentServiceImpl implements ShipmentService {

	private final ShipmentDao shipmentDao;

	private final ShipmentConverter shipmentConverter;

	private final ShipmentItemDao shipmentItemDao;

	private final ShipmentItemConverter shipmentItemConverter;

	private final ItemNoDao itemNoDao;

	private final ShopDetailServiceClient shopDetailServiceClient;

	private final SubDao subDao;

	private final SubThirdInfoDao subThirdInfoDao;

	private final SubItemDao subItemDao;

	private final UserAddressSubDao userAddressSubDao;

	private final ShipmentConfig shipmentConfig;

	private final SubService subService;

	private final OrderProducerService orderProducerService;

	private final StockService stockService;

	private final ErpArriveSiteDao erpArriveSiteDao;

	private final UserCompanyVendorInfoClient userCompanyVendorInfoClient;

	private final NgCompanyPrincipalQualificationClient ngCompanyPrincipalQualificationClient;

	private final ContractSubInformationDao contractSubInformationDao;

	private final SubHistoryDao subHistoryDao;

	private final DeliveryCorpDao deliveryCorpDao;

	private final LogisticsClient logisticsClient;

	private final ShopSmsClient shopSmsClient;

	private final AliCloudSMSServiceClient smsServiceClient;

	private final SystemParameterService systemParameterService;

	private final AmqpSendMsgUtil amqpSendMsgUtil;

	private final GCardAdminClient gCardAdminClient;

	private final ThirdParameterConfig thirdParameterConfig;
	private final ShipmentDetailConverter shipmentDetailConverter;
	private final ObeiOrderPlanConfigConverter obeiOrderPlanConfigConverter;

	private final RedissonClient redissonClient;

	private final RemoteObeiClient remoteObeiClient;

	private final SubItemConverter subItemConverter;

	private final ErpSendDataUrlConfig erpSendDataUrlConfig;

	private final ShipmentGateApplyDao shipmentGateApplyDao;

	private final SubRefundReturnItemDao subRefundReturnItemDao;

	private final SubRefundReturnDao subRefundReturnDao;

	private final SubRefundReturnRenewShipmentDao subRefundReturnRenewShipmentDao;

	private final MessagePoolShipmentService messagePoolShipmentService;

	private final SmartLockerReceiveDao smartLockerReceiveDao;

	private final LocationService locationService;

	private final OpenTenderSkuClient openTenderSkuClient;

	/**
	 * 删除发货
	 *
	 * @param shipmentId
	 * @param shopId
	 */
	@Transactional(rollbackFor = Exception.class)
	public void deleteShipment(Long shipmentId, Long shopId) {
		Shipment shipment = shipmentDao.getShipment(shipmentId);

		if (null == shipment) {
			throw new NotFoundBizException("出单不存在");
		}
		if (!shipment.getShopId().equals(shopId)) {
			throw new BusinessException("供应商不匹配，无法删除");
		}
		if (!ShipmentStatus.APPLYING.equals(shipment.getStatus())) {
			throw new BusinessException("无法删除改状态");
		}
		boolean isErp = OrderSourceEnum.ERP.value().equals(shipment.getOrderSource());
		if (isErp) {
			throw new BusinessException("ERP订单不允许删除");
		}
		List<ShipmentItem> shipmentItems = shipmentItemDao.findShipmentItems(shipmentId);
		if (null == shipmentItems || shipmentItems.isEmpty()) {
			return;
		}
		List<SubItem> updateSubItem = new ArrayList<>();
		for (ShipmentItem item : shipmentItems) {
			SubItem subItem = subItemDao.getSubItem(item.getSubItemId());
			if (null == subItem) {
				continue;
			}
			// 返回库存
			stockService.addActualHold(subItem.getProdId(), subItem.getSkuId(), Optional.ofNullable(item.getShipmentCount()).orElse(BigDecimal.ZERO).longValue());
			// 返回剩余数量
			subItem.setRemainCount(NumberUtil.add(subItem.getRemainCount(), item.getShipmentCount()));
			updateSubItem.add(subItem);
		}
		subItemDao.batchUpdate(updateSubItem);
		shipmentDao.deleteShipment(shipmentId);
		shipmentItemDao.deleteByList(shipmentItems);
	}

	@Override
	public R<String> cancelShipment(Long shipmentId, Long shopId,Boolean onlyCancelItem) {
		Shipment shipment = shipmentDao.getShipment(shipmentId);
		if (null == shipment) {
			return R.fail("出单不存在");
		}
		if (!shipment.getShopId().equals(shopId)) {
			return R.fail("供应商不匹配，无法删除");
		}
		if(onlyCancelItem) {
			List<ShipmentItem> shipmentItems = shipmentItemDao.findShipmentItems(shipmentId);
			if (null == shipmentItems || shipmentItems.isEmpty()) {
				return R.fail("未找到配送单项次");
			}
			List<ItemNo> updateItemNo = new ArrayList<>();
			for (ShipmentItem item : shipmentItems) {
				ItemNo itemNo = itemNoDao.getBySubItemId(item.getSubItemId());
				if (null == itemNo) {
					continue;
				}
				itemNo.setOutCount(itemNo.getOutCount().subtract(item.getShipmentCount()));
				itemNo.setRemainCount(itemNo.getItemCount().subtract(itemNo.getOutCount()));
				updateItemNo.add(itemNo);
			}
			itemNoDao.batchUpdate(updateItemNo);
			return R.ok();
		}

		if (!ShipmentStatus.STOCK_OUT.equals(shipment.getStatus())) {
			return R.fail("无法删除改状态");
		}
		boolean isErp = OrderSourceEnum.ERP.value().equals(shipment.getOrderSource());
		if (isErp) {
			if(20 == shipment.getArrivalApplyStatus()) {
				return R.fail("已提交到货申请，无法删除");
			}
		}
		Sub sub = subDao.getSubBySubNumber(shipment.getOrderNo());
        if (null == sub) {
            return R.fail("未找到订单");
        }
		if(!OrderStatusEnum.PARTIAL_SHIPMENT.value().equals(sub.getStatus())) {
			return R.fail("订单状态无法取消");
		}
		List<ShipmentItem> shipmentItems = shipmentItemDao.findShipmentItems(shipmentId);
		if (null == shipmentItems || shipmentItems.isEmpty()) {
			return R.fail("未找到配送单项次");
		}
		List<SubItem> updateSubItem = new ArrayList<>();
		List<ItemNo> updateItemNo = new ArrayList<>();
		for (ShipmentItem item : shipmentItems) {
			SubItem subItem = subItemDao.getSubItem(item.getSubItemId());
			if (null == subItem) {
				continue;
			}
			if (!isErp) {
				stockService.addActualHold(subItem.getProdId(), subItem.getSkuId(), Optional.ofNullable(item.getShipmentCount()).orElse(BigDecimal.ZERO).longValue());
			}else {
				ItemNo itemNo = itemNoDao.getBySubItemId(item.getSubItemId());
				if (null == itemNo) {
					continue;
				}
				itemNo.setOutCount(itemNo.getOutCount().subtract(item.getShipmentCount()));
				itemNo.setRemainCount(itemNo.getItemCount().subtract(itemNo.getOutCount()));
				updateItemNo.add(itemNo);
			}
			subItem.setOutCount(subItem.getOutCount().subtract(item.getShipmentCount()));
			// 返回剩余数量
			subItem.setRemainCount(NumberUtil.add(subItem.getRemainCount(), item.getShipmentCount()));
			updateSubItem.add(subItem);
		}
		shipment.setStatus(ShipmentStatus.CANCEL);
		if(isErp) {
			itemNoDao.batchUpdate(updateItemNo);
		}
		subItemDao.batchUpdate(updateSubItem);
		shipmentDao.updateShipment(shipment);
		return R.ok();
	}
	
	/**
	 * 发货单货物列表
	 *
	 * @param shipmentId
	 * @return
	 */
	public List<ShipmentItemDTO> findShipmentItemList(Long shipmentId) {
		List<ShipmentItem> items = shipmentItemDao.findShipmentItems(shipmentId);
		return items.stream().map(item -> {
			ShipmentItemDTO partialShipmentItem = new ShipmentItemDTO();
			BeanUtils.copyProperties(item, partialShipmentItem);
			return partialShipmentItem;
		}).collect(Collectors.toList());
	}

	/**
	 * 获取发货信息
	 *
	 * @param shipmentId
	 * @return
	 */
	public PartialShipmentDTO getPartialShipmentInfo(Long shipmentId) {
		PartialShipmentDTO partialShipment = shipmentDao.getPartialShipmentInfo(shipmentId);
		if (null == partialShipment) {
			return partialShipment;
		}
		List<ShipmentItem> items = shipmentItemDao.findShipmentItems(partialShipment.getShipmentId());
		List<ShipmentItemDTO> partialShipmentItems = items.stream().map(item -> {
			ShipmentItemDTO partialShipmentItem = new ShipmentItemDTO();
			BeanUtils.copyProperties(item, partialShipmentItem);
			return partialShipmentItem;
		}).collect(Collectors.toList());
		partialShipment.setShipmentItems(partialShipmentItems);
		return partialShipment;
	}

	/**
	 * 分批发货列表
	 *
	 * @param param
	 * @return
	 */
	public List<PartialShipmentDTO> findPartialShipmentList(ShipmentQueryParamDTO param) {
		List<PartialShipmentDTO> shipmentPartials = shipmentDao.findPartialShipmentList(param);
		if (CollUtil.isEmpty(shipmentPartials)) {
			throw new BusinessException("shipmentPartials为空");
		}
		for (PartialShipmentDTO shipmentPartial : shipmentPartials) {
			log.info("开始进行处理");
			log.info(shipmentPartial.toString());
			List<ShipmentItem> items = shipmentItemDao.findShipmentItems(shipmentPartial.getShipmentId());
			List<ShipmentItemDTO> partialShipmentItems = shipmentDetailConverter.to(items);
			shipmentPartial.setShipmentItems(partialShipmentItems);

			Map<Long, Long> itemMap = items.stream().collect(Collectors.toMap(ShipmentItem::getItemNoId, ShipmentItem::getSubItemId, (k1, k2) -> k1));
			List<ItemNo> itemNos = itemNoDao.queryAllByIds(new ArrayList<>(itemMap.keySet()));
			//List<SubItem> subItems = subItemDao.queryAllByIds(new ArrayList<>(itemMap.values()));

			if (CollUtil.isNotEmpty(itemNos)) {
				Map<Long, ItemNo> itemNoIdAndCount = itemNos.stream().peek(itemNo -> {
					itemNo.setInCount(Optional.ofNullable(itemNo.getInCount()).orElse(BigDecimal.ZERO));
					itemNo.setRejectCount(Optional.ofNullable(itemNo.getRejectCount()).orElse(BigDecimal.ZERO));
					itemNo.setReturnCount(Optional.ofNullable(itemNo.getReturnCount()).orElse(BigDecimal.ZERO));
//				itemNo.setModifyCount(Optional.ofNullable(itemNo.getModifyCount()).orElse(BigDecimal.ZERO));
				}).collect(Collectors.toMap(ItemNo::getId, Function.identity()));

				shipmentPartial.getShipmentItems().forEach(shipmentItem -> {
					ItemNo itemNo = itemNoIdAndCount.get(shipmentItem.getItemNoId());
					shipmentItem.setCompletionCount(itemNo.getInCount());
					shipmentItem.setRejectCount(itemNo.getRejectCount());
//				shipmentItem.setModifyCount(itemNo.getModifyCount());
				});
			}
			shipmentPartial.setPrice(BooleanUtil.isTrue(param.getDetail()) ? shipmentPartial.getShipmentItems().stream().map(ShipmentItemDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add) : null);
		}
		return shipmentPartials;
	}


	/**
	 * 修改物流信息
	 *
	 * @param shipmentLogistics
	 */
	public int updateShipmentLogistics(ShipmentLogisticsDTO shipmentLogistics) {
		PartialShipmentDTO shipment = shipmentDao.getPartialShipmentInfo(shipmentLogistics.getShipmentId());
		if (null == shipment) {
			throw new NotFoundBizException("找不到该发货单");
		}
		if (!shipment.getShopId().equals(shipmentLogistics.getShopId())) {
			throw new CannotDoException("供应商不匹配");
		}

		Instant instant = shipment.getOutOperatorTime().toInstant();
		LocalDateTime appTime = LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
		LocalDateTime nowTime = LocalDateTime.now(ZoneId.of("UTC"));
		// 是否操过一个小时
		boolean isMoreThan = nowTime.isAfter(appTime.plusHours(1L));
		if (isMoreThan) {
			throw new TimeoutException("无法修改，已经超过一个小时！");
		}
		return shipmentDao.updateShipmentLogistics(shipmentLogistics);
	}


	/**
	 * 剩余入库
	 */
	public SuppliesInfoDTO getAllSuppliesInfo(ShipmentItemQueryParamDTO queryParam) {
		Long shopId = queryParam.getShopId();
		String buyerId = queryParam.getBuyerId();

		SuppliesInfoDTO suppliesInfoDTO = new SuppliesInfoDTO();

		Sub sub = subDao.getSubById(queryParam.getSubId());
		if (null == sub) {
			return suppliesInfoDTO;
		}
		if (null == shopId && null == buyerId) {
			return suppliesInfoDTO;
		} else if (null != shopId && !sub.getShopId().equals(shopId)) {
			return suppliesInfoDTO;
		} else if (null != buyerId && !sub.getUserId().equals(buyerId)) {
			return suppliesInfoDTO;
		}
		suppliesInfoDTO.setOrderSource(sub.getOrderSource());
		suppliesInfoDTO.setSubId(sub.getSubId());
		List<SubItem> subItems = subItemDao.getSubItemByParam(queryParam);
		if (null == subItems || subItems.isEmpty()) {
			return suppliesInfoDTO;
		}
		List<SuppliesDTO> suppliesList = subItems.stream()
			.map(subItem -> {
				SuppliesDTO suppliesDTO = new SuppliesDTO();
				suppliesDTO.setChecked(false);
				suppliesDTO.setOrderSource(sub.getOrderSource());
				suppliesDTO.setSubId(sub.getSubId());
				suppliesDTO.setOutCount(subItem.getOutCount());
				suppliesDTO.setRemainCount(subItem.getRemainCount());
				suppliesDTO.setInCount(subItem.getInCount());
//				suppliesDTO.setRemainInCount(Integer.parseInt(subItem.getBasketCount() + "")  - subItem.getInCount());
				suppliesDTO.setRemainInCount(NumberUtil.sub(Optional.ofNullable(subItem.getActualCount()).orElse(subItem.getBasketCount()), subItem.getInCount()));
				suppliesDTO.setShipmentCount(0);
				suppliesDTO.setPic(subItem.getPic());
				suppliesDTO.setProdId(subItem.getProdId());
				suppliesDTO.setProdName(subItem.getProdName());
				suppliesDTO.setSkuId(subItem.getSkuId());
				suppliesDTO.setSubItemId(subItem.getSubItemId());
				// erp下单则包装项号
				if (OrderSourceEnum.ERP.value().equals(suppliesDTO.getOrderSource())) {
					List<ItemNo> itemNoList = itemNoDao.findBySubItemId(suppliesDTO.getSubItemId());
					if (null != itemNoList && !itemNoList.isEmpty()) {
						List<SuppliesItemNoDTO> suppliesItemNos = itemNoList.stream()
							.map(itemNo -> {
								SuppliesItemNoDTO suppliesItemNo = new SuppliesItemNoDTO();
								suppliesItemNo.setChecked(false);
								suppliesItemNo.setItemNo(itemNo.getItemNo());
								suppliesItemNo.setItemNoId(itemNo.getId());
								suppliesItemNo.setOutCount(itemNo.getOutCount());
								suppliesItemNo.setRemainCount(itemNo.getRemainCount());
								suppliesItemNo.setInCount(itemNo.getInCount());
								suppliesItemNo.setRemainInCount(NumberUtil.sub(itemNo.getOutCount(), itemNo.getInCount()));
								suppliesItemNo.setShipmentCount(BigDecimal.ZERO);
								suppliesItemNo.setSubItemId(itemNo.getSubItemId());
								return suppliesItemNo;
							}).collect(Collectors.toList());
						suppliesDTO.setSuppliesItemNos(suppliesItemNos);
					}
				}
				return suppliesDTO;
			}).collect(Collectors.toList());
		suppliesInfoDTO.setSuppliesList(suppliesList);
		return suppliesInfoDTO;
	}


	/**
	 * 物料列表
	 */
	public SuppliesInfoDTO getSuppliesInfo(ShipmentItemQueryParamDTO queryParam) {
		Long shopId = queryParam.getShopId();
		String buyerId = queryParam.getBuyerId();

		SuppliesInfoDTO suppliesInfoDTO = new SuppliesInfoDTO();

		Sub sub = subDao.getSubById(queryParam.getSubId());
		if (null == sub) {
			return suppliesInfoDTO;
		}
		if (null == shopId && null == buyerId) {
			return suppliesInfoDTO;
		} else if (null != shopId && !sub.getShopId().equals(shopId)) {
			return suppliesInfoDTO;
		} else if (null != buyerId && !sub.getUserId().equals(buyerId)) {
			return suppliesInfoDTO;
		}
		suppliesInfoDTO.setOrderSource(sub.getOrderSource());
		suppliesInfoDTO.setSubId(sub.getSubId());
		List<SubItem> subItems = subItemDao.getSubItemByParam(queryParam);
		if (null == subItems || subItems.isEmpty()) {
			return suppliesInfoDTO;
		}
		List<SuppliesDTO> suppliesList = subItems.stream()
			.filter(subItem -> subItem.getRemainCount().compareTo(BigDecimal.ZERO) > 0)
			.map(subItem -> {
				SuppliesDTO suppliesDTO = new SuppliesDTO();
				suppliesDTO.setChecked(false);
				suppliesDTO.setOrderSource(sub.getOrderSource());
				suppliesDTO.setSubId(sub.getSubId());
				suppliesDTO.setOutCount(subItem.getOutCount());
				suppliesDTO.setRemainCount(subItem.getRemainCount());
				suppliesDTO.setInCount(subItem.getInCount());
				suppliesDTO.setRemainInCount(NumberUtil.sub(subItem.getOutCount(), subItem.getInCount()));
				suppliesDTO.setShipmentCount(0);
				suppliesDTO.setPic(subItem.getPic());
				suppliesDTO.setProdId(subItem.getProdId());
				suppliesDTO.setProdName(subItem.getProdName());
				suppliesDTO.setSkuId(subItem.getSkuId());
				suppliesDTO.setSubItemId(subItem.getSubItemId());
				// erp下单则包装项号
				if (OrderSourceEnum.ERP.value().equals(suppliesDTO.getOrderSource())) {
					List<ItemNo> itemNoList = itemNoDao.findBySubItemId(suppliesDTO.getSubItemId());
					if (null != itemNoList && !itemNoList.isEmpty()) {
						List<SuppliesItemNoDTO> suppliesItemNos = itemNoList.stream()
							.filter(itemNo -> itemNo.getRemainCount().compareTo(BigDecimal.ZERO) > 0)
							.map(itemNo -> {
								SuppliesItemNoDTO suppliesItemNo = new SuppliesItemNoDTO();
								suppliesItemNo.setChecked(Boolean.FALSE);
								suppliesItemNo.setItemNo(itemNo.getItemNo());
								suppliesItemNo.setItemNoId(itemNo.getId());
								suppliesItemNo.setOutCount(itemNo.getOutCount());
								suppliesItemNo.setRemainCount(itemNo.getRemainCount());
								suppliesItemNo.setInCount(itemNo.getInCount());
								suppliesItemNo.setRemainInCount(NumberUtil.sub(itemNo.getOutCount(), itemNo.getInCount()));
								suppliesItemNo.setShipmentCount(BigDecimal.ZERO);
								suppliesItemNo.setSubItemId(itemNo.getSubItemId());

								return suppliesItemNo;
							}).collect(Collectors.toList());
						suppliesDTO.setSuppliesItemNos(suppliesItemNos);
					}
				}
				return suppliesDTO;
			}).collect(Collectors.toList());
		suppliesInfoDTO.setSuppliesList(suppliesList);
		return suppliesInfoDTO;
	}


	/**
	 * 发货
	 *
	 * @param shipmentOperate the 发货信息
	 */
//	@GlobalTransactional(rollbackFor = Exception.class)
	@Transactional
	public void toShipment(ShipmentOperateDTO shipmentOperate) {
		log.info("调用发货: 请求参数【 {} 】", JSON.toJSONString(shipmentOperate, false));
		Sub sub = subDao.getSubById(shipmentOperate.getSubId());
		if (null == sub) {
			throw new NotFoundBizException("订单不存在");
		}
		// 判断操作权限
		checkRules(shipmentOperate, sub);
		if (!OrderStatusEnum.PAID.value().equals(sub.getStatus())) {
			throw new DataErrorException("订单状态不正确");
		}
		if (!PurchaseTypeEnum.ENTERPRISE.value().equals(sub.getPurchaseType()) && ShipmentTypeConst.SHIPMENT_PARTIAL.equals(shipmentOperate.getShipmentType())) {
			throw new DataErrorException("个人无法进行分批发货");
		}
		// 判断是否erp下单
		boolean isErp = OrderSourceEnum.ERP.value().equals(sub.getOrderSource());
		boolean isMro = OrderSourceEnum.MRO.value().equals(sub.getOrderSource());
		// 不是所有erp订单都走门禁 [ 合金、辅材不需要门禁 ]
		boolean isAccessControl = false;
		// 供应商
		UserCompanyVendorInfoDTO userCompanyVendorInfo = null;
		// 采购商
		UserCompanyVendorInfoDTO buyerVendorInfo = null;

		ContractSubInformation contractSubInformation = null;

		UserAddressSub userAddressSub = userAddressSubDao.getUserAddressSub(sub.getAddrOrderId());
		// 全部发货
		if (shipmentOperate.getShipmentType().equals(ShipmentTypeConst.SHIPMENT_ALL)) {

			String contractType = null;
			// 自己送货 且 全发
			if (isErp) {
				contractSubInformation = contractSubInformationDao.getByContractNumber(sub.getContractNum());
				if (null == contractSubInformation) {
					throw new NotFoundBizException("合同信息不存在！");
				}
				contractType = contractSubInformation.getContractType();
				//一期  不是所有erp订单都走门禁 [ 合金、辅材不需要门禁 ]
				//isAccessControl = ErpContractTypeEnum.AUXILIARY_MATERIAL.getCode().equals(contractSubInformation.getContractType()) || ErpContractTypeEnum.ALLOY.getCode().equals(contractSubInformation.getContractType());
				//二期  不是所有erp订单都走门禁 [ 合金、不需要门禁 ]
				isAccessControl = ErpContractTypeEnum.ALLOY.getCode().equals(contractSubInformation.getContractType());
				// 采购商
				buyerVendorInfo = userCompanyVendorInfoClient.findByUniformNo(contractSubInformation.getCompanyNumber(), SecurityConstants.FROM_IN).getResult();
				if (null == buyerVendorInfo) {
					throw new NotFoundBizException("没有采购商信息！");
				}
				// 供应商
				userCompanyVendorInfo = userCompanyVendorInfoClient.findByFullName(sub.getShopName(), SecurityConstants.FROM_IN).getResult();
//				if (null == userCompanyVendorInfo) {
//					throw new NotFoundBizException("没有供应商信息！");
//				}
			}
			if (null == userAddressSub) {
				throw new NotFoundBizException("发货地址不存在");
			}
			List<SubItem> subItems = subItemDao.getSubItemBySubId(sub.getSubId());
			if (null == subItems || subItems.isEmpty()) {
				throw new NotFoundBizException("没有订单项");
			}
			Shipment shipment = new Shipment();
			shipment.setSubId(sub.getSubId());
			shipment.setLogisticsName(shipmentOperate.getLogisticsName());
			shipment.setLogisticsNo(shipmentOperate.getLogisticsNo());
			shipment.setLogisticsId(shipmentOperate.getLogisticsId());
			shipment.setLogisticsAmount(shipmentOperate.getLogisticsAmount());
			shipment.setLogisticsType(shipmentOperate.getLogisticsType());
			shipment.setStatus(ShipmentStatus.STOCK_OUT);
			shipment.setApplicantId(shipmentOperate.getOperatorId());
			shipment.setApplicantName(shipmentOperate.getOperatorName());
			shipment.setApplicantTime(new Date());
			shipment.setApplicantType(shipmentOperate.getOperatorType());
			shipment.setBatchNo(CommonServiceUtil.getSubNumber(shipmentOperate.getOperatorId()));
			shipment.setBuyerId(sub.getUserId());
			shipment.setShopId(sub.getShopId());
			shipment.setRemark(shipmentOperate.getRemark());
			shipment.setConsignee(userAddressSub.getReceiver()); // 收货人
			shipment.setReceiverAddress(userAddressSub.getDetailAddress()); // 收货地址
			shipment.setContactNumber(userAddressSub.getMobile()); // 联系人电话
			shipment.setContractNum(sub.getContractNum()); // 合同号
			shipment.setShipmentType(ShipmentTypeConst.SHIPMENT_ALL); // 全部发货
			shipment.setOrderNo(sub.getSubNumber());
			shipment.setOrderSource(sub.getOrderSource());
			shipment.setGateAuditStatus(ShipmentGateAuditEnum.CANNOT_APPLY.getValue());

//			 物流单号
			sub.setDvyFlowId(shipmentOperate.getLogisticsNo());
//			 物流id
			sub.setDvyTypeId(shipmentOperate.getLogisticsId());
//			物流方式
			sub.setDeliveryType(shipmentOperate.getLogisticsType());
			sub.setShipmentType(ShipmentTypeConst.SHIPMENT_ALL);
			R<List<GCard>> gCardR = this.gCardAdminClient.queryGcardBySubnum(sub.getSubNumber(), SecurityConstants.FROM_IN);
			List<GCard> gCardList = gCardR.getResult();
			if (CollectionUtil.isEmpty(gCardList)) {
				sub.setStatus(OrderStatusEnum.CONSIGNMENT.value()); // 待收货
			} else {
				List<String> collect = gCardList.stream().map(GCard::getSettlementMode).distinct().collect(Collectors.toList());
				if (collect.contains(CardSettlementModeEnum.PLATFORM_JOIN.value())) {
					sub.setStatus(OrderStatusEnum.TO_BE_PLATFORM_AUDIT_DELIVERING.value());//平台参与分账的福利卡支付的订单需要平台审核发货
				} else {
					sub.setStatus(OrderStatusEnum.CONSIGNMENT.value()); // 待收货
				}
			}
			sub.setDvyType(shipmentOperate.getLogisticsName()); // 物流名称
			//一期是 设备和设备走派车申请
			if (StringUtils.isNotBlank(contractType) && ErpContractTypeEnum.DEVICE.getCode().equals(contractType) || ErpContractTypeEnum.SPARE_PARTS.getCode().equals(contractType) || ErpContractTypeEnum.AUXILIARY_MATERIAL.getCode().equals(contractType)) {
				shipment.setDispatchingCarsStatus(DispatchingCarsStatusEnum.ALLOWED.getCode());
			} else {
				shipment.setDispatchingCarsStatus(DispatchingCarsStatusEnum.NOT_ALLOWED.getCode());//不进行派车申请
			}


			Long shipmentId = shipmentDao.saveShipment(shipment);

			//调用快递100的订阅请求接口，
			try {
				//如果是自已发货，则不需要 调用接口
				if (shipmentOperate.getLogisticsId() > 0) {
					String deliveryCorpCode = deliveryCorpDao.getDeliveryCorpCodeByTypeId(shipmentOperate.getLogisticsId());
					if (AppUtils.isNotBlank(deliveryCorpCode)) {
						R<Boolean> pollResult = logisticsClient.poll(deliveryCorpCode, shipmentOperate.getLogisticsNo(), SecurityConstants.FROM_IN);
						if (!pollResult.getResult()) {
							log.warn("调用快递100的订阅请求接口失败，物流公司: {} , 物流单号: {}", deliveryCorpCode, shipmentOperate.getLogisticsNo());
						}
					}
				}
			} catch (Exception ex) {
				log.error("调用快递100的订阅请求接口失败 , 物流单号: {}", shipmentOperate.getLogisticsNo());
			}

			List<ShipmentItem> itemList = new ArrayList<>();
			List<ItemNo> itemNoUpdateList = new ArrayList<>();
			List<ApplyShipmentGoodsDTO> erpGoods = new ArrayList<>();

			for (SubItem sItem : subItems) {
				if (sItem.getRemainCount() == null) {
//					sItem.setRemainCount(Integer.valueOf(sItem.getBasketCount() + ""));
					sItem.setRemainCount(Optional.ofNullable(sItem.getActualCount()).orElse(sItem.getBasketCount()));
				}
				sItem.setOutCount(sItem.getRemainCount());  // 全部发
				sItem.setRemainCount(BigDecimal.ZERO);
				// 判断是否是erp订单
				if (isErp) {
					// 找项次
					log.info("进入2");
					List<ItemNo> itemNoList = itemNoDao.findBySubItemId(sItem.getSubItemId());
					if (null != itemNoList && !itemNoList.isEmpty()) {
						for (ItemNo itemNo : itemNoList) {
							itemNo.setOutCount(itemNo.getRemainCount()); // 全部发
							itemNo.setRemainCount(BigDecimal.ZERO);
							ShipmentItem shipmentItem = new ShipmentItem();
							shipmentItem.setVolume(sItem.getVolume());
							shipmentItem.setWeight(sItem.getWeight());
							shipmentItem.setItemNoId(itemNo.getId());
							shipmentItem.setItemNo(itemNo.getItemNo());
							shipmentItem.setSubItemId(sItem.getSubItemId());
							shipmentItem.setContractNum(sub.getContractNum());
							shipmentItem.setShipmentCount(itemNo.getOutCount());
							shipmentItem.setBatchNo(shipment.getBatchNo());
							shipmentItem.setPic(sItem.getPic());
							shipmentItem.setProdId(itemNo.getProdId());
							shipmentItem.setProdName(sItem.getProdName());
							shipmentItem.setSubItemId(itemNo.getSubItemId());
							shipmentItem.setRejectCount(BigDecimal.ZERO);
							shipmentItem.setSkuId(itemNo.getSkuId());
							shipmentItem.setReceiveCount(BigDecimal.ZERO);
							shipmentItem.setShipmentId(shipmentId);
							// 加入申请发货列表
							ApplyShipmentGoodsDTO applyGoods = new ApplyShipmentGoodsDTO();
							applyGoods.setProdNum(Optional.ofNullable(shipmentItem.getShipmentCount()).orElse(BigDecimal.ZERO));
							applyGoods.setErpApiNo("ERPIN01");
							applyGoods.setItemNo(itemNo.getItemNo());
							applyGoods.setContractNumber(Optional.ofNullable(contractSubInformation.getContract()).orElse(StrUtil.SPACE));
							applyGoods.setBatchNo(shipment.getBatchNo());
							applyGoods.setOrderNumber(sub.getSubNumber());
							applyGoods.setArriveDate(shipment.getApplicantTime());
							erpGoods.add(applyGoods);
							itemList.add(shipmentItem);
							itemNoUpdateList.add(itemNo);
						}
					}
				} else {
					log.info("进入1");
					ShipmentItem shipmentItem = new ShipmentItem();
					shipmentItem.setVolume(sItem.getVolume());
					shipmentItem.setWeight(sItem.getWeight());
					shipmentItem.setSubItemId(sItem.getSubItemId());
					shipmentItem.setContractNum(sub.getContractNum());
					shipmentItem.setShipmentCount(sItem.getOutCount());
					shipmentItem.setBatchNo(shipment.getBatchNo());
					shipmentItem.setPic(sItem.getPic());
					shipmentItem.setReceiveCount(BigDecimal.ZERO);
					shipmentItem.setRejectCount(BigDecimal.ZERO);
					shipmentItem.setProdId(sItem.getProdId());
					shipmentItem.setProdName(sItem.getProdName());
					shipmentItem.setSubItemId(sItem.getSubItemId());
					shipmentItem.setSkuId(sItem.getSkuId());
					shipmentItem.setShipmentId(shipmentId);
					itemList.add(shipmentItem);
				}

				//erp/中标采购商下单不减库存
				if (isMro) {
					// 减少实际库存
//					boolean conf = stockService.reduceActualHold(sItem.getProdId(), sItem.getSkuId(), sItem.getBasketCount());
					boolean conf = stockService.reduceActualHold(sItem.getProdId(), sItem.getSkuId(), Optional.ofNullable(sItem.getActualCount()).orElse(sItem.getBasketCount()).longValue());
					if (!conf && log.isWarnEnabled()) {

						log.warn("发货时调用失败 addActualHold for prodId: {}, skuId: {}， basketCount: {} ",
							sItem.getProdId(), sItem.getSkuId(), Optional.ofNullable(sItem.getActualCount()).orElse(sItem.getBasketCount()));
						throw new ParameterErrorException("减库存失败");
					}
				}

			}
			if (itemList.isEmpty()) {
				throw new NotFoundBizException("没有要发货的商品");
			}
			shipmentItemDao.batchSave(itemList);
			if (!subItems.isEmpty()) {
				subItemDao.batchUpdate(subItems);
			}
			if (!itemNoUpdateList.isEmpty()) {
				itemNoDao.batchUpdate(itemNoUpdateList);
			}
			// 发erp
			if (isErp) {

				if (erpGoods.isEmpty()) {
					throw new NotFoundBizException("没有要发货商品");
				}

				if (!isAccessControl) {
					// 到货申请
					String body = JSON.toJSONString(erpGoods);
					log.info("发送mq到货申请 ：{}", body);
					this.amqpSendMsgUtil.convertAndSend(ErpOrderMQConstant.ERP_ORDER_CHAIN_EXCHANGE, ErpOrderMQConstant.ERP_TO_SHIPMENT_ROUTING_KEY, body);
				}
			}
		} else if (shipmentOperate.getShipmentType().equals(ShipmentTypeConst.SHIPMENT_PARTIAL)) {
			sub.setShipmentType(ShipmentTypeConst.SHIPMENT_PARTIAL);
			sub.setStatus(OrderStatusEnum.PARTIAL_SHIPMENT.value()); // 分批发货
		} else {
			throw new ParameterErrorException("参数错误");
		}
		sub.setDvyDate(new Date());
		subDao.update(sub);

		//发货订单推送区块链
		this.amqpSendMsgUtil.convertAndSend(SubTOBlockChainConstant.SUB_BLOCK_CHAIN_EXCHANGE, SubTOBlockChainConstant.SUB_BLOCK_CHAIN_KEY, sub.getSubNumber());

		//如果是个人买家
		if (PurchaseTypeEnum.PERSON.value().equals(sub.getPurchaseType())) {
			//判断是否符合发送条件
			if (shopSmsClient.enabledSendSms(ShopSmsParticularTypeEnum.SHIPMENTS.value(), sub.getShopId(), userAddressSub.getMobile(), SecurityConstants.FROM_IN).getResult()) {
				//发送发货短信
				Map<AliDayuFieldEnum, String> map = new LinkedMap<>(2);
				map.put(AliDayuFieldEnum.order, sub.getSubNumber());
				map.put(AliDayuFieldEnum.dvycompany, shipmentOperate.getLogisticsName());

				//获取短信模板
				String templateCode = systemParameterService.getSystemParameterByName(SmsSystemParamterConstant.DELIVER_GOODS, String.class);
				R r = this.smsServiceClient.sendSms(userAddressSub.getMobile(), map, templateCode, false, null, SecurityConstants.FROM_IN);
				if (r.getStatus().equals(200)) {
					log.info("###发送发货短信成功###");
					//创建短信明细,扣减商家短信数量
					this.shopSmsClient.decreaseSmsCount(sub.getShopId(), ShopSmsParticularTypeEnum.SHIPMENTS.value(), userAddressSub.getMobile(), SecurityConstants.FROM_IN);
				}
			}
		}

		//保存订单记录
		SubHistory subHistory = new SubHistory();
		Date date = new Date();
		String time = subHistory.DateToString(date);
		subHistory.setRecDate(date);
		subHistory.setSubId(sub.getSubId());
		subHistory.setStatus(SubHistoryEnum.ORDER_PAY.value());
		String str = "商家于" + time + "完成发货";
		subHistory.setUserName(sub.getShopName());
		subHistory.setReason(str);
		subHistoryDao.saveSubHistory(subHistory);
	}

	public void autoCreateShipment(List<DeliveryDetail> deliveryDetails) {
		if (CollectionUtil.isEmpty(deliveryDetails)) {
			throw new NotFoundBizException("配送单详细查询失败");
		}

		//判断是否ERP订单，如果是ERP则发送欧贝送货单打印，取第一条订单项记录。一次发货，为同一个订单。
		boolean isErp = false;
		SubItem subItem = subItemDao.getByThirdItemNumber(deliveryDetails.get(0).getDeliveryDetails().get(0).getOrderRowCode());

		Long accountBookId = 0L;
		if (subItem != null && StringUtil.isNotBlank(subItem.getSubNumber())) {
			Sub sub = subDao.getSubBySubNumber(subItem.getSubNumber());
			if (sub != null) {
				isErp = sub.getOrderSource().equals(OrderSourceEnum.ERP.value());
				accountBookId = sub.getAccountBookId();
			}
			if (sub.getStatus().equals(OrderStatusEnum.ADVANCE_ORDER.value())) {
				log.info("该配送单对应订单为预订单,需采购员确认");
				throw new BusinessException("该配送单对应订单为预订单,需采购员确认");
			}
			if (sub.getStatus().equals(OrderStatusEnum.CLOSE.value())) {
				log.info("订单已取消");
				throw new BusinessException("订单已取消");
			}
		}

		JSONArray jsonArray = new JSONArray();

		for (DeliveryDetail deliveryDetail : deliveryDetails) {
			//真正生成配送单
			JSONObject jsonObject = doAutoCreateShipment(deliveryDetail, isErp);
			if (ObjectUtil.isNotEmpty(jsonObject)) {
				jsonArray.add(jsonObject);
			}
		}
		if (isErp) {
			log.info("发送欧贝送货单打印!");
			remoteObeiClient.thridIndustrialSendObjData(ObeiSendDataEnum.JALDPD.getCode(), jsonArray, accountBookId,(params, result) -> {
				orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.OBEI, ObeiSendDataEnum.JALDPD.getCode(), params, HttpStatus.HTTP_OK, result);
			});
		} else {
			log.info("非ERP订单，不发送欧贝送货单打印信息!");
		}
	}

	public JSONObject doAutoCreateShipment(DeliveryDetail deliveryDetail, boolean isErp) {
		SubItem subItem = subItemDao.getByProperties(new LambdaEntityCriterion<>(SubItem.class)
			.eq(SubItem::getThirdItemNumber, deliveryDetail.getDeliveryDetails().get(0).getOrderRowCode()).limit(0, 1)
		);
		if (Objects.isNull(subItem)) {
			throw new NotFoundBizException("订单子项不存在");
		}
		Sub sub = subDao.getSubBySubNumber(subItem.getSubNumber());
		if (Objects.isNull(sub)) {
			throw new NotFoundBizException("订单不存在");
		}
		SubThirdInfo subThirdInfo = subThirdInfoDao.getSubBySubNumber(subItem.getSubNumber());
		if (Objects.isNull(subThirdInfo)) {
			throw new NotFoundBizException("订单信息不存在");
		}

		UserAddressSub userAddressSub = userAddressSubDao.getUserAddressSub(sub.getAddrOrderId());
		if (null == userAddressSub) {
			throw new NotFoundBizException("用户订单不存在！");
		}
		Long shopId = thirdParameterConfig.getObeiSetting().getShopId();
		R<ShopDetail> shopDetailByShopId = shopDetailServiceClient
			.getShopDetailByShopId(shopId);
		if (Objects.isNull(shopDetailByShopId) || !shopDetailByShopId.hasBody()) {
			log.error("无法找到供应商用户信息");
			return null;
		}

		Map<String, DeliveryDetailItem> orderRowCodeMap = deliveryDetail.getDeliveryDetails()
			.stream()
			.collect(Collectors.toMap(DeliveryDetailItem::getOrderRowCode, Function.identity()));

		List<SubItem> subItems = subItemDao.queryByProperties(new LambdaEntityCriterion<>(SubItem.class)
			.in(SubItem::getThirdItemNumber, orderRowCodeMap.keySet())
		);

		List<ItemNo> itemNos = itemNoDao.queryByProperties(new LambdaEntityCriterion<>(ItemNo.class)
			.in(ItemNo::getSubItemId, subItems.stream().map(SubItem::getSubItemId).collect(Collectors.toList())));

		ShipmentOperateDTO shipment = bindShipmentOperateDTO(sub, shopDetailByShopId);
		shipment.setLogisticsNo(deliveryDetail.getTransportCode());
		shipment.setLogisticsType(LogisticsTypeEnum.KUAIDI.getIntValue());
		shipment.setLogisticsId(KuaiDiEnum.SELF.getNum().longValue());
		doShipmentV2(shipment, sub);

		List<ShipmentItemDTO> shipmentItemDTOS = mappingProperty(deliveryDetail.getDeliveryDetails(), subItems, orderRowCodeMap, itemNos);

		ContractSubInformation contractSubInformation = contractSubInformationDao.getByContractNumber(sub.getContractNum());

		PartialShipmentDTO partialShipmentDTO = new PartialShipmentDTO();
		partialShipmentDTO.setShopId(shopId)
			.setApplicantName(deliveryDetail.getSendUserName())
			.setApplicantType(UserAccountTypeConst.MAIN)
			.setContractNum(contractSubInformation == null ? "" : contractSubInformation.getContract())
			.setThirdShipNo(deliveryDetail.getDeliveryDetails().get(0).getDeliveryCode())
			.setLogisticsNo(deliveryDetail.getDeliveryDetails().get(0).getDeliveryCode())
			.setOrderNo(sub.getSubNumber())
			.setSubId(sub.getSubId())
			.setApplicantId(shopDetailByShopId.getResult().getUserId())
			.setShipmentItems(shipmentItemDTOS).setThirdShipItemNo(deliveryDetail.getDeliveryDetails().get(0).getRowCode());

		//发货，如果是ERP还需要做门禁申请
		log.info("applyPartialShipment 申请发货 分批发货 ");
		//创建配送单
		applyPartialShipment(partialShipmentDTO, Boolean.TRUE);

		shipment.setShipmentId(partialShipmentDTO.getShipmentId());
		shipment.setLogisticsId(0L);
		confirmStockOut(shipment);

		if (isErp) {
			if (null == contractSubInformation) {
				throw new NotFoundBizException("合同信息不存在！");
			}
			JSONObject reportData = StrUtil.isNotEmpty(contractSubInformation.getErpReportData()) ? CollUtil.isNotEmpty(JSONObject.parseArray(contractSubInformation.getErpReportData())) ? JSONObject.parseArray(contractSubInformation.getErpReportData()).getJSONObject(0) : null : null;

			JSONObject jsonObject = new JSONObject();
			if (AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId().equals(sub.getAccountBookId())) {
				//南钢：发货单打印信息推送接口
			List<ReceiveDeliveryInfoParam.DeliveryItemVo> deliveryItemVos = mappingProperty(reportData, userAddressSub, contractSubInformation, subItems, itemNos);
			ReceiveDeliveryInfoParam receiveDeliveryInfoParam = new ReceiveDeliveryInfoParam();
			receiveDeliveryInfoParam
				.setObeiDeliveryCode(deliveryDetail.getDeliveryDetails().get(0).getDeliveryCode())
				.setErpDeliveryNum(Optional.ofNullable(subThirdInfo.getErpDeliveryNum()).orElse(StrUtil.SPACE))
				.setWarehouseName(Optional.ofNullable(subThirdInfo.getStoreKeeperName()).orElse(StrUtil.SPACE))
				.setWarehouseMobile(Optional.ofNullable(subThirdInfo.getStoreKeeperPhoneNumber()).orElse(StrUtil.SPACE))
				.setWorkingName(reportData != null ? reportData.getString("erpReportCompany") : StrUtil.SPACE)
				.setErpDeliveryItem(deliveryItemVos);
				jsonObject.put("data", receiveDeliveryInfoParam);
			} else {
				ReceiveDeliveryPurchaserInfoParam shipmentPrintVo = buildPurchaserPrintData(sub, contractSubInformation, userAddressSub, subItems, itemNos, deliveryDetail.getDeliveryDetails().get(0).getDeliveryCode());
				jsonObject.put("data", shipmentPrintVo);
			}

			//发货单号
			jsonObject.put("deliveryCode", deliveryDetail.getDeliveryDetails().get(0).getDeliveryCode());
			return jsonObject;
		}
		return null;
	}

	private ReceiveDeliveryPurchaserInfoParam buildPurchaserPrintData(Sub sub, ContractSubInformation contractSubInfo, UserAddressSub userAddressSub, List<SubItem> subItems, List<ItemNo> itemNos, String shipmentNo) {
		//组装打印参数 放入缓存
		ReceiveDeliveryPurchaserInfoParam receiveDeliveryInfoParam = new ReceiveDeliveryPurchaserInfoParam();

		Map<String, SubItem> subItemMap = subItems.stream().collect(Collectors.toMap(SubItem::getThirdSkuId, Function.identity()));
		Map<Long, ItemNo> itemNoMap = itemNos.stream().collect(Collectors.toMap(ItemNo::getSubItemId, Function.identity()));

		String dlyArea;
		String dlyAddress = null;
		if (ObjectUtil.isNotEmpty(userAddressSub)) {
			if(userAddressSub.getProvinceId() != null && !DeliveryTypeEnum.EXPRESS_DELIVERY_CABINET.value().equals(sub.getDeliveryType())) {
				Province province = locationService.getProvinceById(userAddressSub.getProvinceId());
				City city = locationService.getCityById(userAddressSub.getCityId());
				Area area = locationService.getAreaById(userAddressSub.getAreaId());
				if (AppUtils.isNotBlank(area)) {
					dlyArea = province.getProvince() + " " + city.getCity() + " " + area.getArea();
				} else {
					dlyArea = province.getProvince() + " " + city.getCity();
				}
				dlyAddress = dlyArea + " " + userAddressSub.getSubAdds();
			}else{
				dlyAddress = userAddressSub.getDetailAddress();
			}
		}

		ErpReportDataDTO erpReportDataDTO = com.legendshop.util.JSONUtil.getArray(contractSubInfo.getErpReportData(), ErpReportDataDTO.class).get(0);

		// 订单信息
		receiveDeliveryInfoParam.setObeiDeliveryCode(shipmentNo);
		receiveDeliveryInfoParam.setOrderNo(sub.getSubNumber());
		receiveDeliveryInfoParam.setContract(contractSubInfo.getContract());
		receiveDeliveryInfoParam.setContractType(contractSubInfo.getContractType());

		// 申请人信息
		receiveDeliveryInfoParam.setOperator(erpReportDataDTO.getErpReporter());
		receiveDeliveryInfoParam.setOperatorMobile(StrUtil.DASHED);
		receiveDeliveryInfoParam.setWorkingName(erpReportDataDTO.getErpReportCompany());

		// 收货人信息
		receiveDeliveryInfoParam.setStoreKeeper(userAddressSub.getReceiver());
		receiveDeliveryInfoParam.setStoreKeeperPhone(userAddressSub.getMobile());
		receiveDeliveryInfoParam.setDeliveryAddress(dlyAddress);
		receiveDeliveryInfoParam.setConsignee(contractSubInfo.getCompanyName());
		receiveDeliveryInfoParam.setContractType(contractSubInfo.getContractType());

		//详情信息
		List<ReceiveDeliveryPurchaserInfoParam.DeliveryItemVo> shipmentDetailList = new ArrayList<>();
		for (String thirdSkuId : subItemMap.keySet()) {
			ReceiveDeliveryPurchaserInfoParam.DeliveryItemVo printItemVo = new ReceiveDeliveryPurchaserInfoParam.DeliveryItemVo();
			SubItem subItem = subItemMap.get(thirdSkuId);

			if (ObjectUtil.isEmpty(receiveDeliveryInfoParam.getThirdOrderNo())) {
				receiveDeliveryInfoParam.setThirdOrderNo(subItem.getThirdNumber());
			}
			ItemNo itemNo = itemNoMap.get(subItem.getSubItemId());

			printItemVo.setErpOrderRowCode(subItem.getSubItemNumber());
			printItemVo.setSkuId(subItem.getThirdSkuId());
			printItemVo.setProdName(subItem.getProdName());
			printItemVo.setAttribute(subItem.getAttribute());
			printItemVo.setUnit(subItem.getUnit());
			printItemVo.setItemNo(itemNo.getPlanItemNo());
			printItemVo.setSourceRequest(sub.getOrderRemark());

			printItemVo.setMaterialCode(subItem.getMaterialCode());
			printItemVo.setMaterialName(subItem.getMaterialName());
			printItemVo.setMaterialUnit(subItem.getMaterialUnit());
			printItemVo.setMaterialInfo(subItem.getMaterialInfo());

			printItemVo.setOrderItemNum(subItem.getActualCount());

			shipmentDetailList.add(printItemVo);
		}
		 receiveDeliveryInfoParam.setErpDeliveryItem(shipmentDetailList);
		return  receiveDeliveryInfoParam;
	}

	/**
	 * 将 欧贝 打印的发货单消息
	 *
	 * @param erpReportData          申报人数据
	 * @param userAddressSub         订单地址
	 * @param contractSubInformation 合同信息
	 * @param subItems               订单项
	 * @return
	 */
	private List<ReceiveDeliveryInfoParam.DeliveryItemVo> mappingProperty(JSONObject erpReportData, UserAddressSub userAddressSub, ContractSubInformation contractSubInformation, List<SubItem> subItems, List<ItemNo> itemNos) {
		List<ReceiveDeliveryInfoParam.DeliveryItemVo> deliveryItemVoList = obeiOrderPlanConfigConverter.toList5(subItems);
		if (CollectionUtil.isNotEmpty(subItems)) {
			Map<String, SubItem> subNumberAndSubItem = subItems.stream()
				.collect(Collectors.toMap(SubItem::getSubItemNumber, Function.identity()));

			Map<Long, String> subItemIdAndItemId = itemNos.stream()
				.collect(Collectors.toMap(ItemNo::getSubItemId, ItemNo::getItemNo));

			for (ReceiveDeliveryInfoParam.DeliveryItemVo deliveryItemVo : deliveryItemVoList) {
				SubItem subItem = subNumberAndSubItem.get(deliveryItemVo.getErpOrderRowCode());
				deliveryItemVo.setErpOrderRowCode(subItem.getSubItemNumber());
				deliveryItemVo.setErpContractNumber(contractSubInformation.getContract());
				deliveryItemVo.setReceiveName(Optional.ofNullable(userAddressSub.getReceiver()).orElse(StrUtil.SPACE));
				deliveryItemVo.setReceiveMobile(Optional.ofNullable(userAddressSub.getMobile()).orElse(StrUtil.SPACE));
				deliveryItemVo.setErpItemCode(subItem.getMaterialCode());
				deliveryItemVo.setErpDeliveryTime(subItem.getDeliveryDate());
				deliveryItemVo.setDeclareMobile(erpReportData != null ? erpReportData.getString("erpReporter") + erpReportData.getString("erpReportPhone") : StrUtil.SPACE);
				deliveryItemVo.setProcurementRequirement(Optional.ofNullable(subItem.getSourcingRequest()).orElse(StrUtil.SPACE));
				deliveryItemVo.setErpOrderLineCode(subItemIdAndItemId.get(subItem.getSubItemId()));
			}
		}
		return deliveryItemVoList;
	}


	/**
	 * 将 欧贝 的发货单消息
	 *
	 * @param deliveryDetailItems
	 * @return
	 */
	private List<ShipmentItemDTO> mappingProperty(List<DeliveryDetailItem> deliveryDetailItems, List<SubItem> subItems, Map<String, DeliveryDetailItem> orderRowCodeMap, List<ItemNo> itemNos) {
//		Map<String, DeliveryDetailItem> collect = deliveryDetailItems
//			.stream()
//			.collect(Collectors.toMap(DeliveryDetailItem::getOrderRowCode, Function.identity()));
//
//		List<SubItem> subItems = subItemDao.queryByProperties(new LambdaEntityCriterion<>(SubItem.class)
//			.in(SubItem::getThirdItemNumber, collect.keySet())
//		);

		List<ShipmentItemDTO> shipmentItemDTOS = obeiOrderPlanConfigConverter.toShipmentItemDTO(deliveryDetailItems);

		if (CollectionUtil.isNotEmpty(subItems)) {
			Map<String, Long> thridItemNumberAndSubItemId = subItems.stream()
				.collect(Collectors.toMap(SubItem::getThirdItemNumber, SubItem::getSubItemId));

//			List<ItemNo> itemNos = itemNoDao.queryByProperties(new LambdaEntityCriterion<>(ItemNo.class)
//				.in(ItemNo::getSubItemId, thridItemNumberAndSubItemId.values()));
			Map<Long, Long> subItemIdAndItemId = itemNos.stream()
				.collect(Collectors.toMap(ItemNo::getSubItemId, ItemNo::getId));
			for (ShipmentItemDTO shipmentItemDTO : shipmentItemDTOS) {

				Long subItemId = thridItemNumberAndSubItemId.get(shipmentItemDTO.getOtherOrderNo());
				shipmentItemDTO.setSubItemId(subItemId);

				Long itemId = subItemIdAndItemId.get(shipmentItemDTO.getSubItemId());
				shipmentItemDTO.setItemNoId(itemId);

				DeliveryDetailItem deliveryDetailItem = orderRowCodeMap.get(shipmentItemDTO.getOtherOrderNo());
				if (Objects.nonNull(deliveryDetailItem)) {
					shipmentItemDTO.setAmount(deliveryDetailItem.getSkuAmount());
					shipmentItemDTO.setPrice(deliveryDetailItem.getPriceTax());
					shipmentItemDTO.setThirdShipRowNo(deliveryDetailItem.getRowCode());
				}
			}
		}
		return shipmentItemDTOS;
	}

	private ShipmentOperateDTO bindShipmentOperateDTO(Sub sub, R<ShopDetail> shopDetailByShopId) {
		return new ShipmentOperateDTO()
			.setShopId(sub.getShopId())
			.setSubId(sub.getSubId())
			.setUserId(shopDetailByShopId.getResult().getUserId())
			.setOperatorName(shopDetailByShopId.getResult().getCompanyName())
			.setOperatorId(shopDetailByShopId.getResult().getUserId())
			.setOperatorType(UserAccountTypeConst.MAIN)
			.setShipmentType(ShipmentTypeConst.SHIPMENT_PARTIAL);
	}


	/**
	 * 发货
	 *
	 * @param shipmentOperate the 发货信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void doShipmentV2(ShipmentOperateDTO shipmentOperate, Sub sub) {
		// {"logisticsId":0,"logisticsName":"自己发货","logisticsType":1,"operatorId":"b6bc6102-b0f0-486a-b6c6-9eb9536af59b","operatorName":"legendshop","operatorType":1,"shipmentType":2,"shopId":1501,"subId":157051,"userId":"b6bc6102-b0f0-486a-b6c6-9eb9536af59b"}
		log.info("平台发货: 请求参数【 {} 】", JSON.toJSONString(shipmentOperate, false));

		if (Objects.isNull(sub)) {
			throw new NotFoundBizException("订单不存在");
		}
		checkRules(shipmentOperate, sub);
		if (OrderStatusEnum.PARTIAL_SHIPMENT.value().equals(sub.getStatus())) {
			log.info("Orders status are already {}", OrderStatusEnum.PARTIAL_SHIPMENT);
			return;
		}
		UserAddressSub userAddressSub = userAddressSubDao.getUserAddressSub(sub.getAddrOrderId());
		if (Objects.isNull(userAddressSub)) {
			throw new NotFoundBizException("发货地址不存在");
		}
		List<SubItem> subItems = subItemDao.getSubItemBySubId(sub.getSubId());
		if (CollectionUtil.isEmpty(subItems)) {
			throw new NotFoundBizException("没有订单项");
		}
		// 部分发货
		if (shipmentOperate.getShipmentType().equals(ShipmentTypeConst.SHIPMENT_PARTIAL)) {
			sub.setShipmentType(ShipmentTypeConst.SHIPMENT_PARTIAL);
			sub.setStatus(OrderStatusEnum.PARTIAL_SHIPMENT.value()); // 分批发货
		} else {
			throw new ParameterErrorException("参数错误");
		}
		sub.setDvyDate(new Date());
		sub.setDvyFlowId(shipmentOperate.getLogisticsNo());
		sub.setDvyTypeId(shipmentOperate.getLogisticsId());
		sub.setDvyType(String.valueOf(shipmentOperate.getLogisticsType()));
		subDao.updateProperties(sub);

		//发货订单推送区块链
		this.amqpSendMsgUtil.convertAndSend(SubTOBlockChainConstant.SUB_BLOCK_CHAIN_EXCHANGE, SubTOBlockChainConstant.SUB_BLOCK_CHAIN_KEY, sub.getSubNumber());
		//保存订单记录
		SubHistory subHistory = new SubHistory();
		Date date = new Date();
		String time = subHistory.DateToString(date);
		subHistory.setRecDate(date);
		subHistory.setSubId(sub.getSubId());
		subHistory.setStatus(SubHistoryEnum.ORDER_PAY.value());
		String str = "商家于" + time + "完成发货";
		subHistory.setUserName(sub.getShopName());
		subHistory.setReason(str);
		subHistoryDao.saveSubHistory(subHistory);
	}

	private void checkRules(ShipmentOperateDTO shipmentOperate, Sub sub) {
		// 判断操作权限
		if (!sub.getShopId().equals(shipmentOperate.getShopId())) {
			throw new CannotDoException("无权限操作");
		}
//		if (!RefundReturnStatusEnum.ORDER_NO_REFUND.value().equals(sub.getRefundState())) {
//			throw new NotFoundBizException("订单退款中");
//		}
//		if (!PurchaseTypeEnum.ENTERPRISE.value().equals(sub.getPurchaseType()) && ShipmentTypeConst.SHIPMENT_PARTIAL.equals(shipmentOperate.getShipmentType())) {
//			throw new DataErrorException("个人无法进行分批发货");
//		}
	}


	/**
	 * 调用门禁申请
	 *
	 * @param body
	 * @return
	 */
	private boolean reqErpGateApply(Object body) {
		if (null == body) {
			return false;
		}
		log.info("请求的接口：{}", shipmentConfig.getErpHost() + erpSendDataUrlConfig.getCreateForMro());
		String url = shipmentConfig.getErpHost() + erpSendDataUrlConfig.getCreateForMro();
		String json = JSON.toJSONString(body, new PascalNameFilter(), SerializerFeature.WriteNullNumberAsZero, SerializerFeature.WriteNullStringAsEmpty, SerializerFeature.WriteNullListAsEmpty);
		log.info("请求门禁申请接口: {}, 数据： {}", url, json);
		Map<String, Object> param = new HashMap<>();

		param.put("data", URLUtil.encode(json, CharsetUtil.CHARSET_UTF_8));
		String result2 = HttpUtil.createPost(url)
			.contentType(ContentType.FORM_URLENCODED.toString())
			.form(param).execute().body();
		log.info("门禁申请接口响应: {}", result2);
		Map result = null;
		if (StringUtil.isNotBlank(result2)) {
			result = JSON.parseObject(result2, Map.class);
		}
		String status = (result == null) ? null : (result.get("status") == null ? "" : result.get("status").toString());
		int statusCode = 500;
		if ("200".equalsIgnoreCase(status)) {
			statusCode = 200;
		} else {
			log.error("门禁申请失败： {}", json);
		}
		orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, url, json, statusCode, result2);
		return statusCode == 200;
	}

	/**
	 * 申请发货 分批发货
	 *
	 * @param shipmentDTO the 发货信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void applyPartialShipment(PartialShipmentDTO shipmentDTO, boolean forceApply) {
		log.info("forceApply值为" + forceApply);
		log.info("分批发货: 请求参数【 {} 】", JSON.toJSONString(shipmentDTO, false));
		if (StringUtil.isBlank(shipmentDTO.getApplicantId()) ||
			null == shipmentDTO.getApplicantType() ||
			StringUtil.isBlank(shipmentDTO.getApplicantName())) {
			throw new ParameterErrorException("请传入申请人信息");
		}
		Sub sub = subDao.getSubById(shipmentDTO.getSubId());
		if (null == sub) {
			throw new RuntimeException("订单不存在");
		}

		UserAddressSub userAddressSub = userAddressSubDao.getUserAddressSub(sub.getAddrOrderId());
		if (null == userAddressSub) {
			throw new RuntimeException("发货地址不存在");
		}
		List<SubItem> subItems = subItemDao.getSubItemBySubId(sub.getSubId());
		if (null == subItems || subItems.isEmpty()) {
			throw new RuntimeException("没有订单项");
		}
		// 检查是否已经发货完成
		boolean isFinish = checkShipmentFinish(subItems);
		if (isFinish) {
			throw new RuntimeException("已经全发货完");
		}


		log.info("订单判断需要申请门禁？");
		boolean isErp = OrderSourceEnum.ERP.value().equals(sub.getOrderSource());
		boolean isMro = OrderSourceEnum.MRO.value().equals(sub.getOrderSource());
		if (forceApply) {
			shipmentDTO.setDispatchingCarsStatus(DispatchingCarsStatusEnum.ALLOWED.getCode());
		}

		shipmentDTO.setOrderNo(sub.getSubNumber());
		shipmentDTO.setOrderSource(sub.getOrderSource());
		shipmentDTO.setBatchNo(CommonServiceUtil.getSubNumber(shipmentDTO.getApplicantId()));
        //获取合同号
        String contractNum = subItemDao.getContractNumByParam(sub.getSubNumber());
        if (null != contractNum) {
            shipmentDTO.setContractNum(contractNum);
        }
		shipmentDTO.setShopId(sub.getShopId()); // 供应商
		shipmentDTO.setBuyerId(sub.getUserId()); // 采购商
		shipmentDTO.setApplicantTime(new Date());
		shipmentDTO.setConsignee(userAddressSub.getReceiver()); // 收货人
		shipmentDTO.setReceiverAddress(userAddressSub.getDetailAddress()); // 收货地址
		shipmentDTO.setContactNumber(userAddressSub.getMobile()); // 联系人电话
		shipmentDTO.setStatus(ShipmentStatus.APPLYING);
		shipmentDTO.setShipmentType(ShipmentTypeConst.SHIPMENT_PARTIAL);

		List<ShipmentItemDTO> itemList = shipmentDTO.getShipmentItems();
		// 分批发货需要选择
		if (null == itemList || itemList.isEmpty()) {
			throw new ParameterErrorException("请选择商品");
		}
		log.info("生成单据，saveShipment");
		// 生成单据
		Shipment shipment = obeiOrderPlanConfigConverter.toShipment(shipmentDTO);
		// 保存配送单记录
		Long shipmentId = shipmentDao.saveShipment(shipment);
		shipmentDTO.setShipmentId(shipmentId);
		List<ItemNo> updateItemNoList = new ArrayList<>();
		List<SubItem> updateSubItemList = new ArrayList<>();
		List<ApplyShipmentGoodsDTO> erpGoodsList = new ArrayList<>();


		List<ShipmentItem> shipmentItems = itemList
			.stream()
			.map(partialShipmentItem -> {
				SubItem subItem = subItemDao.getSubItem(partialShipmentItem.getSubItemId());
				// 检查订单项是否真实
				if (null == subItem || !subItem.getSubNumber().equals(shipmentDTO.getOrderNo())) {
					throw new NotFoundBizException("订单项数据不存在");
				}

				log.info("生成ShipmentItem ,判断数量");
				// 判断数量
				if (subItem.getRemainCount() == null) {
					subItem.setRemainCount(Optional.ofNullable(subItem.getActualCount()).orElse(subItem.getBasketCount()));
				}
				BigDecimal remainCount = NumberUtil.sub(subItem.getRemainCount(), partialShipmentItem.getShipmentCount());
//				if(remainCount.compareTo(BigDecimal.ZERO) < 0) {
//					throw new BusinessException("发货数量不能大于剩余数量! skuId=" + partialShipmentItem.getSkuId() + "发货数量：" + partialShipmentItem.getShipmentCount() + "剩余数量：" + subItem.getRemainCount());
//				}

				log.info("生成ShipmentItem ,减库存！");
				if (isMro && StrUtil.isEmpty(subItem.getThirdNumber())) {
					// 减少实际库存
					boolean conf = stockService.reduceActualHold(subItem.getProdId(), subItem.getSkuId(), Optional.ofNullable(partialShipmentItem.getShipmentCount()).orElse(BigDecimal.ZERO).longValue());
					if (!conf) {
						// 在支付后减库存，有可能导致超卖现象
						if (log.isWarnEnabled()) {
							log.warn("发货时调用失败 reduceActualHold for prodId: {}, skuId: {}， basketCount: {}",
								subItem.getProdId(), subItem.getSkuId(), Optional.ofNullable(partialShipmentItem.getShipmentCount()).orElse(BigDecimal.ZERO).longValue());
						}
						throw new ParameterErrorException("减库存失败");
					}
				}

				subItem.setRemainCount(remainCount);
				updateSubItemList.add(subItem);
				ShipmentItem shipmentItem = new ShipmentItem();
				shipmentItem.setShipmentId(shipmentId);
				shipmentItem.setBatchNo(shipment.getBatchNo());
				shipmentItem.setContractNum(shipment.getContractNum());
				shipmentItem.setProdId(subItem.getProdId());
				shipmentItem.setSkuId(subItem.getSkuId());
				shipmentItem.setPic(subItem.getPic());
				shipmentItem.setProdName(subItem.getProdName());
				shipmentItem.setSubItemId(subItem.getSubItemId());
				shipmentItem.setWeight(subItem.getWeight());
				shipmentItem.setVolume(subItem.getVolume());
				shipmentItem.setReceiveCount(BigDecimal.ZERO);
				shipmentItem.setRefundCount(BigDecimal.ZERO);
				shipmentItem.setRejectCount(BigDecimal.ZERO);
				shipmentItem.setPrice(partialShipmentItem.getPrice());
				shipmentItem.setAmount(partialShipmentItem.getAmount());
				shipmentItem.setShipmentCount(partialShipmentItem.getShipmentCount());
				shipmentItem.setItemNoId(partialShipmentItem.getItemNoId());
				shipmentItem.setThirdShipRowNo(partialShipmentItem.getThirdShipRowNo());

				log.info("ERP下单则需要对应到具体的项次判断！");
				// ERP下单则需要对应到具体的项次
				if (isErp) {
					ItemNo itemNo = itemNoDao.getByItemNoIdAndSubItemId(shipmentItem.getItemNoId(), shipmentItem.getSubItemId());
					if (null == itemNo) {
						throw new NotFoundBizException("项次 " + shipmentItem.getItemNo() + " 不存在");
					}

					remainCount = NumberUtil.sub(itemNo.getRemainCount(), shipmentItem.getShipmentCount()).compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : NumberUtil.sub(itemNo.getRemainCount(), shipmentItem.getShipmentCount());

					itemNo.setRemainCount(remainCount);
					// 加入更新列表
					updateItemNoList.add(itemNo);

					if (AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId().equals(sub.getAccountBookId())) {
						ContractSubInformation contractSubInformation = contractSubInformationDao.getByContractNumber(sub.getContractNum());
						if (ObjectUtil.isEmpty(contractSubInformation)) {
							throw new NotFoundBizException("合同下单信息不存在");
						}

						// 加入申请发货列表
						ApplyShipmentGoodsDTO applyGoods = new ApplyShipmentGoodsDTO();
						applyGoods.setProdNum(Optional.ofNullable(shipmentItem.getShipmentCount()).orElse(BigDecimal.ZERO));
						applyGoods.setErpApiNo("ERPIN01");
						applyGoods.setItemNo(itemNo.getItemNo());
						applyGoods.setOrderNumber(subItem.getSubNumber());

						// 南钢erp配送单参数处理
						applyGoods.setContractNumber(Optional.ofNullable(contractSubInformation.getContract()).orElse(StrUtil.EMPTY));
						applyGoods.setBatchNo(shipment.getBatchNo());
						applyGoods.setArriveDate(shipment.getApplicantTime());
						erpGoodsList.add(applyGoods);
					}


					shipmentItem.setItemNo(itemNo.getItemNo());
					shipmentItem.setItemNoId(itemNo.getId());
				}
				return shipmentItem;
			}).collect(Collectors.toList());

		if (CollectionUtil.isEmpty(shipmentItems)) {
			throw new NotFoundBizException("没有要发货的商品");
		}

		log.info("shipmentItems保存：{}", JSONUtil.toJsonStr(shipmentItems));
		shipmentItemDao.batchSave(shipmentItems);

		if (!updateSubItemList.isEmpty()) {
			subItemDao.batchUpdate(updateSubItemList);
		}
		if (!updateItemNoList.isEmpty()) {
			itemNoDao.batchUpdate(updateItemNoList);
		}

		// todo 后续删除，整合至ErpShipmentStrategy中
		// 调用erp接口
		log.info("准备调用erp接口");
		if (isErp && AppUtils.isNotBlank(erpGoodsList) && DispatchingCarsStatusEnum.ALLOWED.getCode().equals(shipmentDTO.getDispatchingCarsStatus())) { //派车申请状态
			// 账套不为空 且 不是xzl账套，就不执行
			if (ObjectUtil.isNotEmpty(sub.getAccountBookId()) && !AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId().equals(sub.getAccountBookId())) {
				return;
			}
			log.info("进入判断条件");
			if (CollectionUtil.isEmpty(erpGoodsList)) {
				throw new NotFoundBizException("没有要发货商品");
			}

			log.info("全局事务提交成功后，发MQ做到货申请!");
			//全局事务提交成功后，发MQ申请
			if (GlobalTransactionContext.getCurrent() != null) {
				log.info("全局事务不为空 增强事务!");
				TransactionHookManager.registerHook(new TransactionHookAdapter() {
					@Override
					public void afterCompletion() {
					}

					@Override
					public void afterCommit() {
						super.afterCommit();
						log.info("全局事务提交成功,发送消息");
						amqpSendMsgUtil.convertAndSend(ErpOrderMQConstant.ERP_ORDER_CHAIN_EXCHANGE, ErpOrderMQConstant.ARRIVAL_APPLT_ROUTING_KEY, shipment.getBatchNo());
					}
				});
			}
		}
	}


	@Override
	public ShipmentVo mroArrivalApply(String batchNo) {
		log.info("进入到货申请！");
		List<ApplyShipmentGoodsDTO> erpGoodsList = new ArrayList<>();
		Shipment shipment = shipmentDao.getByBatchNo(batchNo);
		List<SubItem> updateSubItemList = new ArrayList<>();
		if (ObjectUtil.isEmpty(shipment)) {
			throw new NotFoundBizException("查出来的shipment为空");
		}
		Sub sub = subDao.getSubById(shipment.getSubId());

		List<ShipmentItem> shipmentItemList = this.shipmentItemDao.getShipmentItemByShipmentId(shipment.getShipmentId());

		ContractSubInformation contractSubInformation = contractSubInformationDao.getByContractNumber(sub.getContractNum());

		//无人驿站不需要做到货申请，调用无人驿站通知快递信息
		if(DeliveryTypeEnum.EXPRESS_DELIVERY_CABINET.value().equals(sub.getDeliveryType())){
			log.info("此配送单为无人驿站配送");
			AsyncOrderRequest asyncOrderRequest = new AsyncOrderRequest();
			//全部都定义为内部物流编号
			asyncOrderRequest.setExpressId(9999);
			asyncOrderRequest.setExpressNumber(shipment.getThirdShipNo());
			asyncOrderRequest.setStationId(Integer.valueOf(sub.getStationId()));

			//根据sub表的addrOrderId获取收件人电话
			UserAddressSub userAddressSub = this.userAddressSubDao.getUserAddressSub(sub.getAddrOrderId());
			log.info("此配送单地址信息："+ JSONObject.toJSONString(userAddressSub));
			asyncOrderRequest.setReceiverMobile(userAddressSub.getMobile());

			log.info("调用信息信息："+ JSONObject.toJSONString(asyncOrderRequest));
			DiyiResponse<Boolean> booleanDiyiResponse = DiyiClient.asyncLogisticsInfo(asyncOrderRequest);
			log.info("调用信息结果："+ JSONObject.toJSONString(booleanDiyiResponse));
			if(!booleanDiyiResponse.getStatus()){
				ShipmentVo shipmentVo = new ShipmentVo();
				shipmentVo.setArrivalApplyStatus(10);
				shipmentVo.setReturnMessage(Optional.ofNullable(booleanDiyiResponse.getMessage()).orElse("物流信息同步到驿站失败"));
				return shipmentVo;
			}

			shipment.setArrivalApplyStatus(20);
			shipment.setArrivalApplyRemark("成功");
			shipment.setDispatchingCarsStatus(DispatchingCarsStatusEnum.ALREADY_DISPATCHING.getCode());
			shipmentDao.update(shipment);
			return shipmentDetailConverter.to2(shipment);
		}



		shipmentItemList.stream()
				.map(partialShipmentItem -> {
					SubItem subItem = subItemDao.getSubItem(partialShipmentItem.getSubItemId());
					// 检查订单项是否真实

					BigDecimal remainCount = NumberUtil.sub(subItem.getRemainCount(), partialShipmentItem.getShipmentCount());
					subItem.setRemainCount(remainCount);
					updateSubItemList.add(subItem);
					ShipmentItem shipmentItem = new ShipmentItem();
					shipmentItem.setShipmentId(shipment.getId());
					shipmentItem.setBatchNo(shipment.getBatchNo());
					shipmentItem.setContractNum(shipment.getContractNum());
					shipmentItem.setProdId(subItem.getProdId());
					shipmentItem.setSkuId(subItem.getSkuId());
					shipmentItem.setPic(subItem.getPic());
					shipmentItem.setProdName(subItem.getProdName());
					shipmentItem.setSubItemId(subItem.getSubItemId());
					shipmentItem.setWeight(subItem.getWeight());
					shipmentItem.setVolume(subItem.getVolume());
					shipmentItem.setReceiveCount(BigDecimal.ZERO);
					shipmentItem.setRefundCount(BigDecimal.ZERO);
					shipmentItem.setRejectCount(BigDecimal.ZERO);
					shipmentItem.setPrice(partialShipmentItem.getPrice());
					shipmentItem.setAmount(partialShipmentItem.getAmount());
					shipmentItem.setShipmentCount(partialShipmentItem.getShipmentCount());
					shipmentItem.setItemNoId(partialShipmentItem.getItemNoId());
					shipmentItem.setThirdShipRowNo(partialShipmentItem.getThirdShipRowNo());

					// ERP下单则需要对应到具体的项次
					log.info("ERP下单则需要对应到具体的项次");
					if (OrderSourceEnum.ERP.value().equals(sub.getOrderSource())) {
						ItemNo itemNo = itemNoDao.getByItemNoIdAndSubItemId(shipmentItem.getItemNoId(), shipmentItem.getSubItemId());
						remainCount = NumberUtil.sub(itemNo.getRemainCount(), shipmentItem.getShipmentCount());

						itemNo.setRemainCount(remainCount);
						itemNo.setOutCount(NumberUtil.add(itemNo.getOutCount(), shipmentItem.getShipmentCount()));
						// 加入申请发货列表
						ApplyShipmentGoodsDTO applyGoods = new ApplyShipmentGoodsDTO();
						applyGoods.setProdNum(Optional.ofNullable(shipmentItem.getShipmentCount()).orElse(BigDecimal.ZERO));
						applyGoods.setErpApiNo("ERPIN01");
						applyGoods.setItemNo(itemNo.getItemNo());
						applyGoods.setOrderNumber(subItem.getSubNumber());
						applyGoods.setContractNumber(Optional.ofNullable(contractSubInformation.getContract()).orElse(StrUtil.SPACE));
						applyGoods.setBatchNo(shipment.getBatchNo());
						applyGoods.setArriveDate(shipment.getApplicantTime());
						erpGoodsList.add(applyGoods);

						shipmentItem.setItemNo(itemNo.getItemNo());
						shipmentItem.setItemNoId(itemNo.getId());
					}
					return shipmentItem;
				}).collect(Collectors.toList());

		log.info("请求的地址：{}",shipmentConfig.getErpHost() + erpSendDataUrlConfig.getArrivalMroV2());
		String url = shipmentConfig.getErpHost() + erpSendDataUrlConfig.getArrivalMroV2();

		log.info(String.format("请求路径{}:%s", url));
		String body = JSON.toJSONString(erpGoodsList);

		log.info(String.format("请求内容{}:%s", body));
//		R<String> r = HttpClientUtil.sendPost(url, body, null);
		//ESB切换http请求为https
//		R<String> r = HttpClientUtil.sendPost(url, applyMesaage, null);
		HttpRequest post = HttpUtil.createPost(url).body(body);
		HttpResponse execute = post.execute();
		Integer status = null;
		String msg = "";
		if (execute.isOk()) {
			log.info("返回结果：" + execute.body());
			String responseBody = execute.body();
			com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(responseBody);
			status =  new Integer((String)jsonObject.get("status"));
			msg = jsonObject.get("msg").toString();
		}
		orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, url, body, status,msg);

		shipment.setUpdateTime(new DateTime());

		if (Objects.equals(status, HttpStatus.HTTP_OK) || Objects.equals(status, 999)) {
			log.info("保存成功");
			//申请成功，保存数据库
			shipment.setArrivalApplyStatus(20);
			shipment.setArrivalApplyRemark("成功");
			shipment.setDispatchingCarsStatus(DispatchingCarsStatusEnum.ALLOWED.getCode());
			shipmentDao.update(shipment);
			return shipmentDetailConverter.to2(shipment);
		}

		log.error("调用到货申请接口失败 地址: {}, 参数: {}, 响应消息: {}", url, body, msg);
		//更新数据表
		shipment.setArrivalApplyStatus(10);
		shipment.setArrivalApplyRemark(msg);
		shipment.setDispatchingCarsStatus(DispatchingCarsStatusEnum.NOT_ALLOWED.getCode());
		shipmentDao.update(shipment);
		//返回错误信息
		ShipmentVo shipmentVo = new ShipmentVo();
		shipmentVo.setArrivalApplyStatus(10);
		shipmentVo.setReturnMessage(Optional.ofNullable(msg).orElse("申请失败"));
		return shipmentVo;
	}

	@Override
	public R<String> uploadErpDelivreyFile(DeliveryNoteFilesDTO deliveryNoteFilesDTO) {
		if(StringUtil.isBlank(deliveryNoteFilesDTO.getBatchNo())) {
			return R.fail("送货单号为空！");
		}
		if(deliveryNoteFilesDTO.getFile() == null) {
			return R.fail("送货单文件为空！");
		}
		if(!"application/pdf".equals(deliveryNoteFilesDTO.getFile().getContentType()) || deliveryNoteFilesDTO.getFile().getSize()/1024 > 2048) {
			return R.fail("文件过大或者文件非pdf格式！");
		}
		Shipment shipment = shipmentDao.getByBatchNo(deliveryNoteFilesDTO.getBatchNo());
		Sub sub = subDao.getSubById(shipment.getSubId());
		List<ShipmentItem> shipmentItemList = this.shipmentItemDao.getShipmentItemByShipmentId(shipment.getShipmentId());
		ContractSubInformation contractSubInformation = contractSubInformationDao.getByContractNumber(sub.getContractNum());
		log.info("请求的地址：{}",shipmentConfig.getErpHost() + erpSendDataUrlConfig.getGetDeliveryNoteFiles());
		String url = shipmentConfig.getErpHost() + erpSendDataUrlConfig.getGetDeliveryNoteFiles();

		log.info(String.format("请求路径{}:%s", url));
		Map<String,Object> map = new HashMap<>();
        File file = null;
        try {
            file = FileUtil.multipartFileToFile(deliveryNoteFilesDTO.getFile());
			map.put("file", file);
			map.put("orderNumber",sub.getSubNumber());
			map.put("bath_no",shipment.getBatchNo());
			map.put("item_no",shipmentItemList.get(0).getItemNo());
			map.put("contract_number",Optional.ofNullable(contractSubInformation.getContract()).orElse(StrUtil.SPACE));
			log.info(String.format("请求内容{}:%s", map.toString()));
			HttpRequest post = HttpUtil.createPost(url).form(map);

			HttpResponse execute = post.execute();
			Integer status = null;
			String msg = "";
			if (execute.isOk()) {
				log.info("返回结果：" + execute.body());
				String responseBody = execute.body();
				com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(responseBody);
				status =  new Integer((String)jsonObject.get("status"));
				msg = jsonObject.get("msg").toString();
			}else {
				return R.fail(com.alibaba.fastjson.JSONObject.parseObject(execute.body()).get("msg").toString());
			}
			orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, url, map.toString(), status,msg);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
			if(file != null) {
				FileUtil.delteTempFile(file);
			}
		}
		return R.ok();
	}

	/**
	 * 确认出库
	 *
	 * @param shipmentOperateDTO
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void confirmStockOut(ShipmentOperateDTO shipmentOperateDTO) {
		confirmStockOutWithoutNotification(shipmentOperateDTO);
		Shipment shipment = shipmentDao.getShipment(shipmentOperateDTO.getShipmentId());
		Sub sub = subDao.getSubById(shipment.getSubId());
		//鑫采商城配送单生成消息通知
		messagePoolShipmentService.pushShipmentCreate(sub, shipment);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void confirmStockOutWithoutNotification(ShipmentOperateDTO shipmentOperateDTO) {
		log.info("确认出库: 请求参数【 {} 】", JSON.toJSONString(shipmentOperateDTO, false));
		if (null == shipmentOperateDTO.getShipmentId()) {
			throw new NotFoundBizException("数据不存在");
		}

		Shipment shipment = shipmentDao.getShipment(shipmentOperateDTO.getShipmentId());
		if (null == shipment) {
			throw new NotFoundBizException("数据不存在");
		}

		// 判断供应商
		if (!shipment.getShopId().equals(shipmentOperateDTO.getShopId())) {
			throw new DataErrorException("无法操作");
		}
		Sub sub = subDao.getSubById(shipment.getSubId());
		if (null == sub) {
			throw new NotFoundBizException("订单不存在");
		}

		shipment.setOutOperatorId(shipmentOperateDTO.getOperatorId());
		shipment.setOutOperatorName(shipmentOperateDTO.getOperatorName());
		shipment.setOutOperatorTime(new Date());
		shipment.setOutOperatorType(shipmentOperateDTO.getOperatorType());
		shipment.setLogisticsName(shipmentOperateDTO.getLogisticsName());
		shipment.setLogisticsId(shipmentOperateDTO.getLogisticsId());
		shipment.setLogisticsNo(shipmentOperateDTO.getLogisticsNo());
		shipment.setLogisticsAmount(shipmentOperateDTO.getLogisticsAmount());
		shipment.setLogisticsType(shipmentOperateDTO.getLogisticsType());
		shipment.setStatus(ShipmentStatus.STOCK_OUT);

		// 去重操作
		Map<Long, SubItem> longSubItemMap = new HashMap<>();
		Map<Long, ItemNo> longItemNoMap = new HashMap<>();

		List<ShipmentItem> shipmentItems = shipmentItemDao.findShipmentItems(shipment.getShipmentId());
		if (CollectionUtil.isNotEmpty(shipmentItems)) {
			List<Long> subItemIds = shipmentItems.stream().map(ShipmentItem::getSubItemId).collect(Collectors.toList());
			List<Long> itemNoIds = shipmentItems.stream().map(ShipmentItem::getItemNoId).collect(Collectors.toList());

			longSubItemMap = subItemDao.queryByProperties(new LambdaEntityCriterion<>(SubItem.class)
					.in(SubItem::getSubItemId, subItemIds)).stream().collect(Collectors.toMap(SubItem::getId, Function.identity()));

			longItemNoMap = itemNoDao.queryByProperties(new LambdaEntityCriterion<>(ItemNo.class)
					.in(ItemNo::getId, itemNoIds)).stream().collect(Collectors.toMap(ItemNo::getId, Function.identity()));
			;

			for (ShipmentItem item : shipmentItems) {
				SubItem subItem = longSubItemMap.get(item.getSubItemId());
				if (Objects.nonNull(subItem)) {
					BigDecimal outCount = NumberUtil.add(subItem.getOutCount(), item.getShipmentCount());
					subItem.setOutCount(outCount);
				}
				if (Objects.nonNull(item.getItemNoId())) {
					ItemNo itemNo = longItemNoMap.get(item.getItemNoId());
					if (Objects.nonNull(itemNo)) {
						BigDecimal outCount = NumberUtil.add(itemNo.getOutCount(), item.getShipmentCount());
						itemNo.setOutCount(outCount);
					}
				}
			}

			if (!longSubItemMap.isEmpty()) {
				subItemDao.batchUpdate(ListUtil.toList(longSubItemMap.values()));
			}
			if (!longItemNoMap.isEmpty()) {
				itemNoDao.batchUpdate(ListUtil.toList(longItemNoMap.values()));
			}
		}

		shipmentDao.updateShipment(shipment);
	}

	/**
	 * 确认入库
	 *
	 * @param shipmentOperateDTO shipmentOperateDTO
	 */
	@Transactional(rollbackFor = Exception.class)
	public void confirmStockIn(ShipmentOperateDTO shipmentOperateDTO) {
		Shipment shipment = null;
		String thirdNumber = "";
		if (UserAccountTypeConst.ERP.equals(shipmentOperateDTO.getOperatorType())) {
			shipment = shipmentDao.getByBatchNo(shipmentOperateDTO.getBatchNo());
		} else {
			shipment = shipmentDao.getShipment(shipmentOperateDTO.getShipmentId());
		}
		if (null == shipment) {
			throw new NotFoundBizException("数据不存在");
		}
		// 判断订单来源
		if (UserAccountTypeConst.ERP.equals(shipmentOperateDTO.getOperatorType()) && !OrderSourceEnum.ERP.value().equals(shipment.getOrderSource())) {
			throw new CannotDoException("无法操作");
		}
		// 验证是否能操作 判断采购商
//		if (!UserAccountTypeConst.ERP.equals(shipmentOperateDTO.getOperatorType()) && !shipment.getBuyerId().equals(shipmentOperateDTO.getBuyerId())) {
//			throw new CannotDoException("无法操作");
//		}
		// 判断状态如果不是已出库（30）或者ERP收货未入库（31）状态，不允许确认入库
		if (!(ShipmentStatus.STOCK_OUT.equals(shipment.getStatus()) || ShipmentStatus.RECEIPT_GOODS.equals(shipment.getStatus()))) {
			throw new DataErrorException("当前状态不允许确认入库");
		}


		Map<Long, SubItem> subItemMap = new HashMap<>();
		Map<Long, ItemNo> itemNoMap = new HashMap<>();

		List<ShipmentItem> shipmentItems = shipmentItemDao.findShipmentItems(shipment.getShipmentId());
		if (null != shipmentItems && !shipmentItems.isEmpty()) {
			for (ShipmentItem shipmentItem : shipmentItems) {
				SubItem subItem = subItemMap.get(shipmentItem.getSubItemId());
				if (null == subItem) {
					subItem = subItemDao.getSubItem(shipmentItem.getSubItemId());
					if (null != subItem) {
						subItemMap.put(shipmentItem.getSubItemId(), subItem);
						if (StringUtil.isBlank(thirdNumber)) {
							thirdNumber = subItem.getThirdNumber();
						}
					}
				}
				if (null != subItem) {
					subItem.setInCount(NumberUtil.add(subItem.getInCount(), shipmentItem.getShipmentCount()));
				}


				if (null != shipmentItem.getItemNoId()) {
					ItemNo itemNo = itemNoMap.get(shipmentItem.getItemNoId());
					if (null == itemNo) {
						itemNo = itemNoDao.getItemNo(shipmentItem.getItemNoId());
						if (null != itemNo) {
							itemNoMap.put(itemNo.getId(), itemNo);
						}
					}
					if (null != itemNo) {
						itemNo.setInCount(NumberUtil.add(itemNo.getInCount(), shipmentItem.getShipmentCount()));
					}
				}

				//shipmentItem收货数量 == 发货数量， MRO订单是确认收货完整
				shipmentItem.setReceiveCount(shipmentItem.getShipmentCount());
			}
			List<SubItem> subItems = new ArrayList<>(subItemMap.values());
			List<ItemNo> itemNos = new ArrayList<>(itemNoMap.values());
			if (null != subItems && !shipmentItems.isEmpty()) {
				subItemDao.batchUpdate(subItems);
			}
			if (null != itemNos && !itemNos.isEmpty()) {
				itemNoDao.batchUpdate(itemNos);
			}
			shipmentItemDao.update(shipmentItems);
		}

		shipment.setInOperatorId(shipmentOperateDTO.getOperatorId());
		shipment.setInOperatorName(shipmentOperateDTO.getOperatorName());
		shipment.setInOperatorTime(new Date());
		shipment.setInOperatorType(shipmentOperateDTO.getOperatorType());
		shipment.setStatus(ShipmentStatus.STOCK_IN);
		shipment.setInCompleteOperatorTime(new Date());
		shipment.setReceiveRemark(shipmentOperateDTO.getRemark());
		shipmentDao.updateShipment(shipment);
		orderReceive(shipment,false);

		final Long shopId = shipment.getShopId();
		final String finalBatchNo = shipment.getBatchNo();
		final Long finalShipmentId = shipment.getShipmentId();

		String userId = "1";
		Sub sub = subDao.getSubBySubNumber(shipment.getOrderNo());
		if (OrderSourceEnum.MRO.value().equals(sub.getOrderSource())) {
			userId = "091fa85c-43be-4b4b-819e-dcca787d0cd8";
		}
		final String buyerUserId = userId;

		//事务处理完，通知第三方已入库
		String finalThirdNumber = thirdNumber;
		TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
			@Override
			public void afterCommit() {
				log.info("-----------确认收货入库，将真正的收货信息回调给第三方事务后提交-------");
				try {
					ShipmentCallBackThirdDTO shipmentCallBackThirdDTO = new ShipmentCallBackThirdDTO();
					shipmentCallBackThirdDTO.setUserId(buyerUserId);
					shipmentCallBackThirdDTO.setShopId(shopId);
					shipmentCallBackThirdDTO.setBatchNo(finalBatchNo);
					shipmentCallBackThirdDTO.setShipmentId(finalShipmentId);
					shipmentCallBackThirdDTO.setAccountBookId(sub.getAccountBookId());
					callbackThirdProcessor(shipmentCallBackThirdDTO);
//					callbackThirdProcessor(shipmentItems, companyPrincipalByUserIdResult, shopId, finalThirdNumber);
				} catch (Exception e) {
					log.error("通知第三方已入库出现异常 :{}", e.getMessage());
				}
			}
		});
	}


	public void orderReceive(Shipment shipment, Boolean orderMeasureType) {
		// 获取子项和配送单列表
		List<SubItem> subItems = subItemDao.getSubItemBySubId(shipment.getSubId());
		List<Shipment> shipmentList = shipmentDao.getByOrderNo(shipment.getOrderNo());

		// 判断是否全部入库完成
		boolean isAllFinish = !orderMeasureType ? checkIsAllStore(subItems, shipmentList) : checkMeteringOrderAllStore(subItems);
		boolean isAllCancel = checkIsAllCancel(subItems, shipmentList);
		Sub sub = subDao.getSubById(shipment.getSubId());
		if (isAllFinish) {
			if (isAllCancel) {
				// 处理订单取消逻辑
				handleOrderCancellation(sub);
			} else {
				// 处理订单完成逻辑
				handleOrderCompletion(sub);
			}
		}
	}

	// 处理订单完成的逻辑
	private void handleOrderCompletion(Sub sub) {
        if (OrderSourceEnum.MRO.value().equals(sub.getOrderSource())) {
            boolean result = subService.orderReceive(sub, sub.getUserId());
            if (!result) {
                throw new BusinessException("确认收货失败");
            }
        } else {
            sub.setStatus(OrderStatusEnum.CLEAN_PAYMENT.value());
            sub.setFinallyDate(new Date());
            subDao.update(sub);
            // 发送订单信息到区块链
            this.amqpSendMsgUtil.convertAndSend(SubTOBlockChainConstant.SUB_BLOCK_CHAIN_EXCHANGE, SubTOBlockChainConstant.SUB_BLOCK_CHAIN_KEY, sub.getSubNumber());
        }
    }

	// 处理订单取消的逻辑
	private void handleOrderCancellation(Sub sub) {
		sub.setStatus(OrderStatusEnum.CLOSE.value());
		sub.setCancelType(OrderCancelTypeEnum.USER_CANCEL_PAID.getCode());
		sub.setCancelDate(new Date());
		sub.setCancelReason("用户拒收所有商品并退货!");
		subDao.update(sub);
	}

	// 检查是否全部入库或取消
	public boolean checkIsAllStore(List<SubItem> subItems, List<Shipment> shipmentList) {
		boolean allDelivered = subItems.stream().allMatch(subItem -> subItem.getRemainCount().compareTo(BigDecimal.ZERO) == 0);

		long shipmentSize = shipmentList.size();
		long stockInShipmentSize = shipmentList.stream().filter(shipment -> ShipmentStatus.STOCK_IN.equals(shipment.getStatus())).count();
		long cancelShipmentSize = shipmentList.stream().filter(shipment -> ShipmentStatus.CANCEL.equals(shipment.getStatus())).count();
		boolean allStockInOrCancel = stockInShipmentSize + cancelShipmentSize == shipmentSize;

		return allDelivered && allStockInOrCancel;
	}

	// 检查是否全部取消
	public boolean checkIsAllCancel(List<SubItem> subItems, List<Shipment> shipmentList) {
		boolean allDelivered = subItems.stream().allMatch(subItem -> subItem.getRemainCount().compareTo(BigDecimal.ZERO) == 0);
		boolean shipmentAllCancel = shipmentList.stream().allMatch(shipment -> ShipmentStatus.CANCEL.equals(shipment.getStatus()));
		return allDelivered && shipmentAllCancel;
	}


	public Boolean checkMeteringOrderAllStore(List<SubItem> subItems) {
		//大于 0.95 （=1-0.05）算完成
		return subItems
			.stream()
			.filter(item -> {
					if(item.getInCount().compareTo(Optional.ofNullable(item.getActualCount()).orElse(item.getBasketCount()).multiply(new BigDecimal(0.95))) > 0){
						return true;
					}else {
						return false;
					}
				}
			)
			.count() == subItems.size();
	}

	/**
	 * 检查是否全部出库完成
	 *
	 * @param subItems
	 * @return
	 */
	public boolean checkIsAllStore(List<SubItem> subItems) {
		// 判断全部入库的数量等于订单项数量
		log.info("subItems : {}", JSONUtil.toJsonStr(subItems));
		log.info("subItems size : {}", subItems.size());
		log.info("subItems 收货完成的项为 : {}",
			subItems
				.stream()
				.filter(item -> item.getInCount().compareTo(Optional.ofNullable(item.getActualCount()).orElse(item.getBasketCount())) > -1)
				.count()
		);
		return subItems
			.stream()
			.filter(item -> item.getInCount().compareTo(Optional.ofNullable(item.getActualCount()).orElse(item.getBasketCount())) > -1)
			.count() == subItems.size();
	}


	/**
	 * 拒绝收货
	 *
	 * @param shipmentOperateDTO
	 */
	@Transactional(rollbackFor = Exception.class)
	public void rejectReceive(ShipmentOperateDTO shipmentOperateDTO) {
		Shipment shipment = null;
		if (UserAccountTypeConst.ERP.equals(shipmentOperateDTO.getOperatorType())) {
			shipment = shipmentDao.getByBatchNo(shipmentOperateDTO.getBatchNo());
		} else {
			shipment = shipmentDao.getShipment(shipmentOperateDTO.getShipmentId());
		}
		if (null == shipment) {
			throw new NotFoundBizException("数据不存在");
		}

		// 验证是否能操作 判断采购商
		if (!shipment.getBuyerId().equals(shipmentOperateDTO.getBuyerId())) {
			throw new CannotDoException("无法操作");
		}
		// 判断状态
		if (!ShipmentStatus.STOCK_OUT.equals(shipment.getStatus())) {
			throw new DataErrorException("当前状态不允许拒绝入库");
		}

		List<ShipmentItem> itemList = shipmentItemDao.findShipmentItems(shipment.getShipmentId());
		List<SubItem> updateSubItemList = new ArrayList<>();
		List<ItemNo> updateItemNoList = new ArrayList<>();
		BigDecimal remainCount, outCount;
		for (ShipmentItem item : itemList) {
			SubItem subItem = subItemDao.getSubItem(item.getSubItemId());
			// 退数量
			remainCount = NumberUtil.add(subItem.getRemainCount(), item.getShipmentCount());
			outCount = NumberUtil.sub(subItem.getOutCount(), item.getShipmentCount());
			subItem.setRemainCount(remainCount);
			subItem.setOutCount(outCount);
			updateSubItemList.add(subItem);

			if (null != item.getItemNoId()) {
				ItemNo itemNo = itemNoDao.getItemNo(item.getItemNoId());
				remainCount = NumberUtil.add(itemNo.getRemainCount(), item.getShipmentCount());
				outCount = NumberUtil.sub(itemNo.getOutCount(), item.getShipmentCount());
				itemNo.setRemainCount(remainCount);
				itemNo.setOutCount(outCount);
				updateItemNoList.add(itemNo);
			}
		}
		if (!updateSubItemList.isEmpty()) {
			subItemDao.batchUpdate(updateSubItemList);
		}
		if (!updateItemNoList.isEmpty()) {
			itemNoDao.batchUpdate(updateItemNoList);
		}

		shipment.setRejectOperatorId(shipmentOperateDTO.getOperatorId());
		shipment.setRejectOperatorName(shipmentOperateDTO.getOperatorName());
		shipment.setRejectOperatorTime(new Date());
		shipment.setRejectOperatorType(shipmentOperateDTO.getOperatorType());
		shipment.setStatus(ShipmentStatus.CANCEL);
		shipment.setReceiveRemark(shipmentOperateDTO.getRemark());
		shipmentDao.updateShipment(shipment);
	}


	/**
	 * 检查发货是否完成
	 */
	public boolean checkShipmentFinish(List<SubItem> subItems) {
		// 检查是否还有剩余数量
		return subItems.stream().noneMatch(subItem -> subItem.getRemainCount().compareTo(BigDecimal.ZERO) > 0);
	}

	/**
	 * 根据批次号
	 *
	 * @param batchNo
	 * @return
	 */
	public Shipment getShipment(String batchNo) {
		return shipmentDao.getByBatchNo(batchNo);
	}


	/**
	 * 物流信息
	 *
	 * @param batchNo
	 * @return
	 */
	public ShipmentOrderExpressDTO getShipmentOrderExpress(String batchNo) {
		List<ShipmentItem> shipmentItems = shipmentItemDao.zczyFindShipmentItems(batchNo);
		ShipmentOrderExpressDTO shipmentOrderExpress = shipmentDao.getShipmentOrderExpress(batchNo);
		shipmentOrderExpress.setShipmentItems(shipmentItems);
		return shipmentOrderExpress;
	}

	/**
	 * 商品体积、重量
	 *
	 * @param batchNo
	 * @return
	 */
	public Double[] getBenefactors(String batchNo) {
		List<ShipmentItem> shipmentItems = shipmentItemDao.zczyFindShipmentItems(batchNo);
		double weight = shipmentItems.stream().filter(w -> w.getWeight() != null).mapToDouble(ShipmentItem::getWeight).sum();
		double volume = shipmentItems.stream().filter(w -> w.getVolume() != null).mapToDouble(ShipmentItem::getVolume).sum();
		return new Double[]{weight, volume};
	}

	public List<Shipment> findShipments(ShipmentQueryParamDTO shipmentQueryParamDTO) {
		return shipmentDao.findShipments(shipmentQueryParamDTO);
	}

	/**
	 * 通过订单号查询发货单信息
	 *
	 * @param orderNumber
	 * @return
	 */
	public List<ShipmentDTO> findShipmentsByOrderNumber(String orderNumber) {
		//查询订单的所有配送单
		List<Shipment> shipmentList = shipmentDao.queryByProperties(new EntityCriterion().eq("orderNo", orderNumber));
		List<ShipmentDTO> shipmentDTOList = shipmentConverter.to(shipmentList);

		if (shipmentDTOList.size() > 0) {
			//查询所以有的配送单项
			List<ShipmentItem> shipmentItemList = shipmentItemDao.queryByProperties(new EntityCriterion().in("batchNo", shipmentList.stream().map(Shipment::getBatchNo).toArray()));
			shipmentDTOList.forEach(u -> {
				//找出当前配送单上的所有配送项，
				List<ShipmentItem> shipmentItemsForBatch = shipmentItemList.stream().filter(i -> i.getBatchNo().equals(u.getBatchNo())).collect(Collectors.toList());
				List<ShipmentItemDTO> shipmentItemDTOS = shipmentItemConverter.to(shipmentItemsForBatch);
				//把转换成的List放到ShipmentDto中
				u.setShipmentItems(shipmentItemDTOS);
			});
		}
		return shipmentDTOList;
	}


	@Override
	public List<ShipmentDTO> findShipmentsByOrderNumberAndStatus(String orderNumber, Integer status) {
		//查询订单的所有配送单
		List<Shipment> shipmentList = shipmentDao.queryByProperties(new EntityCriterion().eq("orderNo", orderNumber).eq("status", status));
		List<ShipmentDTO> shipmentDTOList = shipmentConverter.to(shipmentList);

		if (shipmentDTOList.size() > 0) {
			//查询所以有的配送单项
			List<ShipmentItem> shipmentItemList = shipmentItemDao.queryByProperties(new EntityCriterion().in("batchNo", shipmentList.stream().map(Shipment::getBatchNo).toArray()));
			shipmentDTOList.forEach(u -> {
				//找出当前配送单上的所有配送项，
				List<ShipmentItem> shipmentItemsForBatch = shipmentItemList.stream().filter(i -> i.getBatchNo().equals(u.getBatchNo())).collect(Collectors.toList());
				List<ShipmentItemDTO> shipmentItemDTOS = shipmentItemConverter.to(shipmentItemsForBatch);
				//把转换成的List放到ShipmentDto中
				u.setShipmentItems(shipmentItemDTOS);
			});
		}
		return shipmentDTOList;
	}

	/**
	 * 入库未完成的出库单
	 */
	public int finishedAllShipmentByErp(String userId, String userName, String subNumber) {
		return shipmentDao.finishedAllShipmentByErp(userId, userName, subNumber);
	}


	public Shipment findById(Long id) {
		return this.shipmentDao.getShipment(id);
	}


	/**
	 * 批量派车申请订单查询
	 */
	public List<MySubBaseDto> getdispatchingCarsOrder(String shopId) {
		return shipmentDao.getdispatchingCarsOrder(shopId);
	}


	/**
	 * @param erpShipCallbackParam
	 * @return
	 * @see ShipmentService#processErpShipmentCallback(ErpShipCallbackParam)
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean erpShipmentCallback(ErpShipCallbackParam erpShipCallbackParam) {
		String erpShipCallbackParamString = JSONObject.toJSONString(erpShipCallbackParam);
		log.info("收到erp收获确认回调：{}", erpShipCallbackParamString);
		orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "erp收货确认", erpShipCallbackParamString);

		// 抢锁，如果失败了，抛异常中断
		log.info("抢锁，如果失败了，抛异常中断");
		RLock lock = redissonClient.getLock(RedissonConstants.SHIPMENT_WALLET_LOCK_KEY + erpShipCallbackParam.getBatchNumber() + erpShipCallbackParam.getStatus());
		try {
			if (!lock.tryLock(5L, 60L, TimeUnit.SECONDS)) {
				log.info("consumptionCentre 更新收货单失败，抢锁失败，操作内容：{}", JSONUtil.toJsonStr(erpShipCallbackParam));
				throw new BusinessException("更新收货单失败，抢锁失败！");
			}
		} catch (Exception e) {
			log.error("consumptionCentre 更新收货单，抢锁异常，操作内容：" + JSONUtil.toJsonStr(erpShipCallbackParam));
			log.error(e.getMessage());
			throw new BusinessException("更新收货单失败，抢锁异常");
		}

		//事务完成后，提交成功后，进行解锁
		TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
			@Override
			public void afterCommit() {
				lock.unlock();
			}
		});
		this.processErpShipmentCallback(erpShipCallbackParam);

		return Boolean.TRUE;
	}


	/**
	 * @param erpShipCallbackParam
	 * @see ShipmentService#erpShipmentCallback(ErpShipCallbackParam)
	 */
	public void processErpShipmentCallback(ErpShipCallbackParam erpShipCallbackParam) {

		String orderNumber = erpShipCallbackParam.getOrderNumber();
		// 将 传过来的物料号 跟 订单子项 管理起来

		//是否接收入库， false为验收入库
		boolean isReceipt = Objects.equals(ErpShipCallbackParam.RECEIPT_GOODS, erpShipCallbackParam.getStatus());

		//查询配送单消息，根据配送单号
		Shipment shipment = shipmentDao.getByProperties(new LambdaEntityCriterion<>(Shipment.class).eq(Shipment::getBatchNo, erpShipCallbackParam.getBatchNumber()));

		// 只有俩种状态可以进入
		if (!erpShipCallbackParam.getStatus().equals(ErpShipCallbackParam.RECEIPT_GOODS) && !erpShipCallbackParam.getStatus().equals(ErpShipCallbackParam.STOCK_IN)) {
			log.error("状态非法  是否是收货 isReceipt {} {}",
				Objects.equals(ErpShipCallbackParam.RECEIPT_GOODS, erpShipCallbackParam.getStatus()),
				erpShipCallbackParam.getStatus());
			throw new BusinessException("状态非法  是否是收货 isReceipt {} {}" + Objects.equals(ErpShipCallbackParam.RECEIPT_GOODS, erpShipCallbackParam.getStatus()));
			//return;
		}

		//配送单状态
		if (!shipment.getStatus().equals(ShipmentStatus.STOCK_OUT) && !shipment.getStatus().equals(ShipmentStatus.RECEIPT_GOODS)) {
			log.error("状态非法  是否是收货 isReceipt {} {}",
				Objects.equals(ErpShipCallbackParam.RECEIPT_GOODS, erpShipCallbackParam.getStatus()),
				shipment.getStatus());
			throw new BusinessException("状态非法  是否是收货 isReceipt {} {}:" + Objects.equals(ErpShipCallbackParam.RECEIPT_GOODS, erpShipCallbackParam.getStatus()));
			//return;
		}

		Sub sub = subDao.getSubBySubNumber(orderNumber);
		R<NgCompanyPrincipalQualificationDTO> companyPrincipalQualificationDTOR = ngCompanyPrincipalQualificationClient.getNgCompanyPrincipalByUserId(sub.getUserId(), SecurityConstants.FROM_IN);
		if (!companyPrincipalQualificationDTOR.isSuccess() && !companyPrincipalQualificationDTOR.hasBody()) {
			log.error("查询不到,采购方信息NgCompanyPrincipalQualification");
			throw new BusinessException("查询不到,采购方信息NgCompanyPrincipalQualification");
		}
		NgCompanyPrincipalQualificationDTO companyPrincipalByUserIdResult = companyPrincipalQualificationDTOR.getResult();

		if (Objects.equals(erpShipCallbackParam.getContractNumber() + (StrUtil.isEmpty(erpShipCallbackParam.getContractVersion()) ? StrUtil.EMPTY : erpShipCallbackParam.getContractVersion()), sub.getContractNum())) {

			// 将 传过来的物料号 跟 订单子项 管理起来
			Map<String, ErpShipCallbackParam.Good> materialCodes = erpShipCallbackParam.getGoodsList().stream()
				.collect(Collectors.toMap(ErpShipCallbackParam.Good::getMaterialCode, Function.identity()));

			List<SubItem> subItemAllList = subItemDao.queryBySubNumber(orderNumber);
			List<SubItem> subItems = subItemAllList.stream().filter(subItem -> materialCodes.keySet().stream().anyMatch(materialCode -> Objects.equals(subItem.getMaterialCode(), materialCode))).collect(Collectors.toList());
			if (erpShipCallbackParam.getGoodsList().size() != subItems.size()) {
				log.error("查询不到相同订单项数量:" + subItems.size());
				throw new BusinessException("查询不到相同订单项数量:" + subItems.size());
			}


			if (CollUtil.isEmpty(subItems)) {
				log.error("修改订单项的数量，剩下:" + subItems.size());
				throw new BusinessException("修改订单项的数量，剩下:" + subItems.size());
			}
			//判断是否是售后单
		 	Boolean isRefund = ShipmentRefundRenewTypeEnum.REFUND_RENEW.getValue().equals(shipment.getRefundRenewType());
			if(isRefund) {
				receiveRenewShipment(shipment,sub,erpShipCallbackParam,isReceipt);
			}else {
				// 1、 修改 订单项的，入库数量，剩余数量
				log.info("修改订单项的，入库数量，剩余数量");
				Map<String, SubItem> subItemCollection = subItems.stream().collect(Collectors.toMap(SubItem::getMaterialCode, Function.identity()));
				log.info("物料号分组后的 subItemCollection :{}", JSONObject.toJSONString(subItemCollection));
				Map<Long, SubItem> subItemCollectionBySubItemId = subItems.stream().collect(Collectors.toMap(SubItem::getSubItemId, Function.identity()));
				log.info("订单项次id分组后的 subItemCollectionBySubItemId :{}", JSONObject.toJSONString(subItemCollectionBySubItemId));
				materialCodes.forEach((s, good) -> {
					SubItem subItem = subItemCollection.get(s);
					if (Objects.nonNull(subItem)) {
						good.setSubItemId(subItem.getSubItemId());
						log.info("拒绝收货的数量 {}", Optional.ofNullable(good.getRefuseAmount()).orElse(BigDecimal.ZERO));
						subItem.setRemainCount(NumberUtil.add(subItem.getRemainCount(), good.getRefuseAmount()));

						if (shipment.getStatus().equals(ShipmentStatus.STOCK_OUT)) {
							log.info(" subItem 入库数量:" + Optional.ofNullable(subItem.getInCount()).orElse(BigDecimal.ZERO) + " 加上:" + good.getSureAmount() + "收货数量");
							subItem.setInCount(NumberUtil.add(subItem.getInCount(), good.getSureAmount()));
						} else if (shipment.getStatus().equals(ShipmentStatus.RECEIPT_GOODS)) {
							log.info(" subItem  入库数量:" + Optional.ofNullable(subItem.getInCount()).orElse(BigDecimal.ZERO) + " 减去:" + good.getRefuseAmount() + "拒收数量");
							// 因为第二次 的总数量 是 以  确认收到 的 为基础  所以再次 拒收就在原基础上减少
							subItem.setInCount(NumberUtil.sub(subItem.getInCount(), good.getRefuseAmount()));
						}

						if ((good.getMeasureType()==null || good.getMeasureType()!=10) && Optional.ofNullable(subItem.getInCount()).orElse(BigDecimal.ZERO).compareTo(Optional.ofNullable(subItem.getActualCount()).orElse(subItem.getBasketCount())) > 0) {
							log.info("入库数量不能大于购买数量");
							throw new BusinessException("入库数量不能大于购买数量");
						}
					}
				});


				Map<Long, ErpShipCallbackParam.Good> stringGoodMap = erpShipCallbackParam.getGoodsList().stream()
					.collect(Collectors.toMap(ErpShipCallbackParam.Good::getSubItemId, Function.identity()));

				log.info("第二步");
				//2、修改ItemNo 入库数量【累计的收货数量】,拒收数量【累计的拒收数量】,剩余数量【剩下可发货的数量】

				List<ItemNo> itemNos = itemNoDao.queryByProperties(new LambdaEntityCriterion<>(ItemNo.class)
					.eq(ItemNo::getSubId, sub.getSubId())
					.in(ItemNo::getSubItemId, stringGoodMap.keySet())
				);

				//  修改item 项
				Map<Long, ItemNo> itemNoCollection = itemNos.stream().collect(Collectors.toMap(ItemNo::getSubItemId, Function.identity()));
				itemNoCollection.forEach((subItemId, itemNo) -> {
					ErpShipCallbackParam.Good good = stringGoodMap.get(subItemId);
					if (Objects.nonNull(good)) {
						if (shipment.getStatus().equals(ShipmentStatus.STOCK_OUT)) {
							log.info(" itemNo 项 : 入库数量:" + Optional.ofNullable(itemNo.getInCount()).orElse(BigDecimal.ZERO) + "加上:" + good.getSureAmount() + "收货数量");
							itemNo.setInCount(NumberUtil.add(itemNo.getInCount(), good.getSureAmount()));
						} else if (shipment.getStatus().equals(ShipmentStatus.RECEIPT_GOODS)) {
							log.info(" itemNo 项 :入库数量:" + Optional.ofNullable(itemNo.getInCount()).orElse(BigDecimal.ZERO) + "减去:" + Optional.ofNullable(good.getRefuseAmount()).orElse(BigDecimal.ZERO) + "拒收数量");
							// 因为第二次 的总数量 是 以  确认收到 的 为基础  所以再次 拒收就在原基础上减少
							itemNo.setInCount(NumberUtil.sub(itemNo.getInCount(), good.getRefuseAmount()));
						}
//					itemNo.setRejectCount(NumberUtil.add(itemNo.getRejectCount(), good.getRefuseAmount()));
					}
				});

				//3、修改 ShipmentItem 收货数量 ，拒收数量
				List<ShipmentItem> shipmentItems = shipmentItemDao.queryByProperties(new LambdaEntityCriterion<>(ShipmentItem.class)
					.eq(ShipmentItem::getShipmentId, shipment.getShipmentId())
				);

				//检查整个配送单是否完成
				Boolean shipmentMeasureType = false;
				Integer shipmentSize = 0;
				Boolean orderMeasureType = false;

				for (ShipmentItem shipmentItem : shipmentItems) {
					ErpShipCallbackParam.Good good = stringGoodMap.get(shipmentItem.getSubItemId());
					//是否计量
					Boolean measureType = false;
					if (Objects.nonNull(good)) {
						shipmentSize++;
						measureType = (good.getMeasureType() != null && good.getMeasureType().equals(10));
						if (measureType) {
							shipmentMeasureType = true;
							orderMeasureType = true;
						}
						if ((good.getSureAmount().compareTo(Optional.ofNullable(shipmentItem.getShipmentCount()).orElse(BigDecimal.ZERO)) > 0) && !measureType) {
							log.error("收货数量大于发货数量！");
							throw new BusinessException("收货数量大于发货数量！");
						}
						if (shipment.getStatus().equals(ShipmentStatus.STOCK_OUT)) {
							log.info(" ShipmentItem 项 : 接收数量:" + Optional.ofNullable(shipmentItem.getReceiveCount()).orElse(BigDecimal.ZERO) + "加上:" + good.getSureAmount() + " 收货数量");
							shipmentItem.setReceiveCount(NumberUtil.add(shipmentItem.getReceiveCount(), good.getSureAmount()));
						} else if (shipment.getStatus().equals(ShipmentStatus.RECEIPT_GOODS)) {
							log.info(" ShipmentItem 项 : 接收数量:" + Optional.ofNullable(shipmentItem.getReceiveCount()).orElse(BigDecimal.ZERO) + "减去:" + Optional.ofNullable(good.getRefuseAmount()).orElse(BigDecimal.ZERO) + " 拒收数量");
							// 因为第二次 的总数量 是 以  确认收到 的 为基础  所以再次 拒收就在原基础上减少
							shipmentItem.setReceiveCount(NumberUtil.sub(shipmentItem.getReceiveCount(), good.getRefuseAmount()));
						}
						shipmentItem.setRejectCount(NumberUtil.add(shipmentItem.getRejectCount(), good.getRefuseAmount()));

						// 配送差值
						//BigDecimal shipmentSubNum = NumberUtil.sub(shipmentItem.getShipmentCount(), good.sureAmount);
						BigDecimal shipmentSubNum = shipmentItem.getRejectCount();
						// 如果是非过磅的实际的 订单项 批次项 配送单项 需要修改数量
						if (!measureType) {
							log.info("非计量配送单处理");
							SubItem tempSubItem = subItemCollectionBySubItemId.get(shipmentItem.getSubItemId());
							SubItem subItem = subItemCollection.get(tempSubItem.getMaterialCode());
							//过磅差值
							subItem.setOutCount(NumberUtil.sub(subItem.getOutCount(), shipmentSubNum));
							ItemNo itemNo = itemNoCollection.get(subItem.getSubItemId());
							itemNo.setRemainCount(NumberUtil.add(itemNo.getRemainCount(), shipmentSubNum));
							itemNo.setOutCount(NumberUtil.sub(itemNo.getOutCount(), shipmentSubNum));
							log.info("处理完成，subitem出库量{},itemNo出库量{}", subItem.getOutCount(), itemNo.getOutCount());
						}
					}


				}

				shipmentItemDao.updateProperties(shipmentItems);
				itemNoDao.updateProperties(ListUtil.toList(itemNoCollection.values()));


				//4、修改配送单信息 按照订单项发多次
				//配送单项全部完成才进行操作
				long count = shipmentItems.stream().filter(shipmentItem -> shipmentItem.getShipmentCount().compareTo(shipmentItem.getReceiveCount()) > 0).count();
				log.info("配送单项未完成数量：{}", count);
				log.info("配送单shipmentMeasureType:{},shipmentSize:{}", shipmentMeasureType, shipmentSize);
				if ((count == 0 && !shipmentMeasureType) || (shipmentSize == shipmentItems.size() && shipmentMeasureType)) {
					log.info("配送单项全部完成，进入配送单状态处理");
					if (isReceipt) {
						log.info("状态变成:ERP收货未入库");
//					subItemCollection.values().forEach(subItem -> subItem.setItemStatus(OrderItemStatusEnum.SHIPPED.getValue()));
						shipment.setStatus(ShipmentStatus.RECEIPT_GOODS);
						shipment.setInOperatorTime(new Date());
					} else {
						log.info("状态变成:已入库");
						shipment.setStatus(ShipmentStatus.STOCK_IN);
						shipment.setInCompleteOperatorTime(new Date());
						//第三方特有功能
						log.info("进入 ERP收货未入库 发送将真正的收货信息回调给第三方！");
						TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
							@Override
							public void afterCommit() {
								log.info("-----------确认收货入库，将真正的收货信息回调给第三方事务后提交-------");
								try {
									ShipmentCallBackThirdDTO shipmentCallBackThirdDTO = new ShipmentCallBackThirdDTO();
									shipmentCallBackThirdDTO.setUserId(sub.getUserId());
									shipmentCallBackThirdDTO.setShopId(sub.getShopId());
									shipmentCallBackThirdDTO.setBatchNo(shipment.getBatchNo());
									shipmentCallBackThirdDTO.setShipmentId(shipment.getShipmentId());
									shipmentCallBackThirdDTO.setAccountBookId(sub.getAccountBookId());
									callbackThirdProcessor(shipmentCallBackThirdDTO);
								} catch (Exception e) {
									log.error("通知第三方已入库出现异常 :{}", e.getMessage());
								}
							}
						});
					}
				}
				subItemDao.updateProperties(ListUtil.toList(subItemCollection.values()));
				shipmentDao.updateProperties(shipment);
				//5、检查是否全部配送
				orderReceive(shipment,orderMeasureType);
			}

		} else {
			log.error("合同编码不一致 {} {}", erpShipCallbackParam.getContractNumber() + erpShipCallbackParam.getContractVersion(), sub.getContractNum());
		}
	}


	/**
	 * 将真正的收货信息回调给第三方
	 */
//	private void callbackThirdProcessor(List<ShipmentItem> shipmentItems, NgCompanyPrincipalQualificationDTO companyPrincipalByUserIdResult, Long shopId, String thirdNumber) {
//		thridIndustrialStrategyContext.getStrategy(shopId).configDeliveryOrder(shipmentItems,companyPrincipalByUserIdResult,shopId,thirdNumber);
//	}

	/**
	 * 将真正的收货信息回调给第三方
	 */
	public void callbackThirdProcessor(ShipmentCallBackThirdDTO shipmentCallBackThirdDTO) {
		this.amqpSendMsgUtil.convertAndSend(ThridIndustrialOrderMQConstant.THRID_ORDER_CHAIN_EXCHANGE, ThridIndustrialOrderMQConstant.THRID_SUPPLIER_SHIPMENT_CONFIRM_ROUTING_KEY, shipmentCallBackThirdDTO);
	}

	@Cacheable("erpArriveSite")
	public List<ErpArriveSite> getArriveSite() {
		return this.erpArriveSiteDao.queryAll();
	}

	@Override
	public PageSupport<ShipmentListVo> getShipmentByshopId(Long shopId, ShipmentListDTO shipmentListDTO) {

		if (ObjectUtil.isEmpty(shipmentListDTO)) {
			log.info("实体为空");
			return null;
		}

		List<SubItem> subItems = new ArrayList<>();
		//判断第三订单号 是否为空
		if (AppUtils.isNotBlank(shipmentListDTO.getThirdSubNum())) {
			subItems = subItemDao.queryByLikeThirdNumber(shipmentListDTO.getThirdSubNum());
			List<String> subNumList = subItems.stream().map(SubItem::getSubNumber).collect(Collectors.toList());
			if (AppUtils.isBlank(subItems)) {
				subNumList = new ArrayList<>();
				subNumList.add("-1");
			}
			shipmentListDTO.setSubNumList(subNumList);
		}

		//分页从数据库获取配送单列表
		PageSupport<ShipmentListVo> shipmentListVoPageSupport = this.shipmentDao.queryByshopIdAndTerms(shopId, shipmentListDTO);
		List<ShipmentListVo> resultList = shipmentListVoPageSupport.getResultList();
		if (AppUtils.isBlank(shipmentListDTO.getThirdSubNum())) {
			subItems = subItemDao.querySubItemBySubNumber(resultList.stream().map(ShipmentListVo::getSubNum).distinct().collect(Collectors.toList()));
		}
		List<String> batchNoList = new ArrayList<>();
		if (AppUtils.isNotBlank(resultList)) {
			batchNoList = resultList.stream().map(ShipmentListVo::getBatchNo).collect(Collectors.toList());
		}
		List<ProductInfoVo> productInfoVoList = shipmentItemDao.getShipmentProdInfo(batchNoList);
		for (ShipmentListVo shipmentListVo : resultList) {
			List<ProductInfoVo> shipmentProdInfo = productInfoVoList.stream().filter((item) -> item.getBatchNo().equals(shipmentListVo.getBatchNo())).collect(Collectors.toList());
			SubItem subItem = subItems.stream().filter((item) -> item.getSubNumber().equals(shipmentListVo.getSubNum())).findFirst().orElse(null);
			String thirdNo = null;
			if (ObjectUtil.isNotEmpty(subItem)) {
				thirdNo = subItem.getThirdNumber();
			}
			shipmentListVo.setThirdSubNum(thirdNo);
			if (CollUtil.isNotEmpty(shipmentProdInfo)) {
				shipmentListVo.setData(shipmentProdInfo);
			} else {
				shipmentListVo.setData(Collections.emptyList());
			}
		}
		shipmentListVoPageSupport.setResultList(resultList);
		return shipmentListVoPageSupport;
	}

	@Override
	public List<ShipmentListVo> getShipmentByOrderNumber(String subNumber) {

        ShipmentListDTO shipmentListDTO = new ShipmentListDTO();

		List<SubItem> subItems = new ArrayList<>();

		List<String> subNumList = new ArrayList<>();
		subNumList.add(subNumber);
		shipmentListDTO.setSubNumList(subNumList);


		//分页从数据库获取配送单列表
		if(ObjectUtil.isEmpty(shipmentListDTO.getPageSize())) {
			shipmentListDTO.setPageSize(999);
		}
		PageSupport<ShipmentListVo> shipmentListVoPageSupport = shipmentDao.queryByTerms(shipmentListDTO);
		List<ShipmentListVo> resultList = shipmentListVoPageSupport.getResultList();
		if (AppUtils.isBlank(shipmentListDTO.getThirdSubNum())) {
			subItems = subItemDao.queryBySubNumber(subNumber);
		}
		List<String> batchNoList = new ArrayList<>();
		if (AppUtils.isNotBlank(resultList)) {
			batchNoList = resultList.stream().map(ShipmentListVo::getBatchNo).collect(Collectors.toList());
		}
		List<ProductInfoVo> productInfoVoList = shipmentItemDao.getShipmentProdInfo(batchNoList);
		for (ShipmentListVo shipmentListVo : resultList) {
			List<ProductInfoVo> shipmentProdInfo = productInfoVoList.stream().filter((item) -> item.getBatchNo().equals(shipmentListVo.getBatchNo())).collect(Collectors.toList());
			SubItem subItem = subItems.stream().filter((item) -> item.getSubNumber().equals(shipmentListVo.getSubNum())).findFirst().orElse(null);
			String thirdNo = null;
			if (ObjectUtil.isNotEmpty(subItem)) {
				thirdNo = subItem.getThirdNumber();
			}
			shipmentListVo.setThirdSubNum(thirdNo);
			if (CollUtil.isNotEmpty(shipmentProdInfo)) {
				shipmentListVo.setData(shipmentProdInfo);
			} else {
				shipmentListVo.setData(Collections.emptyList());
			}
		}
		shipmentListVoPageSupport.setResultList(resultList);
		return resultList;
	}

	public PageSupport<ShipmentListVo> getShipmentByshopIdAndAbleToGateApply(Long shopId, PageParams pageDTO) {

		PageSupport<ShipmentListVo> listVoPageSupport = this.shipmentDao.queryShippingLists(shopId, pageDTO);

		List<ShipmentListVo> resultList = listVoPageSupport.getResultList();
		if (CollUtil.isEmpty(resultList)) {
			log.info("搜索结果为空");
			return null;
		}
		for (ShipmentListVo shipmentListVo : resultList) {
			List<ProductInfoVo> shipmentProdInfo = this.shipmentItemDao.getShipmentProdInfo(shipmentListVo.getBatchNo());
			if (CollUtil.isNotEmpty(shipmentProdInfo)) {
				shipmentListVo.setData(shipmentProdInfo);
			}
		}

		return null;
	}

	public List<SubShipmentListVo> getShipmentListBySubDetailDTO(SubDetailDTO subItemDTO) {
		if (StringUtil.isEmpty(subItemDTO.getSubNumber())) {
			return Collections.emptyList();
		}
		List<Shipment> shipmentList = shipmentDao.getByOrderNo(subItemDTO.getSubNumber());
		return CollUtil.isEmpty(shipmentList) ? Collections.emptyList() : shipmentList.stream().map(shipment -> {
			SubShipmentListVo subItemListVo = new SubShipmentListVo();
			subItemListVo.setLogisticsNo(shipment.getLogisticsNo());
			subItemListVo.setShipmentNo(shipment.getBatchNo());
			subItemListVo.setStatus(shipment.getStatus());
			subItemListVo.setShipmentId(shipment.getShipmentId());
			subItemListVo.setDispatchingCarsStatus(shipment.getDispatchingCarsStatus());
			List<Long> subItemIdList = shipmentItemDao.getSubItemIdByBatchNo(shipment.getBatchNo());
			List<SubProductInfoVo> subItemList = new ArrayList<>();
			for (Long item : subItemIdList) {
				ShipmentItem byBatchNoAndSubItemId = shipmentItemDao.getByBatchNoAndSubItemId(shipment.getBatchNo(), item);
				SubProductInfoVo subProductInfoVo = new SubProductInfoVo();
				SubItem subItem = subItemDao.getById(item);
				subProductInfoVo = subItemConverter.toSubProductInfoVo(subItem);
				subProductInfoVo.setOutCount(byBatchNoAndSubItemId.getShipmentCount());
				subProductInfoVo.setInCount(byBatchNoAndSubItemId.getReceiveCount());

				if(byBatchNoAndSubItemId.getRefundCount().compareTo(BigDecimal.ZERO) > 0){
					subItemListVo.setCustomerExpect(JdCustomerExpectEnum.RETURN_GOODS.value());
				}

				if(byBatchNoAndSubItemId.getRejectCount().compareTo(byBatchNoAndSubItemId.getRefundCount()) > 0){
					subItemListVo.setCustomerExpect(JdCustomerExpectEnum.EXCHANGE.value());
				}

				subItemList.add(subProductInfoVo);
			}
			Sub sub = subDao.getSubBySubNumber(shipment.getOrderNo());
			subItemListVo.setDeliveryType(sub.getDeliveryType());
			subItemListVo.setSubItemList(subItemList);
//			if (CollUtil.isNotEmpty(subItemIdList)) {
//				subItemListVo.setSubItemList(subItemConverter.toSubProductInfoVo(subItemDao.queryAllByIds(subItemIdList)));
//			}
			return subItemListVo;
		}).collect(Collectors.toList());
	}

	@Override
	public String getExpress(String dvyFlowId, Long dvyTypeId, String reciverMobile) {
		List<Shipment> shipmentList = shipmentDao.queryByDvyFlowIdAndReciverMobile(dvyFlowId, reciverMobile);
		if (ObjectUtil.isNotEmpty(shipmentList)) {
			JSONArray expressList = new JSONArray();
			for (Shipment shipment : shipmentList) {
				if (ObjectUtil.isEmpty(shipment.getThirdShipNo()) || (ObjectUtil.isNotEmpty(shipment.getShopId()) && shipment.getShopId().intValue() != ShopEnum.JD.getNum())) {
					return null;
				}
				log.info("查询京东物流信息");
				OrderTrackRequest request = new OrderTrackRequest();
				Sub subBySubNumber = subDao.getSubBySubNumber(shipment.getOrderNo());
				request.setJdOrderId(shipment.getThirdShipNo())
					.setWaybillCode(1)
					.setAccountBookId(subBySubNumber.getAccountBookId());
//					.setToken(jdAccessTokenUtil.getIOPAccessToken());
				CommonResponse<OrderTrackResponse> result = OrderClient.orderTrack(request);
				if (result.getSuccess()) {
					for (OrderTrack orderTrack : result.getResult().getOrderTrack()) {
						JSONObject express = new JSONObject();
						express.put("ftime", orderTrack.getMsgTime());
						express.put("context", orderTrack.getContent());
						expressList.add(express);
						;
					}
				} else {
					log.warn("查询京东IOP物流信息失败：{}", result);
					return null;
				}
			}
			return expressList.toString();
		}
		return null;
	}

	@Override
	public void updateERPOldShipment(String orderNo, DateTime erpDateTime) {

		List<Shipment> shipments;

		//有入参给线上调试用
		if (StrUtil.isNotEmpty(orderNo)) {
			shipments = shipmentDao.getByOrderNo(orderNo);
		} else {
			shipments = shipmentDao.queryErpOldByOutTime(erpDateTime);
		}

		if (CollUtil.isEmpty(shipments)) {
			return;
		}
		for (Shipment shipment : shipments) {
			ErpQueryOrderInspInfoRequest erpQueryOrderInspInfoRequest = new ErpQueryOrderInspInfoRequest();
			erpQueryOrderInspInfoRequest.setErpApiNo("MROOR01");
			erpQueryOrderInspInfoRequest.setOrderNumber(shipment.getOrderNo());
			erpQueryOrderInspInfoRequest.setContractNumber(shipment.getContractNum());
			erpQueryOrderInspInfoRequest.setBatchNumber(shipment.getBatchNo());
			log.info("请求erp订单入库接口参数：{}", erpQueryOrderInspInfoRequest);
			JSONObject jsonObject = ErpSendDataUtils.erpESBSendPostData(erpSendDataUrlConfig.getEsbQueryOrderInspInfo(), erpQueryOrderInspInfoRequest, JSONObject.class);
			orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, erpSendDataUrlConfig.getQueryOrderInspInfo(), JSONUtil.toJsonStr(erpQueryOrderInspInfoRequest), jsonObject.get("status").equals("200") ? HttpStatus.HTTP_OK : HttpStatus.HTTP_INTERNAL_ERROR, JSONUtil.toJsonStr(jsonObject));
			if (Objects.isNull(jsonObject) || jsonObject.get("status").equals("500") || ObjectUtil.isEmpty(jsonObject.get("data"))) {
				continue;
			}
			List<ErpQueryOrderInspInfoResponse> erpQueryOrderInspInfoResponse = JSONArray.parseArray(JSON.toJSONString(jsonObject.get("data")), ErpQueryOrderInspInfoResponse.class);
			log.info("erp订单入库接口返回data具体信息：{}", erpQueryOrderInspInfoResponse);
			try {
				//其他方法调用了，有修改，如果后面要用的话，请先看一下代码逻辑
//				updateERPOldShipmentItem(erpQueryOrderInspInfoResponse, shipment);
			} catch (Exception e) {
				log.info("更新erp配送单对应数据失败，配送单id：{}，原因：{}", shipment.getShipmentId(), e.getMessage());
			}
		}
		List<String> orderNoList = new ArrayList<>(shipments.stream().map(Shipment::getOrderNo).collect(Collectors.toSet()));
		List<Sub> subs = subDao.queryErrorStatusByOrderNo(orderNoList);
		if (CollUtil.isEmpty(subs)) {
			return;
		}
		for (Sub sub : subs) {
			if (sub.getPayManner().equals(PayMannerEnum.DELIVERY_ON_PAY.value())) {
				sub.setStatus(OrderStatusEnum.CLEAN_PAYMENT.value());
			} else {
				sub.setStatus(OrderStatusEnum.SUCCESS.value());
			}
			sub.setFinallyDate(new Date());
		}
		subDao.update(subs);
	}

	@Override
	public List<ShipmentItemReturnDTO> queryShipmentItemReturnList(Long shipmentId) {
		List<ShipmentItemReturnDTO> shipmentItemReturnDTOS = shipmentDao.queryShipmentItemReturn(shipmentId);
		for (ShipmentItemReturnDTO shipmentItemReturnDTO : shipmentItemReturnDTOS) {
			if (shipmentItemReturnDTO.getRefundCount() != null) {
				shipmentItemReturnDTO.setRemainCount(shipmentItemReturnDTO.getShipmentCount().subtract(shipmentItemReturnDTO.getRefundCount()));
			} else {
				shipmentItemReturnDTO.setRemainCount(shipmentItemReturnDTO.getShipmentCount());
			}
		}
		return shipmentItemReturnDTOS;
	}

	@Override
	public List<ShipmentItemReturnDTO> queryShipmentItemReturnList(String batchNo) {
		List<ShipmentItemReturnDTO> shipmentItemReturnDTOS = shipmentDao.queryShipmentItemReturn(batchNo);
		for (ShipmentItemReturnDTO shipmentItemReturnDTO : shipmentItemReturnDTOS) {
			if (shipmentItemReturnDTO.getRefundCount() != null) {
				shipmentItemReturnDTO.setRemainCount(shipmentItemReturnDTO.getShipmentCount().subtract(shipmentItemReturnDTO.getRefundCount()));
			} else {
				shipmentItemReturnDTO.setRemainCount(shipmentItemReturnDTO.getShipmentCount());
			}
		}
		return shipmentItemReturnDTOS;
	}

	@Override
	@Transactional
	public void updateAfterSalesStatus(String batchNo) {
		List<ShipmentItem> shipmentItems = shipmentItemDao.zczyFindShipmentItems(batchNo);
		long count = shipmentItems.stream().filter(shipmentItem -> !shipmentItem.getRefundCount().equals(shipmentItem.getShipmentCount())).count();
		if (count != 0) {
			return;
		}
		Shipment shipment = shipmentDao.getByBatchNo(batchNo);
		shipment.setStatus(ShipmentStatus.CANCEL);
		shipmentDao.update(shipment);

		//如果配送单项全部取消的情况下，判断订单能否全部取消
		subService.updateAfterSalesStatus(shipment.getOrderNo());
	}

	@Override
	public void erpOldShipmentArrivalApply() {
		List<String> batchNos = shipmentDao.queryBatchNotArrivalApplyByShopId(ThirdIndustrialEnum.Obei.getShopId());
		for (String batchNo : batchNos) {
			try {
				log.info("到货申请补偿,batchNo: {}", batchNo);
				mroArrivalApply(batchNo);
			} catch (Exception e) {
				log.info("到货申请补偿失败，batchNo: {}，原因：{}", batchNo, e.getMessage());
			}
		}
	}

	@Override
	public void checkGateApplyStatus(Shipment shipment) {
		List<ShipmentGateApply> shipmentGateApplies = this.shipmentGateApplyDao.getByShipmentIdAndApplyNo(shipment.getId());
		AtomicReference<GateApplyResult<applyInfo>> applyInfoGateApplyResult = new AtomicReference<>(new GateApplyResult<>());
		if (CollUtil.isNotEmpty(shipmentGateApplies)) {
			for (ShipmentGateApply shipmentGateApply : shipmentGateApplies) {
				if (StrUtil.isBlank(shipmentGateApply.getApplyNo())) {
					log.info("没有数据");
					continue;
				}
				try {
					log.info("请求的地址： {}", erpSendDataUrlConfig.getQueryAccessControlStatus());

					applyInfoGateApplyResult.set(ErpSendDataUtils.erpSendData(erpSendDataUrlConfig.getQueryAccessControlStatus() + "?applyNo=" + shipmentGateApply.getApplyNo(), new TypeReference<GateApplyResult<applyInfo>>() {
					}));
				} catch (Exception ex) {
					log.error(ex.getMessage());
				} finally {
					GateApplyResult<applyInfo> applyInfoGateApplyResult1 = applyInfoGateApplyResult.get();
					if (ObjectUtil.isNotEmpty(applyInfoGateApplyResult1)) {
						assert applyInfoGateApplyResult1 != null;
						List<applyInfo> resultApplyInfo = applyInfoGateApplyResult1.getApplyInfo();
						if (CollUtil.isNotEmpty(resultApplyInfo)) {
							log.info("更新门禁申请状态");
							for (cn.legendshop.obei.param.applyInfo info : resultApplyInfo) {
								if (info.getStus().equals("S")) {
									shipmentGateApply.setApplyStatus(ShipmentGateApplyEnum.PASSED.getNum());
									shipment.setGateAuditStatus(ShipmentGateAuditEnum.APPROVED.getValue());
									shipment.setUpdateTime(new DateTime());
									this.shipmentDao.update(shipment);
									break;
								}
							}
						}
					} else {
						log.info("查询结果为空");
					}

				}
			}
			this.shipmentGateApplyDao.update(shipmentGateApplies);
		}
	}

	@Transactional
	public void updateERPOldShipmentItem(List<ErpQueryOrderInspInfoResponse> erpQueryOrderInspInfoResponse, Shipment shipment,String subNumber) {
		Map<String, ErpQueryOrderInspInfoResponse> infoResponseMap = erpQueryOrderInspInfoResponse.stream().collect(Collectors.toMap(ErpQueryOrderInspInfoResponse::getPoItemNo, Function.identity()));
		List<ShipmentItem> shipmentItem = shipmentItemDao.getShipmentItemByShipmentId(shipment.getShipmentId());
		List<SubItem> subItems = subItemDao.querySubItemBySubNumber(shipment.getOrderNo());
		// 是否需要分批发货
		boolean isMeasureType = false;
		Map<Long, SubItem> subItemMap = subItems.stream().collect(Collectors.toMap(SubItem::getSubItemId, Function.identity()));
		List<ItemNo> itemNos = itemNoDao.queryBySubItemId(new ArrayList<>(subItemMap.keySet()));
		Map<Long,ItemNo> itemNoMap = itemNos.stream().collect(Collectors.toMap(ItemNo::getSubItemId,Function.identity()));
		for (ShipmentItem item : shipmentItem) {
			ErpQueryOrderInspInfoResponse infoResponse = infoResponseMap.get(item.getItemNo());
			if (!Objects.isNull(infoResponse) && infoResponse.getStatus().equals("1")) {
				BigDecimal sureAmount = new BigDecimal(infoResponse.getSureAmount());
				item.setReceiveCount(sureAmount);
				item.setShipmentCount(sureAmount);
				SubItem subItem = subItemMap.get(item.getSubItemId());
				BigDecimal remainCount =subItem.getActualCount().subtract(sureAmount);
				subItem.setInCount(sureAmount);
				subItem.setOutCount(sureAmount);
				subItem.setRemainCount(remainCount);
				ItemNo itemNo = itemNoMap.get(item.getSubItemId());
				itemNo.setRemainCount(remainCount);
				itemNo.setOutCount(sureAmount);
				itemNo.setInCount(sureAmount);
				if(remainCount.compareTo(BigDecimal.ZERO) > 0) {
					isMeasureType = true;
				}
			}
		}
		long count = erpQueryOrderInspInfoResponse.stream().filter(e -> e.getStatus().equals("1")).count();
		shipmentItemDao.update(shipmentItem);
		subItemDao.update(subItems);
		itemNoDao.update(itemNos);
		if (count == shipmentItem.size()) {
			shipment.setStatus(ShipmentStatus.STOCK_IN);
		}

		Sub sub = subDao.getSubBySubNumber(subNumber);
		if (AppUtils.isNotBlank(sub) && isMeasureType ) {
			sub.setStatus(OrderStatusEnum.PARTIAL_SHIPMENT.value());
			sub.setShipmentType(2);
			subDao.update(sub);
		}
		shipmentDao.update(shipment);
	}

	@Override
	public Boolean judgeThirdShipmentByThirdShipNo(String thirdShipNo) {
		List<Shipment> shipmentList = shipmentDao.queryByThirdShipNo(thirdShipNo);
		if (ObjectUtil.isEmpty(shipmentList)) {
			return false;
		}
		//判断送货单子项
		for (Shipment shipment : shipmentList) {
			List<ShipmentItem> shipmentItemList = shipmentItemDao.zczyFindShipmentItems(shipment.getBatchNo());
			if (ObjectUtil.isEmpty(shipmentItemList)) {
				return false;
			}
			List<String> thirdItemNo = shipmentItemList.stream().map(ShipmentItem::getThirdShipRowNo).collect(Collectors.toList());
			return !thirdItemNo.contains(null);
		}
		return false;
	}

	@Override
	public void confirmThirdShipment(String subNumber) {
		Sub sub = subDao.getSubBySubNumber(subNumber);
		if (!ThirdIndustrialEnum.Obei.getShopId().equals(sub.getShopId())) {
			return;
		}
		if (ObjectUtil.isEmpty(sub) || !OrderStatusEnum.CONSIGNMENT.value().equals(sub.getStatus())) {
			log.info("订单状态异常，非“待收货”!");
			throw new BusinessException("订单状态异常，非“待收货”!");
		}
		List<SubItem> subItemList = subItemDao.getSubItem(sub.getSubNumber());
		for (SubItem subItem : subItemList) {
			if (!BigDecimal.ZERO.equals(subItem.getRemainCount())) {
				log.info("还未发货完，暂不能确认收货");
				throw new BusinessException("还未发货完，暂不能确认收货");
			}
		}
		List<Shipment> shipmentList = shipmentDao.getByOrderNo(sub.getSubNumber());
		List<ShipmentItem> shipmentItems = shipmentItemDao.queryBySubNumber(sub.getSubNumber());
		for (ShipmentItem shipmentItem : shipmentItems) {
			shipmentItem.setReceiveCount(shipmentItem.getShipmentCount());
		}
		shipmentItemDao.update(shipmentItems);
		for (Shipment shipment : shipmentList) {
			TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
				@Override
				public void afterCommit() {
					log.info("-----------确认收货入库，将真正的收货信息回调给第三方事务后提交-------");
					try {
						ShipmentCallBackThirdDTO shipmentCallBackThirdDTO = new ShipmentCallBackThirdDTO();
						//使用xzl账套
						shipmentCallBackThirdDTO.setUserId("091fa85c-43be-4b4b-819e-dcca787d0cd8");
						shipmentCallBackThirdDTO.setShopId(sub.getShopId());
						shipmentCallBackThirdDTO.setBatchNo(shipment.getBatchNo());
						shipmentCallBackThirdDTO.setShipmentId(shipment.getShipmentId());
						shipmentCallBackThirdDTO.setAccountBookId(sub.getAccountBookId());
						callbackThirdProcessor(shipmentCallBackThirdDTO);
					} catch (Exception e) {
						log.error("通知第三方已入库出现异常 :{}", e.getMessage());
					}
				}
			});
		}
	}

	@Override
	public Shipment getByThirdShipNo(String thirdShipNo, Long shopId) {
		List<Shipment> shipmentList = shipmentDao.queryByThirdShipNo(thirdShipNo);
		if (ObjectUtil.isEmpty(shipmentList)) {
			return null;
		}
		List<Shipment> shipments = shipmentList.stream().filter(shipment -> shipment.getShopId().equals(shopId)).collect(Collectors.toList());
		if (ObjectUtil.isEmpty(shipmentList)) {
			return null;
		} else {
			return shipments.get(0);
		}
	}

	@Override
	public String createAfterSaleShipment(PartialShipmentDTO shipmentDTO, SubRefundReturnRenewShipment subRefundReturnRenewShipment,Boolean forceApply) {
		log.info("进入创建配送售后单方法");
		Sub sub = subDao.getSubById(shipmentDTO.getSubId());
		if (null == sub) {
			throw new RuntimeException("订单不存在");
		}

		UserAddressSub userAddressSub = userAddressSubDao.getUserAddressSub(sub.getAddrOrderId());
		if (null == userAddressSub) {
			throw new RuntimeException("发货地址不存在");
		}
		List<SubItem> subItems = subItemDao.getSubItemBySubId(sub.getSubId());
		if (null == subItems || subItems.isEmpty()) {
			throw new RuntimeException("没有订单项");
		}
		SubRefundReturn subRefundReturn = subRefundReturnDao.getByRefundSn(shipmentDTO.getRefundNo());
		if(!RefundReturnTypeEnum.REFUND_EXCHANGE.value().equals(subRefundReturn.getApplyType())) {
			throw new RuntimeException("售后单不是换货类型");
		}
		if(OrderBarterRefundStatusEnum.AFTERSALES_PURCHASER_DELIVERGOODS_FINISH.getCode() > subRefundReturn.getRefundStatus() || OrderBarterRefundStatusEnum.AFTERSALES_PROCESSING_COMPLETED.getCode() < subRefundReturn.getRefundStatus()) {
			throw new RuntimeException("售后单状态异常");
		}
		log.info("订单判断需要申请门禁？");
		boolean isErp = OrderSourceEnum.ERP.value().equals(sub.getOrderSource());
		if (forceApply) {
			shipmentDTO.setDispatchingCarsStatus(DispatchingCarsStatusEnum.ALLOWED.getCode());
		}

		// todo 后续需要将第三方工业品的参数抽出
		shipmentDTO.setOrderNo(sub.getSubNumber());
		shipmentDTO.setOrderSource(sub.getOrderSource());
		if (shipmentDTO.getShopId().equals(ThirdIndustrialEnum.JD.getShopId())) {
			shipmentDTO.setBatchNo(shipmentDTO.getThirdShipItemNo());
		}
		else if (shipmentDTO.getShopId().equals(ThirdIndustrialEnum.Zkh.getShopId())) {
			//震坤行的BatchNo与震坤行的配送单编号一致
		}
		else  {
			shipmentDTO.setBatchNo(CommonServiceUtil.getSubNumber(shipmentDTO.getApplicantId()));
		}
		shipmentDTO.setShopId(sub.getShopId()); // 供应商
		shipmentDTO.setBuyerId(sub.getUserId()); // 采购商
		shipmentDTO.setApplicantTime(new Date());
		shipmentDTO.setConsignee(userAddressSub.getReceiver()); // 收货人
		shipmentDTO.setReceiverAddress(userAddressSub.getDetailAddress()); // 收货地址
		shipmentDTO.setContactNumber(userAddressSub.getMobile()); // 联系人电话
		shipmentDTO.setStatus(ShipmentStatus.APPLYING);
		shipmentDTO.setShipmentType(ShipmentTypeConst.SHIPMENT_PARTIAL);

		List<ShipmentItemDTO> itemList = shipmentDTO.getShipmentItems();
		// 分批发货需要选择
		if (null == itemList || itemList.isEmpty()) {
			throw new ParameterErrorException("请选择商品");
		}
		log.info("生成单据，saveShipment");
		if(shipmentDTO.getRenewShipmentType().equals(1)) {
			String thirdShipNo = shipmentDTO.getBatchNo() + "_self";
			SubRefundReturnRenewShipment subRefundReturnRenewShipment1 = subRefundReturnRenewShipmentDao.getByThirdShipmentNoAndShopId(thirdShipNo,sub.getShopId());
			if(ObjectUtil.isNotEmpty(subRefundReturnRenewShipment1)) {
				throw new BusinessException("已有售后配送单记录");
			}
			SubRefundReturnRenewShipment subRefundReturnRenewShipment2 = new SubRefundReturnRenewShipment();
			subRefundReturnRenewShipment2.setRefundSn(shipmentDTO.getRefundNo());
			subRefundReturnRenewShipment2.setCreateTime(new Date());
			subRefundReturnRenewShipment2.setShopId(sub.getShopId());
			subRefundReturnRenewShipment2.setThirdShipmentNo(thirdShipNo);
			subRefundReturnRenewShipment2.setRenewShipmentType(1);
			subRefundReturnRenewShipment2.setUpdateTime(new Date());
			subRefundReturnRenewShipment2.setBatchNo(shipmentDTO.getBatchNo());
			subRefundReturnRenewShipmentDao.save(subRefundReturnRenewShipment2);
		}

		// 生成单据
		Shipment shipment = obeiOrderPlanConfigConverter.toShipment(shipmentDTO);
		shipment.setRefundRenewType(ShipmentRefundRenewTypeEnum.REFUND_RENEW.getValue());
		shipment.setRefundNo(shipmentDTO.getRefundNo());
		Long shipmentId = shipmentDao.saveShipment(shipment);
		shipmentDTO.setShipmentId(shipmentId);
		List<SubRefundReturnItem> updateItemList = new ArrayList<>();
		List<ApplyShipmentGoodsDTO> erpGoodsList = new ArrayList<>();

		List<ShipmentItem> shipmentItems = itemList
			.stream()
			.map(partialShipmentItem -> {
				SubRefundReturnItem  subRefundReturnItem = subRefundReturnItemDao.getByProperties(new LambdaEntityCriterion<>(SubRefundReturnItem.class).eq(SubRefundReturnItem::getItemId,partialShipmentItem.getSubItemId()).eq(SubRefundReturnItem::getRefundId,subRefundReturn.getRefundId()));
				SubItem subItem = subItemDao.getSubItem(partialShipmentItem.getSubItemId());
				// 检查订单项是否真实
				if (null == subItem || !subItem.getSubNumber().equals(shipmentDTO.getOrderNo())) {
					throw new NotFoundBizException("订单项数据不存在");
				}

				// 检查订单项是否真实
				if (null == subRefundReturnItem) {
					throw new NotFoundBizException("售后单项数据不存在");
				}
				if(NumberUtil.sub(subRefundReturnItem.getRemainNum(), partialShipmentItem.getShipmentCount()).compareTo(BigDecimal.ZERO) < 0) {
					throw new BusinessException("配送数量大于剩余数量");
				}
				BigDecimal remainCount = NumberUtil.sub(subRefundReturnItem.getRemainNum(), partialShipmentItem.getShipmentCount());
				subRefundReturnItem.setRemainNum(remainCount);
				updateItemList.add(subRefundReturnItem);
				ShipmentItem shipmentItem = new ShipmentItem();
				shipmentItem.setShipmentId(shipmentId);
				shipmentItem.setBatchNo(shipment.getBatchNo());
				shipmentItem.setContractNum(shipment.getContractNum());
				shipmentItem.setProdId(subItem.getProdId());
				shipmentItem.setSkuId(subItem.getSkuId());
				shipmentItem.setPic(subItem.getPic());
				shipmentItem.setProdName(subItem.getProdName());
				shipmentItem.setSubItemId(subItem.getSubItemId());
				shipmentItem.setWeight(subItem.getWeight());
				shipmentItem.setVolume(subItem.getVolume());
				shipmentItem.setReceiveCount(BigDecimal.ZERO);
				shipmentItem.setRefundCount(BigDecimal.ZERO);
				shipmentItem.setRejectCount(BigDecimal.ZERO);
				shipmentItem.setPrice(partialShipmentItem.getPrice());
				shipmentItem.setAmount(partialShipmentItem.getAmount());
				shipmentItem.setShipmentCount(partialShipmentItem.getShipmentCount());
				shipmentItem.setItemNoId(partialShipmentItem.getItemNoId());
				shipmentItem.setThirdShipRowNo(partialShipmentItem.getThirdShipRowNo());
				return shipmentItem;
			}).collect(Collectors.toList());

		if (CollectionUtil.isEmpty(shipmentItems)) {
			throw new NotFoundBizException("没有要发货的商品");
		}
		log.info("shipmentItems保存：{}",JSONUtil.toJsonStr(shipmentItems));
		shipmentItemDao.batchSave(shipmentItems);

		if (!updateItemList.isEmpty()) {
			log.info("更新售后单项1");
			subRefundReturnItemDao.update(updateItemList);
		}

		subRefundReturn.setRefundStatus(OrderBarterRefundStatusEnum.AFTERSALES_MERCHANT_DELIVERGOODS.getCode());
		subRefundReturnDao.update(subRefundReturn);

		// 调用erp接口
		log.info("准备调用erp接口");
		if (isErp && DispatchingCarsStatusEnum.ALLOWED.getCode().equals(shipmentDTO.getDispatchingCarsStatus())) { //派车申请状态
			log.info("进入判断条件");
			if (CollectionUtil.isEmpty(erpGoodsList)) {
				throw new NotFoundBizException("没有要发货商品");
			}

			log.info("发MQ做到货申请!");
			amqpSendMsgUtil.convertAndSend(ErpOrderMQConstant.ERP_ORDER_CHAIN_EXCHANGE, ErpOrderMQConstant.ARRIVAL_APPLT_ROUTING_KEY, shipment.getBatchNo());

		}
		return shipmentDTO.getBatchNo();
	}

	private void receiveRenewShipment(Shipment shipment,Sub sub,ErpShipCallbackParam erpShipCallbackParam,Boolean isReceipt) {
		List<ShipmentItem> shipmentItems = shipmentItemDao.queryByProperties(new LambdaEntityCriterion<>(ShipmentItem.class)
			.eq(ShipmentItem::getShipmentId, shipment.getShipmentId())
		);
		Map<Long, ErpShipCallbackParam.Good> stringGoodMap = erpShipCallbackParam.getGoodsList().stream()
			.collect(Collectors.toMap(ErpShipCallbackParam.Good::getSubItemId, Function.identity()));
		//检查整个配送单是否完成
		Boolean shipmentMeasureType = false;
		Integer shipmentSize = 0;

		for (ShipmentItem shipmentItem : shipmentItems) {
			ErpShipCallbackParam.Good good = stringGoodMap.get(shipmentItem.getSubItemId());
			//是否计量
			Boolean measureType = false;
			if (Objects.nonNull(good)) {
				shipmentSize++;
				measureType = (good.getMeasureType() != null && good.getMeasureType().equals(10));
				if (measureType) {
					shipmentMeasureType = true;
				}
				if ((good.getSureAmount().compareTo(Optional.ofNullable(shipmentItem.getShipmentCount()).orElse(BigDecimal.ZERO)) > 0) && !measureType) {
					log.error("收货数量大于发货数量！");
					throw new BusinessException("收货数量大于发货数量！");
				}
				if (shipment.getStatus().equals(ShipmentStatus.STOCK_OUT)) {
					log.info(" ShipmentItem 项 : 接收数量:" + Optional.ofNullable(shipmentItem.getReceiveCount()).orElse(BigDecimal.ZERO) + "加上:" + good.getSureAmount() + " 收货数量");
					shipmentItem.setReceiveCount(NumberUtil.add(shipmentItem.getReceiveCount(), good.getSureAmount()));
				} else if (shipment.getStatus().equals(ShipmentStatus.RECEIPT_GOODS)) {
					log.info(" ShipmentItem 项 : 接收数量:" + Optional.ofNullable(shipmentItem.getReceiveCount()).orElse(BigDecimal.ZERO) + "减去:" + Optional.ofNullable(good.getRefuseAmount()).orElse(BigDecimal.ZERO) + " 拒收数量");
					// 因为第二次 的总数量 是 以  确认收到 的 为基础  所以再次 拒收就在原基础上减少
					shipmentItem.setReceiveCount(NumberUtil.sub(shipmentItem.getReceiveCount(), good.getRefuseAmount()));
				}
				shipmentItem.setRejectCount(NumberUtil.add(shipmentItem.getRejectCount(), good.getRefuseAmount()));

			}


		}

		shipmentItemDao.updateProperties(shipmentItems);


		//修改配送单信息 按照订单项发多次
		//配送单项全部完成才进行操作
		long count = shipmentItems.stream().filter(shipmentItem -> shipmentItem.getShipmentCount().compareTo(shipmentItem.getReceiveCount()) > 0).count();
		log.info("配送单项未完成数量：{}", count);
		log.info("配送单shipmentMeasureType:{},shipmentSize:{}", shipmentMeasureType, shipmentSize);
		if ((count == 0 && !shipmentMeasureType) || (shipmentSize == shipmentItems.size() && shipmentMeasureType)) {
			log.info("配送单项全部完成，进入配送单状态处理");
			if (isReceipt) {
				log.info("状态变成:ERP收货未入库");
//					subItemCollection.values().forEach(subItem -> subItem.setItemStatus(OrderItemStatusEnum.SHIPPED.getValue()));
				shipment.setStatus(ShipmentStatus.RECEIPT_GOODS);
			} else {
				log.info("状态变成:已入库");
				shipment.setStatus(ShipmentStatus.STOCK_IN);
				shipment.setInCompleteOperatorTime(new Date());
				//第三方特有功能
				log.info("进入 ERP收货未入库 发送将真正的收货信息回调给第三方！");
				Sub finalSub = sub;
				TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
					@Override
					public void afterCommit() {
						log.info("-----------确认收货入库，将真正的收货信息回调给第三方事务后提交-------");
						try {
							ShipmentCallBackThirdDTO shipmentCallBackThirdDTO = new ShipmentCallBackThirdDTO();
							shipmentCallBackThirdDTO.setUserId(finalSub.getUserId());
							shipmentCallBackThirdDTO.setShopId(finalSub.getShopId());
							shipmentCallBackThirdDTO.setBatchNo(shipment.getBatchNo());
							shipmentCallBackThirdDTO.setShipmentId(shipment.getShipmentId());
							shipmentCallBackThirdDTO.setAccountBookId(finalSub.getAccountBookId());
							callbackThirdProcessor(shipmentCallBackThirdDTO);
						} catch (Exception e) {
							log.error("通知第三方已入库出现异常 :{}", e.getMessage());
						}
					}
				});
			}
		}
		shipmentDao.updateProperties(shipment);
		if(shipment.getRefundRenewType() == 1 && shipment.getStatus().equals(ShipmentStatus.STOCK_IN)) {
			SubRefundReturn subRefundReturn = subRefundReturnDao.getByRefundSn(shipment.getRefundNo());
			List<SubRefundReturnItem> subRefundReturnItems = subRefundReturnItemDao.queryByRefundId(subRefundReturn.getRefundId());
			Map<Long, ShipmentItem> shipmentItemMap = shipmentItems.stream().collect(Collectors.toMap(ShipmentItem::getSkuId, Function.identity()));
			for(SubRefundReturnItem subRefundReturnItem:subRefundReturnItems) {
				subRefundReturnItem.setRefundSuccessNum(shipmentItemMap.get(subRefundReturnItem.getSkuId()).getReceiveCount());
			}
			subRefundReturn.setRefundStatus(OrderBarterRefundStatusEnum.AFTERSALES_PROCESSING_COMPLETED.getCode());
			List<SubRefundReturn> subRefundReturnList = subRefundReturnDao.queryByProperties(new LambdaEntityCriterion<>(SubRefundReturn.class).eq(SubRefundReturn::getSubNumber, shipment.getOrderNo()).lt(SubRefundReturn::getRefundStatus,OrderBarterRefundStatusEnum.AFTERSALES_PROCESSING_COMPLETED.getCode()));
			if(subRefundReturnList.size() <= 1) {
				sub.setRefundState(2);
			}
			subRefundReturnDao.update(subRefundReturn);
			subRefundReturnItemDao.update(subRefundReturnItems);
		}
		subDao.update(sub);
	}

	@Override
	public List<SubShipmentListVo> queryAfterSaleShipment(AfterSaleShipmentDTO afterSaleShipmentDTO) {
		if(ObjectUtil.isEmpty(afterSaleShipmentDTO)) {
			return null;
		}
		List<Shipment> shipmentList = shipmentDao.queryByProperties(new EntityCriterion().eq("refundNo",afterSaleShipmentDTO.getRefundNo()).eq("refundRenewType",1));
		return CollUtil.isEmpty(shipmentList) ? Collections.emptyList() : shipmentList.stream().map(shipment -> {
			SubShipmentListVo subItemListVo = new SubShipmentListVo();
			subItemListVo.setLogisticsNo(shipment.getLogisticsNo());
			subItemListVo.setShipmentNo(shipment.getBatchNo());
			subItemListVo.setStatus(shipment.getStatus());
			subItemListVo.setShipmentId(shipment.getShipmentId());
			subItemListVo.setDispatchingCarsStatus(shipment.getDispatchingCarsStatus());
			List<ShipmentItem> shipmentItemList = shipmentItemDao.zczyFindShipmentItems(shipment.getBatchNo());
			List<SubProductInfoVo> subItemList = new ArrayList<>();
			for (ShipmentItem item : shipmentItemList) {
				SubProductInfoVo subProductInfoVo = new SubProductInfoVo();
				subProductInfoVo.setProductName( item.getProdName() );
				subProductInfoVo.setPic( item.getPic() );
				subProductInfoVo.setOutCount( item.getShipmentCount() );
				subProductInfoVo.setInCount( item.getReceiveCount() );
				subProductInfoVo.setPrice( item.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() );
				subProductInfoVo.setBasketCount( item.getShipmentCount() );
				subProductInfoVo.setProductTotalAmount( item.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() );
				subProductInfoVo.setProdId( item.getProdId() );
				subProductInfoVo.setRefundCount( java.math.BigDecimal.ZERO );
				subProductInfoVo.setActualCount( item.getShipmentCount() );
				subItemList.add(subProductInfoVo);
			}
			subItemListVo.setSubItemList(subItemList);
			return subItemListVo;
		}).collect(Collectors.toList());
	}

	@Override
	public Boolean cancelThirdIndustrialShipment(String thirdshipmentNo,Long shopId) {
		log.info("开始取消配送单");
		if(StringUtil.isBlank(thirdshipmentNo)) {
			log.error("第三方配送单号为空");
			return Boolean.FALSE;
		}
		Shipment shipment = shipmentDao.getByThirdShipNoShopId(thirdshipmentNo,shopId);
		if(ObjectUtil.isEmpty(shipment)) {
			log.error("未找到第三方配送单{},店铺ID{}",thirdshipmentNo,shopId);
			return Boolean.FALSE;
		}
		Sub sub = subDao.getSubById(shipment.getSubId());
		if(ObjectUtil.isNull(sub) || ShipmentStatus.RECEIPT_GOODS <= sub.getStatus() ||  !AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId().equals(sub.getAccountBookId())) {
			log.error("该订单不能取消发货单！");
			return Boolean.FALSE;
		}
		if(sub.getOrderSource() == OrderSourceEnum.ERP.value() && shipment.getArrivalApplyStatus()==20){
			log.error("该订单不能取消发货单！");
			return Boolean.FALSE;
		}
			List<ShipmentItem> shipmentItemList = shipmentItemDao.getShipmentItemByShipmentId(shipment.getShipmentId());
		List<SubItem> subItemList = subItemDao.getSubItemBySubId(shipment.getSubId());
		shipment.setStatus(ShipmentStatus.CANCEL);
		for (ShipmentItem shipmentItem : shipmentItemList) {
			shipmentItem.setRejectCount(shipmentItem.getShipmentCount());
			for (SubItem subItem : subItemList) {
				if (subItem.getSubItemId().equals(shipmentItem.getSubItemId())) {
					subItem.setOutCount(subItem.getOutCount().subtract(shipmentItem.getShipmentCount()));
					subItem.setRemainCount(subItem.getRemainCount().add(shipmentItem.getShipmentCount()));
				}
			}
		}
		shipmentDao.update(shipment);
		subItemDao.update(subItemList);
		log.info("配送单:{} 已成功取消", shipment.getBatchNo());
		return Boolean.TRUE;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void smartLockerConfirmReceipt(Long shipmentId) {
		//先获取shipment
		Shipment shipment = shipmentDao.getShipment(shipmentId);
		if(ObjectUtil.isNotEmpty(shipment) && shipment.getStatus().equals(ShipmentStatus.STOCK_IN)){
			log.info("#######已经入库，不需要入库处理#####");
			return;
		}


		List<ShipmentItem> shipmentItemList = shipmentItemDao.getShipmentItemByShipmentId(shipmentId);
		Map<Long, ShipmentItem> shipmentItemMap = shipmentItemList.stream().collect(Collectors.toMap(ShipmentItem::getSubItemId, Function.identity()));

		List<Long> subItemIds = new ArrayList<>(shipmentItemMap.keySet());
		List<SubItem> subItemList = subItemDao.queryAllByIds(subItemIds);
		if(CollUtil.isEmpty(subItemList)){
			log.info(shipmentId + " 找不到对应的订单子项");
		}

		List<ItemNo> itemNos = itemNoDao.queryByProperties(new LambdaEntityCriterion<>(ItemNo.class)
				.eq(ItemNo::getSubId, shipment.getSubId())
				.in(ItemNo::getSubItemId, subItemIds)
		);
		Map<Long, ItemNo> itemNoMap = itemNos.stream().collect(Collectors.toMap(ItemNo::getSubItemId, Function.identity()));


		//修改subItem 和配送单子表的收货数量
		subItemList.forEach(e->{
			ShipmentItem shipmentItem = shipmentItemMap.get(e.getId());
			if(ObjectUtil.isEmpty(shipmentItem)){
				log.info("订单子项" + e.getSubItemNumber()  + "找不到对应的配送单子项");
			}

			//入库数量 + 配送单上的数量
			e.setInCount(NumberUtil.sub(e.getInCount(), shipmentItem.getShipmentCount()));

			ItemNo itemNo = itemNoMap.get(e.getId());
			if(ObjectUtil.isEmpty(shipmentItem)){
				log.info("项次表" + e.getSubItemNumber()  + "找不到对应的配送单子项");
			}

			//修改item表的数量
			itemNo.setInCount(NumberUtil.add(itemNo.getInCount(), shipmentItem.getShipmentCount()));

			//收货数量 = 配送数量
			shipmentItem.setReceiveCount(shipmentItem.getShipmentCount());
		});

		log.info("状态变成:已入库");
		shipment.setStatus(ShipmentStatus.STOCK_IN);
		shipment.setInCompleteOperatorTime(new Date());
		subItemDao.update(subItemList);
		shipmentDao.update(shipment);
		shipmentItemDao.update(shipmentItemList);
		itemNoDao.update(itemNos);

		//检查是否全部配送完成
		orderReceive(shipment,false);

		SmartLockerReceive smartLockerReceive = this.smartLockerReceiveDao.getByShipmentId(shipmentId);
		smartLockerReceive.setStatus(SmartLockerRecieveEnum.RECEIVE.getValue());
		this.smartLockerReceiveDao.update(smartLockerReceive);
	}

	@Override
	public Boolean smartLockerNoticeErp(Long shipmentId) {
		List<ShipmentItem> shipmentItemList = shipmentItemDao.getShipmentItemByShipmentId(shipmentId);

        Boolean noticeErp = Boolean.TRUE;
		for (ShipmentItem shipmentItem : shipmentItemList) {

			//先判断有没有调用过
			if(Boolean.TRUE.equals(shipmentItem.getNotifyFlag())){
				log.info("shipmentItem " + shipmentItem.getThirdShipRowNo() + "已经调用过了");
				XxlJobLogger.log("shipmentItem " + shipmentItem.getThirdShipRowNo() + "已经调用过了");
				continue;
			}

			//先查是备件还是辅材
			ContractSubInformation contractSubInformation ;
			// 因为合同号 有的带版本号，目前比较乱，所以如果一个字段就查另一个字段试试。
			contractSubInformation = contractSubInformationDao.getByContract(shipmentItem.getContractNum());
			if(AppUtils.isBlank(contractSubInformation)){
				contractSubInformation = contractSubInformationDao.getByContractNumber(shipmentItem.getContractNum());
			}

			SmartLockerNotifyErpDTO smartLockerNotifyErpDTO = new SmartLockerNotifyErpDTO();
			smartLockerNotifyErpDTO.setCompId(String.valueOf(contractSubInformation.getContract().charAt(3)));
			smartLockerNotifyErpDTO.setAmt(shipmentItem.getAmount().toString());
			smartLockerNotifyErpDTO.setQty(shipmentItem.getShipmentCount().toString());
			smartLockerNotifyErpDTO.setBatchNo(shipmentItem.getBatchNo());

			SubItem subItem = this.subItemDao.getById(shipmentItem.getSubItemId());

			smartLockerNotifyErpDTO.setMatrlno(subItem.getMaterialCode());
			smartLockerNotifyErpDTO.setPoNo(contractSubInformation.getContract());
			smartLockerNotifyErpDTO.setPoItemNo(shipmentItem.getItemNo());

			shipmentItem.setNotifyFlag(Boolean.TRUE);

			String requestUrl = null;
			if(ContractPayTypeEnum.SPARE_PARTS.getCode().equals(contractSubInformation.getContractType())){
				requestUrl = erpSendDataUrlConfig.getAttachmentSmartLockerNotifyErpConfirm();
			}else {
				requestUrl = erpSendDataUrlConfig.getSecondarySteelSmartLockerNotifyErpConfirm();
			}

			Boolean result = erpSendData(requestUrl, JSON.toJSONString(Collections.singletonList(smartLockerNotifyErpDTO)));

			if(result){
				this.shipmentItemDao.update(shipmentItem);
			}else {
				// 推送失败标识修改
				noticeErp = Boolean.FALSE;
			}
		}

		return noticeErp;
	}

	private Boolean erpSendData(String url, String body) {
		log.info("请求路径url:" + url);
		try {
			log.info("确认收货 请求参数 {} ", body);
			HttpRequest post = HttpUtil.createPost(ESB_URL + url).body(body);
			System.out.println(post);
			HttpResponse execute = post.execute();
			orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, url, body, 200,JSONObject.toJSONString(execute));
			System.out.println(execute);
			if (execute.isOk()) {
				log.info("返回结果：" + execute.body());
				XxlJobLogger.log("返回结果：" + execute.body());
				String responseBody = execute.body();
				JSONObject jsonObject = JSONObject.parseObject(responseBody);
				Object status = jsonObject.get("status");
				String msg = String.valueOf(jsonObject.get("msg"));
				if(ObjectUtil.isNotEmpty(status)){
					if("200".equals(String.valueOf(status))){
						return Boolean.TRUE;
					}
					// 如果ERP那边已经验收入库了,这边直接处理入库完成
					if(msg.contains("已经验收入库")){
						return Boolean.TRUE;
					}
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return Boolean.FALSE;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<String> createShipment(ShipmentBatchDTO shipmentBatchDTO) {
		//判断是全部发货还是部分发货
		List<ShipmentBatchItem> shipmentItemList = shipmentBatchDTO.getShipmentItemList();
		// 去除发货数量为0的数据
		shipmentItemList = shipmentItemList.stream().filter(item -> item.getShipmentCount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
		if (AppUtils.isBlank(shipmentItemList)) {
			return R.fail("发货项次不存在或发货数量全部为0！");
		}
		int shipmentType = 1;
		for (ShipmentBatchItem item : shipmentItemList) {
			if (item.getShipmentCount().compareTo(item.getUnShipmentCount()) < 0) {
				shipmentType = 2;
				break;
			}
		}
		shipmentBatchDTO.setShipmentType(shipmentType);

		//校验
		// 1、通过订单号，找出对应的订单项信息
		List<SubItem> subItemList = subItemDao.querySubItemBySubNumber(shipmentBatchDTO.getSubNumber());
		if (AppUtils.isBlank(subItemList)) {
			return R.fail("找不到订单项 订单号:" + shipmentBatchDTO.getSubNumber());
		}
		if (ObjectUtil.isNotEmpty(subItemList.get(0).getThirdNumber())) {
			return R.fail("第三方订单不允许鑫采商城主动发货！");
		}
		Sub sub = subDao.getSubBySubNumber(shipmentBatchDTO.getSubNumber());
		if (AppUtils.isBlank(sub)) {
			return R.fail("鑫采商城订单不存在！订单号：" + shipmentBatchDTO.getSubNumber());
		}

		// 2、校验商品是否可用，存在不可用商品无法发货（【中标专区】商品需维护商品信息，即isUsed=1状态为可用后，才可以发货）
		if (OrderSourceEnum.TENDER.value().equals(sub.getOrderSource())) {
			List<Long> tenderSkuIdList = subItemList.stream().map(SubItem::getSkuId).collect(Collectors.toList());
			R<List<OpenTenderSkuVO>> tenderSkuListR = openTenderSkuClient.listSku(new OpenTenderSkuQuery().setTenderSkuIdList(tenderSkuIdList));
			if (!tenderSkuListR.getSuccess()) {
				return R.fail("查询中标商品信息失败！请联系管理员");
			}
			List<OpenTenderSkuVO> tenderSkuList = tenderSkuListR.getResult();
			tenderSkuList.forEach(tenderSku -> {
				if (!tenderSku.getIsUsed()) {
					throw new BusinessException("商品【" + tenderSku.getName() + "】不可用，不允许发货！请先到【商品模块】【中标商品列表】维护商品信息！");
				}
			});
			// 更新subItem的商品图片信息
			subItemList.forEach(subItem -> {
				Optional<OpenTenderSkuVO> optional = tenderSkuList.stream().filter(tenderSku -> tenderSku.getTenderSkuId().equals(subItem.getSkuId())).findFirst();
				if (optional.isPresent()) {
					OpenTenderSkuVO tenderSku = optional.get();
					if (StringUtil.isBlank(subItem.getPic())) {
						subItem.setPic(tenderSku.getImagePath());
					}
				}
			});
			subItemDao.update(subItemList);
		}

		// 3、找出对应的商家信息
		R<ShopDetail> shopDetailR = shopDetailServiceClient.getShopDetailByShopId(sub.getShopId());
		if (shopDetailR == null || shopDetailR.getResult() == null) {
			throw new BusinessException("找不到商家信息！商家ID:" + sub.getShopId());
		}
		ShopDetail shopDetail = shopDetailR.getResult();

		// 4、装配参数PartialShipmentDTO
		PartialShipmentDTO partialShipmentDTO = partialShipmentAssembly(subItemList, sub, shopDetail, shipmentBatchDTO);

		int size = partialShipmentDTO.getShipmentItems().size();
		int distinct = partialShipmentDTO.getShipmentItems().stream().map(ShipmentItemDTO::getSubItemId).distinct().collect(Collectors.toList()).size();
		if (size != distinct) {
			log.error("发货参数,存在相同的订单项！请检查!");
			throw new BusinessException("发货参数,存在相同的订单项！请检查!");
		}

		SubRefundReturnRenewShipment subRefundReturnRenewShipment = null;
		//判断是否是售后配送单
		if (StringUtil.isNotBlank(partialShipmentDTO.getThirdShipNo())) {
			subRefundReturnRenewShipment = subRefundReturnRenewShipmentDao.getByThirdShipmentNoAndShopId(partialShipmentDTO.getThirdShipNo(), partialShipmentDTO.getShopId());
		}
		log.info("售后换新配送单类型：" + partialShipmentDTO.getRefundRenewType());
		if (ShipmentRefundRenewTypeEnum.REFUND_RENEW.getValue().equals(partialShipmentDTO.getRefundRenewType()) || ObjectUtil.isNotEmpty(subRefundReturnRenewShipment)) {
			return R.ok(createAfterSaleShipment(partialShipmentDTO, subRefundReturnRenewShipment, false));
		}
		// 开始生成配送单
		String redisKey = OPEN_SHIPMENT_CREATE_LOCK_KEY + partialShipmentDTO.getOrderNo() + StrUtil.COLON + shopDetail.getShopId();
		RLock lock = redissonClient.getLock(redisKey);
		lock.lock();
		try {
			applyPartialShipment(partialShipmentDTO, Boolean.FALSE);

			log.info("组装ShipmentOperateDTO参数");
			ShipmentOperateDTO shipmentOperateDTO = bindShipmentOperateDTO(partialShipmentDTO, sub, shopDetail);

			log.info("订单更新 保存订单历史，订单更新为已发货状态");
			doShipmentV2(shipmentOperateDTO, sub);

			//如果全部发完了，订单状态更新为待收货
			if (1 == shipmentType) {
				subDao.updateStatus(sub.getSubId(), OrderStatusEnum.CONSIGNMENT.value());
			}
			// 确认出库
			log.info("确认出库 更新配送单信息");
			// todo 这个可以根据商家id 做做不做换个逻辑 这个应该抽象起来
			confirmStockOut(shipmentOperateDTO);
			return R.ok(shipmentOperateDTO.getBatchNo());
		} catch (Exception e) {
			throw new BusinessException("生成配送单异常:" + e.getMessage());
		} finally {
			lock.unlock();
		}
	}

	private PartialShipmentDTO partialShipmentAssembly(List<SubItem> subItemDtos, Sub sub, ShopDetail shopDetail, ShipmentBatchDTO shipmentBatchDTO) {
		List<ShipmentItemDTO> shipmentItemDTOS = mappingProperty(shipmentBatchDTO.getShipmentItemList(), subItemDtos, sub);
		log.info("组装ShipmentItem");
		PartialShipmentDTO partialShipmentDTO = new PartialShipmentDTO();
		//获取合同号
		String contractNum = subItemDao.getContractNumByParam(sub.getSubNumber());
		partialShipmentDTO.setShopId(sub.getShopId())
				.setApplicantName(shopDetail.getSiteName())
				.setApplicantType(UserAccountTypeConst.MAIN)
				//申请时间
				.setApplicantTime(new Date())
				//默认自己发货
				.setLogisticsId(KuaiDiEnum.SELF.getNum().longValue())
				.setContractNum(contractNum != null ? contractNum : "")
				.setLogisticsNo(shipmentBatchDTO.getLogisticsNo())
				.setLogisticsType(shipmentBatchDTO.getLogisticsType())
				.setLogisticsName(shipmentBatchDTO.getLogisticsName())
				//油漆没有三方配送单号
				.setThirdShipNo(null)
				.setThirdShipItemNo("")
				//暂时是用户输入的配送单号
				.setBatchNo(shipmentBatchDTO.getLogisticsNo())
				.setOrderNo(sub.getSubNumber())
				.setSubId(sub.getSubId())
				.setApplicantId(shopDetail.getUserId())
				.setShipmentItems(shipmentItemDTOS);
		return partialShipmentDTO;
	}

	private List<ShipmentItemDTO> mappingProperty(List<ShipmentBatchItem> shipmentItemList, List<SubItem> subItemDtos, Sub sub) {
		log.info("组装mapping  shipmentItemList: {},subItems: {}", JSONUtil.toJsonStr(shipmentItemList), JSONUtil.toJsonStr(subItemDtos));
		Map<Long, SubItem> subItemIdAndSubItemIdMap = subItemDtos.stream().collect(Collectors.toMap(SubItem::getSubItemId, subItem->subItem));
		log.info("订单项表， {}", JSONUtil.toJsonStr(subItemIdAndSubItemIdMap));
		shipmentItemList = shipmentItemList.stream()
				.filter(item -> item.getShipmentCount().compareTo(BigDecimal.ZERO) > 0)
				.collect(Collectors.toList());
		log.info("实际发货的订单项次， {}", JSONUtil.toJsonStr(shipmentItemList));

		//构建配送单项
		return shipmentItemList.stream().map(shipmentItem -> {

			ShipmentItemDTO shipmentItemDTO = new ShipmentItemDTO();
			shipmentItemDTO.setOtherOrderNo(sub.getSubNumber());
			//subItemId
			Long subItemId = Long.valueOf(shipmentItem.getId());
			shipmentItemDTO.setSubItemId(subItemId);
			SubItem subItem = subItemIdAndSubItemIdMap.get(subItemId);
			shipmentItemDTO.setShipmentCount(shipmentItem.getShipmentCount());
			BigDecimal price = BigDecimal.valueOf(subItem.getPrice());
			//发货数量 * 金额
			BigDecimal amount = price.multiply(shipmentItem.getShipmentCount());
			shipmentItemDTO.setAmount(amount);
			shipmentItemDTO.setPrice(BigDecimal.valueOf(subItem.getPrice()));
			return shipmentItemDTO;
		}).collect(Collectors.toList());
	}
	private ShipmentOperateDTO bindShipmentOperateDTO(PartialShipmentDTO partialShipmentDTO, Sub sub, ShopDetail shopDetail) {
		return new ShipmentOperateDTO().setShopId(sub.getShopId()).setSubId(sub.getSubId()).setUserId(shopDetail.getUserId()).setOperatorName(shopDetail.getCompanyName()).setOperatorId(shopDetail.getUserId()).setOperatorType(UserAccountTypeConst.MAIN).setShipmentType(ShipmentTypeConst.SHIPMENT_PARTIAL).setLogisticsNo(partialShipmentDTO.getLogisticsNo()).setLogisticsType(partialShipmentDTO.getLogisticsType()).setLogisticsName(partialShipmentDTO.getLogisticsName()).setLogisticsId(partialShipmentDTO.getLogisticsId()).setShipmentId(partialShipmentDTO.getShipmentId()).setLogisticsAmount(partialShipmentDTO.getLogisticsAmount()).setBatchNo(partialShipmentDTO.getBatchNo());
	}

	@Override
	public List<ShipmentDTO> queryByItemIds(List<Long> shipmentItemIds) {
		List<ShipmentItem> shipmentItemList = shipmentItemDao.queryAllByIds(shipmentItemIds);
		if(ObjectUtil.isEmpty(shipmentItemList)) {
			return null;
		}
		List<Long> shipmentIds = shipmentItemList.stream().map(ShipmentItem::getShipmentId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
		return shipmentConverter.to(shipmentDao.queryAllByIds(shipmentIds));
	}

	@Override
	public Boolean updateDeliveryTime(String thirdShipNo, Long shopId, Date deliveryDate) {
		Shipment shipment = shipmentDao.getByThirdShipNoShopId(thirdShipNo,shopId);
		if(ObjectUtil.isNull(shipment)) {
			return false;
		}
		Sub sub = subDao.getSubBySubNumber(shipment.getOrderNo());
		if(ObjectUtil.isNull(sub)) {
			return false;
		}
		shipment.setInOperatorTime(deliveryDate);
		// 非南钢erp订单 且 配送单状态小于RECEIPT_GOODS需要更新状态
		if (!(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId().equals(sub.getAccountBookId()) && OrderSourceEnum.ERP.value().equals(sub.getOrderSource()))
			&& shipment.getStatus() < ShipmentStatus.RECEIPT_GOODS) {
			shipment.setStatus(ShipmentStatus.RECEIPT_GOODS);
		}
		Integer i = shipmentDao.update(shipment);
		if(i<=0) {
			return false;
		}
		return true;
	}

	@Override
	public String updateErpShipment(String subNumber) {
		List<Shipment> shipments;

		shipments = shipmentDao.getByOrderNo(subNumber);

		if (CollUtil.isEmpty(shipments)) {
			return "未找到订单";
		}
		for (Shipment shipment : shipments) {

				ErpQueryOrderInspInfoRequest erpQueryOrderInspInfoRequest = new ErpQueryOrderInspInfoRequest();
				erpQueryOrderInspInfoRequest.setErpApiNo("MROOR01");
				erpQueryOrderInspInfoRequest.setOrderNumber(shipment.getOrderNo());
				erpQueryOrderInspInfoRequest.setContractNumber(shipment.getContractNum());
				erpQueryOrderInspInfoRequest.setBatchNumber(shipment.getBatchNo());
				log.info("请求erp订单入库接口参数：{}", erpQueryOrderInspInfoRequest);
				JSONObject jsonObject = ErpSendDataUtils.erpESBSendPostData(erpSendDataUrlConfig.getEsbQueryOrderInspInfo(), erpQueryOrderInspInfoRequest, JSONObject.class);
				orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, erpSendDataUrlConfig.getQueryOrderInspInfo(), JSONUtil.toJsonStr(erpQueryOrderInspInfoRequest), jsonObject.get("status").equals("200") ? HttpStatus.HTTP_OK : HttpStatus.HTTP_INTERNAL_ERROR, JSONUtil.toJsonStr(jsonObject));
				if (Objects.isNull(jsonObject) || jsonObject.get("status").equals("500") || ObjectUtil.isEmpty(jsonObject.get("data"))) {
					continue;
				}
				List<ErpQueryOrderInspInfoResponse> erpQueryOrderInspInfoResponse = JSONArray.parseArray(JSON.toJSONString(jsonObject.get("data")), ErpQueryOrderInspInfoResponse.class);
				log.info("erp订单入库接口返回data具体信息：{}", erpQueryOrderInspInfoResponse);
				try {
					updateERPOldShipmentItem(erpQueryOrderInspInfoResponse, shipment,subNumber);
				} catch (Exception e) {
					log.info("更新erp配送单对应数据失败，配送单id：{}，原因：{}", shipment.getShipmentId(), e.getMessage());
				}

		}
		return "";
	}
}
