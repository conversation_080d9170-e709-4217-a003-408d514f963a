package cn.legendshop.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.legendshop.client.DiyiClient;
import cn.legendshop.common.core.config.ErpSendDataUrlConfig;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.dao.AreaDao;
import cn.legendshop.common.dao.CityDao;
import cn.legendshop.common.dao.ProvinceDao;
import cn.legendshop.common.dao.SystemParameterDao;
import cn.legendshop.common.model.entity.Area;
import cn.legendshop.common.model.entity.City;
import cn.legendshop.common.model.entity.Province;
import cn.legendshop.common.model.entity.SystemParameter;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyConstant;
import cn.legendshop.common.utils.BeanUtils;
import cn.legendshop.common.utils.CommonServiceUtil;
import cn.legendshop.common.utils.PageSupportUtil;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.constant.OrderStatusEnum;
import cn.legendshop.model.DiyiResponse;
import cn.legendshop.model.SmartLockerCallbackDTO;
import cn.legendshop.model.request.AsyncOrderRequest;
import cn.legendshop.order.constant.ShipmentStatus;
import cn.legendshop.order.dao.*;
import cn.legendshop.order.dto.*;
import cn.legendshop.order.entity.SmartLocker;
import cn.legendshop.order.entity.SmartLockerReceive;
import cn.legendshop.order.entity.SmartLockerRecord;
import cn.legendshop.order.entity.SmartServiceChargeBill;
import cn.legendshop.order.enums.NoticeEnum;
import cn.legendshop.order.enums.SmartLockerRecieveEnum;
import cn.legendshop.order.model.ContractSubInformation;
import cn.legendshop.order.model.Shipment;
import cn.legendshop.order.model.ShipmentItem;
import cn.legendshop.order.mq.producer.OrderProducerService;
import cn.legendshop.order.query.ErpSmartLockerQuery;
import cn.legendshop.order.query.SmartLockManegeQuery;
import cn.legendshop.order.query.UnFinishShipmentQuery;
import cn.legendshop.order.service.MallShipmentProcessService;
import cn.legendshop.order.service.SmartLockerService;
import cn.legendshop.order.service.convert.SmartLockerConverter;
import cn.legendshop.order.util.Base64ToMultipartFile;
import cn.legendshop.order.vo.ErpShipmentPrintVo;
import cn.legendshop.order.vo.ShipmentPrintVo;
import cn.legendshop.product.api.enums.LogicDeleteEnum;
import cn.legendshop.utils.DiyiSignatureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.update.LambdaUpdate;
import com.legendshop.model.constant.DeliveryTypeEnum;
import com.legendshop.model.constant.SmartLockerConstant;
import com.legendshop.model.entity.Sub;
import com.legendshop.model.entity.SubItem;
import com.legendshop.model.entity.orderreturn.SubRefundReturn;
import com.legendshop.uploader.AttachmentManager;
import com.legendshop.util.AppUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

import static cn.legendshop.url.ErpSendDataUrlConstant.ESB_URL;

/**
 * 智能柜管理实现类
 */
@Service
@Slf4j
@AllArgsConstructor
public class SmartLockerServiceImpl implements SmartLockerService {

    private final SmartLockerDao smartLockerDao;

	private final  SystemParameterDao systemParameterDao;

    private final SmartLockerConverter smartLockerConverter;

    private final SmartLockerReceiveDao smartLockerReceiveDao;

    private final ShipmentDao shipmentDao;

    private final SubDao subDao;

    private final ProvinceDao provinceDao;

    private final CityDao cityDao;

    private final AreaDao areaDao;

    private final SmartLockerRecordDao smartLockerRecordDao;

    private final OrderProducerService orderProducerService;
    private final MallShipmentProcessService mallShipmentProcessService;
    private AttachmentManager attachmentManager;
    private final ErpSendDataUrlConfig erpSendDataUrlConfig;
    private final ContractSubInformationDao contractSubInformationDao;
    private final ShipmentItemDao shipmentItemDao;
    private final SubItemDao subItemDao;
    private final SubRefundReturnDao subRefundReturnDao;
    private final SmartServiceChargeBillDao serviceChargeBillDao;

    @Override
    public PageSupport<SmartLockerDTO> queryLockerList(SmartLockManegeQuery smartLockManegeQuery) {
        PageSupport<SmartLocker> smartLockerPageSupport = smartLockerDao.queryLockerList(smartLockManegeQuery);

        return PageSupportUtil.convertPageSupport(smartLockerPageSupport, smartLockerConverter);
    }

    @Override
    public R<Long> createOrUpdate(SmartLockerDTO smartLockerDTO) {
        if(ObjectUtil.isEmpty(smartLockerDTO)){
            return R.fail("数据为空");
        }
        SmartLocker smartLocker = smartLockerConverter.from(smartLockerDTO);

        if(smartLocker.getId() == null){
            smartLocker.setCreateTime(new DateTime());
        }else {
            SmartLocker smartLocker1 = smartLockerDao.getById(smartLockerDTO.getId());
            smartLocker.setCreateTime(smartLocker1.getCreateTime());
        }
        smartLocker.setUpdateTime(new DateTime());
        smartLocker.setIsDeleted(LogicDeleteEnum.NOT_LOGIC_DELETE.value());

        if(StrUtil.isBlank(smartLocker.getProvince())){
            Province province = provinceDao.getById(smartLocker.getProvinceId());
            smartLocker.setProvince(province.getProvince());
        }

        if(StrUtil.isBlank(smartLocker.getCity())){
            City city = cityDao.getById(smartLocker.getCityId());
            smartLocker.setProvince(city.getCity());
        }

        if(StrUtil.isBlank(smartLocker.getArea())){
            Area area = areaDao.getById(smartLocker.getAreaId());
            smartLocker.setProvince(area.getArea());
        }

        return R.ok(smartLockerDao.saveOrUpdate(smartLocker));
    }

    @Override
    public R<Integer> logicDelete(Long id) {
        return R.ok(smartLockerDao.updateProperties(new LambdaUpdate<>(SmartLocker.class).eq(SmartLocker::getId, id).set(SmartLocker::getIsDeleted, LogicDeleteEnum.IS_LOGIC_DELETE.value())));
    }

    @Override
    public SmartLockerDTO getById(Long id) {
        SmartLocker smartLocker = smartLockerDao.getById(id);
        if(ObjectUtil.isEmpty(smartLocker)){
            return null;
        }
        return smartLockerConverter.to(smartLocker);
    }

    @Override
    public List<ErpSmartLockerDTO> fuzzleQuery(ErpSmartLockerQuery erpSmartLockerQuery) {
        List<SmartLocker> smartLockers = smartLockerDao.fuzzleQuery(erpSmartLockerQuery);
        return smartLockerConverter.to1(smartLockers);
    }

    @Override
    public SmartLockerDTO getByStationId(String stationId) {
        SmartLocker smartLocker = this.smartLockerDao.getByStationId(stationId);
        return smartLockerConverter.to(smartLocker);
    }

	@Override
	public void retryAsync(String orderNumber) {
		//1. 查询驿站订单的配送7天为未完成推送的送货单
		SystemParameter retryAsync = systemParameterDao.getSystemParameterByName(SmartLockerConstant.RETRY_ASYNC_DAY);
		Integer retryAsyncDay = 7;
		if (retryAsync != null){
			retryAsyncDay = Integer.valueOf(retryAsync.getValue());
		}
		Integer curPage = 1;
        Integer pageSize = 10;
		UnFinishShipmentQuery unFinishShipmentQuery = new UnFinishShipmentQuery();
		Date currentDate = new Date();
		unFinishShipmentQuery.setOrderNo(orderNumber);
		unFinishShipmentQuery.setFinishStatus(ShipmentStatus.STOCK_IN.intValue());
		unFinishShipmentQuery.setDeyType(DeliveryTypeEnum.EXPRESS_DELIVERY_CABINET.value());
		unFinishShipmentQuery.setPageSize(pageSize);

		//查询时间
		Date applicationTime = DateUtil.offsetDay(currentDate, -retryAsyncDay);
		unFinishShipmentQuery.setApplicationTime(applicationTime);
		Boolean hasNext = Boolean.TRUE;

		//2. 配送单重新推送
		do {
            XxlJobLogger.log("配送单重新推送驿站：当前页数:{}",curPage);
			unFinishShipmentQuery.setCurPage(curPage);
			PageSupport<Shipment> unFinishShipmentList = shipmentDao.queryUnFinishShipmentList(unFinishShipmentQuery);
			List<Shipment> resultList = unFinishShipmentList.getResultList();
			if (CollectionUtil.isEmpty(resultList) || resultList.size() < pageSize){
				hasNext = Boolean.FALSE;
			}
			if (CollectionUtil.isNotEmpty(resultList)){
				for (Shipment shipment: resultList){
					log.info("配送单重新推送驿站：配送单号:{}",shipment.getBatchNo());
                    XxlJobLogger.log("配送单重新推送驿站：配送单号:{}",shipment.getBatchNo());
					AsyncOrderRequest asyncOrderRequest = new AsyncOrderRequest();
					//全部都定义为内部物流编号
					asyncOrderRequest.setExpressId(9999);
                    asyncOrderRequest.setExpressNumber(shipment.getThirdShipNo());
                    Sub sub = subDao.getSubBySubNumber(shipment.getOrderNo());
                    if (AppUtils.isNotBlank(sub.getStationId())){
                        asyncOrderRequest.setStationId(Integer.valueOf(sub.getStationId()));
                    }
					asyncOrderRequest.setReceiverMobile(shipment.getContactNumber());
					log.info("调用信息信息："+ JSONObject.toJSONString(asyncOrderRequest));
					DiyiResponse<Boolean> response = DiyiClient.asyncLogisticsInfo(asyncOrderRequest);
					log.info("调用信息结果："+ JSONObject.toJSONString(response));
				}
			}
			curPage = curPage+1;
		}while (hasNext);
	}

	@Override
    public DiyiResponse<Boolean> asyncCallBack(SmartLockerCallbackRequestDTO smartLockerCallbackRequestDTO) {
        SmartLockerCallbackDTO smartLockerCallbackDTO = smartLockerConverter.to2(smartLockerCallbackRequestDTO);

        Boolean verifyResult = DiyiSignatureUtil.verifySignature(smartLockerCallbackDTO);
        if(!verifyResult){
            return new DiyiResponse<>(Boolean.FALSE, 500,"验签失败", smartLockerCallbackDTO.getReqTime(), Boolean.FALSE);
        }

		//替换字符串（9月18号之后京东前缀去除）
		if (!StringUtils.isBlank(smartLockerCallbackRequestDTO.getExpressNo()) && smartLockerCallbackDTO.getExpressNo().startsWith(SmartLockerConstant.SHIPMENT_NO_PREFIX)){
			log.info("加了特殊前缀驿站订单,需要去掉前缀。");
			String expressNo = smartLockerCallbackRequestDTO.getExpressNo();
			String replaceExpressNo = expressNo.replace(SmartLockerConstant.SHIPMENT_NO_PREFIX, "");
			smartLockerCallbackRequestDTO.setExpressNo(replaceExpressNo);
		}

		log.info("驿站配送单号：{}",smartLockerCallbackRequestDTO.getExpressNo());
        SmartLockerReceive smartLockerReceive = this.smartLockerReceiveDao.getByThirdShipNo(smartLockerCallbackRequestDTO.getExpressNo());

        // 按照物流单号查询，如果查不到就按照三方配送单号查询
        Shipment shipment = this.shipmentDao.getByLogisticsNo(smartLockerCallbackRequestDTO.getExpressNo());
        if (AppUtils.isBlank(shipment)){
            shipment  = this.shipmentDao.getByThirdShipOrderNo(smartLockerCallbackRequestDTO.getExpressNo());
        }
        if (AppUtils.isBlank(shipment)){
            shipment  = this.shipmentDao.getByThirdShipItemNo(smartLockerCallbackRequestDTO.getExpressNo());
        }

        Integer receiveStatus = SmartLockerRecieveEnum.INIT.value();
        if(OrderStatusEnum.SIGNED.getValue().equals(smartLockerCallbackDTO.getOrderStatus()) || OrderStatusEnum.DELIVERY.getValue().equals(smartLockerCallbackDTO.getOrderStatus())){
           log.info("驿站已签收或者快递外送");
            receiveStatus = SmartLockerRecieveEnum.RECEIVE.getValue();
        }

        if(OrderStatusEnum.RETURN.getValue().equals(smartLockerCallbackDTO.getOrderStatus()) || OrderStatusEnum.REJECT.getValue().equals(smartLockerCallbackDTO.getOrderStatus())){
            receiveStatus = SmartLockerRecieveEnum.REJECT.getValue();
			log.info("驿站已退回或者拒收");
            //更改配送单状态
            if(ObjectUtil.isNotEmpty(shipment)){
                shipment.setStatus(ShipmentStatus.STOCK_OUT);
                shipment.setUpdateTime(new DateTime());
                this.shipmentDao.update(shipment);
            }
        }
        if(ObjectUtil.isEmpty(smartLockerReceive)){
            //说明是第一次调用，新生成一条
            smartLockerReceive = new SmartLockerReceive();
            smartLockerReceive.setCreateTime(new DateTime());
            if(ObjectUtil.isNotEmpty(shipment)){
                smartLockerReceive.setShipmentId(shipment.getShipmentId());
            }
            smartLockerReceive.setNoticeErp(NoticeEnum.INIT.getValue());
            smartLockerReceive.setNoticeThird(NoticeEnum.INIT.getValue());
        }

        smartLockerReceive.setThirdShipNo(smartLockerCallbackRequestDTO.getExpressNo());
        smartLockerReceive.setStatus(receiveStatus);
        smartLockerReceive.setThirdStatus(smartLockerCallbackDTO.getOrderStatus());
        smartLockerReceive.setReceiveTime(new DateTime());
        smartLockerReceive.setUpdateTime(new DateTime());

        this.smartLockerReceiveDao.saveOrUpdate(smartLockerReceive);

        //发短信通知领用人
        if(OrderStatusEnum.CABINET.getValue().equals(smartLockerCallbackDTO.getOrderStatus()) || OrderStatusEnum.SHELF.getValue().equals(smartLockerCallbackDTO.getOrderStatus())) {
            log.info("发送短信通知领用人");
            // 快递单号 取件码 站点名称 柜子格口
            orderProducerService.smartLockerSendSms(smartLockerCallbackRequestDTO.getExpressNo() + StrUtil.COLON + smartLockerCallbackRequestDTO.getPassWord() + StrUtil.COLON + smartLockerCallbackRequestDTO.getStationName() + StrUtil.COLON + smartLockerCallbackRequestDTO.getCustomerSN());
        }

        //通知ERP 如果没有关联到shipment 无效投递
        if (AppUtils.isNotBlank(shipment)){
            sentReceiveCode(shipment,smartLockerCallbackRequestDTO.getExpressNo());
        }


        return new DiyiResponse<>(Boolean.TRUE, 200,"接收成功", smartLockerCallbackDTO.getReqTime(), Boolean.TRUE);
    }


    public void sentReceiveCode(Shipment shipment,String expressNo){
        String url = erpSendDataUrlConfig.getSendReceiveCode();
        List<ShipmentItem> shipmentItemList = shipmentItemDao.getShipmentItemByShipmentId(shipment.getShipmentId());
        Date inOperatorTime = shipment.getInOperatorTime();
        Date arrivalDate =new Date();
        if (AppUtils.isNotBlank(inOperatorTime)){
            arrivalDate=inOperatorTime;
        }
        for (ShipmentItem shipmentItem : shipmentItemList) {
            SentReceiveCodeDTO sentReceiveCodeDTO = new SentReceiveCodeDTO();
            //先查是备件还是辅材
            ContractSubInformation contractSubInformation ;
            // 因为合同号 有的带版本号，目前比较乱，所以如果一个字段就查另一个字段试试。
            contractSubInformation = contractSubInformationDao.getByContract(shipmentItem.getContractNum());
            if(AppUtils.isBlank(contractSubInformation)){
                contractSubInformation = contractSubInformationDao.getByContractNumber(shipmentItem.getContractNum());
            }


            sentReceiveCodeDTO.setCompId(String.valueOf(contractSubInformation.getContract().charAt(3)));
            sentReceiveCodeDTO.setPoNo(contractSubInformation.getContract());
            sentReceiveCodeDTO.setPoItemNo(shipmentItem.getItemNo());
            SubItem subItem = subItemDao.getById(shipmentItem.getSubItemId());
            sentReceiveCodeDTO.setMatrlno(subItem.getMaterialCode());
            sentReceiveCodeDTO.setArrivalDate(arrivalDate.toString());
            //获取取件码
            String password =  smartLockerDao.getPasswordByExpressNo(expressNo);
            sentReceiveCodeDTO.setReceiveCode(password);
            Boolean result = erpSendData(url, JSON.toJSONString(Collections.singletonList(sentReceiveCodeDTO)));
            if (result) {
                log.info("通知ERP成功");
            } else {
                log.info("通知ERP失败 通知参数sentReceiveCodeDTO：{}",sentReceiveCodeDTO);
            }
        }
    }

    private Boolean erpSendData(String url, String body) {
        log.info("请求路径url:" + url);
        try {
            log.info("确认收货 请求参数 {} ", body);
            HttpRequest post = HttpUtil.createPost(ESB_URL + url).body(body);
            System.out.println(post);
            HttpResponse execute = post.execute();
            orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, url, body, 200,JSONObject.toJSONString(execute));
            System.out.println(execute);
            if (execute.isOk()) {
                log.info("返回结果：" + execute.body());
                String responseBody = execute.body();
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                Object status = jsonObject.get("status");
                if(ObjectUtil.isNotEmpty(status)){
                    if("200".equals(String.valueOf(status))){
                        return Boolean.TRUE;
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.info(e.getMessage(), e);
        }
        return Boolean.FALSE;
    }

    @Override
    public void opearteRecord(String operateRecord) throws ParseException {
        SmartLockerCallbackRequestDTO smartLockerCallbackRequestDTO = JSONObject.parseObject(operateRecord, SmartLockerCallbackRequestDTO.class);
        SmartLockerRecord record = this.smartLockerRecordDao.getByExpressNoAndOrderStatus(smartLockerCallbackRequestDTO.getExpressNo(), smartLockerCallbackRequestDTO.getOrderStatus());
        if(ObjectUtil.isNotEmpty(record)){
            return;
        }
        SmartLockerRecord smartLockerRecord = new SmartLockerRecord();
        smartLockerRecord.setExpressNo(smartLockerCallbackRequestDTO.getExpressNo());
        smartLockerRecord.setStatus(smartLockerCallbackRequestDTO.getOrderStatus());
        smartLockerRecord.setStationName(smartLockerCallbackRequestDTO.getStationName());
        smartLockerRecord.setOperatorPhone(smartLockerCallbackRequestDTO.getOperatorPhone());
        smartLockerRecord.setPassword(smartLockerCallbackRequestDTO.getPassWord());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        smartLockerRecord.setOperatorTime(sdf.parse(smartLockerCallbackRequestDTO.getOperatorTime()));
        smartLockerRecord.setOperatorTime(new DateTime());
        this.smartLockerRecordDao.save(smartLockerRecord);
    }

    @Override
    public List<SmartLockerRecordDTO> getRecord(String expressNo) {
        List<SmartLockerRecord> smartLockerRecords = this.smartLockerRecordDao.getByExpressNo(expressNo);
        if(CollUtil.isEmpty(smartLockerRecords)){
            return new ArrayList<>();
        }

        List<SmartLockerRecordDTO> smartLockerRecordDTOS = smartLockerConverter.to3(smartLockerRecords);
        for (SmartLockerRecordDTO smartLockerRecordDTO : smartLockerRecordDTOS) {
            smartLockerRecordDTO.setDesc(OrderStatusEnum.findByValue(smartLockerRecordDTO.getStatus()));
        }

        return smartLockerRecordDTOS;
    }

    @Override
    public R<Long> updateSign(SignPicDTO signPicDTO) {
        // 获取配送单数据
        ShipmentPrintVo result = mallShipmentProcessService.signShipmentData(signPicDTO.getShipmentNo()).getResult();
        ErpShipmentPrintVo erpShipmentPrintVo  = new ErpShipmentPrintVo();
        BeanUtils.copyProperties(result,erpShipmentPrintVo);

        //生成图片
        createPDFByShipment(erpShipmentPrintVo, signPicDTO.getSignPic());
        return null;
    }

    @Override
    public void SmartLockerBill(SmartLockerBillDTO smartLockerBillDTO) {

        log.info("统计驿站服务费定时任务开始");



        //设置统计发货方式类型 20 为驿站收货
        smartLockerBillDTO.setDeliveryType("20");
        try {
            //获取所有的未对账的订单
            List<Sub> subList = subDao.SmartLockerBill(smartLockerBillDTO);

            if (CollectionUtil.isNotEmpty(subList)) {
                //按照店铺分组
                Map<Long, List<Sub>> collect = subList.stream().collect(Collectors.groupingBy(Sub::getShopId));
                for (Long shopId: collect.keySet()){
                    if (AppUtils.isNotBlank(smartLockerBillDTO.getShopId()) ){
                        log.info("只生成该店铺账单：shopId{}",shopId);
                        if (!smartLockerBillDTO.getShopId().equals(shopId)){
                            log.info("本次定时任务不生成该店铺账单： shopID：{}",smartLockerBillDTO.getShopId());
                            continue;
                        }
                    }
                    //如果已经生成了，重置之前的，
                    SmartServiceChargeBill smartServiceChargeBill = serviceChargeBillDao.getByReconciliationTime(smartLockerBillDTO.getReconciliationTime(),smartLockerBillDTO.getShopId());
                    if(AppUtils.isBlank(smartServiceChargeBill)){
                        smartServiceChargeBill = new SmartServiceChargeBill();
                        smartServiceChargeBill.setBeginTime(smartLockerBillDTO.getBeginDate());
                        smartServiceChargeBill.setEndTime(smartLockerBillDTO.getEndDate());
                        smartServiceChargeBill.setCreateTime(new Date());
                        smartServiceChargeBill.setShopId(shopId);
                        String randomSn = CommonServiceUtil.getRandomSn();
                        smartServiceChargeBill.setReconciliationNo(randomSn);
                        smartServiceChargeBill.setReconciliationTime(smartLockerBillDTO.getReconciliationTime());
                    }else {
                        //重置
                        subDao.cleanSmartBillSn(smartServiceChargeBill.getReconciliationNo());
                    }
                    smartServiceChargeBill.setConfirmStatus(10);
                    smartServiceChargeBill.setSettlementStatus(10);

                    BigDecimal billAmount = new BigDecimal(0);
                    BigDecimal orderAmount = new BigDecimal(0);
                    BigDecimal refundAmount = new BigDecimal(0);
                    //统计金额
                    List<Sub> subListShop = collect.get(shopId);
                    for (Sub sub : subListShop) {
                        BigDecimal subBillAmount = BigDecimal.valueOf(sub.getActualTotal()==null? 0 : sub.getActualTotal());
                        orderAmount = orderAmount.add(subBillAmount);
                        //去除售后金额
                        List<SubRefundReturn> subRefundReturns = subRefundReturnDao.queryBySubNumber(sub.getSubNumber());
                        if (Objects.nonNull(subRefundReturns)) {
                            for (SubRefundReturn subRefundReturn : subRefundReturns) {
                                BigDecimal refund =  subRefundReturn.getRefundAmount() == null ? new BigDecimal(0) : subRefundReturn.getRefundAmount();
                                subBillAmount=subBillAmount.subtract(refund);
                                refundAmount = refundAmount.add(refund);
                            }
                        }
                        billAmount=billAmount.add(subBillAmount);
                        // 更新标记
                        sub.setSmartBillSn(smartLockerBillDTO.getReconciliationNo());
                        subDao.update(sub);
                    }
                    smartServiceChargeBill.setBillAmount(billAmount);
                    smartServiceChargeBill.setOrderAmount(orderAmount);
                    smartServiceChargeBill.setRefundAmount(refundAmount);
                    BigDecimal commissionRate = new BigDecimal(0.02);
                    smartServiceChargeBill.setCommissionRate(commissionRate);
                    smartServiceChargeBill.setCommissionAmount(billAmount.multiply(commissionRate));
                    serviceChargeBillDao.save(smartServiceChargeBill);
                }
            }
        } catch (Exception e) {
            log.info("账单错误异常信息", e);
            XxlJobLogger.log("统计账套MRO账单定时任务出错！ 异常：{}", e);
        }
    }

    public void  createPDFByShipment(ErpShipmentPrintVo vo,String signPic){

        Document document =null;
        OutputStream outputStream ;
        try{
            document = new Document(PageSize.A4, 40, 40, 80,0);
            //测试输出地址
            outputStream= Files.newOutputStream(Paths.get("E:\\file" + "/" + vo.getShipmentNo()+".pdf"));
            PdfWriter.getInstance(document,outputStream);
            document.open();
            // 解决中文问题
//            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
//            float size=  writer.getPageSize().getWidth();
            Paragraph pt = new Paragraph("配送清单\n", headFont); // 设置“招标服务费收取通知单”的段落
            pt.setAlignment(1);// 设置文字居中 0靠左 1，居中 2，靠右
            document.add(pt);
            document.add(new Paragraph("\n"));// 添加段落分隔符 换行
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));

            createParagraph("送货单号",vo.getShipmentNo(),document);
            createParagraph("请购单号",vo.getRequisitionNo(),document);
            createParagraph("交货单号",vo.getDeliveryNum(),document);
            createParagraph("经办人",vo.getOperator(),document);
            createParagraph("经办人联系方式",vo.getOperatorMobile(),document);
            createParagraph("收货单位",vo.getConsignee(),document);
            createParagraph("收货地址",vo.getDeliveryAddress(),document);
            createParagraph("仓库收获联系人",vo.getStoreKeeper(),document);
            createParagraph("仓库收货人联系方式",vo.getStoreKeeperPhone(),document);
            createParagraph("仓库名称",vo.getStore(),document);
            createParagraph("发货日期",vo.getDeliveryDate().toString(),document);
            createParagraph("发货单位",vo.getShopFullName(),document);
            createParagraph("用料单位",vo.getWorkingName(),document);
            createParagraph("合同类型",vo.getContractType(),document);

            //第一行标题  table
            PdfPTable table = new PdfPTable(12);
            table.setWidthPercentage(100f);
            table.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.setWidths(new int[]{1,4,3,2,2,2,2,2,2,2,2,2});
            table.getDefaultCell().setBorder(0);
            float cellHeight = 70; // 表格的行高

            table.addCell(createCell("序号", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("商品名称", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("南钢物料编码", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("单位", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("南钢ERP订单号", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("南钢合同号", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("南钢ERP订单号", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("项次号", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("下单数量", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("本次送货数量", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("南钢要货期", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("申报人及联系方式", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));
            table.addCell(createCell("采购要求", textFont, Element.ALIGN_CENTER,Element.ALIGN_MIDDLE,cellHeight));

            document.add(table);
            //放入图片 图片解码
            String data = signPic.split(",")[1];
            String dataURl = signPic.split(",")[0];
//            byte[] bs = Base64Utils.decodeFromString(data);
//            for (int i = 0; i < bs.length; i++) {
//                if (bs[i]<0){
//                    bs[i]+=256;
//                }
//            }

            MultipartFile multipartFile = new Base64ToMultipartFile(data,dataURl);
            String upload = attachmentManager.upload(multipartFile);
            System.out.println(upload);
//            document.add(Image.getInstance(bs));
            // 5.关闭文档
            document.close();

            outputStream.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

//        return outputStream;
    }

    //PDF文字
    private static Font headFont;// 设置字体大小
    private static Font keyNormalFont;// 设置字体大小
    private static Font keyBoldFont;// 设置字体大小
    private static Font theadFont;// 设置字体大小
    private static Font textFont;// 设置字体大小
    private static Font tailFont;// 设置字体大小
    private static Font txtFont;// 设置字体大小
    private static Font keyNormalFonts;// 设置字体大小
    static {
        BaseFont bfChinese;
        try {
            bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            headFont = new Font(bfChinese, 28, Font.NORMAL);// 设置字体大小
            keyNormalFont = new Font(bfChinese, 14, Font.NORMAL);// 设置字体大小
            keyNormalFonts = new Font(bfChinese, 12, Font.NORMAL);// 设置字体大小
            keyBoldFont = new Font(bfChinese, 14, Font.BOLD);// 设置字体大小
            theadFont = new Font(bfChinese, 11, Font.BOLD);// 设置字体大小
            textFont = new Font(bfChinese, 13, Font.BOLD);// 设置字体大小
            txtFont = new Font(bfChinese,11, Font.NORMAL);
            tailFont = new Font(bfChinese,15, Font.BOLD);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //PDF 创建一行
    public void createParagraph(String title,String data,Document document) throws DocumentException {
        Paragraph paragraph = new Paragraph();
        paragraph.add(new Chunk(title, keyNormalFont));
        paragraph.setAlignment(0); // 靠左对齐
        paragraph.add(new Chunk("  ")); // 在这里添加空格字符来增加间隔
        paragraph.add(new Chunk(data, keyNormalFonts));
        document.add(paragraph);
        Paragraph spacer = new Paragraph();
        spacer.setSpacingAfter(10); // 设置间隔后的间距大小
        document.add(spacer);
    }

    //PDF-创建单元格设置样式
    public PdfPCell createCell(String content, Font font, int halign, int valign, float height) {
        PdfPCell cell = new PdfPCell(new Phrase(content, font)); // 创建单元格
        cell.setHorizontalAlignment(halign); // 设置水平对齐
        cell.setVerticalAlignment(valign); // 设置垂直对齐
        cell.setFixedHeight(height); // 设置单元格高度
        return cell;
    }

    public PdfPCell createCell1(String content, Font font, int halign, int valign ) {
        PdfPCell cell = new PdfPCell(new Phrase(content, font)); // 创建单元格
        cell.setHorizontalAlignment(halign); // 设置水平对齐
        cell.setVerticalAlignment(valign); // 设置垂直对齐
        cell.setFixedHeight(50f); // 设置单元格高度
        //cell.setBackgroundColor(bgColor); // 设置背景颜色
        return cell;
    }
}
