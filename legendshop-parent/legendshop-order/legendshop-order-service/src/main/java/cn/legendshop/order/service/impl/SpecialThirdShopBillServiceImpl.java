package cn.legendshop.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.service.OpenPlatformDeveloperConfigService;
import cn.legendshop.common.service.SystemParameterService;
import cn.legendshop.common.utils.CommonServiceUtil;
import cn.legendshop.order.config.OrderServiceConfig;
import cn.legendshop.order.construction.config.ThirdBillProperties;
import cn.legendshop.order.dao.ReconciliationBillDao;
import cn.legendshop.order.dao.ReconciliationBillItemDao;
import cn.legendshop.order.dao.ReconciliationBillLogDao;
import cn.legendshop.order.dto.UpdateReconciliationBillDTO;
import cn.legendshop.order.dto.result.OpenPlatformBillDetailDTO;
import cn.legendshop.order.entity.ReconciliationBill;
import cn.legendshop.order.entity.ReconciliationBillItem;
import cn.legendshop.order.entity.ReconciliationBillSummary;
import cn.legendshop.order.enums.ReconciliationConfirmStatusEnum;
import cn.legendshop.order.enums.ReconciliationSettlementStatusEnum;
import cn.legendshop.order.enums.ReconciliationTypeEnum;
import cn.legendshop.order.mq.producer.OrderProducerService;
import cn.legendshop.order.query.ReconciliationBillQuery;
import cn.legendshop.order.service.ReconciliationBillSummaryService;
import cn.legendshop.order.service.SpecialThirdShopBillService;
import cn.legendshop.order.util.OpenPlatformRequestUtil;
import cn.legendshop.payment.client.ExternalRefundReturnClinet;
import cn.legendshop.payment.client.PaymentTransactionClient;
import cn.legendshop.payment.dto.CashierBillQuery;
import cn.legendshop.payment.dto.openPlatform.ExternalRefundReturnVO;
import cn.legendshop.payment.dto.openPlatform.PaymentTransactionBillDTO;
import cn.legendshop.payment.entity.PaymentTransaction;
import cn.legendshop.payment.entity.ThirdPayTransactionBookkeeping;
import cn.legendshop.payment.enums.*;
import cn.legendshop.user.api.client.GCardUserClient;
import cn.legendshop.user.api.client.ICardUserClient;
import cn.legendshop.user.api.dto.openPlatform.CardTotalUsageDTO;
import com.legendshop.util.StringUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SpecialThirdShopBillServiceImpl implements SpecialThirdShopBillService {

    @Autowired
    protected SystemParameterService systemParameterService;

    @Autowired
    private PaymentTransactionClient paymentTransactionClient;

    @Autowired
    private ExternalRefundReturnClinet externalRefundReturnClinet;

    @Autowired
    private OpenPlatformRequestUtil openPlatformRequestUtil;

    @Autowired
    private OrderServiceConfig orderServiceConfig;

    @Autowired
    private ThirdBillProperties thirdBillProperties;

    @Autowired
    private GCardUserClient gCardUserClient;

    @Autowired
    private ICardUserClient iCardUserClient;

    @Autowired
    private ReconciliationBillSummaryService reconciliationBillSummaryService;

    @Autowired
    private OpenPlatformDeveloperConfigService openPlatformDeveloperConfigService;

    @Autowired
    private ReconciliationBillDao reconciliationBillDao;

    @Autowired
    private ReconciliationBillItemDao reconciliationBillItemDao;

    @Autowired
    private ReconciliationBillLogDao reconciliationBillLogDao;

    @Autowired
    private OrderProducerService orderProducerService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateThirdSpecialShopBill(UpdateReconciliationBillDTO updateReconciliationBillDTO) {

        //获取当前日期
        Date now = new Date();

        // 出账日：字段billingDay，现在为当月1号
        // 结算日：字段settlementDay，现在为上月23号
        // 出账逻辑：每月1号，出上月账单。如9月1日，出8月账单。8月账单周期为2024-07-24 00:00:00 - 2024-08-23 23:59:59
        // 原因：生成账单日期与结算日期，相差7天左右的时间。该周期用于订单售后。在1号时，可能存在的售后订单都已经基本完成。可以减少售后订单产生的结算问题。

        //账单出账日
        Integer billingDay = thirdBillProperties.getBillingDate();

        //这个月的最后一天
        List<String> monthLastDays = StrUtil.split(DatePattern.NORM_DATE_FORMAT.format(cn.hutool.core.date.DateUtil.endOfMonth(cn.hutool.core.date.DateUtil.date())), "-");
        String monthLastDay = monthLastDays.get(monthLastDays.size() - 1);
        if (billingDay >= Integer.parseInt(monthLastDay)){
            billingDay = Integer.parseInt(monthLastDay);
        }

        //账单出账日期
        //拼接这个月的年月，
        Date billingDate = cn.hutool.core.date.DateUtil.parseDateTime(DatePattern.NORM_MONTH_FORMAT.format(cn.hutool.core.date.DateUtil.beginOfMonth(cn.hutool.core.date.DateUtil.date())) + "-"+billingDay+" 00:00:00");

        if (billingDay != cn.hutool.core.date.DateUtil.dayOfMonth(now)){
            log.info("当前时间不是对账时间,任务结束！");
            XxlJobLogger.log("当前时间不是对账时间billingDay,任务结束！");
            return;
        }

        //账单结算日------账单结算时查询当前时间前的支付单
        Integer settlementDay = thirdBillProperties.getSettlementDate();
        //账单结算日期 账单结算时查询当前时间前的支付单
        //上个月的最后一天
        List<String> lastDays = StrUtil.split(DatePattern.NORM_DATE_FORMAT.format(cn.hutool.core.date.DateUtil.endOfMonth(cn.hutool.core.date.DateUtil.lastMonth())), "-");
        String lastDay = lastDays.get(lastDays.size() - 1);
        if (settlementDay >= Integer.parseInt(lastDay)){
            settlementDay = Integer.parseInt(lastDay);
        }
        //拼接上个月的年月，
        Date settlementDate = cn.hutool.core.date.DateUtil.parseDateTime(DatePattern.NORM_MONTH_FORMAT.format(cn.hutool.core.date.DateUtil.beginOfMonth(cn.hutool.core.date.DateUtil.lastMonth())) + "-"+settlementDay+" 23:59:59");
        //生成上个档期  currentDate
        String reconciliationTime =  Optional.ofNullable(updateReconciliationBillDTO.getReconciliationTime()).orElse("OPEN-"+DatePattern.SIMPLE_MONTH_FORMAT.format(cn.hutool.core.date.DateUtil.beginOfMonth(cn.hutool.core.date.DateUtil.lastMonth())));

        //账单开始时间，账单结束时间,账期，类型
        updateReconciliationBillDTO.setBeginDate(cn.hutool.core.date.DateUtil.offsetMonth(cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.offsetDay(settlementDate,1)),-1));
        updateReconciliationBillDTO.setEndDate(settlementDate);
        updateReconciliationBillDTO.setReconciliationTime(reconciliationTime);
        updateReconciliationBillDTO.setType(ReconciliationTypeEnum.OPEN.getType());

        //分组查询3月未生成帐单的第三方商家。
        //3. 查询第三方支付单，获取第三方支付情况  ProfitSharingEnum
        //查询上月的结算日之前的所有未结算的支付单，    结算日是25号，  那就查询25号前，clearing_status 为-1为未分账 的支付单。
        CashierBillQuery query = new CashierBillQuery();
        query.setPaymentStatus(Arrays.asList(PaymentTransactionEnum.PAY_SUCCESS.value(),PaymentTransactionEnum.PAY_REFUND.value()));
        List<Integer> paymentTypeList = new ArrayList<>();
        paymentTypeList.add(PaymentTransactionPaymentTypeEnum.OUT_PLATFORM_PAY.getCode());
        paymentTypeList.add(PaymentTransactionPaymentTypeEnum.MEI_TUAN_PAY.getCode());
        query.setPaymentType(paymentTypeList);
        if(StringUtil.isNotBlank(thirdBillProperties.getSpecialDevelopPlatformIds())) {
            List<Long> specialShopIds =  Arrays.stream(thirdBillProperties.getSpecialDevelopPlatformIds().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            query.setSpecialDevelopPlatfromIds(specialShopIds);
        }
        query.setEndTime(updateReconciliationBillDTO.getEndDate());
        query.setClearingStatus(ProfitSharingEnum.UN_CLEARING.value());
        List<PaymentTransactionBillDTO> paymentTransactionBillList = paymentTransactionClient.querySpecialShopPaymentTransactionList(query).getResult();


        //根据开放平台用户id分组
        Map<Long, List<PaymentTransactionBillDTO>> paymentTransactionBillMap = paymentTransactionBillList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getOpenPlatformDeveloperId())).collect(Collectors.groupingBy(PaymentTransactionBillDTO::getOpenPlatformDeveloperId));

        //查询当前账期生成的账单
        List<ReconciliationBill> reconciliationBillList = reconciliationBillDao.queryByReconciliationTime(reconciliationTime, ReconciliationTypeEnum.OPEN.getType());

        for (ReconciliationBill reconciliationBill : reconciliationBillList) {
            //已生成账单的第三方商家不再生成
            if (paymentTransactionBillMap.containsKey(reconciliationBill.getBuyerUserId())){
                paymentTransactionBillMap.remove(reconciliationBill.getBuyerUserId());
            }
        }


        XxlJobLogger.log("当前账单出账日期：{}结算的是：{}之前的支付单，结算单号为：{}",billingDate,settlementDate,reconciliationTime);

        // 开始账单生成逻辑

        //查询第三方跨月售后单
        Map<Long, List<ExternalRefundReturnVO>> externalRefundReturnMap = new HashMap<>();
        CashierBillQuery externalRefundQuery = new CashierBillQuery();
        externalRefundQuery.setStatus(OpenPlatformPaymentTypeEnum.HANDLE_SUCCESS.getStatus());
        externalRefundQuery.setStartTime(updateReconciliationBillDTO.getBeginDate());
        externalRefundQuery.setEndTime(updateReconciliationBillDTO.getEndDate());
        List<ExternalRefundReturnVO> externalRefundReturnList = externalRefundReturnClinet.queryExternalRefundReturn(externalRefundQuery,SecurityConstants.FROM_IN).getResult();
        if (CollUtil.isNotEmpty(externalRefundReturnList)) {
            externalRefundReturnMap = externalRefundReturnList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getOpenPlatformDeveloperId())).collect(Collectors.groupingBy(ExternalRefundReturnVO::getOpenPlatformDeveloperId));
        }

        //根据不同第三方商城分组生成账单
        List<String> openUserList = new ArrayList<>();
        for (Long openPlatformDeveloperId : paymentTransactionBillMap.keySet()) {
            //根据开发者ID，获取结算模式。
            int settlementType = openPlatformDeveloperConfigService.getDeveloperConfigSettlementType(openPlatformDeveloperId);

            //当前第三方商城下所有未分账的支付单
            List<PaymentTransactionBillDTO> paymentTransactionBillDTOList = paymentTransactionBillMap.get(openPlatformDeveloperId);

            if (CollUtil.isEmpty(paymentTransactionBillDTOList)){
                continue;
            }

            //手续费金额比例
            Integer commissionRate = 0;
            commissionRate = paymentTransactionBillDTOList.get(0).getCommissionRate();
            String source = paymentTransactionBillDTOList.get(0).getSource();
            //第三方商城登录名--唯一标识。
            String openPlatformDeveloperName = Optional.ofNullable(paymentTransactionBillDTOList.get(0).getOpenPlatformDeveloperName()).orElse(paymentTransactionBillDTOList.get(0).getSource());
            //计算账单总支付金额
            BigDecimal orderTotalAmount = paymentTransactionBillDTOList.stream().map(PaymentTransactionBillDTO::getOrderTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
            //计算账单总退款金额
            BigDecimal refundAmount = paymentTransactionBillDTOList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getRefundAmount())).map(PaymentTransactionBillDTO::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);

            // 生成账期
            updateReconciliationBillDTO.setSupplierCode(openPlatformDeveloperName).setShopId(-10L);
            ReconciliationBill reconciliationBill  = initBill(updateReconciliationBillDTO);
            reconciliationBill.setSettlementStatus(ReconciliationSettlementStatusEnum.UN_SETTLEMENT.getStatus());
            reconciliationBill.setBuyerUserId(openPlatformDeveloperId.toString());
            reconciliationBill.setShipmentAmount(orderTotalAmount);
            reconciliationBill.setCommissionRate(commissionRate);
            reconciliationBill.setSupplierName(source);
            reconciliationBill.setId(reconciliationBillDao.createId());
            reconciliationBill.setSettlemetType(settlementType);

            //跨月售后账单
            List<ExternalRefundReturnVO> externalRefundReturnVOList = externalRefundReturnMap.get(openPlatformDeveloperId);
            //跨月售后金额
            BigDecimal externalRefundAmount = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(externalRefundReturnVOList)){
                externalRefundAmount = externalRefundReturnVOList.stream().map(ExternalRefundReturnVO::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            //7. 统计账单明细
            Map<String, BigDecimal> amountMap = statisticalBill(paymentTransactionBillDTOList, externalRefundReturnVOList, reconciliationBill.getId(), source, commissionRate, reconciliationBill.getReconciliationNo(), reconciliationBill.getReconciliationTime(),settlementType);

            reconciliationBill.setPayAmount(amountMap.get("payAmount"));
            reconciliationBill.setIcardAmount(amountMap.get("icardAmount"));
            reconciliationBill.setGcardAmount(amountMap.get("gcardAmount"));
            reconciliationBill.setCouponAmount(amountMap.get("couponAmount"));
            reconciliationBill.setIntegralAmount(amountMap.get("integralAmount"));

            //计算账单结算金额
            refundAmount = NumberUtil.add(refundAmount,externalRefundAmount);
            reconciliationBill.setRefundAmount(refundAmount);

            BigDecimal billAmount = orderTotalAmount.subtract(refundAmount);
//			if(settlementType==10) {
//				reconciliationBill.setBillAmount(billAmount);
//			}
//			else if(settlementType==20) {
//				billAmount = billAmount.subtract(Optional.ofNullable(reconciliationBill.getPayAmount()).orElse(BigDecimal.ZERO));
//				reconciliationBill.setBillAmount(billAmount);
//			}

            billAmount = billAmount.subtract(Optional.ofNullable(reconciliationBill.getPayAmount()).orElse(BigDecimal.ZERO));
            reconciliationBill.setBillAmount(billAmount);

            //计算账单手续费金额
            BigDecimal commissionAmount = NumberUtil.mul(billAmount, commissionRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            //计算账单结算总金额
            BigDecimal reconciliationAmount = NumberUtil.sub(billAmount, commissionAmount);
            //计算账单结算手续费金额
            reconciliationBill.setCommissionAmount(commissionAmount);
            reconciliationBill.setReconciliationAmount(reconciliationAmount);

            //8. 插入账单
            reconciliationBillDao.save(reconciliationBill, reconciliationBill.getId());

            openUserList.add(openPlatformDeveloperName);
            log.info("统计账单完成！账期时间:{},供应商编号:{},账单类型:{} ", updateReconciliationBillDTO.getReconciliationTime(), openPlatformDeveloperId, updateReconciliationBillDTO.getType());

            //更新支付单分账状态
            List<String> paymentIds = paymentTransactionBillDTOList.stream().map(PaymentTransactionBillDTO::getPaymentId).collect(Collectors.toList());
            List<PaymentTransaction> paymentTransactionList = paymentTransactionClient.queryPaymentTransactionListByPaymentIds(paymentIds,SecurityConstants.FROM_IN).getResult();
            for (PaymentTransaction paymentTransaction : paymentTransactionList) {
                paymentTransaction.setClearingStatus(ProfitSharingEnum.IS_CLEARING.value());
                paymentTransaction.setReconciliationNo(reconciliationBill.getReconciliationNo());
                paymentTransaction.setUpdateTime(cn.hutool.core.date.DateUtil.date());
            }
            paymentTransactionClient.updatePaymentTransactionList(paymentTransactionList);

        }
        //账单统计
        this.summaryBilling(reconciliationTime);
    }


    private void summaryBilling(String reconciliationTimes){

        ReconciliationBillQuery reconciliationBillQuery = new ReconciliationBillQuery();
        reconciliationBillQuery.setReconciliationTime(reconciliationTimes);
        List<OpenPlatformBillDetailDTO> openPlatformBillDetailDTOList = reconciliationBillDao.detailInfoList(reconciliationBillQuery);

        if (CollUtil.isEmpty(openPlatformBillDetailDTOList)){
            log.info("-----------------------------当前没有账单需要统计");
            return ;
        }

        List<ReconciliationBillSummary> reconciliationBillSummaryList = new ArrayList<>();
        Map<String, List<OpenPlatformBillDetailDTO>> openPlatformBillDetailDTOMap = openPlatformBillDetailDTOList.stream().collect(Collectors.groupingBy(OpenPlatformBillDetailDTO::getReconciliationTime));
        for (String reconciliationTime : openPlatformBillDetailDTOMap.keySet()) {
            List<OpenPlatformBillDetailDTO> openPlatformBillDetailList = openPlatformBillDetailDTOMap.get(reconciliationTime);

            BigDecimal reconciliationAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getReconciliationAmount())).map(OpenPlatformBillDetailDTO::getReconciliationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal billAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getBillAmount())).map(OpenPlatformBillDetailDTO::getBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal orderAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getOrderAmount())).map(OpenPlatformBillDetailDTO::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal couponAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getCouponAmount())).map(OpenPlatformBillDetailDTO::getCouponAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal shipmentAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getShipmentAmount())).map(OpenPlatformBillDetailDTO::getShipmentAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal payAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getPayAmount())).map(OpenPlatformBillDetailDTO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal gcardAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getGcardAmount())).map(OpenPlatformBillDetailDTO::getGcardAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal icardAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getIcardAmount())).map(OpenPlatformBillDetailDTO::getIcardAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal integralAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getIntegralAmount())).map(OpenPlatformBillDetailDTO::getIntegralAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal refundAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getRefundAmount())).map(OpenPlatformBillDetailDTO::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundTotalAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getRefundTotalAmount())).map(OpenPlatformBillDetailDTO::getRefundTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundCouponAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getRefundCouponAmount())).map(OpenPlatformBillDetailDTO::getRefundCouponAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundPayAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getRefundPayAmount())).map(OpenPlatformBillDetailDTO::getRefundPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundGcardAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getRefundGcardAmount())).map(OpenPlatformBillDetailDTO::getRefundGcardAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundIcardAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getRefundIcardAmount())).map(OpenPlatformBillDetailDTO::getRefundIcardAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundIntegralAmount = openPlatformBillDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getRefundIntegralAmount())).map(OpenPlatformBillDetailDTO::getRefundIntegralAmount).reduce(BigDecimal.ZERO, BigDecimal::add);


            ReconciliationBillSummary reconciliationBillSummary = new ReconciliationBillSummary();
            reconciliationBillSummary.setReconciliationTime(reconciliationTime);
            reconciliationBillSummary.setReconciliationAmount(reconciliationAmount);
            reconciliationBillSummary.setBillAmount(billAmount);
            reconciliationBillSummary.setOrderAmount(orderAmount);
            reconciliationBillSummary.setCouponAmount(couponAmount);
            reconciliationBillSummary.setShipmentAmount(shipmentAmount);
            reconciliationBillSummary.setPayAmount(payAmount);
            reconciliationBillSummary.setGcardAmount(gcardAmount);
            reconciliationBillSummary.setIcardAmount(icardAmount);
            reconciliationBillSummary.setIntegralAmount(integralAmount);
            reconciliationBillSummary.setRefundAmount(refundAmount);
            reconciliationBillSummary.setRefundTotalAmount(refundTotalAmount);
            reconciliationBillSummary.setRefundCouponAmount(refundCouponAmount);
            reconciliationBillSummary.setRefundPayAmount(refundPayAmount);
            reconciliationBillSummary.setRefundGcardAmount(refundGcardAmount);
            reconciliationBillSummary.setRefundIcardAmount(refundIcardAmount);
            reconciliationBillSummary.setRefundIntegralAmount(refundIntegralAmount);
            reconciliationBillSummary.setBillDate(DateUtil.date());
            reconciliationBillSummary.setCreateTime(DateUtil.date());

            reconciliationBillSummaryList.add(reconciliationBillSummary);
        }

        reconciliationBillSummaryService.saveOrUpdate(reconciliationBillSummaryList);
        log.info("-----------------------------账单统计完成");
        return;
    }

    public ReconciliationBill initBill(UpdateReconciliationBillDTO updateReconciliationBillDTO) {
        ReconciliationBill reconciliationBill = new ReconciliationBill();
        String randomSn = CommonServiceUtil.getRandomSn();
        //新的账单 账单未确认
        reconciliationBill.setConfirmStatus(ReconciliationConfirmStatusEnum.UN_CONFIRM.getStatus());
        reconciliationBill.setSettlementStatus(ReconciliationSettlementStatusEnum.UN_SETTLEMENT.getStatus());
        //结算单号
        reconciliationBill.setReconciliationNo(randomSn);
        reconciliationBill.setSupplierCode(updateReconciliationBillDTO.getSupplierCode());
        reconciliationBill.setReconciliationTime(updateReconciliationBillDTO.getReconciliationTime());
        //不同供应商的shopId
        reconciliationBill.setShopId(updateReconciliationBillDTO.getShopId());
        //账单类型
        reconciliationBill.setType(updateReconciliationBillDTO.getType());
        // 创建时间
        reconciliationBill.setCreateTime(new Date());
        // 账期开始时间
        reconciliationBill.setBeginTime(updateReconciliationBillDTO.getBeginDate());
        // 账期结束时间
        reconciliationBill.setEndTime(updateReconciliationBillDTO.getEndDate());
        return reconciliationBill;
    }

    private Map<String, BigDecimal> statisticalBill(@NotNull List<PaymentTransactionBillDTO> openPlatformPaymentTransactionList, List<ExternalRefundReturnVO> externalRefundReturnVOList, Long reconciliationId, String nickName, Integer commissionRate, String reconciliationNo, String reconciliationTime, int settlementType){

        //在线支付结算金额
        BigDecimal payAmount = BigDecimal.ZERO;
        //福利卡结算金额
        BigDecimal gcardAmount = BigDecimal.ZERO;
        //员工卡结算金额
        BigDecimal icardAmount = BigDecimal.ZERO;
        //优惠券结算金额
        BigDecimal couponAmount = BigDecimal.ZERO;
        //优惠券结算金额
        BigDecimal integralAmount = BigDecimal.ZERO;

        //转换paymentId
        List<String> paymentIdList = openPlatformPaymentTransactionList.stream().map(PaymentTransactionBillDTO::getPaymentId).collect(Collectors.toList());

        String source = openPlatformPaymentTransactionList.get(0).getSource();
        String openPlatformApiKey = openPlatformPaymentTransactionList.get(0).getOpenPlatformApiKey();
        Long openPlatformDeveloperId = openPlatformPaymentTransactionList.get(0).getOpenPlatformDeveloperId();

        List<ReconciliationBillItem> reconciliationBillItemList = new ArrayList<>();
        List<ThirdPayTransactionBookkeeping> thirdPayTransactionBookkeepingList = new ArrayList<>();

        //支付单明细正向流程  统计同一支付单下所有退款金额
        for (PaymentTransactionBillDTO paymentTransactionBillDTO : openPlatformPaymentTransactionList) {

            //组装账单详情
            ReconciliationBillItem reconciliationBillItem = new ReconciliationBillItem();

            reconciliationBillItem.setBuyerUserName(nickName);
            reconciliationBillItem.setReconciliationId(reconciliationId);
            reconciliationBillItem.setOrderNo(paymentTransactionBillDTO.getPaySn());
            reconciliationBillItem.setRefundAmount(paymentTransactionBillDTO.getRefundAmount());
            reconciliationBillItem.setThirdOrderNo(paymentTransactionBillDTO.getThirdTradeNo());
            reconciliationBillItem.setRefundAmount(paymentTransactionBillDTO.getRefundAmount());
            reconciliationBillItem.setOrderAmount(paymentTransactionBillDTO.getOrderTotalAmount());
            reconciliationBillItem.setBuyerUserId(paymentTransactionBillDTO.getUserId());

//			if(settlementType==10){
//				reconciliationBillItem.setBillAmount(paymentTransactionBillDTO.getOrderTotalAmount().subtract(Optional.ofNullable(paymentTransactionBillDTO.getRefundAmount()).orElse(BigDecimal.ZERO)).setScale(2, RoundingMode.HALF_UP));
//			}
//			else if(settlementType==20){
//				// 订单金额   - 在线支付金额 -  【退款金额（非在线支付） = 总退款金额 - 在线退款金额】
//				reconciliationBillItem.setBillAmount(paymentTransactionBillDTO.getOrderTotalAmount()
//					.subtract(Optional.ofNullable(paymentTransactionBillDTO.getRefundAmount()).orElse(BigDecimal.ZERO))
//					.add(Optional.ofNullable(paymentTransactionBillDTO.getRefundPayAmount()).orElse(BigDecimal.ZERO))
//					.subtract(Optional.ofNullable(paymentTransactionBillDTO.getPayAmount()).orElse(BigDecimal.ZERO))
//					.setScale(2, RoundingMode.HALF_UP));
//			}

            // 订单金额   - 在线支付金额 -  【退款金额（非在线支付） = 总退款金额 - 在线退款金额】
            reconciliationBillItem.setBillAmount(paymentTransactionBillDTO.getOrderTotalAmount()
                    .subtract(Optional.ofNullable(paymentTransactionBillDTO.getRefundAmount()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(paymentTransactionBillDTO.getRefundPayAmount()).orElse(BigDecimal.ZERO))
                    .subtract(Optional.ofNullable(paymentTransactionBillDTO.getPayAmount()).orElse(BigDecimal.ZERO))
                    .setScale(2, RoundingMode.HALF_UP));

            reconciliationBillItem.setPayAmount(paymentTransactionBillDTO.getPayAmount());
            reconciliationBillItem.setGcardAmount(paymentTransactionBillDTO.getGcardAmount());
            reconciliationBillItem.setIcardAmount(paymentTransactionBillDTO.getIcardAmount());
            reconciliationBillItem.setCouponAmount(paymentTransactionBillDTO.getCouponAmount());
            reconciliationBillItem.setPreDepositAmount(paymentTransactionBillDTO.getPreDepositAmount());
            reconciliationBillItem.setUserAccountDepositAmount(paymentTransactionBillDTO.getUserAccountDepositAmount());
            reconciliationBillItem.setIntegralAmount(paymentTransactionBillDTO.getIntegralAmount());

            reconciliationBillItem.setRefundPayAmount(paymentTransactionBillDTO.getRefundPayAmount());
            reconciliationBillItem.setRefundGcardAmount(paymentTransactionBillDTO.getRefundGcardAmount());
            reconciliationBillItem.setRefundIcardAmount(paymentTransactionBillDTO.getRefundIcardAmount());
            reconciliationBillItem.setRefundCouponAmount(paymentTransactionBillDTO.getRefundCouponAmount());
            reconciliationBillItem.setRefundPreDepositAmount(paymentTransactionBillDTO.getRefundPreDepositAmount());
            reconciliationBillItem.setRefundUserAccountDepositAmount(paymentTransactionBillDTO.getRefundUserAccountDepositAmount());
            reconciliationBillItem.setRefundIntegralAmount(paymentTransactionBillDTO.getRefundIntegralAmount());

            if (ObjectUtil.isNotEmpty(reconciliationBillItem.getRefundAmount())){
                reconciliationBillItem.setRefundTotalAmount(NumberUtil.sub(reconciliationBillItem.getRefundAmount(),reconciliationBillItem.getRefundCouponAmount()));
            }

            //实付总额
            reconciliationBillItem.setShipmentAmount(NumberUtil.sub(reconciliationBillItem.getOrderAmount(),reconciliationBillItem.getCouponAmount()));

            //计算结算金额
            payAmount = NumberUtil.add(payAmount,NumberUtil.sub(reconciliationBillItem.getPayAmount(), reconciliationBillItem.getRefundPayAmount()));
            gcardAmount = NumberUtil.add(gcardAmount,NumberUtil.sub(reconciliationBillItem.getGcardAmount(), reconciliationBillItem.getRefundGcardAmount()));
            icardAmount = NumberUtil.add(icardAmount,NumberUtil.sub(reconciliationBillItem.getIcardAmount(), reconciliationBillItem.getRefundIcardAmount()));
            couponAmount = NumberUtil.add(couponAmount,NumberUtil.sub(reconciliationBillItem.getCouponAmount(), reconciliationBillItem.getRefundCouponAmount()));
            integralAmount = NumberUtil.add(integralAmount,NumberUtil.sub(reconciliationBillItem.getIntegralAmount(), reconciliationBillItem.getRefundIntegralAmount()));

            reconciliationBillItemList.add(reconciliationBillItem);
        }

        //支付单明细逆向流程   退款明细全部保存
        if (CollUtil.isNotEmpty(externalRefundReturnVOList)){
            for (ExternalRefundReturnVO externalRefundReturnVO : externalRefundReturnVOList) {

                //组装账单详情
                ReconciliationBillItem reconciliationBillItem = new ReconciliationBillItem();

                reconciliationBillItem.setBuyerUserName(nickName);
                reconciliationBillItem.setReconciliationId(reconciliationId);
                reconciliationBillItem.setOrderNo(externalRefundReturnVO.getPaySn());
                reconciliationBillItem.setRefundNo(externalRefundReturnVO.getRefundSn());
                reconciliationBillItem.setRefundAmount(externalRefundReturnVO.getRefundAmount());
                reconciliationBillItem.setThirdOrderNo(externalRefundReturnVO.getThirdTradeNo());
                reconciliationBillItem.setRefundAmount(externalRefundReturnVO.getRefundAmount());
                reconciliationBillItem.setThirdRefundNo(externalRefundReturnVO.getThirdRefundSn());
                reconciliationBillItem.setOrderAmount(externalRefundReturnVO.getOrderTotalAmount());
                reconciliationBillItem.setBuyerUserId(externalRefundReturnVO.getUserId());
//				if(settlementType==10){
//					reconciliationBillItem.setBillAmount(NumberUtil.mul(externalRefundReturnVO.getRefundAmount(),100-commissionRate).divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP));
//				}
//				else if(settlementType==20){
//					//非在线退款
//					BigDecimal refundAmount = externalRefundReturnVO.getRefundAmount()
//						.subtract(Optional.ofNullable(externalRefundReturnVO.getRefundPayAmount()).orElse(BigDecimal.ZERO));
//
//					reconciliationBillItem.setBillAmount(NumberUtil.mul(refundAmount,100-commissionRate)
//						.divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP));
//				}

                //非在线退款
                BigDecimal refundAmount = externalRefundReturnVO.getRefundAmount()
                        .subtract(Optional.ofNullable(externalRefundReturnVO.getRefundPayAmount()).orElse(BigDecimal.ZERO));

                reconciliationBillItem.setBillAmount(NumberUtil.mul(refundAmount,100-commissionRate)
                        .divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP));


                reconciliationBillItem.setPayAmount(externalRefundReturnVO.getPayAmount());
                reconciliationBillItem.setGcardAmount(externalRefundReturnVO.getGcardAmount());
                reconciliationBillItem.setIcardAmount(externalRefundReturnVO.getIcardAmount());
                reconciliationBillItem.setCouponAmount(externalRefundReturnVO.getCouponAmount());
                reconciliationBillItem.setPreDepositAmount(externalRefundReturnVO.getPreDepositAmount());
                reconciliationBillItem.setUserAccountDepositAmount(externalRefundReturnVO.getUserAccountDepositAmount());
                reconciliationBillItem.setIntegralAmount(externalRefundReturnVO.getIntegralAmount());

                reconciliationBillItem.setRefundPayAmount(externalRefundReturnVO.getRefundPayAmount());
                reconciliationBillItem.setRefundGcardAmount(externalRefundReturnVO.getRefundGcardAmount());
                reconciliationBillItem.setRefundIcardAmount(externalRefundReturnVO.getRefundIcardAmount());
                reconciliationBillItem.setRefundCouponAmount(externalRefundReturnVO.getRefundCouponAmount());
                reconciliationBillItem.setRefundPreDepositAmount(externalRefundReturnVO.getRefundPreDepositAmount());
                reconciliationBillItem.setRefundUserAccountDepositAmount(externalRefundReturnVO.getRefundUserAccountDepositAmount());
                reconciliationBillItem.setRefundIntegralAmount(externalRefundReturnVO.getRefundIntegralAmount());

                if (ObjectUtil.isNotEmpty(reconciliationBillItem.getRefundAmount())){
                    reconciliationBillItem.setRefundTotalAmount(NumberUtil.sub(reconciliationBillItem.getRefundAmount(),reconciliationBillItem.getRefundCouponAmount()));
                }

                //实付总额
                reconciliationBillItem.setShipmentAmount(NumberUtil.sub(reconciliationBillItem.getOrderAmount(),reconciliationBillItem.getCouponAmount()));

                reconciliationBillItemList.add(reconciliationBillItem);

                paymentIdList.add(externalRefundReturnVO.getPaymentId());

                //计算结算金额
                //跨月售后结算  = 正向流程结算金额  - 跨月数据的退款金额
                payAmount = NumberUtil.sub(payAmount,reconciliationBillItem.getRefundPayAmount());
                gcardAmount = NumberUtil.sub(gcardAmount, reconciliationBillItem.getRefundGcardAmount());
                icardAmount = NumberUtil.sub(icardAmount, reconciliationBillItem.getRefundIcardAmount());
                couponAmount = NumberUtil.sub(couponAmount,reconciliationBillItem.getRefundCouponAmount());
                integralAmount = NumberUtil.sub(integralAmount,reconciliationBillItem.getRefundIntegralAmount());
            }
        }

        //福利卡使用总量   790424003327168512
        List<CardTotalUsageDTO> cardTotalUsageDTOList = gCardUserClient.gcardTotalUsage(paymentIdList, 3, SecurityConstants.FROM_IN).getResult();
        if (CollUtil.isNotEmpty(cardTotalUsageDTOList)){

            for (CardTotalUsageDTO cardTotalUsageDTO : cardTotalUsageDTOList) {
                ThirdPayTransactionBookkeeping thirdPayTransactionBookkeeping = new ThirdPayTransactionBookkeeping();
                thirdPayTransactionBookkeeping.setAmount(cardTotalUsageDTO.getAmount());
                thirdPayTransactionBookkeeping.setRefundAmount(cardTotalUsageDTO.getRefundAmount());
                thirdPayTransactionBookkeeping.setSettlementAmount(NumberUtil.sub(Optional.ofNullable(thirdPayTransactionBookkeeping.getAmount()).orElse(BigDecimal.ZERO),Optional.ofNullable(thirdPayTransactionBookkeeping.getRefundAmount()).orElse(BigDecimal.ZERO)));
                thirdPayTransactionBookkeeping.setCardIssuerId(cardTotalUsageDTO.getId());
                thirdPayTransactionBookkeeping.setDepartment(cardTotalUsageDTO.getCardIssuerName());
                thirdPayTransactionBookkeeping.setOpenPlatformApiKey(openPlatformApiKey);
                thirdPayTransactionBookkeeping.setSource(source);
                thirdPayTransactionBookkeeping.setOpenPlatformDeveloperId(openPlatformDeveloperId);
                thirdPayTransactionBookkeeping.setReconciliationTime(reconciliationTime);
                thirdPayTransactionBookkeeping.setReconciliationNo(reconciliationNo);
                thirdPayTransactionBookkeeping.setCreateTime(DateUtil.date());
                thirdPayTransactionBookkeeping.setType(ThirdPayTransactionBookkeepingEnum.GCARD.getName());
                thirdPayTransactionBookkeeping.setCreateTime(DateUtil.date());

                thirdPayTransactionBookkeepingList.add(thirdPayTransactionBookkeeping);
            }
        }


        //员工卡使用总量
        CardTotalUsageDTO icardTotalUsageDTOList = iCardUserClient.gcardTotalUsage(paymentIdList, 3, SecurityConstants.FROM_IN).getResult();
        if (ObjectUtil.isNotEmpty(icardTotalUsageDTOList) ){

            ThirdPayTransactionBookkeeping thirdPayTransactionBookkeeping = new ThirdPayTransactionBookkeeping();
            thirdPayTransactionBookkeeping.setAmount(icardTotalUsageDTOList.getAmount());
            thirdPayTransactionBookkeeping.setRefundAmount(icardTotalUsageDTOList.getRefundAmount());
            thirdPayTransactionBookkeeping.setSettlementAmount(NumberUtil.sub(Optional.ofNullable(thirdPayTransactionBookkeeping.getAmount()).orElse(BigDecimal.ZERO),Optional.ofNullable(thirdPayTransactionBookkeeping.getRefundAmount()).orElse(BigDecimal.ZERO)));
            thirdPayTransactionBookkeeping.setOpenPlatformApiKey(openPlatformApiKey);
            thirdPayTransactionBookkeeping.setSource(source);
            thirdPayTransactionBookkeeping.setOpenPlatformDeveloperId(openPlatformDeveloperId);
            thirdPayTransactionBookkeeping.setReconciliationTime(reconciliationTime);
            thirdPayTransactionBookkeeping.setReconciliationNo(reconciliationNo);
            thirdPayTransactionBookkeeping.setType(ThirdPayTransactionBookkeepingEnum.ICARD.getName());
            thirdPayTransactionBookkeeping.setCreateTime(DateUtil.date());
            thirdPayTransactionBookkeepingList.add(thirdPayTransactionBookkeeping);
        }

        //保存外部交易统计
        log.info("福利卡、员工卡相关支付单paymentId：{}",paymentIdList.toString());
        if(CollUtil.isNotEmpty(thirdPayTransactionBookkeepingList)){
            log.info("----------------------------------保存第三方交易记录-----------------------------------------");
            paymentTransactionClient.saveThirdPayTransactionBookkeepingList(thirdPayTransactionBookkeepingList);
        }
        //保存账单项
        reconciliationBillItemDao.save(reconciliationBillItemList);

        log.info("=======================在线支付结算金额:{},福利卡结算金额:{},员工卡结算金额:{},优惠券结算金额:{},积分结算金额：{}",payAmount,gcardAmount,icardAmount,couponAmount,integralAmount);
        Map<String, BigDecimal> amountMap = new HashMap<>();
        amountMap.put("payAmount",payAmount);
        amountMap.put("gcardAmount",gcardAmount);
        amountMap.put("icardAmount",icardAmount);
        amountMap.put("couponAmount",couponAmount);
        amountMap.put("integralAmount",integralAmount);

        return amountMap;
    }
}
