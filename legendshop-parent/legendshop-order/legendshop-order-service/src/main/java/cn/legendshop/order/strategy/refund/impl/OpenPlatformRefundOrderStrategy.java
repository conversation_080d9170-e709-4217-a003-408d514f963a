package cn.legendshop.order.strategy.refund.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.dao.IndustrialOuterShopSettleConfigDao;
import cn.legendshop.common.model.IndustrialOuterShopSettleConfig;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyConstant;
import cn.legendshop.common.service.LocationService;
import cn.legendshop.common.service.SystemParameterService;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.open.constants.AfterSaleApi;
import cn.legendshop.open.enums.OpenShopConfigAfterSaleRenewTypeEnum;
import cn.legendshop.open.enums.ResultCodeEnum;
import cn.legendshop.open.factory.OpenPlatformServiceFactory;
import cn.legendshop.open.request.afterSale.AfterSaleCancelRequest;
import cn.legendshop.open.request.afterSale.AfterSaleExpressInfoRequest;
import cn.legendshop.open.request.afterSale.AfterSaleSubmitApplyRequest;
import cn.legendshop.open.response.CommonResponse;
import cn.legendshop.open.sevice.OpenPlatformAfterSaleService;
import cn.legendshop.order.dao.*;
import cn.legendshop.order.dto.afterSales.UserUpExpressDTO;
import cn.legendshop.order.enums.OrderBarterRefundStatusEnum;
import cn.legendshop.order.model.Shipment;
import cn.legendshop.order.model.UserAddressSub;
import cn.legendshop.order.query.erp.ErpReturnBarterParameterDTO;
import cn.legendshop.order.query.purchaser.SubmitAfsApplyParam;
import cn.legendshop.order.service.UserAddressService;
import cn.legendshop.order.strategy.refund.ThirdIndustrialRefundStrategy;
import cn.legendshop.smsmail.api.client.AliCloudSMSServiceClient;
import cn.legendshop.smsmail.api.constant.AliDayuFieldEnum;
import cn.legendshop.smsmail.api.constant.SmsSystemParamterConstant;
import cn.legendshop.user.api.dto.UserAddressDTO;
import com.alibaba.fastjson.JSONObject;
import com.legendshop.model.constant.OpenPlatformAfterSalePickwareTypeEnum;
import com.legendshop.model.constant.RefundReturnTypeEnum;
import com.legendshop.model.constant.ReturnPickTypeEnum;
import com.legendshop.model.constant.SMSTypeEnum;
import com.legendshop.model.entity.Sub;
import com.legendshop.model.entity.SubItem;
import com.legendshop.model.entity.orderreturn.SubRefundReturn;
import com.legendshop.model.entity.orderreturn.SubRefundReturnItem;
import com.legendshop.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.legendshop.open.constants.OpenPlatformGlobalConstants.OPEN_AFTERSALE_SUBMIT_LOCK_KEY;

/**
 * @author: jzh
 * @create: 2023-08-09 09:24
 */
@Service("openPlatformRefundOrderStrategy")
@Slf4j
public class OpenPlatformRefundOrderStrategy  extends BaseRefundOrderStrategy implements ThirdIndustrialRefundStrategy {

	@Autowired
	private SubDao subDao;

	@Autowired
	private SubItemDao subItemDao;

	@Autowired
	private ShipmentDao shipmentDao;


	@Autowired
	private SubRefundReturnDao subRefundReturnDao;

	@Autowired
	private UserAddressSubDao userAddressSubDao;

	@Autowired
	private IndustrialOuterShopSettleConfigDao industrialOuterShopSettleConfigDao;


	@Autowired
	private OpenPlatformServiceFactory openPlatformServiceFactory;

	@Autowired
	private RedissonClient redissonClient;

    @Autowired
	private UserAddressService userAddressService;




	/**
	 * 第三方平台取消订单【整单取消，】，
	 * 比如：
	 * 1、欧贝组单失败，
	 * 2、京东打电话取消订单
	 * 【未发货前取消，发货后不能取消】
	 * @param orderNumber
	 */
	@Override
	public R<Boolean> cancelOrderByThrid(String orderNumber,Long accountBookId) {
		return R.ok();
	}

	/**
	 * 第三方平台取消订单项【非整单取消，按订单项取消】
	 * 比如：
	 * 1、欧贝拒单
	 * 【未发货前取消，发货后不能取消】
	 * @param orderItemNumbers
	 */
	@Override
	public R<Boolean> cancelOrderItemByThrid(List<String> orderItemNumbers) {
		return R.ok();
	}


	private Boolean afterSaleOnline(Long shopId) {
		IndustrialOuterShopSettleConfig industrialOuterShopSettleConfig = industrialOuterShopSettleConfigDao.getByShopId(shopId);
		return OpenShopConfigAfterSaleRenewTypeEnum.ONLINE.getValue().equals(industrialOuterShopSettleConfig.getAfterSaleRenewType());
	}

	/**
	 * 通知第三方申请售后
	 * @param shopId
	 * @return
	 */
	private R<String> thirdAfterSaleApply(AfterSaleSubmitApplyRequest afterSaleSubmitApplyRequest, Long shopId) {
		IndustrialOuterShopSettleConfig industrialOuterShopSettleConfig = industrialOuterShopSettleConfigDao.getByShopId(shopId);

		if(OpenShopConfigAfterSaleRenewTypeEnum.ONLINE.getValue().equals(industrialOuterShopSettleConfig.getAfterSaleRenewType())) {
			OpenPlatformAfterSaleService openPlatformAfterSaleService = openPlatformServiceFactory.getAfterSaleServiceByShopId(shopId);
			CommonResponse<String> afsApply = openPlatformAfterSaleService.submitAfterSaleApply(afterSaleSubmitApplyRequest);
			ResultCodeEnum errorCode = ResultCodeEnum.findByCode(afsApply.getResultCode());
			if (!afsApply.getSuccess() || !ResultCodeEnum.NORMAL.getCode().equals(errorCode.getCode())) {
				throw new BusinessException("开放平台申请售后失败，" + afsApply.getResultMsg());
			}
			return R.ok(afsApply.getResult());
		}else {
			// 售后通知商家
			super.smsNotification(industrialOuterShopSettleConfig.getRefundPhoneNumber(), afterSaleSubmitApplyRequest.getAfterSaleNo());
			return R.ok();
		}
	}


	/**
	 * 售后换货（ERP）
	 * @param parameterDTO
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<String> barter(String parameterDTO) {
		if(StringUtils.isBlank(parameterDTO)) {
			throw new BusinessException("请求数据为空");
		}
		ErpReturnBarterParameterDTO parameter = JSONUtil.toBean(parameterDTO, ErpReturnBarterParameterDTO.class);
		String redisKey = OPEN_AFTERSALE_SUBMIT_LOCK_KEY + parameter.getRefundNumber();
		RLock lock = redissonClient.getLock(redisKey);
		try {
			lock.lock();
			R<String> barter = super.barter(parameterDTO);
			String afterSaleNo = JSONObject.parseObject(barter.getResult()).getString("mroRefundNumber");
			AfterSaleSubmitApplyRequest afterSaleSubmitApplyRequest = new AfterSaleSubmitApplyRequest();
			AfterSaleSubmitApplyRequest.AfsCustomerInfo afsCustomerInfo = new AfterSaleSubmitApplyRequest.AfsCustomerInfo();
			AfterSaleSubmitApplyRequest.AfsPickupWareInfo afsPickupWareInfo = new AfterSaleSubmitApplyRequest.AfsPickupWareInfo();
			List<AfterSaleSubmitApplyRequest.AfsItemInfo> afsItemInfoList = new ArrayList<>();
			Sub sub = subDao.getSubBySubNumber(parameter.getOrderNumber());
			List<SubItem> subItemList = subItemDao.getSubItem(parameter.getOrderNumber());
			Shipment shipment = shipmentDao.getByBatchNo(parameter.getBatchNumber());

			//组装基础信息
			afterSaleSubmitApplyRequest.setAfterSaleNo(afterSaleNo);
			afterSaleSubmitApplyRequest.setThirdShipNo(shipment.getThirdShipNo());
			afterSaleSubmitApplyRequest.setThirdOrderNo(subItemList.get(0).getThirdNumber());
			afterSaleSubmitApplyRequest.setCustomerExpect(parameter.getCustomerExpect()); //退货(10)、换货(20)
			afterSaleSubmitApplyRequest.setQuestionDesc(parameter.getQuestionDesc());
			afterSaleSubmitApplyRequest.setQuestionPic(parameter.getQuestionPic());

			//组装地址信息--erp默认上门取件
	//		String[] addressList = shipment.getReceiverAddress().split(" ");
	//		afsPickupWareInfo.setPickwareProvinceId(Integer.valueOf(locationService.getProvinceByName(addressList[0]).get(0).getProvinceid()));
	//		City city = locationService.getCityByName(addressList[1]).get(0);
	//		afsPickupWareInfo.setPickwareCityId(Integer.valueOf(city.getCityid()));
	//		afsPickupWareInfo.setPickwareCountyId(Integer.valueOf(locationService.queryAreByName(addressList[2], Long.parseLong(city.getCityid())).get(0).getAreaid()));
	//		if (addressList.length >= 4) {
	//			afsPickupWareInfo.setPickwareAddress(addressList[3]);
	//		}
			afsPickupWareInfo.setPickwareType(OpenPlatformAfterSalePickwareTypeEnum.DOOR_TAKE.value());
			UserAddressSub userAddressSub = userAddressSubDao.getUserAddressSub(sub.getAddrOrderId());
			afsPickupWareInfo.setPickwareProvinceId(userAddressSub.getProvinceId());
			afsPickupWareInfo.setPickwareCityId(userAddressSub.getCityId());
			afsPickupWareInfo.setPickwareCountyId(userAddressSub.getAreaId());
			afsPickupWareInfo.setPickwareAddress(userAddressSub.getSubAdds());

			//组装联系人信息
			afsCustomerInfo.setCustomerContactName(shipment.getConsignee());
			afsCustomerInfo.setCustomerTel(shipment.getContactNumber());
			afsCustomerInfo.setCustomerMobilePhone(shipment.getContactNumber());

			//组装商品信息
			Map<Long,SubItem> subItemMap = subItemList.stream().collect(Collectors.toMap(SubItem::getSkuId, Function.identity()));
			for(ErpReturnBarterParameterDTO.ProdData prodData: parameter.getProdData()) {
				AfterSaleSubmitApplyRequest.AfsItemInfo afsItemInfo = new AfterSaleSubmitApplyRequest.AfsItemInfo();
				SubItem subItem = subItemMap.get(Long.valueOf(prodData.getSkuId()));
				if(ObjectUtil.isEmpty(subItem)) {
					throw new BusinessException("售后报错--无法找到订单项");
				}
				afsItemInfo.setSkuId(subItem.getThirdItemNumber());
				afsItemInfo.setProdName(subItem.getProdName());
				afsItemInfo.setSkuNum(prodData.getSkuNum());
				afsItemInfoList.add(afsItemInfo);
			}
			afterSaleSubmitApplyRequest.setPickwareInfo(afsPickupWareInfo);
			afterSaleSubmitApplyRequest.setCustomerInfo(afsCustomerInfo);
			afterSaleSubmitApplyRequest.setAfsItemList(afsItemInfoList);

			String outRefundNo = "";
			R re = thirdAfterSaleApply(afterSaleSubmitApplyRequest,sub.getShopId());
			if(re.isSuccess()) {
				outRefundNo = (String) re.getResult();
			}else {
				return re;
			}
			SubRefundReturn subRefundReturn = subRefundReturnDao.getByRefundSn(afterSaleNo);
			subRefundReturn.setOutRefundNo(outRefundNo);
			subRefundReturnDao.updateProperties(subRefundReturn);
			return barter;
		} catch (Exception e) {
			log.error("申请售后失败!{}", e);
			throw new BusinessException("申请售后失败!原因：" + e.getMessage());
		} finally {
			lock.unlock();
		}
	}

	@Override
	public R<String> purchaserAfterSales(SubmitAfsApplyParam afsApplyParam) {

		String redisKey = OPEN_AFTERSALE_SUBMIT_LOCK_KEY + afsApplyParam.getPurchaserAfterSaleNo();
		RLock lock = redissonClient.getLock(redisKey);
		try {
			lock.lock();
			R<String> mroRefundNumberR = super.purchaserAfterSales(afsApplyParam);
			if (!mroRefundNumberR.isSuccess() || AppUtils.isBlank(mroRefundNumberR.getResult())) {
				return R.fail("商城内部生成售后单失败！");
			}
			String afterSaleNo = mroRefundNumberR.getResult();

			List<AfterSaleSubmitApplyRequest.AfsItemInfo> afsItemInfoList = new ArrayList<>();
			Sub sub = subDao.getSubBySubNumber(afsApplyParam.getOrderNo());
			List<SubItem> subItemList = subItemDao.getSubItem(afsApplyParam.getOrderNo());
			Shipment shipment = shipmentDao.getByBatchNo(afsApplyParam.getShipmentNo());

			AfterSaleSubmitApplyRequest afterSaleSubmitApplyRequest = new AfterSaleSubmitApplyRequest();
			//组装基础信息
			afterSaleSubmitApplyRequest.setAfterSaleNo(afterSaleNo);
			afterSaleSubmitApplyRequest.setThirdShipNo(shipment.getThirdShipNo());
			afterSaleSubmitApplyRequest.setThirdOrderNo(subItemList.get(0).getThirdNumber());
			afterSaleSubmitApplyRequest.setCustomerExpect(afsApplyParam.getCustomerExpect());
			afterSaleSubmitApplyRequest.setQuestionDesc(afsApplyParam.getQuestionDesc());
			afterSaleSubmitApplyRequest.setQuestionPic(afsApplyParam.getQuestionPic());

			//组装取件信息
			AfterSaleSubmitApplyRequest.AfsPickupWareInfo afsPickupWareInfo = new AfterSaleSubmitApplyRequest.AfsPickupWareInfo();
			BeanUtil.copyProperties(afsApplyParam.getPickWareInfo(), afsPickupWareInfo);
            // 上门取件时需要补充信息
            if (OpenPlatformAfterSalePickwareTypeEnum.DOOR_TAKE.value().equals(afsApplyParam.getPickWareInfo().getPickWareType())) {
                UserAddressDTO userAddressDTO = userAddressService.convertAddress(afsApplyParam.getPickWareInfo().getPickWareAddress());
                afsPickupWareInfo.setPickwareProvinceId(userAddressDTO.getProvinceId());
                afsPickupWareInfo.setPickwareCityId(userAddressDTO.getCityId());
                afsPickupWareInfo.setPickwareCountyId(userAddressDTO.getAreaId());
            }

			//组装联系人信息
			AfterSaleSubmitApplyRequest.AfsCustomerInfo afsCustomerInfo = new AfterSaleSubmitApplyRequest.AfsCustomerInfo();
			if (AppUtils.isNotBlank(afsApplyParam.getCustomerInfo())) {
				BeanUtil.copyProperties(afsApplyParam.getCustomerInfo(), afsCustomerInfo);
			} else {
				afsCustomerInfo.setCustomerContactName(shipment.getConsignee());
				afsCustomerInfo.setCustomerTel(shipment.getContactNumber());
				afsCustomerInfo.setCustomerMobilePhone(shipment.getContactNumber());
			}

			//组装商品信息
			Map<String,SubItem> subItemMap = subItemList.stream().collect(Collectors.toMap(SubItem::getThirdSkuId, Function.identity()));
			for(SubmitAfsApplyParam.AfsItemInfo prodData: afsApplyParam.getAfsItemList()) {
				AfterSaleSubmitApplyRequest.AfsItemInfo afsItemInfo = new AfterSaleSubmitApplyRequest.AfsItemInfo();
				SubItem subItem = subItemMap.get(prodData.getSkuId());
				if(ObjectUtil.isEmpty(subItem)) {
					throw new BusinessException("售后报错--无法找到订单项!第三方SkuId = " + prodData.getSkuId());
				}
				afsItemInfo.setSkuId(prodData.getSkuId());
				afsItemInfo.setProdName(subItem.getProdName());
				afsItemInfo.setSkuNum(prodData.getSkuNum());
				afsItemInfoList.add(afsItemInfo);
			}
			afterSaleSubmitApplyRequest.setPickwareInfo(afsPickupWareInfo);
			afterSaleSubmitApplyRequest.setCustomerInfo(afsCustomerInfo);
			afterSaleSubmitApplyRequest.setAfsItemList(afsItemInfoList);

			afterSaleSubmitApplyRequest.setAccountBookId(sub.getAccountBookId());

			String outRefundNo;
			R<String> re = thirdAfterSaleApply(afterSaleSubmitApplyRequest, sub.getShopId());
			if(re.isSuccess()) {
				outRefundNo = re.getResult();
			}else {
				return re;
			}
            // 更新第三方售后单号
			SubRefundReturn subRefundReturn = subRefundReturnDao.getByRefundSn(afterSaleNo);
			subRefundReturn.setOutRefundNo(outRefundNo);
			subRefundReturnDao.updateProperties(subRefundReturn);
			return R.ok(afterSaleNo);
		} catch (Exception e) {
			throw new BusinessException("申请售后失败!原因：" + e.getMessage());
		} finally {
			lock.unlock();
		}
	}

	/**
	 *MRO 售后退货
	 * @param subRefundReturn
	 * @param subRefundReturnItems
	 * @return
	 */
	@Override
	public R<String> mroAfterSales(SubRefundReturn subRefundReturn, List<SubRefundReturnItem> subRefundReturnItems) {
		List<SubItem> subItemList = subItemDao.queryBySubNumber(subRefundReturn.getSubNumber());
		Shipment shipment = new Shipment();
		if(subRefundReturn.getBatchNumber() != null) {
			shipment = shipmentDao.getByBatchNo(subRefundReturn.getBatchNumber());
		}
		Sub sub = subDao.getSubBySubNumber(subRefundReturn.getSubNumber());
		UserAddressSub userAddressSub = userAddressSubDao.getUserAddressSub(sub.getAddrOrderId());
		if(ObjectUtil.isEmpty(subItemList)) {
			return R.fail("订单项为空！订单号为："+subRefundReturn.getSubNumber());
		}
		AfterSaleSubmitApplyRequest afterSaleSubmitApplyRequest = new AfterSaleSubmitApplyRequest();
		AfterSaleSubmitApplyRequest.AfsCustomerInfo afsCustomerInfo = new AfterSaleSubmitApplyRequest.AfsCustomerInfo();
		AfterSaleSubmitApplyRequest.AfsPickupWareInfo afsPickupWareInfo = new AfterSaleSubmitApplyRequest.AfsPickupWareInfo();
		List<AfterSaleSubmitApplyRequest.AfsItemInfo> afsItemInfoList = new ArrayList<>();
		afterSaleSubmitApplyRequest.setAfterSaleNo(subRefundReturn.getRefundSn());
		afterSaleSubmitApplyRequest.setThirdOrderNo(subItemList.get(0).getThirdNumber());
		if(ObjectUtil.isNotEmpty(shipment)) {
			afterSaleSubmitApplyRequest.setThirdShipNo(shipment.getThirdShipNo());
		}
		Integer refundType = null;
		if(RefundReturnTypeEnum.REFUND_RETURN.value().equals(subRefundReturn.getApplyType())) {
			refundType = 10;
		}else if(RefundReturnTypeEnum.REFUND_EXCHANGE.value().equals(subRefundReturn.getApplyType())) {
			refundType = 20;
		}else if(RefundReturnTypeEnum.REFUND.value().equals(subRefundReturn.getApplyType())) {
			refundType = 30;
		}else {
			return R.fail("售后单类型不正确");
		}
		afterSaleSubmitApplyRequest.setCustomerExpect(refundType); //退货(10)、换货(20)、取消（30）
		afterSaleSubmitApplyRequest.setQuestionDesc(subRefundReturn.getBuyerMessage());
		afterSaleSubmitApplyRequest.setQuestionPic(subRefundReturn.getPhotoFile1());

		if(ReturnPickTypeEnum.DOOR_TAKE.value().equals(subRefundReturn.getPickingType())) {
			afsPickupWareInfo.setPickwareType(OpenPlatformAfterSalePickwareTypeEnum.DOOR_TAKE.value());
			afsPickupWareInfo.setPickwareProvinceId(userAddressSub.getProvinceId());
			afsPickupWareInfo.setPickwareCityId(userAddressSub.getCityId());
			afsPickupWareInfo.setPickwareCountyId(userAddressSub.getAreaId());
			afsPickupWareInfo.setPickwareAddress(userAddressSub.getSubAdds());
		}else if(ReturnPickTypeEnum.CUSTOMER_MAIL.value().equals(subRefundReturn.getPickingType())) {
			afsPickupWareInfo.setPickwareType(OpenPlatformAfterSalePickwareTypeEnum.CUSTOMER_MAIL.value());
		}else if(!RefundReturnTypeEnum.REFUND.value().equals(subRefundReturn.getApplyType())){
			return R.fail("商城售后单取件类型异常");
		}

		//组装商品信息
		Map<Long,SubItem> subItemMap = subItemList.stream().collect(Collectors.toMap(SubItem::getSubItemId, Function.identity()));
		for(SubRefundReturnItem subRefundReturnItem: subRefundReturnItems) {
			AfterSaleSubmitApplyRequest.AfsItemInfo afsItemInfo = new AfterSaleSubmitApplyRequest.AfsItemInfo();
			SubItem subItem = subItemMap.get(subRefundReturnItem.getItemId());
			if(ObjectUtil.isEmpty(subItem)) {
				throw new BusinessException("售后报错--无法找到订单项");
			}
			afsItemInfo.setSkuId(subItem.getThirdSkuId());
			afsItemInfo.setProdName(subItem.getProdName());
			afsItemInfo.setSkuNum(subRefundReturnItem.getRefundNum());
			afsItemInfoList.add(afsItemInfo);
		}

		//组装联系人信息
		if(ObjectUtil.isNotEmpty(shipment)) {
			afsCustomerInfo.setCustomerContactName(shipment.getConsignee());
			afsCustomerInfo.setCustomerTel(shipment.getContactNumber());
			afsCustomerInfo.setCustomerMobilePhone(shipment.getContactNumber());
		}else {
			afsCustomerInfo.setCustomerContactName(userAddressSub.getReceiver());
			afsCustomerInfo.setCustomerTel(userAddressSub.getMobile());
			afsCustomerInfo.setCustomerMobilePhone(userAddressSub.getMobile());
		}

		afterSaleSubmitApplyRequest.setPickwareInfo(afsPickupWareInfo);
		afterSaleSubmitApplyRequest.setCustomerInfo(afsCustomerInfo);
		afterSaleSubmitApplyRequest.setAfsItemList(afsItemInfoList);

		afterSaleSubmitApplyRequest.setAccountBookId(sub.getAccountBookId());

		String outRefundNo;
		R re = thirdAfterSaleApply(afterSaleSubmitApplyRequest, subRefundReturn.getShopId());
		if(re.isSuccess()) {
			outRefundNo = (String) re.getResult();
		}else {
			return re;
		}
		subRefundReturn.setOutRefundNo(outRefundNo);
		subRefundReturnDao.updateProperties(subRefundReturn);
		return R.ok();
	}

	/**
	 * 取消售后单
	 * @param subRefundReturn
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<String> cancel(SubRefundReturn subRefundReturn) {
		log.info("进入开放平台售后单取消");
		Sub sub = Optional.ofNullable(subDao.getSubBySubNumber(subRefundReturn.getSubNumber())).orElseThrow(() -> new BusinessException("售后单对应的订单不存在！鑫采商城售后单号：" + subRefundReturn.getRefundSn()));
		AfterSaleCancelRequest afterSaleCancelRequest = new AfterSaleCancelRequest();
		afterSaleCancelRequest.setAfterSaleNo(subRefundReturn.getRefundSn());
		afterSaleCancelRequest.setThirdAfterSaleNo(subRefundReturn.getOutRefundNo());
		afterSaleCancelRequest.setAccountBookId(sub.getAccountBookId());
		if(afterSaleOnline(subRefundReturn.getShopId())) {
			OpenPlatformAfterSaleService openPlatformAfterSaleService = openPlatformServiceFactory.getAfterSaleServiceByShopId(subRefundReturn.getShopId());
			CommonResponse<String> afsApply = openPlatformAfterSaleService.afterSaleCancel(afterSaleCancelRequest);
			orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.OUTER, AfterSaleApi.AFTERSALE_CANCEL, JSONObject.toJSONString(afterSaleCancelRequest), afsApply.getSuccess() ? 200 : 500, JSONObject.toJSONString(afsApply));
			ResultCodeEnum errorCode = ResultCodeEnum.findByCode(afsApply.getResultCode());
			if (!afsApply.getSuccess() || !ResultCodeEnum.NORMAL.getCode().equals(errorCode.getCode())) {
				log.error("取消售后失败，" + afsApply.getResultMsg() + ",请与商家沟通");
				return R.fail("取消售后失败，" + afsApply.getResultMsg() + ",请与商家沟通");
			}
		}
		return super.cancel(subRefundReturn);
	}

	/**
	 * 更新用户售后物流信息
	 */
	@Override
	public R<String> updateSendInfo(UserUpExpressDTO userUpExpressDTO) {
		SubRefundReturn subRefundReturn = subRefundReturnDao.getByRefundSn(userUpExpressDTO.getRefundSn());
		Sub sub = subDao.getSubBySubNumber(subRefundReturn.getSubNumber());
		if(!ReturnPickTypeEnum.CUSTOMER_MAIL.value().equals(subRefundReturn.getPickingType())) {
			throw new BusinessException("售后单非用户邮寄类型");
		}
		AfterSaleExpressInfoRequest afterSaleExpressInfoRequest = new AfterSaleExpressInfoRequest();
		afterSaleExpressInfoRequest.setThirdAfterSaleNo(subRefundReturn.getOutRefundNo());
		afterSaleExpressInfoRequest.setExpressCode(userUpExpressDTO.getExpressNo());
		afterSaleExpressInfoRequest.setExpressCompany(userUpExpressDTO.getExpressName());
		afterSaleExpressInfoRequest.setAccountBookId(sub.getAccountBookId());


		subRefundReturn.setExpressName(userUpExpressDTO.getExpressName());
		subRefundReturn.setExpressNo(userUpExpressDTO.getExpressNo());
		subRefundReturn.setRefundStatus(OrderBarterRefundStatusEnum.AFTERSALES_PURCHASER_DELIVERGOODS.getCode());
		subRefundReturnDao.update(subRefundReturn);
		if(afterSaleOnline(subRefundReturn.getShopId())) {
			OpenPlatformAfterSaleService openPlatformAfterSaleService = openPlatformServiceFactory.getAfterSaleServiceByShopId(subRefundReturn.getShopId());

			CommonResponse<String> response = openPlatformAfterSaleService.afterSaleExpressInfo(afterSaleExpressInfoRequest);
			orderProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.OUTER, AfterSaleApi.AFTERSALE_EXPRESS_INFO, JSONObject.toJSONString(afterSaleExpressInfoRequest), response.getSuccess() ? 200 : 500, JSONObject.toJSONString(response));
			ResultCodeEnum errorCode = ResultCodeEnum.findByCode(response.getResultCode());
			if (!response.getSuccess() || !ResultCodeEnum.NORMAL.getCode().equals(errorCode.getCode())) {
				log.error("向第三方更新售后单物流信息失败，" + response.getResultMsg());
				return R.fail("向第三方更新售后单物流信息失败，" + response.getResultMsg());
			}
		}
		return R.ok();
	}


	/**
	 * 售后申请状态变更--由第三方申请变更（暂只有震坤行使用这个方法）
	 */
	@Override
	public void updateRefundStatus() {
		throw new BusinessException("更新售后状态方法需要实现");
	}
}
