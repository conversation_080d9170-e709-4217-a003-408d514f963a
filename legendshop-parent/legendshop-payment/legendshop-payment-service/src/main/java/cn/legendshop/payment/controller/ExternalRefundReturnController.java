package cn.legendshop.payment.controller;

import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.security.annotation.Inner;
import cn.legendshop.payment.client.ExternalRefundReturnClinet;
import cn.legendshop.payment.dto.CashierBillQuery;
import cn.legendshop.payment.dto.openPlatform.ExternalRefundReturnVO;
import cn.legendshop.payment.service.ExternalRefundReturnService;
import com.legendshop.model.entity.externalrefund.ExternalRefundReturn;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/externalRefundReturn")
public class ExternalRefundReturnController implements ExternalRefundReturnClinet {

	@Autowired
	private ExternalRefundReturnService externalRefundReturnService;

	/**
	 * 获取第三方退款单
	 * @param query
	 * @return
	 */
	@Override
	@PostMapping("/queryExternalRefundReturn")
	public R<List<ExternalRefundReturnVO>> queryExternalRefundReturn(@RequestBody CashierBillQuery query, @RequestHeader(SecurityConstants.FROM) String from) {
		return externalRefundReturnService.queryExternalRefundReturn(query);
	}

	/**
	 * 保存外部订单编号
	 * @param externalRefundReturn
	 * @return
	 */
	@Override
	@PostMapping(value = "/saveExternalRefundReturn")
	public R<Long> saveExternalRefundReturn(ExternalRefundReturn externalRefundReturn) {
		return externalRefundReturnService.save(externalRefundReturn);
	}

	/**
	 * 根据退款单号获取外部退款单
	 * @param refundSn
	 * @return
	 */
	@Override
	@GetMapping(value = "/queryByRefundSn")
	public R<List<ExternalRefundReturnVO>> queryByRefundSn(String refundSn) {
		return externalRefundReturnService.queryByRefundSn(refundSn);
	}

	/**
	 * 更新售后单状态
	 * @param resultList
	 * @return
	 */
	@Override
	@PostMapping(value = "/updateRefundStatus")
	public R<Long> updateRefundStatus(List<ExternalRefundReturnVO> resultList){
		return externalRefundReturnService.updateRefundStatus(resultList);
	}

}
