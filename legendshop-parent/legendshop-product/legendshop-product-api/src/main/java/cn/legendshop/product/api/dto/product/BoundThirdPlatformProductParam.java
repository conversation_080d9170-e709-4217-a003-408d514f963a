package cn.legendshop.product.api.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;


@Getter
@Setter
public class BoundThirdPlatformProductParam {

	@ApiModelProperty(value = "物料号")
	private   String materialCode;

	@ApiModelProperty(value = "计划单号")
	private String planNo;

	@NotNull
	@ApiModelProperty(value = "与之绑定的商品id")
	private Map<String, List<Long>>  boundProductIdsByPlatform;

	@ApiModelProperty(value = "来源")
	private String source;
}
