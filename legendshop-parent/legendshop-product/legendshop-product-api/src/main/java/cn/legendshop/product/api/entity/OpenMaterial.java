package cn.legendshop.product.api.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.legendshop.dao.persistence.*;
import com.legendshop.dao.support.GenericEntity;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;


/**
 *
 * 开放平台料号
 */
@Entity
@Table(name = "ls_open_material")
@Accessors(chain = true)
public class OpenMaterial implements GenericEntity<Long> {

    private static final long serialVersionUID = -3727905834254570124L;

    @Column(name = "id")
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "generator")
    @TableGenerator(name = "generator", pkColumnValue = "openMaterial_SEQ")
    @ExcelIgnore
    private Long id;

    /**
     * 开放平台的key
     */
    @Column(name = "api_key")
    @ExcelProperty("开放平台key")
    private String apiKey;

    /**
     * 物料编码
     */
    @Column(name = "material_code")
    @ExcelProperty("物料编码")
    private String materialCode;

    @Column(name = "material_name")
    @ExcelProperty("物料名称")
    private String materialName;

    @Column(name = "model")
    @ExcelProperty("规格型号")
    private String model;


    @Column(name = "unit")
    @ExcelProperty("计量单位")
    private String unit;

    @Column(name = "category_code")
    @ExcelProperty("板块代码")
    private String categoryCode;

    @Column(name = "category_name")
    @ExcelProperty("板块名称")
    private String categoryName;

    @Column(name = "first_category_code")
    @ExcelProperty("大类代码")
    private String firstCategoryCode;

    @Column(name = "first_category_name")
    @ExcelProperty("大类名称")
    private String firstCategoryName;

    @Column(name = "second_category_code")
    @ExcelProperty("中类代码")
    private String secondCategoryCode;

    @Column(name = "second_category_name")
    @ExcelProperty("中类名称")
    private String secondCategoryName;

    @Column(name = "third_category_code")
    @ExcelProperty("小类代码")
    private String thirdCategoryCode;


    @Column(name = "third_category_name")
    @ExcelProperty("小类名称")
    private String thirdCategoryName;

    @Column(name = "budget_price")
    @ExcelProperty("预算价")
    private String budgetPrice;

    @Column(name = "material_info")
    @ExcelProperty("长描述")
    private String materialInfo;

    @Column(name = "material_short_info")
    @ExcelProperty("短描述")
    private String materialShortInfo;

    @Column(name = "status")
    @ExcelProperty("状态")
    private String status;

    @Column(name = "purchase_shop_id")
    @ExcelProperty("可采购的电商平台")
    private String purchaseShopId;


    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getFirstCategoryCode() {
        return firstCategoryCode;
    }

    public void setFirstCategoryCode(String firstCategoryCode) {
        this.firstCategoryCode = firstCategoryCode;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public String getSecondCategoryCode() {
        return secondCategoryCode;
    }

    public void setSecondCategoryCode(String secondCategoryCode) {
        this.secondCategoryCode = secondCategoryCode;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public String getThirdCategoryCode() {
        return thirdCategoryCode;
    }

    public void setThirdCategoryCode(String thirdCategoryCode) {
        this.thirdCategoryCode = thirdCategoryCode;
    }

    public String getThirdCategoryName() {
        return thirdCategoryName;
    }

    public void setThirdCategoryName(String thirdCategoryName) {
        this.thirdCategoryName = thirdCategoryName;
    }

    public String getBudgetPrice() {
        return budgetPrice;
    }

    public void setBudgetPrice(String budgetPrice) {
        this.budgetPrice = budgetPrice;
    }

    public String getMaterialInfo() {
        return materialInfo;
    }

    public void setMaterialInfo(String materialInfo) {
        this.materialInfo = materialInfo;
    }

    public String getMaterialShortInfo() {
        return materialShortInfo;
    }

    public void setMaterialShortInfo(String materialShortInfo) {
        this.materialShortInfo = materialShortInfo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPurchaseShopId() {
        return purchaseShopId;
    }

    public void setPurchaseShopId(String purchaseShopId) {
        this.purchaseShopId = purchaseShopId;
    }
}
