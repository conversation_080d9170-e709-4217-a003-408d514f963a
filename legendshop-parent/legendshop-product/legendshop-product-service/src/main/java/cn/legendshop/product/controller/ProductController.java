/**
 * LegendShop微服务商城系统
 * ©版权所有,并保留所有权利。
 */
package cn.legendshop.product.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyConstant;
import cn.legendshop.common.security.annotation.Inner;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.product.api.client.ProductServiceClient;
import cn.legendshop.product.api.dto.*;
import cn.legendshop.product.api.dto.product.MroProductDTO;
import cn.legendshop.product.api.dto.product.*;
import cn.legendshop.product.api.entity.JdProduct;
import cn.legendshop.product.api.entity.ObeiProduct;
import cn.legendshop.product.api.entity.TenderSku;
import cn.legendshop.product.api.query.CommonProdQuery;
import cn.legendshop.product.api.query.product.ErpBindInfoQuery;
import cn.legendshop.product.api.query.product.ErpPreSearchQuery;
import cn.legendshop.product.api.query.product.ErpProductReturnInfoQuery;
import cn.legendshop.product.api.vo.ErpBindedMessageQueryVo;
import cn.legendshop.product.api.vo.ErpBrandNameQueryVo;
import cn.legendshop.product.api.vo.MroProductVo;
import cn.legendshop.product.api.vo.ThirdProductSearchRequestVo;
import cn.legendshop.product.facade.ProductFacade;
import cn.legendshop.product.mq.producer.IndustrialProductProducerService;
import cn.legendshop.product.service.*;
import cn.legendshop.product.util.ProductResult;
import cn.legendshop.product.util.Result;
import cn.legendshop.search.api.dto.IndustrialProductForErpDocumentDTO;
import cn.legendshop.search.api.dto.ThirdProductDocumentDTO;
import cn.legendshop.search.api.vo.SearchResult;
import com.alibaba.fastjson.JSONObject;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.model.dto.ProductDto;
import com.legendshop.model.dto.StockAdminDto;
import com.legendshop.model.dto.UpdSkuStockDto;
import com.legendshop.model.dto.stock.StockArmDto;
import com.legendshop.model.entity.Product;
import com.legendshop.util.AppUtils;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/product")
@AllArgsConstructor
public class ProductController {

	private final MsProductService msProductService;

	private final ProductService productService;

	private final TenderSkuService tenderSkuService;

	private final ProductFacade productFacade;

	private final SkuService skuService;

	private final IndustrialProductProducerService industrialProductProducerService;

	private final ThirdIndustrialProductService thirdIndustrialProductService;

	private final CommonIndustrialProductService commonIndustrialProductService;

	private final ErpProductService erpProductService;

	/**
	 * 根据id查询
	 *
	 * @param
	 * @return
	 */
	@PostMapping("/industrial/search")
	@ApiOperation(value = "【采购商-ERP】商品搜索", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<SearchResult<ThirdProductDocumentDTO>> searchBindThirdPlatformProduct(@RequestBody ThirdProductSearchRequestVo thirdProductSearchRequest) {
		thirdProductSearchRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		return ResResultManager.setResultSuccess(msProductService.searchBindThirdPlatformProduct(thirdProductSearchRequest));
	}



	@PostMapping("/preSearchObeiProduct")
	@ApiOperation(value = "预选欧贝商品信息(直接查数据库)", produces = MediaType.APPLICATION_JSON_VALUE)
	public Result<List<PreSearchQueryDTO>> preSearchObeiProduct(@RequestBody ErpPreSearchQuery erpPreSearchQuery) throws UnsupportedEncodingException {

		List<PreSearchQueryDTO> preSearchQueryDTOS = this.productFacade.preSearchObeiProduct(erpPreSearchQuery);
		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "ERP调用预选商品接口", JSONObject.toJSONString(erpPreSearchQuery));

		if (CollUtil.isEmpty(preSearchQueryDTOS)) {

			return ProductResult.setResultMsgError("fail", preSearchQueryDTOS, erpPreSearchQuery.getMaterialCode());
		}

		return ProductResult.setResultMsgSuccess("success", preSearchQueryDTOS, erpPreSearchQuery.getMaterialCode());
	}

	@GetMapping("/recommendProdList")
	@ApiOperation(value = "【采购商】获取所有推荐绑定商品", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<IndustrialProductForErpDocumentDTO>> recommendProdList(@RequestParam("companyId") String companyId,
																		 @RequestParam("itemId") String materialCode) {
		// 南钢companyId默认设置为NG
		if(ObjectUtil.isEmpty(materialCode)) {
			return R.fail("物料编码不能为空");
		}
		return R.ok(msProductService.listRecommendThirdPlatformProduct(companyId, materialCode, AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));
	}

	@GetMapping("/boundProdList")
	@ApiOperation(value = "【采购商】获取所有已绑定商品", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<IndustrialProductForErpDocumentDTO>> boundProdList(@RequestParam("companyId") String companyId,
																	 @RequestParam("itemId") String materialCode) {
		if(ObjectUtil.isEmpty(materialCode)) {
			return R.fail("物料编码不能为空");
		}
		return R.ok(msProductService.listBoundThirdPlatformProduct(companyId, materialCode, AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));
	}

	@GetMapping("/boundThirdPlatformProduct")
	@ApiOperation(value = "【商家】获取所有绑定的商品", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<PageSupport<IndustrialProductForErpDocumentDTO>> boundThirdPlatformProduct(@RequestParam("materialCode") String materialCode,
																						@RequestParam("curPageNO") int curPageNO,
																						@RequestParam("pageSize") int pageSize) {
		return ResResultManager.setResultSuccess(msProductService.getBoundThirdPlatformProduct(curPageNO, pageSize, materialCode));
	}


	@PostMapping("/submit/boundThirdPlatformProduct")
	@ApiOperation(value = "[商家] 绑定提交接口")
	public R submitBoundThirdPlatformProduct(@RequestBody @Validated BoundThirdPlatformProductParam boundThirdPlatformProductParam) {
		log.info("erp调用绑定接口");
		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "选品绑定提交接口", JSONObject.toJSONString(boundThirdPlatformProductParam));
		msProductService.submitBoundThirdPlatformProduct(boundThirdPlatformProductParam);
		return ResResultManager.setResultSuccess();
	}

	@GetMapping("/obeiProductDetail")
	@ApiOperation(value = "商品详情")
	public R getProductDetail(@RequestParam(value = "prodId", required = false) Long id,
							  @RequestParam(value = "prodCode", required = false) String productCode,
							  @RequestParam(value = "supplier", required = false) String supplier,
							  @RequestParam(value = "materialCode", required = false) String materialCode,
							  @RequestParam(value = "factoryCode", required = false) String factoryCode,
							  @RequestParam(value = "notificateNo", required = false) String notificateNo, HttpServletResponse response) throws IOException {
		IndustrialProductDetailForErpDTO product = new IndustrialProductDetailForErpDTO();
		product.setSupplierCode(supplier);

		//是否是第三方选品页面直接跳转
		if (Objects.nonNull(id) && !StringUtils.isEmpty(supplier)) {
			return R.ok(thirdIndustrialProductService.getProductInfoByEntityIdAndSupplier(id, supplier));
		}
		//是否erp直接跳转第三方供应品的跳转详情
		if (!StringUtils.isEmpty(productCode) && !StringUtils.isEmpty(supplier)) {
			return R.ok(thirdIndustrialProductService.getProductInfoByProductCodeForErp(productCode, supplier));
		}
		if (!StringUtils.isEmpty(materialCode) && !StringUtils.isEmpty(factoryCode) && !StringUtils.isEmpty(notificateNo)) {
			String tenderProductDetailUrl = productService.getTenderProductDetailUrl(materialCode, factoryCode, notificateNo);
			response.setStatus(302);
			return ResResultManager.setResultSuccess(tenderProductDetailUrl);
		}
		//兼容一期需求
		if (ThirdIndustrialEnum.Obei.getSupplierCode().equals(supplier) || ThirdIndustrialEnum.Obei.getCode().equals(supplier)) {
			return ResResultManager.setResultError("欧贝商品不存在!");

		} else if (ThirdIndustrialEnum.JD.getSupplierCode().equals(supplier) || ThirdIndustrialEnum.JD.getCode().equals(supplier)) {
			return ResResultManager.setResultError("京东商品不存在!");
		}
		return R.fail("未知商品");
	}

	@PostMapping("/getBySkuIdAndShopId")
	R<IndustrialProductDTO> getBySkuIdAndShopId(@RequestBody CommonProdQuery commonProdQuery){
		return R.ok(msProductService.queryByProductCodes(commonProdQuery.getProductCode(), commonProdQuery.getShopId()));
	}

	@PostMapping("/getMaterialCodeBoundSupplierProduct")
	@Inner
	public R<List<MaterialProductRelDTO>> getMaterialCodeBoundSupplierProduct(@RequestParam("productCode") String productCode,
																			  @RequestParam("supplier") String supplier) {
		List<MaterialProductRelDTO> products = msProductService.getMaterialCodeBoundSupplierProduct(productCode, supplier);
		return ResResultManager.setResultSuccess(products);
	}


	/**
	 * 查询所有在线商品
	 * {@link ProductServiceClient#queryProductsByOnLine(int, int, java.lang.String)}
	 */
	@GetMapping("/queryProductsByOnLine")
	@Inner
	public R<PageSupport<Product>> queryProductsByOnLine(@RequestParam("curPageNO") int curPageNO, @RequestParam("pageSize") int pageSize) {
		PageSupport<Product> productList = msProductService.queryProductsByOnLine(curPageNO, pageSize);
		return ResResultManager.setResultSuccess(productList);
	}

	/**
	 * {@link ProductServiceClient#queryObeiProductsByOnLine(int, int, java.lang.String)}
	 *
	 * @param curPageNO
	 * @param pageSize
	 * @return
	 */
	@GetMapping("/obei/queryProductsByOnLine")
	@Inner
	public R<PageSupport<ObeiProduct>> queryObeiProductsByOnLine(@RequestParam("curPageNO") int curPageNO, @RequestParam("pageSize") int pageSize) {
		PageSupport<ObeiProduct> productList = msProductService.queryObeiProductsByOnLine(curPageNO, pageSize);
		return ResResultManager.setResultSuccess(productList);
	}

	/**
	 * {@link ProductServiceClient#queryAllProductsByOnLine}
	 *
	 * @param curPageNO
	 * @param pageSize
	 * @return
	 */
	@GetMapping("/thirdIndustrialAll/queryProductsByOnLine")
	@Inner
	public R<PageSupport<IndustrialSearchDocumentDTO>> queryAllProductsByOnLine(@RequestParam("curPageNO") int curPageNO, @RequestParam("supplier") String supplier,
																				@RequestParam("pageSize") int pageSize) {
		PageSupport<IndustrialSearchDocumentDTO> productList = msProductService.queryAllProducts(curPageNO, pageSize, supplier);
		return ResResultManager.setResultSuccess(productList);
	}

	@GetMapping("/jd/queryProductsByOnLine")
	@Inner
	public R<PageSupport<JdProduct>> queryJdProductsByOnLine(@RequestParam("curPageNO") int curPageNO, @RequestParam("pageSize") int pageSize) {
		PageSupport<JdProduct> productList = msProductService.queryJdProductsByOnLine(curPageNO, pageSize);
		return ResResultManager.setResultSuccess(productList);
	}


//	/**
//	 * {@link ProductServiceClient#queryThirdPartyProductsByOnLine(int, int, java.lang.String,java.lang.String)}
//	 *
//	 * @param curPageNO
//	 * @param pageSize
//	 * @return
//	 */
//	@GetMapping("/thirdParty/queryProductsByOnLine")
//	@Inner
//	R<PageSupport<ThirdPartyProductSearchIndexBO>> queryThirdPartyProductsByOnLine(@RequestParam("curPageNO") int curPageNO, @RequestParam("pageSize") int pageSize, @RequestParam("supplier") String supplier, @RequestHeader(SecurityConstants.FROM) String form){
//		return ResResultManager.setResultSuccess(msProductService.queryThirdPartyProductsByOnLine(curPageNO, pageSize,supplier));
//	}


//	@GetMapping("/supplierProductCount")
//	@Inner
//	public R<Map<String, Long>> supplierProductCount() {
//		Map<String, Long> productList = msProductService.supplierProductCount();
//		return ResResultManager.setResultSuccess(productList);
//	}

	/**
	 * {@link ProductServiceClient#getObeiProductByProductCode(List, String)}
	 *
	 * @param productCodes
	 * @param from
	 * @return
	 */
	@GetMapping("/product/obei/productByProductCode")
	@Inner
	public List<ObeiProduct> getObeiProductByProductCode(List<String> productCodes, @RequestHeader(SecurityConstants.FROM) String from) {
		List<ObeiProduct> obeiProducts = msProductService.getObeiProductByProductCode(productCodes);
		return obeiProducts;
	}

//	/**
//	 * {@link ProductServiceClient#getObeiProductByProductCode(List, String)}
//	 *
//	 * @param productCodes
//	 * @param from
//	 * @return
//	 */
//	@GetMapping("/product/erpall/productByProductCode")
//	@Inner
//	public List<IndustrialSearchDocumentDTO> getAllProductByProductCode(@RequestParam("supplierCode")String supplierCode, @RequestBody List<String> productCodes, @RequestHeader(SecurityConstants.FROM) String from) {
//		List<IndustrialSearchDocumentDTO> obeiProducts = msProductService.getAllProductByProductCode(supplierCode,productCodes);
//		return obeiProducts;
//	}

//	@PostMapping("/jd/productByProductCode")
//	@Inner
//	public R<List<JdProduct>> getJdIopProductByProductCode(@RequestBody List<Long> productCodes, @RequestHeader(SecurityConstants.FROM) String from) {
//		List<JdProduct> obeiProducts = msProductService.getJdProductByProductCode(productCodes);
//		return ResResultManager.setResultSuccess(obeiProducts);
//	}

	@PostMapping("/industrial/getMroCategoryId")
	@Inner
	public R<Long> getMroCategoryId(@RequestBody String id, @RequestHeader(SecurityConstants.FROM) String from) {

		String[] split = id.split(StrUtil.COLON);

//		switch (ThirdIndustrialEnum.findByShopId(Long.parseLong(split[0]))){
//			case JD:
//				Long mroCategoryId = this.jdIopProductService.getMroCategoryId(Long.parseLong(split[1]));
//				return R.ok(mroCategoryId);
//			default:
//				break;
//		}

		return ResResultManager.setResultSuccess(0L);
	}

	/**
	 * 查询所有在线商品
	 */
	@GetMapping("/queryProductAllByOnLine")
	@Inner
	public R<List<Product>> queryProductAllByOnLine() {
		List<Product> productPageSupport = msProductService.queryProductAllByOnLine();
		return ResResultManager.setResultSuccess(productPageSupport);
	}

	/**
	 * 根据Id查询商品
	 */
	@Inner
	@GetMapping("/getProductById")
	public R<Product> getProductById(@RequestParam("prodId") Long prodId) {
		return ResResultManager.setResultSuccess(msProductService.getProductById(prodId));
	}

	/**
	 * @param prodId
	 * @return
	 * @see ProductServiceClient#getObeiProductById(Long, String)
	 */
	@Inner
	@GetMapping("/obei/getProductById")
	public R<ObeiProduct> getObeiProductById(@RequestParam("prodId") Long prodId) {
		return ResResultManager.setResultSuccess(msProductService.getObeiProductById(prodId));
	}


//	/**
//	 * @param prodIds
//	 * @param supplier 供应商{@link ThirdIndustrialEnum}
//	 * @param from
//	 * @return
//	 * @see ProductServiceClient#queryThirdPartyProductByIds(List ,String ,String)
//	 */
//	@Inner
//	@GetMapping("/thirdParty/getProductById")
//	public R<List<ThirdPartyProductSearchIndexBO>> queryThirdPartyProductByIds(@RequestParam("prodIds") List<Long> prodIds, @RequestParam("supplier") String supplier, @RequestHeader(SecurityConstants.FROM) String from){
//		return R.ok(msProductService.queryThirdPartyProductByIds(prodIds,supplier));
//	}

	@Inner
	@GetMapping("/obei/getProductByIds")
	public R<List<ObeiProduct>> getObeiProductByIds(@RequestParam("prodIds") List<Long> prodIds) {
		return ResResultManager.setResultSuccess(msProductService.getObeiProductByIds(prodIds));
	}

	@Inner
	@GetMapping("/{shopId}/getProductByIds")
	public R<List<JdProduct>> getJdProductByIds(@PathVariable("shopId") Long shopId,@RequestParam("prodIds") List<Long> prodIds) {
		//todo product rebuild
//		return ResResultManager.setResultSuccess(msProductService.getJdProductByIds(prodIds, shopId));
		throw new BusinessException("此接口需要改造");
	}

//	@Inner
//	@GetMapping("/erpAll/getProductByIds")
//	public R<List<IndustrialSearchDocumentDTO>> getERPAllProductByIds(@RequestParam("supplier") String supplier, @RequestParam("prodIds") List<Long> prodIds) {
//		return ResResultManager.setResultSuccess(msProductService.getERPAllProductByIds(supplier,prodIds));
//	}

	/**
	 * 根据Id查询商品
	 */
	@Inner
	@PostMapping("/getProductByIds")
	public R<List<Product>> getProductByIds(@RequestBody List<Long> prodIds) {
		return ResResultManager.setResultSuccess(msProductService.getProductByIds(prodIds));
	}

	/**
	 * 根据商家Id查询商品
	 */
	@GetMapping("/getProductListByShopId")
	@Inner
	public R<List<Product>> getProductListByShopId(@RequestParam("shopId") Long shopId) {
		return ResResultManager.setResultSuccess(msProductService.getProductListByShopId(shopId));
	}

	@GetMapping("/getProductDto")
	@Inner
	public R<ProductDto> getProductDto(@RequestParam("prodId") Long prodId) {
		ProductDto productDto = productService.getProductDto(prodId);
		return ResResultManager.setResultSuccess(productDto);
	}

	@GetMapping("/getProductBasicBySupplierNo")
	@Inner
	public R<ProductBasicDTO> getProductBasicByProdId(@RequestParam("supplierNo") String supplierNo) {
		ProductBasicDTO productBasicDTO = productService.getProductBasicBySupplierNo(supplierNo);
		if (AppUtils.isBlank(productBasicDTO)) {
			return ResResultManager.setResultError("供应商基本信息为空");
		}
		return ResResultManager.setResultSuccess(productBasicDTO);
	}

	/**
	 * 导入招标商品
	 *
	 * @param tenderProductDTOList 导入商品列表
	 * @return R
	 */
	@Inner
	@PostMapping("/saveTenderProduct")
	public R<Map<String, Object>> saveTenderProduct(@RequestBody List<TenderProductDTO> tenderProductDTOList) {
		try {
			Map<String, Object> result = productService.saveTenderProduct(tenderProductDTOList);
			return ResResultManager.setResultSuccess(result);
		} catch (Exception e) {
			return ResResultManager.setResultError("导入商品列表saveTenderProduct失败：" + e.getMessage());
		}
	}

	/**
	 * 招标商品更新有效期
	 *
	 * @param tenderProductDTOList 商品列表
	 * @return R
	 */
	@Inner
	@PostMapping("/ProductEffectiveDateUpdate")
	public R<Boolean> ProductEffectiveDateUpdate(@RequestBody List<TenderProductDTO> tenderProductDTOList) {

		Boolean result = productService.ProductEffectiveDateUpdate(tenderProductDTOList);

		return ResResultManager.setResultSuccess(true);
	}

	/**
	 * 获取tenderSku信息
	 *
	 * @param buyerUserId  采购商userId
	 * @param materialCode 商品料号
	 * @param vendorCode   厂商编号
	 * @param userId       供应商用户ID
	 * @return
	 */
	@GetMapping("/getTenderSkuDtoByMaterialCodeAndUniformNo")
	@Inner
	public R<TenderSkuDTO> getTenderSkuDtoByMaterialCodeAndUniformNo(@RequestParam("buyerUserId") String buyerUserId,
																	 @RequestParam("materialCode") String materialCode,
																	 @RequestParam("vendorCode") String vendorCode,
																	 @RequestParam("userId") String userId,
																	 @RequestParam("notificateNo") String notificateNo) {
		log.info("开始查询中标价格，参数打印：buyerUserId：{},materialCode:{},vendorCode:{},userId:{},notificateNo:{}", buyerUserId, materialCode, vendorCode, userId, notificateNo);
		TenderSkuDTO tenderSkuDTO = tenderSkuService.getTenderSkuDtoByMaterialCodeAndUniformNo(buyerUserId, materialCode, vendorCode, userId, notificateNo);
		if (AppUtils.isBlank(tenderSkuDTO)) {
			return ResResultManager.setResultError("商品的单品信息为空");
		}
		return ResResultManager.setResultSuccess(tenderSkuDTO);
	}


	@GetMapping("/updateProdStocks")
	@Inner
	public R updateProdStocks(@RequestParam("prodId") Long prodId) {
		productService.updateProdStocks(prodId);
		return ResResultManager.setResultSuccess();
	}

	@GetMapping("/getStocksByLockMode")
	@Inner
	public R<Integer> getStocksByLockMode(@RequestParam("prodId") Long prodId) {
		Integer i = productService.getStocksByLockMode(prodId);
		return ResResultManager.setResultSuccess(i);
	}


	@GetMapping("/getStockcontrol")
	@Inner
	public R<PageSupport<StockAdminDto>> getStockcontrol(@RequestParam("curPageNO") String curPageNO, @RequestParam("productName") String productName) {
		PageSupport<StockAdminDto> stockAdminDtoPageSupport = productService.getStockcontrol(curPageNO, productName);
		return ResResultManager.setResultSuccess(stockAdminDtoPageSupport);
	}

	@GetMapping("/getStockwarning")
	@Inner
	public R<PageSupport<StockArmDto>> getStockwarning(@RequestParam("shopId") Long shopId, @RequestParam("curPageNO") String curPageNO, @RequestParam("productName") String productName) {
		PageSupport<StockArmDto> stockArmDtoPageSupport = productService.getStockwarning(shopId, curPageNO, productName);
		return ResResultManager.setResultSuccess(stockArmDtoPageSupport);
	}

	@GetMapping("/getStockList")
	@Inner
	public R<PageSupport<StockArmDto>> getStockList(@RequestParam("shopId") Long shopId, @RequestParam("curPageNO") String curPageNO, @RequestParam("pageSize") int pageSize) {
		PageSupport<StockArmDto> stockArmDtoPageSupport = productService.getStockList(shopId, curPageNO, pageSize);
		return ResResultManager.setResultSuccess(stockArmDtoPageSupport);
	}

	@GetMapping("/getStocksByMode")
	@Inner
	public R<Integer> getStocksByMode(@RequestParam("prodId") Long prodId) {
		Integer i = productService.getStocksByMode(prodId);
		return ResResultManager.setResultSuccess(i);
	}

	@GetMapping("/getStockBySkuId")
	@Inner
	public R<List<UpdSkuStockDto>> getStockBySkuId(@RequestParam("skuId") Long skuId) {
		List<UpdSkuStockDto> updSkuStockDtos = productService.getStockBySkuId(skuId);
		return ResResultManager.setResultSuccess(updSkuStockDtos);
	}

	@GetMapping("/updateBuys")
	@Inner
	public R updateBuys(@RequestParam("value") Long value, @RequestParam("key") Long key) {
		productService.updateBuys(value, key);
		return ResResultManager.setResultSuccess();
	}

	/**
	 * 批量订购商品
	 */
	@PostMapping("/getBatchProductDtoList")
	@Inner
	public R<List<BatchProductDTO>> getBatchProductDtoList(@RequestBody BatchProdSelectParmDTO batchProdSelectParmDto) {
		List<BatchProductDTO> batchProductDtoList = productService.getBatchProductDtoList(batchProdSelectParmDto);
		if (AppUtils.isBlank(batchProductDtoList)) {
			return ResResultManager.setResultError("批量订货商品为空");
		}
		return ResResultManager.setResultSuccess(batchProductDtoList);
	}

	/**
	 * 查询需求列表的商品
	 *
	 * @param skuId
	 * @return
	 */
	@GetMapping("/getDemandProdDtoBySkuId")
	@Inner
	public R<DemandProductDTO> getDemandProdDtoBySkuId(@RequestParam("skuId") Long skuId, @RequestParam("userId") String userId) {
		Long tenderMainBodyId = tenderSkuService.getTenderMainBodyIdByUserId(userId);
		DemandProductDTO demandProductDto = productService.getDemandProdDtoBySkuId(skuId, tenderMainBodyId);
		if (AppUtils.isBlank(demandProductDto)) {
			return ResResultManager.setResultError("商品数据为空");
		}
		return ResResultManager.setResultSuccess(demandProductDto);
	}

	/**
	 * 根据skuIds查询料号
	 */
	@PostMapping("/getMaterialCodeBySkuIds")
	@Inner
	public R<List<String>> getMaterialCodeBySkuIds(@RequestBody List<Long> skuIds) {
		List<String> materialCodeList = tenderSkuService.getMaterialCodeBySkuIds(skuIds);
		return ResResultManager.setResultSuccess(materialCodeList);
	}

	@PostMapping("/querySkuInfoBySkuIds")
	@Inner
	public R<List<UpdSkuStockDto>> querySkuInfoBySkuIds(@RequestBody List<Long> skuIds) {
		List<UpdSkuStockDto> updSkuStockDtos = this.skuService.querySkuInfoBySkuIds(skuIds);
		return ResResultManager.setResultSuccess(updSkuStockDtos);
	}

	@PostMapping(value = "/getTenderSkuBySkuIds")
	@Inner
	public R<List<TenderSku>> getTenderSkuBySkuIds(@RequestParam("skuIds") List<Long> skuIds) {
		List<TenderSku> tenderSkuList = tenderSkuService.getTenderSkuBySkuIds(skuIds);
		return ResResultManager.setResultSuccess(tenderSkuList);
	}

	@GetMapping("/todayNewProduct")
	@Inner
	public R<Integer> todayNewProduct() {
		Integer result = productService.todayNewProduct();
		return ResResultManager.setResultSuccess(result);
	}

	@PostMapping("/updateProdActualStocks")
	@Inner
	public R<String> updateProdActualStocks(Long prodId) {
		productService.updateProdActualStocks(prodId);
		return ResResultManager.setResultSuccess();
	}

	/**
	 * 获取招标有效期过期的商品ID列表
	 *
	 * @return R
	 */
	@PostMapping("/queryTenderProductExpireList")
	@Inner
	public R<List<String>> queryTenderProductExpireList() {
		List<String> result = productService.queryTenderProductExpireList();
		return ResResultManager.setResultSuccess(result);
	}

	/**
	 * @desc 更新招标商品分类错误的商品
	 */
	@GetMapping("/updateTenderProdErrorCategory")
	public R updateTenderProdErrorCategory() {
		this.productService.updateTenderProdErrorCategory();
		return ResResultManager.setResultSuccess();
	}

	/**
	 * @return
	 */
	@Inner
	@PostMapping("/getShopProductsByPage")
	public R<PageSupport<Product>> getShopProductsByPage(@RequestParam Integer pageNo,
														 @RequestParam Integer pageSize,
														 @RequestParam Long shopId,
														 @RequestParam Integer status) {
		PageSupport<Product> productPageSupport = this.productService.queryProductsByShop(pageNo, pageSize, shopId, status);
		return ResResultManager.setResultSuccess(productPageSupport);
	}

	@Inner
	@GetMapping("/isAllowComfirmSub")
	public R<Boolean> isAllowComfirmSub(@RequestParam Long shopId, @RequestParam Integer status) {
		Boolean allowComfirmSub = this.productService.isAllowComfirmSub(shopId, status);
		return ResResultManager.setResultSuccess(allowComfirmSub);
	}

	/**
	 * 下架所有通过接口获取的聚福堂商品
	 *
	 * @return 下架数量
	 */
	@Inner
	@GetMapping("/offJFTProductByInterface")
	public R<Integer> offJFTProductByInterface() {
		Integer counts = this.productService.deleteJFTProduct();
		return ResResultManager.setResultSuccess(counts);
	}


	/**
	 * 批量导入
	 *
	 * @param query
	 * @return
	 */
	@PostMapping(value = "/modifyMaCodeRel", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public R<List<ReturnErpProductInfoDto>> modifyMaCodeRel(@RequestBody ErpProductReturnInfoQuery query) throws UnsupportedEncodingException {
		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "批量导入接口", JSONObject.toJSONString(query));
		List<ReturnErpProductInfoDto> productDataVos = this.productFacade.updateMaCodeBindThirdProd(query);
		if (productDataVos == null) {
			ThirdIndustrialEnum code = ThirdIndustrialEnum.findBySupplierCode(query.getSupplier());

			//在批量导入料号与第三方工业品关系后，直接通知MRO确认。
			return ResResultManager.setResultError(URLEncoder.encode("传输的字段为空或不正确/" + code.getName() + "商品不存在", CharsetUtil.UTF_8));

		}

		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP,"批量导入结果", JSONObject.toJSONString(productDataVos));

		return ResResultManager.setResultSuccess(productDataVos);
	}


	@PostMapping(value = "/bindProductInfoSyncs", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public R bindProductInfoSyncs(@RequestBody ErpBindInfoQuery erpBindInfoQuery) throws UnsupportedEncodingException {

		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP,"手动导入确认绑定接口", JSONObject.toJSONString(erpBindInfoQuery));
		List<BindProductInfoSync> BindProductInfoSyncs = this.productFacade.updateBindingRel(erpBindInfoQuery);
		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP,"手动导入确认绑定结果", JSONObject.toJSONString(BindProductInfoSyncs));
		return ResResultManager.setResultSuccess(BindProductInfoSyncs);
	}


//	@PostMapping(value = "/postProductList", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
//	public R postProductList(@RequestBody SelectedProductReturnVo productReturnVo) {
//
//		industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP,"选品返回", JSONObject.toJSONString(productReturnVo));
//		Boolean result = this.productFacade.getProductDataBySupplierCode(productReturnVo);
//		if (result) {
//			return ResResultManager.setResultSuccess();
//		}
//		return ResResultManager.setResultError("所选的商品已经不存在，请重新挑选");
//	}

	/**
	 * 选品页接口
	 * @param erpBindedMessageQueryVo
	 * @return
	 */
	@PostMapping("/queryAllBindedProduct")
	@ApiOperation(value = "查询已经绑定的第三方商品")
	public R<PageSupport<ReturnBindedProductInfoDTO>> queryAllBindedProduct(@RequestBody ErpBindedMessageQueryVo erpBindedMessageQueryVo) {

		PageSupport<ReturnBindedProductInfoDTO> returnBindedProductInfoDTOPageSupport = this.erpProductService.queryAllBindedProductInfo(erpBindedMessageQueryVo);
		return ResResultManager.setResultSuccess(returnBindedProductInfoDTOPageSupport);
	}

	@Inner
	@GetMapping("/industrial/getPreciseProductInfo")
	public R<IndustrialSearchDocumentDTO> getPreciseProductInfo(@RequestParam("id") String id) {
		return ResResultManager.setResultSuccess(this.msProductService.getPreciseProductInfo(id));
	}

	@Inner
	@GetMapping("/consumer/getPreciseProductInfo")
	public R<ConsumerProductDocumentDTO> getConsumerPreciseProductInfo(@RequestParam("id") String id) {
		return ResResultManager.setResultSuccess(this.msProductService.getConsumerPreciseProductInfo(id));
	}

	@Inner
	@GetMapping("/consumer/queryConsumerIndexInfoByPage")
	R<List<ConsumerProductDocumentDTO>> queryConsumerIndexInfoByPage(@RequestParam("pageNumber") int pageNumber,@RequestParam("pageSize") int pageSize, @RequestParam("key") String key, @RequestHeader(SecurityConstants.FROM) String from){
		return ResResultManager.setResultSuccess(this.msProductService.queryConsumerIndexInfoByPage(pageNumber, pageSize, key));
	}

	@Inner
	@GetMapping("/consumer/queryVopIndex")
	R<List<ConsumerProductDocumentDTO>> queryConsumerIndexInfoByPage(@RequestParam("lastQueryId") Long lastQueryId, @RequestHeader(SecurityConstants.FROM) String from){
		return ResResultManager.setResultSuccess(this.msProductService.queryVopDocument(lastQueryId));
	}

	@Inner
	@PostMapping("/industrial/batchGetPreciseProductInfo")
	public R<List<IndustrialSearchDocumentDTO>> getBatchPreciseProductInfo(@RequestBody List<String> id) {
		return ResResultManager.setResultSuccess(this.msProductService.queryPreciseProductInfo(id));
	}

	@PostMapping("/erpQueryBrandName")
	public  R<PageSupport<String>> erpQueryBrandName(@RequestBody ErpBrandNameQueryVo erpBrandNameQueryVo) {
		if(erpBrandNameQueryVo.getBrandName()==null){
			return R.fail("品牌名为null");
		}
		PageSupport<String> result = this.productFacade.erpQueryBrandName(erpBrandNameQueryVo);
		if(ObjectUtil.isNotEmpty(result.getResultList())) {
			return R.ok(result);
		}
		return R.ok();
	}

	@PostMapping(value ="/getDeliveryInfo", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@Inner
	public R<String> getDeliveryInfo(@RequestBody AddressQueryDTO addressQueryDTO){
		return R.ok(this.productService.requestProdDeliveryStatus(addressQueryDTO));
	}

	@PostMapping(value ="/getStock", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@Inner
	public R<Integer> getStock(@RequestBody AddressQueryDTO addressQueryDTO){
		return R.ok(this.productService.getStock(addressQueryDTO));
	}

	@PostMapping(value ="/getProductDetail")
	@Inner
	public R<String> getProductDetail(@RequestParam("id") String id, @RequestHeader(SecurityConstants.FROM) String from){
		return R.ok(this.productService.getProductDetail(id));
	}

	@PostMapping(value ="/queryProductPrice")
	@Inner
	public R<List<CommonSupplierProductDTO>> queryProductPrice(@RequestParam("ids") List<Long> ids , @RequestParam("userId") String userId,@RequestHeader(SecurityConstants.FROM) String from){
		return R.ok(this.msProductService.queryByProdIds(ids, userId));
	}

	@GetMapping("/platformList")
	public R<List<PlatformInfoDTO>> queryProductInfo(@RequestParam(value = "innerCodes", required = false) String innerCodes) {
		List<PlatformInfoDTO> platformList = commonIndustrialProductService.getPlatformList(null,null);
		if(StrUtil.isBlank(innerCodes) || innerCodes.equals("all")){
			return R.ok(platformList);
		}else {
			return R.ok(platformList.stream().filter(e->!e.getInnerCode().equals(innerCodes)).collect(Collectors.toList()));
		}
	}

	@PostMapping(value = "/queryPaintProdList")
	public R<PageSupport<MroProductVo>> queryPaintProdList(@RequestBody MroProductDTO mroProductDTO) {
		return R.ok(productService.queryProdListByMroProductDTO(mroProductDTO));
	}
}
