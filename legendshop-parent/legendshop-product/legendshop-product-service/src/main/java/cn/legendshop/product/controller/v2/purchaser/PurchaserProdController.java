package cn.legendshop.product.controller.v2.purchaser;

import cn.hutool.core.util.ObjectUtil;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.dao.OpenPlatformBuyerDao;
import cn.legendshop.common.model.OpenPlatformBuyer;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.payment.constant.OpenPlatformParamEnum;
import cn.legendshop.product.api.dto.IndustrialProductDetailForErpDTO;
import cn.legendshop.product.api.dto.PlatformInfoDTO;
import cn.legendshop.product.api.dto.SupplierCategoryDTO;
import cn.legendshop.product.api.dto.product.OpenBindThirdPlatformConfirmParam;
import cn.legendshop.product.api.dto.product.OpenBindThirdPlatformProductParam;
import cn.legendshop.product.api.dto.product.OpenMaterialDTO;
import cn.legendshop.product.api.entity.JDCategoryUpLoad;
import cn.legendshop.product.api.entity.OpenMaterialProductRecommend;
import cn.legendshop.product.api.query.open.*;
import cn.legendshop.product.api.vo.*;
import cn.legendshop.product.api.vo.open.OpenBaseIndustrialProdVo;
import cn.legendshop.product.api.vo.open.OpenBindProdVo;
import cn.legendshop.product.api.vo.open.OpenIndustrialProdVo;
import cn.legendshop.product.api.vo.open.OpenRecommendProdVo;
import cn.legendshop.product.facade.ProductFacade;
import cn.legendshop.product.service.*;
import cn.legendshop.search.api.dto.IndustrialProductForErpDocumentDTO;
import cn.legendshop.search.api.dto.ThirdProductDocumentDTO;
import cn.legendshop.search.api.vo.SearchResult;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.legendshop.common.excel.util.EasyExcelUtils;
import com.legendshop.common.excel.util.ExcelReadUtil;
import com.legendshop.dao.support.PageSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 开放平台 购买者接口
 * <AUTHOR>
 * @data 2023/11/8
 **/


@Slf4j
@RestController
@RequestMapping("/v2/open/purchaser")
@AllArgsConstructor
@Api(tags = "【采购商】开放平台商品模块")
public class PurchaserProdController {

	private final MsProductService msProductService;


	private final ThirdIndustrialProductService thirdIndustrialProductService;


	private final ProductService productService;

	private final ProductFacade productFacade;

	private final MsCategoryService categoryService;

	private final JdAddressService jdAddressService;

	private final JdIopProductService jdIopProductService;

	private final CommonIndustrialProductService commonIndustrialProductService;


	private final OpenProductService openProductService;

	private final OpenPlatformBuyerDao openPlatformBuyerDao;

	/**
	 * @param
	 * @return
	 */
	@PostMapping("/search")
	@ApiOperation(value = "【采购商】开放平台工业品选品", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<SearchResult<ThirdProductDocumentDTO>> searchBindThirdPlatformProduct(HttpServletRequest request, @RequestBody ThirdProductSearchRequestVo thirdProductSearchRequest) {
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByApiKey(request.getHeader(OpenPlatformParamEnum.API_KEY.getValues()));
		if (ObjectUtil.isEmpty(buyer)) {
			throw new BusinessException("未找到账套对应的buyer信息，请联系管理员配置！");
		}
		thirdProductSearchRequest.setAccountBookId(buyer.getAccountBookId());
		return ResResultManager.setResultSuccess(msProductService.searchBindThirdPlatformProduct(thirdProductSearchRequest));
	}


	@GetMapping("/boundProdList")
	@ApiOperation(value = "【采购商】获取所有已绑定商品", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<IndustrialProductForErpDocumentDTO>> boundProdList(HttpServletRequest request,
																						@RequestParam("companyId") String companyId,
																						@RequestParam("itemId") String materialCode) {
		if(ObjectUtil.isEmpty(materialCode)) {
			return R.fail("物料编码不能为空");
		}
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByApiKey(request.getHeader(OpenPlatformParamEnum.API_KEY.getValues()));
		if (ObjectUtil.isEmpty(buyer)) {
			throw new BusinessException("未找到账套对应的buyer信息，请联系管理员配置！");
		}
		return R.ok(openProductService.getBoundThirdPlatformProduct(companyId, materialCode, buyer.getAccountBookId()));
	}


	@GetMapping("/recommendProdList")
	@ApiOperation(value = "【采购商】获取所有推荐绑定商品", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<IndustrialProductForErpDocumentDTO>> recommendProdList(HttpServletRequest request,
																						@RequestParam("companyId") String companyId,
																						@RequestParam("itemId") String materialCode) {
		if(ObjectUtil.isEmpty(materialCode)) {
			return R.fail("物料编码不能为空");
		}
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByApiKey(request.getHeader(OpenPlatformParamEnum.API_KEY.getValues()));
		if (ObjectUtil.isEmpty(buyer)) {
			throw new BusinessException("未找到账套对应的buyer信息，请联系管理员配置！");
		}
		return R.ok(openProductService.getRecommendThirdPlatformProduct(companyId, materialCode, buyer.getAccountBookId()));
	}

	@GetMapping("/sameProdList")
	@ApiOperation(value = "【采购商】获取所有已绑定商品的相同商品", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<IndustrialProductForErpDocumentDTO>> sameProdList(HttpServletRequest request,
																		 @RequestParam("companyId") String companyId,
																		 @RequestParam("itemId") String materialCode) {
		if(ObjectUtil.isEmpty(materialCode)) {
			return R.fail("物料编码不能为空");
		}
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByApiKey(request.getHeader(OpenPlatformParamEnum.API_KEY.getValues()));
		if (ObjectUtil.isEmpty(buyer)) {
			throw new BusinessException("未找到账套对应的buyer信息，请联系管理员配置！");
		}
		return R.ok(openProductService.getSameThirdPlatformProduct(companyId, materialCode, buyer.getAccountBookId()));
	}

	@GetMapping("/similarProdList")
	@ApiOperation(value = "【采购商】获取所有已绑定商品的相似商品", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<IndustrialProductForErpDocumentDTO>> similarProdList(HttpServletRequest request,
																			@RequestParam("companyId") String companyId,
																			@RequestParam("itemId") String materialCode) {
		if(ObjectUtil.isEmpty(materialCode)) {
			return R.fail("物料编码不能为空");
		}
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByApiKey(request.getHeader(OpenPlatformParamEnum.API_KEY.getValues()));
		if (ObjectUtil.isEmpty(buyer)) {
			throw new BusinessException("未找到账套对应的buyer信息，请联系管理员配置！");
		}
		return R.ok(openProductService.getSimilarThirdPlatformProduct(companyId, materialCode, buyer.getAccountBookId()));
	}


	/**
	 * 【采购商】获取相同商品
	 * @param request HttpServletRequest
	 * @param shopId 店铺id
	 * @param prodId 商品id
	 * @return R<List<IndustrialProductForErpDocumentDTO>>
	 */
	@GetMapping("/listSameProduct")
	public R<List<IndustrialProductForErpDocumentDTO>> listSameProduct(HttpServletRequest request,
																	   @RequestParam("shopId") Long shopId, @RequestParam("prodId") Long prodId) {
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByApiKey(request.getHeader(OpenPlatformParamEnum.API_KEY.getValues()));
		if (ObjectUtil.isEmpty(buyer)) {
			throw new BusinessException("未找到账套对应的buyer信息，请联系管理员配置！");
		}
		return openProductService.listSameProduct(shopId, prodId, buyer.getAccountBookId());
	}

	/**
	 * 【采购商】获取相似商品
	 *
	 * @param request HttpServletRequest
	 * @param shopId  店铺id
	 * @param prodId  商品id
	 * @return R<List < IndustrialProductForErpDocumentDTO>>
	 */
	@GetMapping("/listSimilarProduct")
	public R<List<IndustrialProductForErpDocumentDTO>> listSimilarProduct(HttpServletRequest request,
																		  @RequestParam("shopId") Long shopId, @RequestParam("prodId") Long prodId) {
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByApiKey(request.getHeader(OpenPlatformParamEnum.API_KEY.getValues()));
		if (ObjectUtil.isEmpty(buyer)) {
			throw new BusinessException("未找到账套对应的buyer信息，请联系管理员配置！");
		}
		return openProductService.listSimilarProduct(shopId, prodId, buyer.getAccountBookId());
	}	

	@PostMapping("/preBind")
	@ApiOperation(value = "【采购商】开放平台预绑定", produces = MediaType.APPLICATION_JSON_VALUE)
	public R searchBindThirdPlatformProduct(HttpServletRequest request, @Validated @RequestBody OpenBindThirdPlatformProductParam openBindThirdPlatformProductParam) {
		String apiKey = request.getHeader(OpenPlatformParamEnum.API_KEY.getValues());
		log.info("拿到api:{}", apiKey);
		openBindThirdPlatformProductParam.setApiKey(apiKey);
		return openProductService.preBind(openBindThirdPlatformProductParam);
	}


	@PostMapping("/materialBinding")
	@ApiOperation(value = "【采购商】开放平台确认绑定", produces = MediaType.APPLICATION_JSON_VALUE)
	public R searchBindThirdPlatformProduct(HttpServletRequest request,@Validated @RequestBody OpenBindThirdPlatformConfirmParam openBindThirdPlatformConfirmParam) {
		String apiKey = request.getHeader(OpenPlatformParamEnum.API_KEY.getValues());
		log.info("拿到api:{}", apiKey);
		openBindThirdPlatformConfirmParam.setApiKey(apiKey);
		return  openProductService.confirmBind(openBindThirdPlatformConfirmParam);
	}
	@PostMapping("/getProductBaseInfo")
	public R<List<OpenBaseIndustrialProdVo>> getProductBaseInfo(@Validated @RequestBody OpenProdQuery openProdQuery) {
		R<List<OpenBaseIndustrialProdVo>> openIndustrialProdVoR = openProductService.queryBaseIndustrialProd(openProdQuery);
		return openIndustrialProdVoR;
	}


	@PostMapping("/getProductInfo")
	public R<OpenIndustrialProdVo> getProdInfo(@Validated @RequestBody OpenProdQuery openProdQuery) {
        return openProductService.queryIndustrialProd(openProdQuery);
	}

	@PostMapping("/bindInfo")
	public R<OpenBindProdVo> getProdInfo(HttpServletRequest request, @Validated @RequestBody OpenBindProdQuery openBindProdQuery) {
		String apiKey = request.getHeader(OpenPlatformParamEnum.API_KEY.getValues());
		log.info("拿到api:{}", apiKey);
		openBindProdQuery.setApiKey(apiKey);
		R<OpenBindProdVo> openBindProdVoR = openProductService.queryBindInfo(openBindProdQuery);
		return openBindProdVoR;
	}


	/**
	 * 【采购商】3.3.1 采购商物料信息同步到鑫采商城
	 * @param request
	 * @param openMaterialDTO
	 * @return
	 */
	@PostMapping("/updateMaterial")
	public R<String> updateMaterial(HttpServletRequest request, @Validated @RequestBody OpenMaterialDTO openMaterialDTO) {
		String apiKey = request.getHeader(OpenPlatformParamEnum.API_KEY.getValues());
		log.info("拿到api:{}", apiKey);
		openMaterialDTO.setApiKey(apiKey);
		R<String> result = openProductService.updateMaterial(openMaterialDTO);
		return result;
	}

	/**
	 * 【采购商】物料绑定列表
	 * @param bindProdPageQuery
	 * @return
	 */
		@PostMapping("/queryAllBindProd")
	public R<PageSupport<OpenBindProdVo>> queryAllBindProd(@RequestBody OpenBindProdPageQuery bindProdPageQuery) {
		return openProductService.queryAllBindProd(bindProdPageQuery);
	}

	/**
	 * 【采购商】物料绑定推荐列表
	 *
	 * @param recommendProdPageQuery
	 * @return
	 */
	@PostMapping("/queryAllRecommendProd")
	public R<PageSupport<OpenRecommendProdVo>> queryAllRecommendProd(
			@RequestBody OpenRecommendProdPageQuery recommendProdPageQuery) {
		return openProductService.queryAllRecommendProd(recommendProdPageQuery);
	}

	/**
	 * 【采购商】物料绑定推荐列表 删除
	 * @param id
	 * @return
	 */
	@GetMapping("/deleteRecommendProd/{id}")
	public R deleteRecommendProd(@PathVariable("id") Long id) {
		return openProductService.deleteRecommendProd(id);
	}

	/**
	 * 【采购商】物料绑定推荐列表 导入模板下载
	 *
	 * @param query
	 * @return
	 */
	@ApiOperation(value = "导入模板下载", produces = MediaType.APPLICATION_JSON_VALUE)
	@RequestMapping(value = "/downloadTemplate")
	public void downloadTemplate(HttpServletResponse response, @RequestBody OpenRecommendProdPageQuery query) throws IOException {
		if (ObjectUtil.isEmpty(query.getAccountBookId())) {
			throw new BusinessException("请选择账套后再下载！否则无法生成对应模板");
		}
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByAccountBookId(query.getAccountBookId());
		if (ObjectUtil.isEmpty(buyer)) {
			throw new BusinessException("请联系管理员！未找到账套对应的buyer信息，无法下载模板");
		}
		List<OpenMaterialProductRecommend> dataset = openProductService.downloadTemplate(buyer.getApiKey());
		EasyExcelUtils.exportLocalByExcel(response, dataset, "template_" + buyer.getCompanyName(), OpenMaterialProductRecommend.class);
	}

	@PostMapping("/importRecommendProd")
	public R importRecommendProd(MultipartHttpServletRequest request) {
		MultipartFile file = request.getFile("importFile");
		if (ObjectUtil.isEmpty(file)) {
			return R.fail("上传文件不能为空");
		}
		// 判断文件类型
		String fileName = file.getOriginalFilename();
		if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
			return R.fail("导入列表请上传excel类型文件");
		}

		try {
			AnalysisEventListener<OpenMaterialProductRecommend> listener = ExcelReadUtil.getListener(openProductService::batchSaveRecommendProds);
			// 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
			EasyExcel.read(file.getInputStream(), OpenMaterialProductRecommend.class, listener).sheet().headRowNumber(1).doRead();
		} catch (IOException e) {
			return R.fail("导入失败！" + e);
		}
		// 更新推荐价格比例定时任务
		openProductService.calculateRecommendPriceRatio("1");
		return R.ok();
	}

	@GetMapping("/prodInfo")
	@ApiOperation(value = "商品详情")
	public R getProductDetail(@RequestParam(value = "prodId", required = false) Long id,
							  @RequestParam(value = "prodCode", required = false) String productCode,
							  @RequestParam(value = "supplier", required = false) String supplier,
							  @RequestParam(value = "materialCode", required = false) String materialCode,
							  @RequestParam(value = "factoryCode", required = false) String factoryCode,
							  @RequestParam(value = "notificateNo", required = false) String notificateNo, HttpServletResponse response) throws IOException {
		IndustrialProductDetailForErpDTO product = new IndustrialProductDetailForErpDTO();
		product.setSupplierCode(supplier);

		//是否是第三方选品页面直接跳转
		if (Objects.nonNull(id) && !StringUtils.isEmpty(supplier)) {
			return R.ok(thirdIndustrialProductService.getProductInfoByEntityIdAndSupplier(id, supplier));
		}
		//是否erp直接跳转第三方供应品的跳转详情
		if (!StringUtils.isEmpty(productCode) && !StringUtils.isEmpty(supplier)) {
			return R.ok(thirdIndustrialProductService.getProductInfoByProductCodeForErp(productCode, supplier));
		}
		if (!StringUtils.isEmpty(materialCode) && !StringUtils.isEmpty(factoryCode) && !StringUtils.isEmpty(notificateNo)) {
			String tenderProductDetailUrl = productService.getTenderProductDetailUrl(materialCode, factoryCode, notificateNo);
			response.setStatus(302);
			return ResResultManager.setResultSuccess(tenderProductDetailUrl);
		}
		return R.fail("未知商品");
	}


	/**
	 * 选品页的类目
	 *
	 * @param erpCaterpgroyQueryVo
	 * @return
	 */
	@PostMapping("/getExistCategory")
	@ApiOperation(value = "选品页的类目")
	public R<List<SupplierCategoryDTO>> getExistObeiCategory(@RequestBody ErpCaterpgroyQueryVo erpCaterpgroyQueryVo) {

		List<SupplierCategoryDTO> existIndustrialCategory = this.categoryService.getExistIndustrialCategory(erpCaterpgroyQueryVo);

		return R.ok(existIndustrialCategory);
	}


	@PostMapping("/queryBrandName")
	@ApiOperation(value = "品牌名查询")
	public R<PageSupport<String>> erpQueryBrandName(@RequestBody ErpBrandNameQueryVo erpBrandNameQueryVo) {
		if (erpBrandNameQueryVo.getBrandName() == null) {
			return R.fail("品牌名为null");
		}
		PageSupport<String> result = this.productFacade.erpQueryBrandName(erpBrandNameQueryVo);
		if (ObjectUtil.isNotEmpty(result.getResultList())) {
			return R.ok(result);
		}
		return R.ok();
	}


	@GetMapping("/address/{level}/{code}")
	@ApiModelProperty("获取地址信息")
	public R<AddressVo> address(@PathVariable("level") Long level, @PathVariable("code") String code) {
		return R.ok(this.jdAddressService.getAddressInfo(level, code));
	}


	@PostMapping("/saleableCheck")
	@ApiModelProperty("下单前校验")
	public R<String> jdProductSaleableCheck(@RequestBody JdSaleableCheckVo jdSaleableCheckVo) {
		if (jdSaleableCheckVo.getSupplierCode().equals(ThirdIndustrialEnum.JD.getCode())) {
			return R.ok(this.jdIopProductService.totalSaleCheck(jdSaleableCheckVo));
		}
		return R.ok();
	}

	@GetMapping("/platformList")
	@ApiModelProperty("平台列表")
	public R<List<PlatformInfoDTO>> platformList(HttpServletRequest request, String materialCode) {
		String apiKey = request.getHeader(OpenPlatformParamEnum.API_KEY.getValues());
		// 获取账套对应的apiKey
		OpenPlatformBuyer buyer = openPlatformBuyerDao.getByApiKey(apiKey);
		if (ObjectUtil.isEmpty(buyer)) {
			return R.ok();
		}

//		if (StringUtils.isBlank(materialCode)) {
//			return R.fail("物料编码不能为空");
//		}

		return R.ok(commonIndustrialProductService.getPlatformList(buyer.getAccountBookId(), materialCode));
	}

	@PostMapping("/uploadJdCategory")
	public R uploadJdCategory(MultipartHttpServletRequest request) {
		MultipartFile file = request.getFile("importFile");
		if (ObjectUtil.isEmpty(file)) {
			return R.fail("上传文件不能为空");
		}
		//判断文件类型
		String fileName = file.getOriginalFilename();
		if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
			return R.fail("导入列表请上传excel类型文件");
		}

		try {
			AnalysisEventListener<JDCategoryUpLoad> listener = ExcelReadUtil.getListener(openProductService::uploadJdCategory);
			// 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
			EasyExcel.read(file.getInputStream(), JDCategoryUpLoad.class, listener).sheet().headRowNumber(1).doRead();
		} catch (IOException e) {
			return R.fail("导入失败！" + e);
		}
		return R.ok();
	}
}
