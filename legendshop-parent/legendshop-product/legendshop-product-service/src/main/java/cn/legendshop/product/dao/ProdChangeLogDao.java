package cn.legendshop.product.dao;

import cn.legendshop.product.api.dto.ProdChangeLogDTO;
import cn.legendshop.product.api.entity.OpenMaterialProductRel;
import cn.legendshop.product.api.entity.ProdChangeLog;
import cn.legendshop.product.api.query.open.OpenBindProdPageQuery;
import cn.legendshop.product.api.vo.open.OpenBindProdVo;
import com.legendshop.dao.Dao;
import com.legendshop.dao.GenericDao;
import com.legendshop.dao.support.PageSupport;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商品变更记录DAO
 * <AUTHOR>
 */
public interface ProdChangeLogDao extends Dao<ProdChangeLog, Long> {

	/**
	 * 保存日志，分表保存
	 */
	Long saveProdChangeLog(ProdChangeLog prodChangeLog, String logTableName);

	/**
	 * 通过请求日期获取表名，按月存放数据。
	 * @param RequestDate
	 * @return
	 */
	String getLogTableName(Date RequestDate);

	/**
	 * 判断表是否存在，不存在则生成表。
	 * @param logTableName
	 * @return
	 */
	Boolean logTableExist(String logTableName);

	/**
	 * 查询商品变更日志
	 * @param prodChangeLogDTO
	 * @return
	 */
	PageSupport<ProdChangeLog> queryProdChangeLog(ProdChangeLogDTO prodChangeLogDTO);

	/**
	 * 查询商品变更日志（特钢）
	 * @param prodChangeLogDTO
	 * @return
	 */
	PageSupport<ProdChangeLog> queryTGProdChangeLog(ProdChangeLogDTO prodChangeLogDTO);

	List<ProdChangeLog> queryPriceHistory(ProdChangeLogDTO prodChangeLogDTO);
}
