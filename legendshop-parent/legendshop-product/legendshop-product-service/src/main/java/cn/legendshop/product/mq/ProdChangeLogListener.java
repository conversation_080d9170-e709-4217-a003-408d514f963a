package cn.legendshop.product.mq;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.rabbitmq.constants.ProdChangeLogMQConstant;
import cn.legendshop.product.api.client.OpenProductClient;
import cn.legendshop.product.api.dto.ProdChangeLogDTO;
import cn.legendshop.product.api.dto.SameProductPriceMQDTO;
import cn.legendshop.product.api.entity.IndustrialProduct;
import cn.legendshop.product.api.entity.ProdChangeLog;
import cn.legendshop.product.api.entity.ProdPriceContacts;
import cn.legendshop.product.api.entity.SameProductPrice;
import cn.legendshop.product.api.query.open.SameProductQuery;
import cn.legendshop.product.api.vo.ProdPriceContactsVo;
import cn.legendshop.product.dao.ProdChangeLogDao;
import cn.legendshop.product.dao.ProdPriceContactsDao;
import cn.legendshop.product.dao.SameProductPriceDao;
import cn.legendshop.search.api.dto.IndustrialProductForErpDocumentDTO;
import cn.legendshop.user.api.client.SsiteMessageClient;
import cn.legendshop.user.api.client.UserDetailClient;
import cn.legendshop.user.api.client.auth.AdminUserClient;
import cn.legendshop.user.api.client.v2.UserWxOpenClient;
import cn.legendshop.user.api.dto.SiteMessageDTO;
import cn.legendshop.user.api.entity.AdminUserRole;
import cn.legendshop.user.api.entity.UserWxOpenId;
import cn.legendshop.wxOfficialAccount.api.client.CommonToolsWxOfficialAccountClient;
import cn.legendshop.wxOfficialAccount.api.dto.SenMessageDTO;
import cn.legendshop.wxOfficialAccount.api.enums.WxMessageTemplateEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.legendshop.model.entity.AdminUser;
import com.legendshop.model.entity.UserDetail;
import com.legendshop.util.AppUtils;
import com.mysql.jdbc.StringUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.mockito.internal.matchers.Same;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description: 商品价格变化日志记录监听者
 * <AUTHOR>
 **/
@Slf4j
@Component
public class ProdChangeLogListener {

    @Autowired
    private ProdChangeLogDao prodChangeLogDao;
    
    @Autowired
    private SameProductPriceDao sameProductPriceDao;
    
    @Autowired
    private ProdPriceContactsDao prodPriceContactsDao;

    @Autowired
    private UserWxOpenClient userWxOpenClient;
    
    @Autowired
    private UserDetailClient userDetailClient;
    
    @Autowired
    private SsiteMessageClient siteMessageClient;
    
    @Autowired
    private CommonToolsWxOfficialAccountClient commonToolsWxOfficialAccountClient;

    @Autowired
    private OpenProductClient openProductClient;

    @Autowired
    private AdminUserClient adminUserClient;

    /**
     * 保存商品变更日志MQ监听事件
     *
     * @param prodChangeLogDTO
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = ProdChangeLogMQConstant.CREATE_PROD_CHANGELOG_QUEUE, durable = "true"),
            exchange = @Exchange(
                    value = ProdChangeLogMQConstant.PROD_CHANGELOG_EXCHANGE,
                    ignoreDeclarationExceptions = "true",
                    type = ExchangeTypes.TOPIC),
            key = {ProdChangeLogMQConstant.CREATE_PROD_CHANGELOG_ROUTE_KEY}
    ),concurrency = "10")
    public void listenSaveThirdLog(ProdChangeLogDTO prodChangeLogDTO, Channel channel, Message message) throws IOException {
        if (ObjectUtil.isNull(prodChangeLogDTO)) {
            log.warn("mq接受对象prodChangeLogDTO为空。");
            return;
        }
        log.info("prodChangeLogDTO==={}",prodChangeLogDTO);

        try {
            String logTableName = prodChangeLogDao.getLogTableName(prodChangeLogDTO.getRequestDate());

            if(prodChangeLogDao.logTableExist(logTableName))
            {
                ProdChangeLog prodChangeLog = new ProdChangeLog();
                BeanUtil.copyProperties(prodChangeLogDTO, prodChangeLog);
                prodChangeLogDao.saveProdChangeLog(prodChangeLog, logTableName);
            }
            else{
                log.error("日志保存出错，分表规则不成功，没有找到表！");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }

    /**
     * 保存中特电商采购比价记录MQ监听事件
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = ProdChangeLogMQConstant.CREATE_SAME_PRODUCT_PRICE_QUEUE, durable = "true"),
            exchange = @Exchange(
                    value = ProdChangeLogMQConstant.SAME_PRODUCT_PRICE_EXCHANGE,
                    ignoreDeclarationExceptions = "true",
                    type = ExchangeTypes.TOPIC),
            key = {ProdChangeLogMQConstant.CREATE_SAME_PRODUCT_PRICE_ROUTE_KEY}
    ),concurrency = "10")
    public void listenSaveSameProductPriceLog(List<SameProductPriceMQDTO> mqMessages, Channel channel, Message message) throws IOException {
        if (CollectionUtils.isEmpty(mqMessages)) {
            log.warn("比价记录mq接受对象mqMessages为空。");
            return;
        }
        log.info("进入比价记录MQ，记录商品为:{}", JSON.toJSON(mqMessages));

        for (SameProductPriceMQDTO mqMessage : mqMessages) {
            try {
                List<SameProductPrice> sameProductPriceList = new ArrayList<>();

                IndustrialProduct product = openProductClient.getByShopIdAndSkuId(mqMessage.getOriginalShopId(), mqMessage.getOriginalSkuId());
                if(AppUtils.isBlank(product)){
                    continue;
                }

                // 1. 记录原始商品到比价表
                SameProductPrice originalProductPrice = new SameProductPrice();
                originalProductPrice.setShopId(mqMessage.getOriginalShopId());
                originalProductPrice.setProdId(product.getId());
                originalProductPrice.setSkuId(mqMessage.getOriginalSkuId());
                originalProductPrice.setSellPrice(product.getPrice());
                originalProductPrice.setSubNumber(mqMessage.getSubNumber());
                originalProductPrice.setProductName(product.getName());
                originalProductPrice.setModel(product.getModel());
                sameProductPriceList.add(originalProductPrice);

                // 2. 查询相同商品列表（包含所有店铺）
                SameProductQuery sameProductQuery = new SameProductQuery();
                sameProductQuery.setAccountBookId(mqMessage.getAccountBookId());
                sameProductQuery.setProdId(product.getId());
                sameProductQuery.setShopId(mqMessage.getOriginalShopId());
                R<List<IndustrialProductForErpDocumentDTO>> listR = openProductClient.querySameProductList(sameProductQuery);

                // 如果没找到相同商品则不记录
                if (!listR.isSuccess() || CollectionUtil.isEmpty(listR.getResult())) {
                    continue;
                }

                // 3. 过滤原始店铺
                List<IndustrialProductForErpDocumentDTO> filteredList = listR.getResult().stream()
                        .filter(dto -> !mqMessage.getOriginalShopId().equals(dto.getShopId()))
                        .collect(Collectors.toList());

                // 4. 先按店铺取最低价，再全局取前三
                Map<Long, IndustrialProductForErpDocumentDTO> shopMinPriceMap = filteredList.stream()
                        .collect(Collectors.toMap(
                                IndustrialProductForErpDocumentDTO::getShopId,
                                Function.identity(),
                                (existing, replacement) -> existing.getSupplierSalePrice().compareTo(replacement.getSupplierSalePrice()) <= 0 ? existing : replacement
                        ));

                List<IndustrialProductForErpDocumentDTO> competitiveProducts = shopMinPriceMap.values().stream()
                        .sorted(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice))
                        .limit(3)
                        .collect(Collectors.toList());

                // 5. 记录竞品价格
                competitiveProducts.forEach(topProduct -> {
                    SameProductPrice record = new SameProductPrice();
                    record.setShopId(topProduct.getShopId());
                    record.setProdId(topProduct.getProdId());
                    record.setSkuId(topProduct.getProductCode());
                    record.setSellPrice(topProduct.getSupplierSalePrice());
                    record.setProductName(topProduct.getProductName());
                    record.setModel(topProduct.getTypeGauge());
                    record.setSubNumber(mqMessage.getSubNumber());
                    sameProductPriceList.add(record);
                });

                // 保存比价结果
                if (CollectionUtils.isNotEmpty(sameProductPriceList)) {
                    String logTableName = "ls_same_product_price";
                    sameProductPriceDao.batchSaveSameProductPrice(sameProductPriceList,logTableName);
                }
            } catch (Exception e) {
                log.error("处理比价消息失败: {}", message, e);
                // 可以添加重试逻辑或错误处理
            } finally {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
        }
    }

    /**
     * 指定商品价格变化微信公众号提醒MQ监听事件
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = ProdChangeLogMQConstant.CREATE_PROD_PRICE_CONTACTS_QUEUE, durable = "true"),
            exchange = @Exchange(
                    value = ProdChangeLogMQConstant.PROD_PRICE_CONTACTS_EXCHANGE,
                    ignoreDeclarationExceptions = "true",
                    type = ExchangeTypes.TOPIC),
            key = {ProdChangeLogMQConstant.CREATE_PROD_PRICE_CONTACTS_ROUTE_KEY}
    ),concurrency = "5")
    public void listenProdPriceWXMessage(ProdPriceContactsVo vo, Channel channel, Message message) throws IOException {
        if (ObjectUtil.isNull(vo)) {
            log.warn("指定商品价格微信公众号提醒mq接受对象vo为空。");
            return;
        }
        log.info("进入指定商品价格微信公众号提醒MQ，入参:{}", vo);

        try {
            String type = "商品价格变更通知";
            String reason ="指定商品价格发生变更";

            if(AppUtils.isNotBlank(vo.getBidPrice())){
                if(vo.getBidPrice().compareTo(vo.getAfterPrice()) > 0 && vo.getBidPrice().compareTo(vo.getBeforePrice()) > 0){
                    return;
                }
            }

            // 根据姓名获取到userid集合
            String contacts = vo.getContacts();
            List<AdminUser> adminUsers = new ArrayList<>();
            R<List<AdminUser>> adminResult = adminUserClient.getAdminUser(contacts);
            if (adminResult.isSuccess() && CollectionUtil.isNotEmpty(adminResult.getResult())) {
                adminUsers = adminResult.getResult();
            }
            List<String> userIds = adminUsers.stream()
                    .map(AdminUser::getId)
                    .collect(Collectors.toList());
            log.info("微信要通知的userid列表为{}",userIds);

            // 如果用户ID列表不为空，则发送消息提醒
            if(CollectionUtils.isNotEmpty(userIds)){
                String text = String.format("%s%s价格发生变更，原价格为%s，变更后价格为%s，价格变化率为 %s",
                        vo.getShopName(), vo.getSkuId(), vo.getBeforePrice(), vo.getAfterPrice(), vo.getPriceChangeRate());

                if (AppUtils.isNotBlank(vo.getMaterialCode())) {
                    text += String.format("，物料编码为%s", vo.getMaterialCode());
                }

                if (AppUtils.isNotBlank(vo.getBidPrice())) {
                    text += String.format("，中标价格为%s。", vo.getBidPrice());
                }

                Long msgId = siteMessageClient.send(new SiteMessageDTO("系统","后台管理员", type, text)).getResult();
                for (String userId : userIds) {
                    // 公众号通知
                    if (AppUtils.isNotBlank(userId)){
                        List<UserWxOpenId> result = userWxOpenClient.queryByUserId(userId).getResult();
                        log.info("微信要通知的OPENID列表为{}", result);
                        // 一个userId可能有多个人要通知
                        if (CollectionUtils.isNotEmpty(result)) {
                            for (UserWxOpenId item : result) {
                                if (AppUtils.isNotBlank(result)){
                                    SenMessageDTO senMessageDTO = new SenMessageDTO();
                                    senMessageDTO.setTouser(item.getOpenId());  //指定用户
                                    senMessageDTO.setTemplateId(WxMessageTemplateEnum.NEW_TICKET_NOTICE.getCode()); //指定模板
                                    senMessageDTO.setUrl("https://ykhs365.com/message?messageId="+msgId); // 详情跳转地址
                                    JSONObject jsonObject = new JSONObject();  // 模板内容
                                    jsonObject.put("thing10", type); //工单类别
                                    jsonObject.put("thing6",reason ); //发起原因
                                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm");
                                    String format = outputFormat.format(new Date());
                                    jsonObject.put("time7", format);//发送时间
                                    senMessageDTO.setData(jsonObject);
                                    String result1 = commonToolsWxOfficialAccountClient.sendMessage(senMessageDTO).getResult();
                                    log.info("发送指定商品价格变更通知结果: {}", result1);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}

































