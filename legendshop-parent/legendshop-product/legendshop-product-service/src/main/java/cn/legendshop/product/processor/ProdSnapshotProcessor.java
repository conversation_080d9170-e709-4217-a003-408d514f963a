package cn.legendshop.product.processor;

import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.event.EventProcessor;
import cn.legendshop.common.core.event.ProductEventProcessor;
import cn.legendshop.order.client.SubItemServiceClient;
import cn.legendshop.product.service.ProductSnapshotService;
import com.legendshop.model.entity.SubItem;
import com.legendshop.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("productSnapshotProcessor")
public class ProdSnapshotProcessor implements ProductEventProcessor<List<String>> {

	private Logger log = LoggerFactory.getLogger(ProdSnapshotProcessor.class);

	@Autowired
	private SubItemServiceClient subItemServiceClient;

	@Autowired
	private ProductSnapshotService productSnapshotService;

	/**
	 * task 为subId订单号集合,异步执行
	 */
	@Override
	@Async
	public void process(List<String> task) {
		if(AppUtils.isBlank(task)){
			return ;
		}
		log.debug("<----- Prod Snapshot  Processor starting 商品快照处理");
		for(String subNumber: task){
			log.debug("processor task by subnumber {}", subNumber);
			List<SubItem> subItems = subItemServiceClient.getSubItemBySubNumber(subNumber, SecurityConstants.FROM_IN).getResult();
			if(AppUtils.isNotBlank(subItems)){
				for (SubItem subItem : subItems) {
					try {
						//保存商品快照
						productSnapshotService.saveProdSnapshot(subItem);
					} catch (Exception e) {
						e.printStackTrace();
						log.error("product Snapshot save fail {}",e.getMessage());
					}
				}
			}
		}
		log.debug(" end ---->   ");
	}
}
