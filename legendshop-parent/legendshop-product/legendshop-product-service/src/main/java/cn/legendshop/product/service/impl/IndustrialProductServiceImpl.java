package cn.legendshop.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.IndustrialTypeEnum;
import cn.legendshop.common.core.enums.IsMroSelfEnum;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.SqlUtil;
import cn.legendshop.common.dao.IndustrialOuterShopSwitchSettleDao;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.open.dto.SkuInfoDTO;
import cn.legendshop.open.enums.OpenPlatformSwitchEnum;
import cn.legendshop.open.factory.OpenPlatformServiceFactory;
import cn.legendshop.open.request.product.SellPriceRequest;
import cn.legendshop.open.sevice.OpenPlatformProductService;
import cn.legendshop.order.client.v2.QuotationClient;
import cn.legendshop.product.api.bo.ProductBO;
import cn.legendshop.product.api.bo.SkuBO;
import cn.legendshop.product.api.constant.ProductConstant;
import cn.legendshop.product.api.constant.ProductServiceConstant;
import cn.legendshop.product.api.dto.*;
import cn.legendshop.product.api.dto.product.OpenProductDetailDTO;
import cn.legendshop.product.api.dto.product.PreSearchQueryDTO;
import cn.legendshop.product.api.dto.product.SkuIdDTO;
import cn.legendshop.product.api.entity.IndustrialProduct;
import cn.legendshop.product.api.entity.MaterialProductRel;
import cn.legendshop.product.api.entity.ProdPriceContacts;
import cn.legendshop.product.api.enums.IndexUpdateFlagEnum;
import cn.legendshop.product.api.enums.IndustrialProdTypeEnum;
import cn.legendshop.product.api.enums.MroIndustrialProdStatusEnum;
import cn.legendshop.product.api.query.ProductConsultQuery;
import cn.legendshop.product.api.query.ProductDetailQuery;
import cn.legendshop.product.api.query.product.ErpPreSearchQuery;
import cn.legendshop.product.api.query.product.ErpProductQuery;
import cn.legendshop.product.api.query.product.ThridIndustrialProductSimpleQuery;
import cn.legendshop.product.api.vo.ProdPriceContactsVo;
import cn.legendshop.product.api.vo.SupplierCategoryBindErpVo;
import cn.legendshop.product.api.vo.updataListVo;
import cn.legendshop.product.config.ProductThreadPoolFactory;
import cn.legendshop.product.dao.*;
import cn.legendshop.product.mq.producer.OuterIndustrialProductProducerService;
import cn.legendshop.product.mq.producer.ProdChangeLogProducerService;
import cn.legendshop.product.service.*;
import cn.legendshop.product.service.convert.IndustrialProductConverter;
import cn.legendshop.product.service.convert.ThridIndustrialProductConverter;
import cn.legendshop.product.strategy.ThirdIndustrialStrategyContext;
import cn.legendshop.search.api.client.IndustrialProductIndexClient;
import cn.legendshop.search.api.client.SimilarityProductTaskClient;
import cn.legendshop.search.api.dto.ProductSimilarityTaskDTO;
import java.util.Collections;
import cn.legendshop.user.api.bo.ShopDetailBO;
import cn.legendshop.user.api.client.PaintBuyerClient;
import cn.legendshop.user.api.client.v2.ShopClient;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageParams;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.update.LambdaDelete;
import com.legendshop.dao.support.update.LambdaUpdate;
import com.legendshop.model.constant.ConcreteEnum;
import com.legendshop.model.constant.PayMannerEnum;
import com.legendshop.model.constant.ProductStatusEnum;
import com.legendshop.model.entity.*;
import com.legendshop.util.AppUtils;
import com.legendshop.util.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.legendshop.product.api.constant.ProductConstant.MRO_PLATFORM_ID;
import static cn.legendshop.product.api.constant.ProductServiceConstant.OUTER_INDUSTRIAL_INSTORE_LOCK_KEY;
import static cn.legendshop.product.api.enums.IndexUpdateFlagEnum.UPDATED;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.OFF_SHELF;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.ON_SHELF;

/**
 * 第三方商品数据表(OuterProduct)表服务实现类
 *
 * <AUTHOR> wu
 * @since 2023-05-31 14:59:54
 */
@Service
@Slf4j
@AllArgsConstructor
public class IndustrialProductServiceImpl implements IndustrialProductService {

	private final IndustrialProductDao industrialProductDao;
	private final IndustrialProductConverter industrialProductConverter;
	private final SkuDao skuDao;
	private final ProductDao productDao;
	private final DataSourceTransactionManager dataSourceTransactionManager;
	private final TransactionDefinition transactionDefinition;
	private final OuterIndustrialProductProducerService outerIndustrialProductProducerService;
	private final MaterialProductRelDao materialProductRelDao;
	private final IndustrialOuterShopConfigService industrialOuterShopConfigService;
	private final OpenPlatformServiceFactory openPlatformServiceFactory;
	private final CategoryDao categoryDao;
	private final ShopClient shopClient;
	private final RedissonClient redissonClient;
	private final ShopDetailDao shopDetailDao;
	private final ImgFileDao imgFileDao;
	private final IndustrialProductIndexClient industrialProductIndexClient;
	private final ThridIndustrialProductConverter thridIndustrialProductConverter;
	private final MsCategoryService categoryService;
	private final ThirdPartyProductAdjustmentService thirdPartyProductAdjustmentService;
	private final IndustrialCategoryService industrialCategoryService;
	private final FavoriteProductService favoriteProductService;
	private final ProductConsultService productConsultService;
	private final ThirdIndustrialStrategyContext thirdIndustrialStrategyContext;
	private final ProdPropImageDao prodPropImageDao;
	private final SkuService skuService;
	private final BrandDao brandDao;
	private final ProductThreadPoolFactory productThreadPoolFactory;

	private final MessagePoolProductService messagePoolProductService;

	private final IndustrialOuterShopSwitchSettleDao industrialOuterShopSwitchSettleDao;

	private final PaintBuyerClient paintBuyerClient;

	private final UserAddressService userAddressService;

	private final ProdChangeLogProducerService prodChangeProducerService;

	private final QuotationClient quotationClient;

	private final SimilarityProductTaskClient similarityProductTaskClient;

	private final ProdPriceContactsService prodPriceContactsService;

	@Override
	public R<String> saveOrUpdate(IndustrialProductDTO industrialProductDTO) {
		//初始数据
		int update = 0;
		//获取第三方配置
		IndustrialOuterShopConfig outerShopConfig = industrialOuterShopConfigService.getByShopId(industrialProductDTO.getShopId());
		//获取商品信息
		IndustrialProduct industrialProduct = this.industrialProductDao.getByShopIdAndSkuId(industrialProductDTO.getSkuId(), industrialProductDTO.getShopId());

		//更新MRO商品表
		this.inStore(industrialProduct, outerShopConfig);

		//同步消息给ERP
		sendLatestProdInfoToErp(industrialProductDTO, update, industrialProduct, outerShopConfig);

		//更新es
		this.outerIndustrialProductProducerService.updateIndustrialIndex(industrialProduct.getShopId() + StrUtil.COLON + industrialProduct.getSkuId());

//		// 更新询比价报价单上架信息
//		quotationClient.updateQuotationProduct(industrialProductDTO.getSkuId());
		return R.ok();
	}

	@Override
	public R<String> save(IndustrialProduct industrialProduct) {
		industrialProductDao.saveProdByShopId(industrialProduct, industrialProduct.getShopId());
		return R.ok();
	}

	@Override
	public IndustrialSearchDocumentDTO buildIndex(IndustrialProduct industrialProduct) {

		String shopName = null;
		String innerCode = null;
		String nickName = null;
		Long shopId = industrialProduct.getShopId();
		StringBuilder stringBuilder = new StringBuilder();
		List<String> skuMaterialCodeRes = new ArrayList<>();
		IndustrialSearchDocumentDTO industrialSearchDocumentDTO = new IndustrialSearchDocumentDTO();

		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(shopId);

		//实体转换
		thridIndustrialProductConverter.toProduct(industrialProduct, industrialSearchDocumentDTO);

		if (ObjectUtil.isNotEmpty(config) && !config.getShopId().equals(MRO_PLATFORM_ID)) {
			shopName = config.getShopName();
			innerCode = config.getInnerCode();
			nickName = config.getNickName();

			//erp 绑定关系设置
			List<MaterialProductRel> materialProductRels = materialProductRelDao.queryBinding(industrialProduct.getSkuId(), innerCode);
			if (CollUtil.isNotEmpty(materialProductRels)) {
				skuMaterialCodeRes.addAll(materialProductRels.stream().map(MaterialProductRel::getMaterialCode).distinct().collect(Collectors.toList()));
				String boundMaterialCode = materialProductRels.stream().map(MaterialProductRel::getMaterialCode).collect(Collectors.joining(StrUtil.COMMA));
				//设置ERP绑定的信息
				industrialSearchDocumentDTO.setBoundMaterialCode(boundMaterialCode);
				industrialSearchDocumentDTO.setBound(Boolean.TRUE);

			}
			industrialSearchDocumentDTO.setSupplierName(config.getInnerName());
			stringBuilder.append(CollectionUtils.isEmpty(skuMaterialCodeRes) ? "" : StringUtils.join(skuMaterialCodeRes, " "));
			industrialSearchDocumentDTO.setIsMroSelf(IsMroSelfEnum.THIRD_PROD.getType());
			industrialSearchDocumentDTO.setPlatformId(config.getPlatformId());

			//添加第三方类目
			SupplierCategoryBindErpVo thirdCategoryByCode = industrialCategoryService.getThirdCategoryByCode(industrialProduct.getCategory(), shopId);
			if (ObjectUtil.isNotNull(thirdCategoryByCode)) {
				thridIndustrialProductConverter.first(thirdCategoryByCode, industrialSearchDocumentDTO);
				List<SupplierCategoryBindErpVo> children = thirdCategoryByCode.getChildren();
				if (CollectionUtils.isNotEmpty(children)) {
					thridIndustrialProductConverter.second(children.get(0), industrialSearchDocumentDTO);
					children = children.get(0).getChildren();
					if (CollectionUtils.isNotEmpty(children)) {
						thridIndustrialProductConverter.third(children.get(0), industrialSearchDocumentDTO);
						if (CollectionUtils.isNotEmpty(children)) {
							thridIndustrialProductConverter.forth(children.get(0), industrialSearchDocumentDTO);
						}
					}
				}
				stringBuilder.append(" " + industrialSearchDocumentDTO.categoryToString());
			}
		}

		//自营平台
		if (ObjectUtil.isEmpty(config) || config.getShopId().equals(MRO_PLATFORM_ID)) {
			ShopDetailBO shopDetailBO = shopClient.getShopDetailByShopId(shopId, null).getResult();
			shopName = shopDetailBO.getSiteName();
			industrialSearchDocumentDTO.setSupplierName(shopDetailBO.getSiteName());
			industrialSearchDocumentDTO.setAdjustedPrice(industrialSearchDocumentDTO.getSupplierSalePrice());
			industrialSearchDocumentDTO.setIsMroSelf(IsMroSelfEnum.MRO_SELF.getType());
			industrialSearchDocumentDTO.setPlatformId(MRO_PLATFORM_ID);
		}

		//组装最基本的索引信息
		industrialSearchDocumentDTO.setShopName(shopName);
		industrialSearchDocumentDTO.setIndustrialProdId(industrialProduct.getId());
		//找到该商家对应的枚举
		industrialSearchDocumentDTO.setSupplier(innerCode);

		//图片处理一下，只需要一张
		if (StrUtil.isNotBlank(industrialSearchDocumentDTO.getImage())) {
			String[] split = industrialSearchDocumentDTO.getImage().split(StrUtil.COMMA);
			industrialSearchDocumentDTO.setImage(split[0]);
		}

		// 根据关联的  进行添加一 二 三 级
		Long currentCategoryId = industrialProduct.getCategoryMroSupplierId();
		Category currentCategory = null;

		//调价id可能会出现为空的情况
		//调价id设为0是MRO平台
		if (currentCategoryId != null && currentCategoryId > ProductConstant.MRO_ADJUST_ID) {
			do {
				currentCategory = categoryService.getCategoryById(currentCategoryId);
				if (AppUtils.isNotBlank(currentCategory)) {
					if (currentCategory.getGrade() == 1) {
						industrialProductConverter.first(currentCategory, industrialSearchDocumentDTO);
					} else if (currentCategory.getGrade() == 2) {
						industrialProductConverter.second(currentCategory, industrialSearchDocumentDTO);
					} else {
						industrialProductConverter.third(currentCategory, industrialSearchDocumentDTO);
					}
					stringBuilder.append(currentCategory.getName() + " ");
					currentCategoryId = currentCategory.getParentId();
				}
			} while (AppUtils.isNotBlank(currentCategory));
		}


		String[] category = industrialProduct.getCategory().split(StrUtil.COLON);
		if (category.length == 1) {
//			SupplierCategoryBindErpVo thirdCategoryByCode = thirdIndustrialStrategyContext.getStrategy(shopId).getThirdCategoryByCode(industrialProduct.getCategory());
//			SupplierCategoryBindErpVo thirdCategoryByCode = industrialCategoryService.getThirdCategoryByCode(industrialProduct.getCategory(), shopId);
//			if (ObjectUtil.isNotNull(thirdCategoryByCode)) {
//				thridIndustrialProductConverter.first(thirdCategoryByCode, industrialSearchDocumentDTO);
//				List<SupplierCategoryBindErpVo> children = thirdCategoryByCode.getChildren();
//				if (CollectionUtils.isNotEmpty(children)) {
//					thridIndustrialProductConverter.second(children.get(0), industrialSearchDocumentDTO);
//					children = children.get(0).getChildren();
//					if (CollectionUtils.isNotEmpty(children)) {
//						thridIndustrialProductConverter.third(children.get(0), industrialSearchDocumentDTO);
//						if (CollectionUtils.isNotEmpty(children)) {
//							thridIndustrialProductConverter.forth(children.get(0), industrialSearchDocumentDTO);
//						}
//					}
//				}
//				stringBuilder.append( " " + industrialSearchDocumentDTO.categoryToString());
//			}
		}

		//组装调价ID
		industrialSearchDocumentDTO.setMroCategoryId(industrialProduct.getCategoryMroSupplierId());
		industrialSearchDocumentDTO.setAdjustId(industrialProduct.getCategoryMroSupplierId());

		if (StrUtil.isNotBlank(nickName)) {
			stringBuilder.append(nickName + " ");
		}

		if (StrUtil.isNotBlank(industrialProduct.getBrandName())) {
			stringBuilder.append(industrialProduct.getBrandName() + " ");
		}
		stringBuilder.append(shopName + " " + industrialProduct.getSkuId() + " " + industrialProduct.getName() + " ");

		if (StrUtil.isNotBlank(industrialProduct.getModel())) {
			stringBuilder.append(industrialProduct.getModel()).append(" ");
		}
		// 类目信息
		stringBuilder.append(industrialSearchDocumentDTO.categoryToString());
		//补充关键字
		industrialSearchDocumentDTO.setKeyword(stringBuilder.toString());
		industrialSearchDocumentDTO.setProdType(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode());
		industrialSearchDocumentDTO.setForMro(Boolean.TRUE);
		industrialSearchDocumentDTO.setBound(CollectionUtils.isEmpty(skuMaterialCodeRes) ? Boolean.FALSE : Boolean.TRUE);
		industrialSearchDocumentDTO.setId(industrialSearchDocumentDTO.getShopId() + StrUtil.COLON + industrialProduct.getSkuId());
		industrialSearchDocumentDTO.setStatus(industrialProduct.getState() == 1 ? ON_SHELF.getThirdStatus() : OFF_SHELF.getThirdStatus());
		industrialSearchDocumentDTO.setProductNameSuggest(industrialSearchDocumentDTO.getProductNameSuggest() + StrUtil.SPACE + industrialSearchDocumentDTO.getProductCode());

		//计算调价后的价格，目前只做排序使用
		if (Objects.nonNull(industrialProduct.getCategoryMroSupplierId()) && industrialProduct.getCategoryMroSupplierId() > ProductConstant.MRO_ADJUST_ID) {
			this.calculatePrice(industrialSearchDocumentDTO, null);
		}


		return industrialSearchDocumentDTO;
	}

	private void calculatePrice(IndustrialSearchDocumentDTO industrialSearchDocumentDTO, String userId) {
		ProductPriceAdjustmentDTO productPriceAdjustmentDTO = new ProductPriceAdjustmentDTO().setProdPriceAdjustmentData(Stream.of(thridIndustrialProductConverter.toCalculateProductPrice(industrialSearchDocumentDTO)).collect(Collectors.toList()));
		productPriceAdjustmentDTO.setUserId(userId);
		List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData> result = thirdPartyProductAdjustmentService.calculateProductPriceAdjustment(productPriceAdjustmentDTO);
		for (ProductPriceAdjustmentDTO.ProductPriceAdjustmentData productPriceAdjustmentData : result) {
			industrialSearchDocumentDTO.setAdjustedPrice(productPriceAdjustmentData.getTotalPrice());
			industrialSearchDocumentDTO.setSortPrice(productPriceAdjustmentData.getTotalPrice());
		}
	}

	private void sendLatestProdInfoToErp(IndustrialProductDTO industrialProductDTO, int update, IndustrialProduct industrialProduct, IndustrialOuterShopConfig outerShopConfig) {
		if (update > 0) {
			//发送mq通知erp更新商品
			log.info("发送mq通知erp更新商品");
			MaterialProductRel rel = this.materialProductRelDao.getByProperties(new EntityCriterion().eq("supplier", outerShopConfig.getInnerCode()).eq("productCode", industrialProductDTO.getSkuId()).limit(1));
			if (ObjectUtil.isNotEmpty(rel)) {
				outerIndustrialProductProducerService.sendProductUpdateErpNotice(industrialProduct.getShopId() + StrUtil.COLON + industrialProduct.getSkuId());
			}
		}
	}

	@Override
	public IndustrialProduct getByShopIdAndSkuId(Long shopId, String skuId) {
		return this.industrialProductDao.getByShopIdAndSkuId(skuId, shopId);
	}

	@Override
	public IndustrialProduct getByShopIdAndProdId(Long shopId, Long prodId) {
		return this.industrialProductDao.getByShopIdAndProdId(prodId, shopId).get(0);
	}

	@Override
	public List<IndustrialProduct> queryByShopIdAndSkuId(List<Long> shopIds, List<String> skuIds) {
		if (CollUtil.isEmpty(shopIds) || CollUtil.isEmpty(skuIds)) {
			return null;
		}

		List<IndustrialProduct> industrialProducts = new ArrayList<>();
		for (Long shopId : shopIds) {
			industrialProducts.addAll(this.industrialProductDao.queryByShopIdAndSkuId(skuIds, shopId));
		}
		return industrialProducts;
	}

	@Override
	public List<IndustrialProduct> queryByShopIdAndId(List<Long> shopIds, List<Long> prodIds) {
		if (CollUtil.isEmpty(shopIds) || CollUtil.isEmpty(prodIds)) {
			return null;
		}
		List<Long> distinctShopIds = shopIds.stream().distinct().collect(Collectors.toList());
		List<IndustrialProduct> industrialProducts = new ArrayList<>();
		for (Long shopId : distinctShopIds) {
			industrialProducts.addAll(this.industrialProductDao.queryByShopIdAndIds(prodIds, shopId));
		}
		return industrialProducts;
	}

	//入库
	public Boolean inStore(IndustrialProduct industrialProduct, IndustrialOuterShopConfig config) {

		if (industrialProduct.getProdId() != null) {
			log.debug("不需要入库,已经有商品id: " + industrialProduct.getProdId());
			return Boolean.TRUE;
		}
		//加锁
		String redisKey = OUTER_INDUSTRIAL_INSTORE_LOCK_KEY + industrialProduct.getSkuId() + StrUtil.COLON + industrialProduct.getShopId();
		TransactionStatus transactionStatus = null;

		RLock lock = redissonClient.getLock(redisKey);
		try {
			if (!lock.tryLock(2L, 10L, TimeUnit.SECONDS)) {
				log.info("已经有商品正在进行更新");
				throw new BusinessException("正在进行更新操作");
			}
		} catch (Exception e) {
			log.info("已经有商品正在进行更新");
			throw new BusinessException("正在进行更新操作");
		}
		//先检查一下是否入库成功
		IndustrialProduct checkInfo = this.industrialProductDao.getByShopIdAndSkuId(industrialProduct.getSkuId(), industrialProduct.getShopId());
		if (ObjectUtil.isEmpty(checkInfo) || checkInfo.getProdId() != null) {
			//已经入库，不需要进行下列操作
			lock.unlock();
			return Boolean.TRUE;
		}

		try {
			//手动开启事务
			transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);

			//初始化
			CommonIndustrialProdDTO product = new CommonIndustrialProdDTO();
			DateTime now = new DateTime();

			//获取商家信息
			ShopDetail shopdetailInfo = this.shopDetailDao.getById(industrialProduct.getShopId());
			//打包product信息
			productInfoPack(industrialProduct, now, shopdetailInfo, product, config);
			Long prodId = prodInStore(product);
			log.debug("入sku库：{}", prodId);
			Long skuId = skuInfoInStore(product, prodId);

			Integer seq = 1;
			String[] image = industrialProduct.getImagePath().split(StrUtil.COMMA);
			try {
				List<ImgFile> imgFileLists = Arrays.stream(image).skip(1).map(value -> {
					if (ObjectUtil.isEmpty(value)) {
						return null;
					}
					ImgFile imgFile = new ImgFile();
					imgFile.setUserName(config.getShopName());
					imgFile.setProductId(prodId);
					imgFile.setProductType(Short.valueOf("1"));
					imgFile.setFilePath(value);
					imgFile.setFileType("jpg");
					imgFile.setUpoadTime(new DateTime());
					imgFile.setStatus(1);
					imgFile.setStatus(seq);
					imgFile.setShopId(config.getShopId());
					return imgFile;
				}).collect(Collectors.toList());
				this.imgFileDao.save(imgFileLists);
			} catch (Exception e) {
				e.printStackTrace();
				log.info("图片导入失败，请检查格式是否正确");
			}


			if (industrialProduct.getProdId() == null) {
				industrialProduct.setProdId(prodId);
				industrialProduct.setMroSkuId(skuId);
				industrialProduct.setUpdateTime(new DateTime());
				industrialProduct.setUpdateFlag(IndexUpdateFlagEnum.UNUPDATE.getValue());
				this.industrialProductDao.updateProdByShopId(industrialProduct, industrialProduct.getShopId());
			}

			dataSourceTransactionManager.commit(transactionStatus);
		} catch (Exception e) {
			log.error("导入到prod表错误");
			e.printStackTrace();
			if (transactionStatus != null) {
				dataSourceTransactionManager.rollback(transactionStatus);
			}
		} finally {
			lock.unlock();
		}

		return Boolean.TRUE;
	}

	@Override
	public List<PreSearchQueryDTO> preSearchOuterProduct(ErpPreSearchQuery erpPreSearchQuery) throws UnsupportedEncodingException {
		if (StrUtil.isEmpty(erpPreSearchQuery.getSpecificParam())) {
			log.error("规格为空");
			return Collections.emptyList();
		}
		erpPreSearchQuery.setSpecificParam(URLDecoder.decode(erpPreSearchQuery.getSpecificParam(), CharsetUtil.UTF_8));
		Long shopId = industrialOuterShopConfigService.getBySupplierCode(erpPreSearchQuery.getSupplier()).getShopId();
		List<IndustrialProduct> industrialProducts = this.industrialProductDao.queryBySpecifimentParamAndShopId(erpPreSearchQuery.getSpecificParam(), shopId);
		if (CollUtil.isNotEmpty(industrialProducts)) {
			return getPreSearchQueryDTOS(industrialProducts);
		}
		String[] s = erpPreSearchQuery.getSpecificParam().split(" ");
		if (s.length >= 2) {
			List<IndustrialProduct> industrialProducts1 = this.industrialProductDao.queryBySpecifimentParamAndShopId(s[1], shopId);
			if (CollUtil.isNotEmpty(industrialProducts1)) {
//				log.info("根据模糊搜索查询成功");
				return getPreSearchQueryDTOS(industrialProducts1);
			} else {
//				log.info("查不到数据");
				return Collections.emptyList();
			}
		} else {
//			log.info("传过来的模糊规格属性为空");
			return Collections.emptyList();
		}
	}

	@Override
	public Boolean updateStatus(List<updataListVo> updataListVos, Integer flag) {
		if (MroIndustrialProdStatusEnum.findByCode(flag).getNum() == null) {
			return Boolean.FALSE;
		}
		List<Long> ids = updataListVos.stream().map(updataListVo::getId).collect(Collectors.toList());
		return SqlUtil.retBool(this.industrialProductDao.updateProperties(new LambdaUpdate<>(IndustrialProduct.class).in(IndustrialProduct::getId, ids).set(IndustrialProduct::getState, flag)));
	}

	@Override
	public R<String> updateProductStatus(OpenProductStateDTO openProductStateDTO) {
		Long shopId = null;
		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByOpenPlatformAppKey(openProductStateDTO.getAppKey());
		if (ObjectUtil.isEmpty(config)) {
			//内存没有就读数据库
			shopId = this.shopClient.getByOpenPlatformAppKey(openProductStateDTO.getAppKey()).getResult();
			if (shopId == null) {
				return R.fail("此appKey未绑定商家，请先进行绑定商家操作");
			}
		} else {
			shopId = config.getShopId();
		}

		openProductStateDTO.setShopId(shopId);
		this.outerIndustrialProductProducerService.updateProductStatus(openProductStateDTO);

		// 根据商品状态变化创建相似商品任务
		List<IndustrialProduct> industrialProducts = this.industrialProductDao.queryByShopIdAndSkuId(openProductStateDTO.getSkus(), shopId);
		for (IndustrialProduct product : industrialProducts) {
			if (openProductStateDTO.getState().equals(OFF_SHELF.getMroStatus())) {
				// 商品下架，创建删除任务
				similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(3).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, shopId, product.getId()))));
			} else if (openProductStateDTO.getState().equals(ON_SHELF.getMroStatus())) {
				// 商品上架，创建更新任务
				similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(2).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, shopId, product.getId()))));
			}
		}

//		if (1== openProductStateDTO.getState()){
//			for (String sku :openProductStateDTO.getSkus()){
//				quotationClient.updateQuotationProduct(sku);
//			}
//		}

		return R.ok();
	}

	@Override
	public void update(IndustrialProduct industrialProduct) {
		this.industrialProductDao.updateProdByShopId(industrialProduct, industrialProduct.getShopId());
	}

	@Override
	public void update(List<IndustrialProduct> industrialProducts, Long shopId) {
		this.industrialProductDao.updateProdsByShopId(industrialProducts, shopId);
	}

	@Override
	public R<String> updateProductPrice(OpenProductPriceDTO openProductPriceDTO) {
		log.info("进入商品价格更新方法。");
		Long shopId = null;
		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByOpenPlatformAppKey(openProductPriceDTO.getAppKey());
		if (ObjectUtil.isEmpty(config)) {
			//内存没有就读数据库
			shopId = this.shopClient.getByOpenPlatformAppKey(openProductPriceDTO.getAppKey()).getResult();
			if (shopId == null) {
				return R.fail("此appKey未绑定商家，请先进行绑定商家操作");
			}
		} else {
			shopId = config.getShopId();
		}

		Map<String, SkuPriceDTO> skuMap = openProductPriceDTO.getSkuPrices().stream().collect(Collectors.toMap(SkuPriceDTO::getSkuId, e -> e));
		List<IndustrialProduct> industrialProducts = this.industrialProductDao.queryByShopIdAndSkuId(new ArrayList<>(skuMap.keySet()), shopId);

		// 用于存储价格变更记录
		List<ProdChangeLogDTO> priceChangeRecords = new ArrayList<>();

		for (IndustrialProduct industrialProduct : industrialProducts) {
			SkuPriceDTO skuPriceDTO = skuMap.get(industrialProduct.getSkuId());
			log.info("商品价格更新：商品sku为:{}",industrialProduct.getSkuId());
			// 记录变更前的价格
			BigDecimal beforePrice = industrialProduct.getPrice();

			industrialProductConverter.to5(skuPriceDTO, industrialProduct);

			if (industrialProduct.getMroSkuId() != null) {
				Sku sku = skuDao.getSkuById(industrialProduct.getMroSkuId());
				if (ObjectUtil.isNotEmpty(sku)) {
					sku.setPrice(industrialProduct.getPrice().doubleValue());
					skuDao.update(sku);
				}
			}

			// 计算变更后的价格和价格变化比率
			BigDecimal afterPrice = industrialProduct.getPrice();
			BigDecimal priceChangeRate = calculatePriceChangeRate(beforePrice, afterPrice); // 调用价格变化率计算方法

			if(priceChangeRate.compareTo(BigDecimal.ZERO) != 0) {
			// 创建价格变更记录
			ProdChangeLogDTO record = new ProdChangeLogDTO();
			record.setShopId(shopId);
			record.setShopName(config != null ? config.getShopName() : "未知");
			record.setSkuId(industrialProduct.getSkuId());  // 商品sku
			record.setBeforePrice(beforePrice);  //变更前价格
			record.setAfterPrice(afterPrice);  //变更后价格
			record.setPriceChangeRate(priceChangeRate);  // 价格变化比率
			record.setRequestDate(new Date());  // 当前请求日期
			priceChangeRecords.add(record); // 将记录添加到列表
			}

		}

		this.industrialProductDao.update(industrialProducts);

		// 发送MQ消息以进行异步处理
		if(priceChangeRecords != null && !priceChangeRecords.isEmpty()) {
			priceChangeRecords.forEach(record -> {
				prodChangeProducerService.sendPriceChangeMessage(record);// 发送每个价格变更记录到MQ

				// 判断该商品是否需要提醒
				List<ProdPriceContacts> priceLogList = prodPriceContactsService.getBySkuAndShopId(record.getShopId(), record.getSkuId());
				log.info("需要进行价格变化公众号提醒的商品:{}",JSON.toJSONString(priceLogList));
				if(AppUtils.isNotBlank(priceLogList)) {
					for (ProdPriceContacts priceLog : priceLogList) {
						ProdPriceContactsVo vo = new ProdPriceContactsVo();
						vo.setShopId(priceLog.getShopId());
						vo.setSkuId(priceLog.getSkuId());
						vo.setShopName(record.getShopName());
						vo.setMaterialCode(priceLog.getMaterialCode());
						vo.setBeforePrice(record.getBeforePrice());
						vo.setAfterPrice(record.getAfterPrice());
						vo.setBidPrice(priceLog.getBidPrice());
						vo.setContacts(priceLog.getContacts());
						log.info("指定商品提醒的联系人:{}",priceLog.getContacts());
						vo.setPriceChangeRate(record.getPriceChangeRate());
						prodChangeProducerService.sendProdPriceWXMessage(vo);// 发送价格变更微信公众号提醒MQ
					}
				}
			});
		}

		industrialProducts.forEach(e -> outerIndustrialProductProducerService.updateIndustrialIndex(e.getShopId() + StrUtil.COLON + e.getSkuId()));

		List<String> skuIds = openProductPriceDTO.getSkuPrices().stream().map(SkuPriceDTO::getSkuId).collect(Collectors.toList());

		// 绑定商品通知采购商:更新价格
		messagePoolProductService.pushBindProductPriceChange(shopId, skuIds);
		return R.ok();
	}

	/**
	 * 计算价格变化率
	 */
	private BigDecimal calculatePriceChangeRate(BigDecimal beforePrice, BigDecimal afterPrice) {
		if (beforePrice == null || beforePrice.compareTo(BigDecimal.ZERO) == 0) {
			return BigDecimal.ZERO;
		}
		return afterPrice.subtract(beforePrice)
				.divide(beforePrice, 4, RoundingMode.HALF_UP);
	}

	@Override
	public R<String> openPlatformUpdateProduct(OpenProductDetailDTO productDetailDTO) {

		Long shopId = null;
		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByOpenPlatformAppKey(productDetailDTO.getAppKey());


		if (ObjectUtil.isEmpty(config)) {
			//内存没有就读数据库
			shopId = this.shopClient.getByOpenPlatformAppKey(productDetailDTO.getAppKey()).getResult();
			if (shopId == null) {
				return R.fail("此appKey未绑定商家，请先进行绑定商家操作");
			}
		} else {
			shopId = config.getShopId();
		}

		//放入第三方商品表
		IndustrialProduct industrialProduct = this.industrialProductDao.getByShopIdAndSkuId(productDetailDTO.getSkuId(), shopId);
		return inStoreCheck(productDetailDTO, shopId, config, industrialProduct);
	}

	@Override
	public CommonProdInfoDTO updateProductByProdId(Product product) {
		CommonProdInfoDTO commonProdInfoDTO = new CommonProdInfoDTO();
		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(product.getShopId());
		if (ObjectUtil.isEmpty(config)) {
			commonProdInfoDTO.setPrice(new BigDecimal(product.getCash()));
			commonProdInfoDTO.setStatus(product.getStatus());
			return commonProdInfoDTO;
		}

		log.info("更新一日未更新商品数据，prodid: {}", product.getId());
		OpenPlatformProductService openPlatformProductService = openPlatformServiceFactory.getProductServiceByShopId(product.getShopId());
		product.setUpdateTime(new Date());
		List<Sku> skuList = skuDao.getSkuByProd(product.getId());


		List<String> outerId = skuList.stream().map(Sku::getOuterId).collect(Collectors.toList());
		List<IndustrialProduct> industrialProducts = this.industrialProductDao.queryByShopIdAndSkuId(outerId, product.getShopId());


		SellPriceRequest sellPriceRequest = new SellPriceRequest();
		sellPriceRequest.setSkuIds(outerId);
		sellPriceRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		Map<String, SkuInfoDTO> skuPriceInfoMap = null;
		try {
			skuPriceInfoMap = openPlatformProductService.getSellPrice(sellPriceRequest).getResult().stream().collect(Collectors.toMap(SkuInfoDTO::getSkuId, java.util.function.Function.identity()));
		} catch (Exception e) {
			return commonProdInfoDTO.setStatus(OFF_SHELF.getThirdStatus());
		}
		log.info("返回的数据" + JSON.toJSONString(skuPriceInfoMap));


		for (IndustrialProduct industrialProduct : industrialProducts) {
			SkuInfoDTO skuInfoDTO = skuPriceInfoMap.get(industrialProduct.getSkuId().toString());
			commonProdInfoDTO.setPrice(skuInfoDTO.getSellPrice());
			commonProdInfoDTO.setThirdSalePrice(skuInfoDTO.getMarketPrice());
			//更新价格
			if (skuInfoDTO.getSellPrice().compareTo(industrialProduct.getPrice()) != 0 || skuInfoDTO.getMarketPrice().compareTo(industrialProduct.getMarketPrice()) != 0 || (ObjectUtil.isNotEmpty(skuInfoDTO.getTaxCode()) && !skuInfoDTO.getTaxCode().equals(industrialProduct.getTaxCode()))) {
				product.setCash(skuInfoDTO.getSellPrice().doubleValue());
				product.setPrice(skuInfoDTO.getMarketPrice().doubleValue());
				industrialProduct.setTaxCode(skuInfoDTO.getTaxCode());
				industrialProduct.setPrice(skuInfoDTO.getSellPrice());
				industrialProduct.setMarketPrice(skuInfoDTO.getMarketPrice());
				industrialProduct.setUpdateTime(new DateTime());
				industrialProduct.setUpdateFlag(IndexUpdateFlagEnum.UNUPDATE.getValue());
				industrialProductDao.updateProdByShopId(industrialProduct, industrialProduct.getShopId());

				try {
					Sku sku = skuList.stream().filter(e -> e.getOuterId().equals(industrialProduct.getSkuId())).findFirst().orElse(null);
					if (ObjectUtil.isNotEmpty(sku)) {
						sku.setPrice(skuInfoDTO.getSellPrice().doubleValue());
					}
				} catch (Exception e) {

				}
			}
		}

		productDao.update(product);
		skuDao.update(skuList);


		log.debug("-----------修改完成后更新索引-------");
		industrialProducts.forEach(e -> {
			industrialProductIndexClient.updateIndex(e.getShopId() + StrUtil.COLON + e.getSkuId(), SecurityConstants.FROM_IN);
		});

		return commonProdInfoDTO;
	}

//	@Override
//	public void updateAdjustId(String categoryCode, Long adjustId, Long shopId) {
//		industrialProductDao.updateAdustIdByShopId(categoryCode, adjustId, shopId);
//		outerIndustrialProductProducerService.updateIndexByCategory(shopId + StrUtil.COLON + categoryCode);
//	}

	@Override
	public void deleteAdjustId(Long categoryMroSupplierId, Long shopId) {
		industrialProductDao.deleteAdustIdByShopId(categoryMroSupplierId, shopId);
	}

	@Override
	public PageSupport<IndustrialProduct> getThridIndustrialProdsByCategory(String category, Long shopId, PageParams pageParams) {
		return this.industrialProductDao.getThridIndustrialProdsByCategory(category, pageParams, shopId);
	}

	@Override
	public ThridIndustrialMaterialCodeProductDTO getThridIndustrialMaterialCode(String productCode, String innerCode, String materialCode) {
		log.info("商品id： 店铺号： {} ,{},{}", productCode, industrialOuterShopConfigService.getByInnerCode(innerCode).getShopId(), materialCode);
		IndustrialProduct industrialProduct = this.industrialProductDao.getByShopIdAndSkuId(productCode, industrialOuterShopConfigService.getByInnerCode(innerCode).getShopId());
		if (ObjectUtil.isEmpty(industrialProduct)) {
			return null;
		}
		ThridIndustrialMaterialCodeProductDTO thridIndustrialMaterialCodeProductDTO = industrialProductConverter.to7(industrialProduct);
		thridIndustrialMaterialCodeProductDTO.setMaterialCode(materialCode);
		log.info("看有没有返回参数: {} ", thridIndustrialMaterialCodeProductDTO.toString());
		return thridIndustrialMaterialCodeProductDTO;
	}

	@Override
	public ThridIndustrialMaterialCodeProductDTO getByOpenMaterialCode(String productCode, String supplierCode, String materialCode) {
		IndustrialProduct industrialProduct = this.industrialProductDao.getBySupplierCodeAndSkuId(supplierCode, productCode);
		if (ObjectUtil.isEmpty(industrialProduct)) {
			return null;
		}
		ThridIndustrialMaterialCodeProductDTO thridIndustrialMaterialCodeProductDTO = industrialProductConverter.to7(industrialProduct);
		thridIndustrialMaterialCodeProductDTO.setMaterialCode(materialCode);
		log.info("看有没有返回参数: {} ", thridIndustrialMaterialCodeProductDTO.toString());
		return thridIndustrialMaterialCodeProductDTO;
	}

	@Override
	public ErpProductInfoDTO queryErpProductInfo(ErpProductQuery query) {

		IndustrialProduct industrialProduct = this.industrialProductDao.getByShopIdAndSkuId(query.getThirdSkuId(), industrialOuterShopConfigService.getByInnerCode(query.getSupplier()).getShopId());
		ErpProductInfoDTO erpProductInfoDTO = industrialProductConverter.to10(industrialProduct);
		erpProductInfoDTO.setImagePath(erpProductInfoDTO.getImagePath().split(";")[0]);
		return erpProductInfoDTO;
	}

	@Override
	public Boolean updateMroStatus(List<updataListVo> updataListVos, Integer flag, Long shopId) {
		if (MroIndustrialProdStatusEnum.findByCode(flag).getNum() == null) {
			return Boolean.FALSE;
		}
		List<Long> prodId = updataListVos.stream().map(updataListVo::getId).collect(Collectors.toList());

		return SqlUtil.retBool(this.industrialProductDao.updateMroStatusByShopId(prodId, flag, shopId));
	}

	@Override
	public PageSupport<ThirdIndustrialProductDTO> getThridIndustrialProds(ThridIndustrialProductSimpleQuery query, Long shopId) {
		return this.industrialProductDao.getThridIndustrialProdsByShopId(query, shopId);
	}

	@Override
	public List<IndustrialSearchDocumentDTO> buildIndexs(List<SkuIdDTO> outerSkuInfos, Long shopId) {


		List<Long> ids = outerSkuInfos.stream().map(SkuIdDTO::getId).collect(Collectors.toList());

		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(shopId);
		//初始化
		List<IndustrialProductDTO> outerProducts = this.industrialProductDao.queryInfoByIdsAndShopId(ids, shopId);

		List<IndustrialSearchDocumentDTO> industrialSearchDocumentDTOS = new CopyOnWriteArrayList<>();

		long startTime = System.currentTimeMillis();

		List<List<IndustrialProductDTO>> arrays = Lists.partition(outerProducts, 80);
		List<CompletableFuture<Boolean>> futureList = arrays.stream()
			.map(productDTOList -> CompletableFuture.supplyAsync(() -> processList(config, productDTOList, industrialSearchDocumentDTOS), productThreadPoolFactory.getThreadPool()))
			.collect(Collectors.toList());

		//Wait for them all to complete
		CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));

		voidCompletableFuture.join();

		long endTime = System.currentTimeMillis();

		//程序块运行时间
		log.info("运行时间为：" + (endTime - startTime) + "毫秒");

		return industrialSearchDocumentDTOS;
	}

	private boolean processList(IndustrialOuterShopConfig config, List<IndustrialProductDTO> outerProducts, List<IndustrialSearchDocumentDTO> industrialSearchDocumentDTOS) {
		String shopName = null;
		String innerCode = null;
		String nickName = null;

		for (IndustrialProductDTO outerProduct : outerProducts) {
			try {
				Long shopId = outerProduct.getShopId();
				List<String> skuMaterialCodeRes = new ArrayList<>();
				StringBuilder stringBuilder = new StringBuilder();
				IndustrialSearchDocumentDTO industrialSearchDocumentDTO = new IndustrialSearchDocumentDTO();
				//实体转换
				thridIndustrialProductConverter.toProduct(outerProduct, industrialSearchDocumentDTO);

				if (ObjectUtil.isNotEmpty(config) && !config.getShopId().equals(MRO_PLATFORM_ID)) {
					shopName = config.getShopName();
					innerCode = config.getInnerCode();
					nickName = config.getNickName();

					//erp 绑定关系设置
					List<MaterialProductRel> materialProductRels = materialProductRelDao.queryBinding(outerProduct.getSkuId(), innerCode);
					if (CollUtil.isNotEmpty(materialProductRels)) {
						skuMaterialCodeRes.addAll(materialProductRels.stream().map(MaterialProductRel::getMaterialCode).distinct().collect(Collectors.toList()));
						String boundMaterialCode = materialProductRels.stream().map(MaterialProductRel::getMaterialCode).collect(Collectors.joining(StrUtil.COMMA));
						//设置ERP绑定的信息
						industrialSearchDocumentDTO.setBoundMaterialCode(boundMaterialCode);
						industrialSearchDocumentDTO.setBound(Boolean.TRUE);

					}

					//第三方平台的标识
					industrialSearchDocumentDTO.setSupplierName(config.getInnerName());
					industrialSearchDocumentDTO.setIsMroSelf(IsMroSelfEnum.THIRD_PROD.getType());
					industrialSearchDocumentDTO.setPlatformId(config.getPlatformId());

					//组装第三方类目
					SupplierCategoryBindErpVo thirdCategoryByCode = industrialCategoryService.getThirdCategoryByCode(outerProduct.getCategory(), shopId);
					if (ObjectUtil.isNotNull(thirdCategoryByCode)) {
						thridIndustrialProductConverter.first(thirdCategoryByCode, industrialSearchDocumentDTO);
						List<SupplierCategoryBindErpVo> children = thirdCategoryByCode.getChildren();
						if (CollectionUtils.isNotEmpty(children)) {
							thridIndustrialProductConverter.second(children.get(0), industrialSearchDocumentDTO);
							children = children.get(0).getChildren();
							if (CollectionUtils.isNotEmpty(children)) {
								thridIndustrialProductConverter.third(children.get(0), industrialSearchDocumentDTO);
								if (CollectionUtils.isNotEmpty(children)) {
									thridIndustrialProductConverter.forth(children.get(0), industrialSearchDocumentDTO);
								}
							}
						}
						stringBuilder.append(" " + industrialSearchDocumentDTO.categoryToString());
					}
				}

				//自营平台
				if (ObjectUtil.isEmpty(config) || config.getShopId().equals(MRO_PLATFORM_ID)) {
					ShopDetailBO shopDetailBO = shopClient.getShopDetailByShopId(outerProduct.getShopId(), null).getResult();
					shopName = shopDetailBO.getSiteName();
					industrialSearchDocumentDTO.setSupplierName(shopDetailBO.getSiteName());
					//自营的标识
					industrialSearchDocumentDTO.setIsMroSelf(IsMroSelfEnum.MRO_SELF.getType());
					industrialSearchDocumentDTO.setPlatformId(MRO_PLATFORM_ID);
					industrialSearchDocumentDTO.setAdjustedPrice(industrialSearchDocumentDTO.getSupplierSalePrice());
				}

				//组装最基本的索引信息
				industrialSearchDocumentDTO.setShopName(shopName);
				industrialSearchDocumentDTO.setIndustrialProdId(outerProduct.getId());
				//找到该商家对应的枚举
				industrialSearchDocumentDTO.setSupplier(innerCode);

				//图片处理一下，只需要一张
				if (StrUtil.isNotBlank(industrialSearchDocumentDTO.getImage())) {
					String[] split = industrialSearchDocumentDTO.getImage().split(StrUtil.COMMA);
					industrialSearchDocumentDTO.setImage(split[0]);
				}

				// 根据关联的  进行添加一 二 三 级
				Long currentCategoryId = outerProduct.getCategoryMroSupplierId();
				Category currentCategory = null;
				//调价id可能会出现为空的情况
				//调价id设为0是MRO平台
				if (currentCategoryId != null && currentCategoryId > ProductConstant.MRO_ADJUST_ID) {
					do {
						currentCategory = categoryService.getCategoryById(currentCategoryId);
						if (AppUtils.isNotBlank(currentCategory)) {
							if (currentCategory.getGrade() == 1) {
								industrialProductConverter.first(currentCategory, industrialSearchDocumentDTO);
							} else if (currentCategory.getGrade() == 2) {
								industrialProductConverter.second(currentCategory, industrialSearchDocumentDTO);
							} else {
								industrialProductConverter.third(currentCategory, industrialSearchDocumentDTO);
							}
							stringBuilder.append(currentCategory.getName() + " ");
							currentCategoryId = currentCategory.getParentId();
						}
					} while (AppUtils.isNotBlank(currentCategory));
				}


				String[] category = outerProduct.getCategory().split(StrUtil.COLON);
				if (category.length == 1) {
//			SupplierCategoryBindErpVo thirdCategoryByCode = thirdIndustrialStrategyContext.getStrategy(shopId).getThirdCategoryByCode(outerProduct.getCategory());
//				SupplierCategoryBindErpVo thirdCategoryByCode = industrialCategoryService.getThirdCategoryByCode(outerProduct.getCategory(), shopId);
//				if (ObjectUtil.isNotNull(thirdCategoryByCode)) {
//					thridIndustrialProductConverter.first(thirdCategoryByCode, industrialSearchDocumentDTO);
//					List<SupplierCategoryBindErpVo> children = thirdCategoryByCode.getChildren();
//					if (CollectionUtils.isNotEmpty(children)) {
//						thridIndustrialProductConverter.second(children.get(0), industrialSearchDocumentDTO);
//						children = children.get(0).getChildren();
//						if (CollectionUtils.isNotEmpty(children)) {
//							thridIndustrialProductConverter.third(children.get(0), industrialSearchDocumentDTO);
//							if (CollectionUtils.isNotEmpty(children)) {
//								thridIndustrialProductConverter.forth(children.get(0), industrialSearchDocumentDTO);
//							}
//						}
//					}
//					stringBuilder.append( " " + industrialSearchDocumentDTO.categoryToString());
//				}
				}

				//组装调价ID
				industrialSearchDocumentDTO.setMroCategoryId(outerProduct.getCategoryMroSupplierId());
				industrialSearchDocumentDTO.setAdjustId(outerProduct.getCategoryMroSupplierId());

				if (StrUtil.isNotBlank(nickName)) {
					stringBuilder.append(nickName + " ");
				}

				if (StrUtil.isNotBlank(outerProduct.getBrandName())) {
					stringBuilder.append(outerProduct.getBrandName() + " ");
				}
				if (StrUtil.isNotBlank(outerProduct.getModel())) {
					stringBuilder.append(outerProduct.getModel() + " ");
				}
				stringBuilder.append(shopName + " " + outerProduct.getSkuId() + " " + outerProduct.getName() + " ");
				// 类目信息
				stringBuilder.append(industrialSearchDocumentDTO.categoryToString());
				//补充关键字
				industrialSearchDocumentDTO.setKeyword(stringBuilder.toString());
				industrialSearchDocumentDTO.setProdType(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode());
				industrialSearchDocumentDTO.setForMro(Boolean.TRUE);
				industrialSearchDocumentDTO.setBound(CollectionUtils.isEmpty(skuMaterialCodeRes) ? Boolean.FALSE : Boolean.TRUE);
				industrialSearchDocumentDTO.setId(industrialSearchDocumentDTO.getShopId() + StrUtil.COLON + outerProduct.getSkuId());
				industrialSearchDocumentDTO.setStatus(outerProduct.getState() == 1 ? ON_SHELF.getThirdStatus() : OFF_SHELF.getThirdStatus());
				industrialSearchDocumentDTO.setProductNameSuggest(industrialSearchDocumentDTO.getProductNameSuggest() + StrUtil.SPACE + industrialSearchDocumentDTO.getProductCode());

				//计算调价后的价格，目前只做排序使用
				if (Objects.nonNull(outerProduct.getCategoryMroSupplierId()) && outerProduct.getCategoryMroSupplierId() > ProductConstant.MRO_ADJUST_ID) {
					this.calculatePrice(industrialSearchDocumentDTO, null);
				}
				if (industrialSearchDocumentDTO.getSortPrice() == null) {
					industrialSearchDocumentDTO.setSortPrice(industrialSearchDocumentDTO.getSupplierSalePrice());
				}
				industrialSearchDocumentDTOS.add(industrialSearchDocumentDTO);
			} catch (Exception e) {
				log.error("重建索引异常：商家id：{} skuid：{}", outerProduct.getShopId(), outerProduct.getSkuId());
				log.error("重建索引异常", e);

			}


		}

		return true;
	}

	@Override
	public List<CommonPriceDTO> getLatestPrice(Long shopId, List<String> productCodes, String userId) {

		List<CommonPriceDTO> commonPriceDTOS = new ArrayList<>();
		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(shopId);


		//初始化
		List<IndustrialProduct> industrialProducts = this.industrialProductDao.queryByShopIdAndSkuId(productCodes, shopId);
		Map<String, IndustrialProduct> industrialProductMap = industrialProducts.stream().collect(Collectors.toMap(IndustrialProduct::getSkuId, e -> e));
		//组装最基本的索引信息
		List<IndustrialSearchDocumentDTO> industrialSearchDocumentDTOS = thridIndustrialProductConverter.toList6(industrialProducts);
		for (IndustrialSearchDocumentDTO industrialSearchDocumentDTO : industrialSearchDocumentDTOS) {

			//自营商家
			if (ObjectUtil.isEmpty(config)) {
				ShopDetailBO shopDetailBO = shopClient.getShopDetailByShopId(shopId, userId).getResult();
				industrialSearchDocumentDTO.setShopName(shopDetailBO.getShopName());
				industrialSearchDocumentDTO.setSupplier("0");
				industrialSearchDocumentDTO.setShopId(shopId);
				industrialSearchDocumentDTO.setSupplierName(shopDetailBO.getSiteName());
			}

			//第三方商家
			if (ObjectUtil.isNotEmpty(config)) {
				industrialSearchDocumentDTO.setShopName(config.getShopName());
				industrialSearchDocumentDTO.setSupplier(config.getInnerCode());
				industrialSearchDocumentDTO.setShopId(config.getShopId());
				industrialSearchDocumentDTO.setSupplierName(config.getInnerName());
			}


			//组装调价ID
			industrialSearchDocumentDTO.setMroCategoryId(industrialSearchDocumentDTO.getAdjustId());

			String[] category = industrialSearchDocumentDTO.getCategory().split(StrUtil.COLON);
			if (category.length == 1) {
//			SupplierCategoryBindErpVo thirdCategoryByCode = thirdIndustrialStrategyContext.getStrategy(shopId).getThirdCategoryByCode(outerProducts.getCategory());
				SupplierCategoryBindErpVo thirdCategoryByCode = industrialCategoryService.getThirdCategoryByCode(industrialSearchDocumentDTO.getCategory(), shopId);
				if (ObjectUtil.isNotNull(thirdCategoryByCode)) {
					thridIndustrialProductConverter.first(thirdCategoryByCode, industrialSearchDocumentDTO);
					List<SupplierCategoryBindErpVo> children = thirdCategoryByCode.getChildren();
					if (CollectionUtils.isNotEmpty(children)) {
						thridIndustrialProductConverter.second(children.get(0), industrialSearchDocumentDTO);
						children = children.get(0).getChildren();
						if (CollectionUtils.isNotEmpty(children)) {
							thridIndustrialProductConverter.third(children.get(0), industrialSearchDocumentDTO);
							if (CollectionUtils.isNotEmpty(children)) {
								thridIndustrialProductConverter.forth(children.get(0), industrialSearchDocumentDTO);
							}
						}
					}
				}
			}

			//计算调价后的价格，目前只做排序使用
			if (Objects.nonNull(industrialSearchDocumentDTO.getAdjustId()) || ObjectUtil.isNotEmpty(config)) {
				this.calculatePrice(industrialSearchDocumentDTO, userId);
			}

			CommonPriceDTO commonPriceDTO = industrialProductConverter.to17(industrialProductMap.get(industrialSearchDocumentDTO.getProductCode()));
			commonPriceDTO.setAdjustedPrice(industrialSearchDocumentDTO.getAdjustedPrice());
			commonPriceDTO.setPreDeliveryDayNum(industrialProductMap.get(industrialSearchDocumentDTO.getProductCode()).getPreDeliveryDayNum());
			commonPriceDTOS.add(commonPriceDTO);
		}


		return commonPriceDTOS;
	}

	@Override
	public R<ProductBO> views(ProductDetailQuery query) {

		Long productId = query.getProductId();

		if (ObjectUtil.isNull(productId)) {
			throw new BusinessException("商品ID不能为空");
		}

		String userId = query.getUserId();
		ProductBO productBO = null;
		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(query.getShopId());
		if (ObjectUtil.isEmpty(config)) {
			//MRO平台
			config = new IndustrialOuterShopConfig();
			config.setShopId(query.getShopId());
			Long databaseShopId = 0L;
			IndustrialProductDTO industrialProduct = industrialProductDao.getInfoByIdAndShopId(productId, databaseShopId);
			productBO = industrialProductConverter.to14(industrialProduct);

			List<IndustrialProduct> industrialProducts = industrialProductDao.queryByShopIdAndProdId(industrialProduct.getProdId(), databaseShopId);
			List<Long> mroSkuIds = industrialProducts.stream().map(IndustrialProduct::getMroSkuId).collect(Collectors.toList());

			Boolean isPaintBuyer = false;
			if(industrialProduct.getCategory() != null && industrialProduct.getCategory().equals("PA;PA;PA")) {
				if (!StringUtils.isBlank(query.getUserId())) {
					R<Boolean> isPaintBuyerR = paintBuyerClient.findBuyer(query.getUserId());
					if(isPaintBuyerR.isSuccess()) {
						isPaintBuyer = isPaintBuyerR.getResult();
					}
				}
			}

			//组装sku参数
			List<Sku> skus = skuDao.queryAllByIds(mroSkuIds);
			List<ProductPropertyImageDTO> propValueImgList = new ArrayList<>();
			Map<Long, List<String>> valueImagesMap = new HashMap<>();
			if (AppUtils.isNotBlank(skus)) {
				//获取到商品  所有的属性图片
				List<ProdPropImage> prodPropImages = prodPropImageDao.getProdPropImageByProdId(industrialProduct.getProdId());
				if (AppUtils.isNotBlank(prodPropImages)) {
					//根据属性值 进行分组,封装成map
					for (ProdPropImage prodPropImage : prodPropImages) {
						Long valueId = prodPropImage.getValueId();
						List<String> imgs = valueImagesMap.get(valueId);
						if (AppUtils.isBlank(imgs)) {
							imgs = new ArrayList<>();
						}
						imgs.add(prodPropImage.getUrl());
						valueImagesMap.put(valueId, imgs);
					}
					//将map转换成 DTO LIST
					for (Long valId : valueImagesMap.keySet()) {
						List<String> imgs = valueImagesMap.get(valId);
						ProductPropertyImageDTO propValueImgDto = new ProductPropertyImageDTO();
						propValueImgDto.setValueId(valId);
						propValueImgDto.setImgList(imgs);
						propValueImgList.add(propValueImgDto);
					}
				}
				productBO.setPropValueImgListJson(JSONUtil.getJson(propValueImgList));

				//检查工业品表价格是否与prod表不一致
				Map<String, IndustrialProduct> industrialProductMap = industrialProducts.stream().collect(Collectors.toMap(IndustrialProduct::getSkuId, Function.identity()));
				skus.forEach(e -> {
					IndustrialProduct skuMapIndustrialProd = industrialProductMap.get(String.valueOf(e.getId()));
					if (BigDecimal.valueOf(e.getPrice()).compareTo(skuMapIndustrialProd.getPrice()) != 0) {
						skuMapIndustrialProd.setPrice(BigDecimal.valueOf(e.getPrice()));
						this.industrialProductDao.update(skuMapIndustrialProd);
					}
				});
			}
			productBO.setPropValueImgList(propValueImgList);

//			获取商品属性和属性值
			List<ProductPropertyDTO> prodPropDtoList = skuService.getPropValListByProd(productBO, skus, valueImagesMap, null, null);
			productBO.setProdPropDtoList(prodPropDtoList);
//			productBO.setProdPropDtoList(null);

			//商品图片列表(没有属性图片时，展示商品图片)
			List<String> prodPics = new ArrayList<>();
			if (AppUtils.isBlank(propValueImgList)) {
				List<ImgFile> imgFileList = imgFileDao.getAllProductPics(industrialProduct.getProdId());
				if (AppUtils.isNotBlank(imgFileList)) {
					for (ImgFile imgFile : imgFileList) {
						prodPics.add(imgFile.getFilePath());
					}
				}
			} else {
				prodPics.add(productBO.getPic());
			}
			productBO.setProductPics(prodPics);

			if (AppUtils.isNotBlank(skus)) {
				List<String> skuPics = skus.stream().map(Sku::getPic).collect(Collectors.toList());
				if (!skuPics.contains(null)) {
					productBO.setProductPics(skuPics);
				}

				//油漆价格保护
				if(industrialProduct.getCategory() != null && industrialProduct.getCategory().equals("PA;PA;PA") && !isPaintBuyer) {
					for(Sku sku:skus) {
						sku.setPrice(null);
					}
					productBO.setPrice(null);
				}
			}

			List<SkuBO> skuBOS = industrialProductConverter.toList15(skus);
			productBO.setSkuBOList(skuBOS)
				.setSkuBOListLength(skuBOS.size())
				.setSkuCount(skuBOS.size())
				.setSkuBoListJson(JSON.toJSONString(skuBOS))
				.setIsThird(Boolean.FALSE)
				.setPlatformId(0L);

		} else {
			//第三方平台
			IndustrialProductDTO industrialProduct = industrialProductDao.getInfoByIdAndShopId(productId, config.getShopId());
			productBO = industrialProductConverter.to14(industrialProduct);


			//销售价或者第三方平台价格不一致，更新数据库和索引
			IndustrialProduct industrialProduct1 = industrialProductConverter.to(industrialProduct);

			//从第三方获取最新的价格
			getLatestPriceFromThirdPlatform(productId, productBO, config, industrialProduct, industrialProduct1);

			SkuBO skuBO = new SkuBO();
			BigDecimal showPrice = new BigDecimal(productBO.getPrice());
			BigDecimal showUntaxedPrice = showPrice.divide(
					BigDecimal.ONE.add(Optional.ofNullable(industrialProduct.getTaxRate()).map(e -> e.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)).orElse(new BigDecimal("0.13"))
					), 2, RoundingMode.HALF_UP);
			skuBO.setCnProperties(industrialProduct1.getModel())
				.setName(industrialProduct1.getName() + StrUtil.SPACE + industrialProduct1.getSkuId())
				.setPrice(showPrice)
				.setOriginalPrice(new BigDecimal(productBO.getMarketPrice()))
				.setUntaxedPrice(showUntaxedPrice)
				.setTaxRate(industrialProduct.getTaxRate().toString())
				.setMiniOrderQty(industrialProduct1.getMiniOrderQty())
				.setSkuDeliveryTime(industrialProduct1.getPreDeliveryDayNum().longValue())
				.setWeight(Double.valueOf(Optional.ofNullable(industrialProduct1.getWeight()).orElse("0")))
				.setOuterId(productBO.getOuterId());

			//组装pic
			List<String> prodPics = Arrays.stream(industrialProduct.getImagePath().split(StrUtil.COMMA)).collect(Collectors.toList());

			//计算调价后的价格
			calculatePrice(productBO, config, industrialProduct, userId);
			skuBO.setPrice(new BigDecimal(productBO.getPrice()));
			//设置sku信息
			List<SkuBO> skuBOS = Collections.singletonList(skuBO);
			productBO.setSkuBOList(skuBOS)
				.setSkuBOListLength(skuBOS.size())
				.setSkuCount(skuBOS.size())
				.setSkuBoListJson(JSON.toJSONString(skuBOS))
				.setIsThird(Boolean.TRUE)
				.setSupplierCode(config.getInnerCode())
				.setSupplierName(config.getShopName())
				.setProductCode(industrialProduct.getSkuId())
				.setProductPics(CollUtil.isEmpty(prodPics) ? new ArrayList<>() : prodPics)
				.setParam(industrialProduct.getParam())
				.setPlatformId(config.getPlatformId())
				.setOuterId(industrialProduct.getSkuId());

			//获取商品详情
			getProductDetail(productBO, industrialProduct1, query.getSource());
			//只更新最小起订量 如果后续要更新其他的，在这后面加
			if (AppUtils.isNotBlank(productBO.getMiniOrderQty())) {
				if (industrialProduct1.getMiniOrderQty().compareTo(productBO.getMiniOrderQty()) != 0) {
					industrialProduct1.setMiniOrderQty(productBO.getMiniOrderQty());
					industrialProductDao.update(industrialProduct1);
					log.info("更新商品SKU {} 成功", industrialProduct1.getSkuId());
				}
			}

			//更新预计发货天数
			Integer buyCount = query.getStock();
			Integer deliveryDays = 0;
			JdAddressDTO jdAddressDTO = new JdAddressDTO();

			//获取中文地址
			String address = null;
			if (query.getAddrId() != null) {
				address = userAddressService.getById(query.getAddrId()).getSubAdds();
			}

			//当获取到地址和购买数量时，获取预计发货天数
			if(buyCount != null && address !=null) {
				jdAddressDTO = getJDAddressFromAddress(address,industrialProduct1);
				deliveryDays = getDeliveryDays(jdAddressDTO, industrialProduct1 ,buyCount);
			}else {
				//默认地址江苏省南京市六合区
				jdAddressDTO.setProvinceId(12);
				jdAddressDTO.setCityId(904);
				jdAddressDTO.setCountyId(908);
				jdAddressDTO.setTownId(0);
				deliveryDays = getDeliveryDays(jdAddressDTO, industrialProduct1 ,buyCount);
			}
			if (deliveryDays != null) {
				if (industrialProduct1.getPreDeliveryDayNum().compareTo(deliveryDays) != 0) {
					industrialProduct1.setPreDeliveryDayNum(deliveryDays);
					industrialProductDao.update(industrialProduct1);
					productBO.setPreDeliveryDayNum(deliveryDays);
					log.info("更新商品SKU {} 成功", industrialProduct1.getSkuId());
				}
			}
		}

		//获取商城类目
		getCategory(productBO);

		//获取商家信息
		ShopDetailBO shopDetailBO = shopClient.getShopDetailByShopId(config.getShopId(), userId).getResult();
		productBO.setShopDetailBO(shopDetailBO);
		//收藏状态默认为未收藏
		productBO.setCollectionFlag(Boolean.FALSE);
		//如果是登录状态，才判断是否收藏
		if (ObjectUtil.isNotNull(userId) && favoriteProductService.isExistsFavorite(productId, productBO.getShopId(), userId, IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getFlag())) {
			productBO.setCollectionFlag(Boolean.TRUE);
		}

		// 商品咨询信息
		ProductConsultQuery consultQuery = new ProductConsultQuery();
		consultQuery.setPageSize(2);
		consultQuery.setProductId(productId);
		consultQuery.setShopId(productBO.getShopId());
		PageSupport<ProductConsultDTO> pageSupport = productConsultService.getUserProductConsultPage(consultQuery);
		List<ProductConsultDTO> productConsultList = pageSupport.getResultList();
		productBO.setProductConsultList(CollUtil.isEmpty(productConsultList) ? null : productConsultList);


		return R.ok(productBO);
	}

	//只获取预计发货天数
	private Integer getDeliveryDays(JdAddressDTO jdAddressDTO, IndustrialProduct industrialProduct, Integer buyCount) {
		return thirdIndustrialStrategyContext.getStrategy(industrialProduct.getShopId()).getDeliveryDay(industrialProduct,jdAddressDTO,buyCount);
	}

	//京东地址编码转换
	private JdAddressDTO getJDAddressFromAddress(String address, IndustrialProduct industrialProduct) {
		return thirdIndustrialStrategyContext.getStrategy(industrialProduct.getShopId()).getJDAddressFromAddress(address);
	}


	private void getProductDetail(ProductBO productBO, IndustrialProduct industrialProduct, String source) {
		IndustrialProductDetailForErpDTO productDetail = thirdIndustrialStrategyContext.getStrategy(industrialProduct.getShopId()).getProductDetail(industrialProduct.getSkuId(), industrialProduct.getShopId(), source);
		if (ObjectUtil.isNotEmpty(productDetail)) {
			if (StrUtil.isNotBlank(productDetail.getIntroduction())) {
				productBO.setContent(productDetail.getIntroduction());
				productBO.setContentM(productDetail.getIntroduction());
			}

			if (StrUtil.isNotBlank(productDetail.getStyle())) {
				productBO.setContentM("<style>" + productDetail.getStyle() + "</style>" + productBO.getContentM());
				productBO.setContent("<style>" + productDetail.getStyle() + "</style>" + productBO.getContent());
			}

			if (CollUtil.isNotEmpty(productDetail.getPhotos())) {
				productBO.getProductPics().addAll(productDetail.getPhotos());
			}
			if (AppUtils.isNotBlank(productDetail.getMiniOrderQty())) {
				productBO.setMiniOrderQty(productDetail.getMiniOrderQty());
			}
		}
	}

	private void getLatestPriceFromThirdPlatform(Long productId, ProductBO productBO, IndustrialOuterShopConfig config, IndustrialProductDTO industrialProduct, IndustrialProduct industrialProduct1) {
		CommonProdInfoDTO commonProdInfoDTO = thirdIndustrialStrategyContext.getStrategy(industrialProduct.getShopId()).dailyUpdateProdInfo(industrialProduct1).getResult();

		if (ObjectUtil.isEmpty(commonProdInfoDTO) || AppUtils.isBlank(commonProdInfoDTO.getPrice()) || AppUtils.isBlank(commonProdInfoDTO.getThirdSalePrice())) {
			log.info(" =======无法获取商品价格，直接返回下架状态，调用返回结果：{} =============", JSON.toJSONString(commonProdInfoDTO));
			updateProd(productId, industrialProduct);
			return;
		}

		if (commonProdInfoDTO.getPrice().compareTo(industrialProduct.getPrice()) != 0 || commonProdInfoDTO.getThirdSalePrice().compareTo(industrialProduct.getMarketPrice()) != 0) {
			industrialProduct.setPrice(commonProdInfoDTO.getPrice());
			industrialProduct.setMarketPrice(commonProdInfoDTO.getThirdSalePrice());
			BigDecimal untaxedPrice = Optional.ofNullable(commonProdInfoDTO.getUntaxedPrice()).orElse(
				commonProdInfoDTO.getPrice().divide(
					BigDecimal.ONE.add(
						Optional.ofNullable(commonProdInfoDTO.getTaxRate()).orElse(new BigDecimal(13)).divide(new BigDecimal(100))
					), 2, RoundingMode.HALF_UP));
			industrialProduct.setUntaxedPrice(untaxedPrice);
			industrialProduct.setTaxRate(Optional.ofNullable(commonProdInfoDTO.getTaxRate()).orElse(new BigDecimal(13)));
			productBO.setMarketPrice(commonProdInfoDTO.getThirdSalePrice().toString());

		}
	}

	private void updateProd(Long productId, IndustrialProductDTO industrialProduct) {
		IndustrialProduct updateProduct = industrialProductConverter.to(industrialProduct);
		updateProduct.setUpdateTime(new DateTime());
		updateProduct.setState(OFF_SHELF.getMroStatus());
		this.industrialProductDao.updateProdByShopId(updateProduct, updateProduct.getShopId());
		//删除缓存
		industrialProductDao.delIndustrialCache(productId, industrialProduct.getShopId());
		outerIndustrialProductProducerService.updateIndustrialIndex(industrialProduct.getShopId() + StrUtil.COLON + industrialProduct.getSkuId());
	}

	@Override
	public Boolean createOrUpdateMroProd(Long prodId) {

		//先删除，后新增，因为无法判断MRO的sku是否改变了什么数据
		this.industrialProductDao.deleteByProperties(new LambdaDelete<>(IndustrialProduct.class).eq(IndustrialProduct::getProdId, prodId));
		outerIndustrialProductProducerService.updateIndustrialIndex(prodId.toString());

		Product product = this.productDao.getProductById(prodId);
		IndustrialProduct industrialProduct = industrialProductConverter.toMroProd(product);
		List<Sku> skus = this.skuDao.queryByProdId(prodId);
		//根据brandId获取品牌名字
		Brand brand = this.brandDao.getById(product.getBrandId());
		if (ObjectUtil.isNotEmpty(brand)) {
			industrialProduct.setBrandName(brand.getBrandName());
		}

		//根据类目ID获取类目，拼接起来
		Category thirdCategory = this.categoryDao.getById(product.getCategoryId());
		if (ObjectUtil.isEmpty(thirdCategory)) {
			return Boolean.TRUE;
		}
		if (thirdCategory.getParentId() != -1L) {
			Category secondCategory = this.categoryDao.getCategoryById(thirdCategory.getParentId());
			Category firstCategory = this.categoryDao.getCategoryById(secondCategory.getParentId());
			industrialProduct.setCategory(firstCategory.getCategoryCode() + ";" + secondCategory.getCategoryCode() + ";" + thirdCategory.getCategoryCode());
		} else {
			industrialProduct.setCategory(thirdCategory.getCategoryCode() + ";;");
		}


		//unit字段可能会为空，空的时候默认--件
		industrialProduct.setSaleUnit(StrUtil.isBlank(product.getUnit()) ? "件" : product.getUnit());
		if (industrialProduct.getMarketPrice() == null) {
			industrialProduct.setMarketPrice(industrialProduct.getPrice());
		}

		for (Sku sku : skus) {
			industrialProduct.setMroSkuId(sku.getSkuId());
			industrialProduct.setCategoryMroSupplierId(ProductConstant.MRO_ADJUST_ID);
			industrialProduct.setSkuId(sku.getSkuId().toString());
			industrialProduct.setState(1);
			industrialProduct.setSkuId(sku.getSkuId().toString());
			industrialProduct.setMroStatus(product.getStatus());
			industrialProduct.setUntaxedPrice(BigDecimal.valueOf(Optional.ofNullable(product.getPrice()).orElse((double) 0)));
			industrialProduct.setTaxRate(BigDecimal.ZERO);
			industrialProduct.setUpdateFlag(UPDATED.getValue());
			industrialProduct.setModel(sku.getCnProperties());
			industrialProduct.setBuys(sku.getBuys());
			industrialProduct.setViews(0);
			industrialProduct.setComments(0);
			this.industrialProductDao.saveProdByShopId(industrialProduct, product.getShopId());
		}
		return Boolean.TRUE;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean createOrUpdateMroProd1(Long prodId, Integer status) {

		//先删除，后新增，因为无法判断MRO的sku是否改变了什么数据
		this.industrialProductDao.deleteByProperties(new LambdaDelete<>(IndustrialProduct.class).eq(IndustrialProduct::getProdId, prodId));
		outerIndustrialProductProducerService.updateIndustrialIndex(prodId.toString());

		Product product = this.productDao.getProductById(prodId);
		List<Sku> skus = this.skuDao.queryByProdId(prodId);
		//根据brandId获取品牌名字
		Brand brand = this.brandDao.getById(product.getBrandId());

		//根据类目ID获取类目，拼接起来
		Category thirdCategory = this.categoryDao.getById(product.getCategoryId());
		if (ObjectUtil.isEmpty(thirdCategory)) {
			return Boolean.TRUE;
		}

		for (Sku sku : skus) {
			IndustrialProduct industrialProduct = new IndustrialProduct();
			industrialProduct.setMroStatus(product.getStatus());
			industrialProduct.setPrice(BigDecimal.valueOf(sku.getPrice()));
			industrialProduct.setImagePath(sku.getPic());
			industrialProduct.setProdId(product.getProdId());
			industrialProduct.setIntroduction(product.getContent());
			industrialProduct.setName(product.getName());
			industrialProduct.setBrandName(product.getBrandName());
			if (product.getWeight() != null) {
				industrialProduct.setWeight(String.valueOf(sku.getWeight()));
			}
			industrialProduct.setUpdateTime(product.getUpdateTime());
			industrialProduct.setShopId(product.getShopId());
			industrialProduct.setViews((int) product.getViews());
			industrialProduct.setBuys((int) product.getBuys());
			industrialProduct.setComments((int) product.getComments());
			industrialProduct.setMiniOrderQty(new BigDecimal("1"));
			industrialProduct.setPreDeliveryDayNum(14);
			industrialProduct.setProductType(10);
			if (ObjectUtil.isNotEmpty(brand)) {
				industrialProduct.setBrandName(brand.getBrandName());
			}
			if (thirdCategory.getParentId() != -1L) {
				Category secondCategory = this.categoryDao.getCategoryById(thirdCategory.getParentId());
				Category firstCategory = this.categoryDao.getCategoryById(secondCategory.getParentId());
				industrialProduct.setCategory(firstCategory.getCategoryCode() + ";" + secondCategory.getCategoryCode() + ";" + thirdCategory.getCategoryCode());
			} else if (thirdCategory.getId().equals(ConcreteEnum.CONCRETE_CATEGORY.value())) {
				//针对油漆修改
				industrialProduct.setCategory(thirdCategory.getCategoryCode() + ";;");
			} else {
				industrialProduct.setCategory(thirdCategory.getCategoryCode() + ";" + thirdCategory.getCategoryCode() + ";" + thirdCategory.getCategoryCode());
			}
			//unit字段可能会为空，空的时候默认--件
			industrialProduct.setSaleUnit(StrUtil.isBlank(product.getUnit()) ? "件" : product.getUnit());
			if (industrialProduct.getMarketPrice() == null) {
				industrialProduct.setMarketPrice(BigDecimal.valueOf(sku.getPrice()));
			}
			industrialProduct.setMroSkuId(sku.getSkuId());
			industrialProduct.setCategoryMroSupplierId(ProductConstant.MRO_ADJUST_ID);
			industrialProduct.setSkuId(sku.getSkuId().toString());
			industrialProduct.setState(status);
			industrialProduct.setMroStatus(product.getStatus());
			industrialProduct.setUntaxedPrice(BigDecimal.valueOf(Optional.ofNullable(product.getPrice()).orElse((double) 0)));
			industrialProduct.setTaxRate(BigDecimal.ZERO);
			industrialProduct.setUpdateFlag(UPDATED.getValue());
			industrialProduct.setModel(sku.getCnProperties());
			industrialProduct.setBuys(sku.getBuys());
			industrialProduct.setViews(0);
			industrialProduct.setComments(0);
			this.industrialProductDao.saveProdByShopId(industrialProduct, product.getShopId());
		}
		return Boolean.TRUE;
	}

	@Override
	public Boolean updateMroProdStatus(Long prodId, Integer status) {
		List<IndustrialProduct> industrialProductList = this.industrialProductDao.getByShopIdAndProdId(prodId, 0L);
		String indexId = null;
		Boolean hasIndex = !industrialProductList.get(0).getCategory().equals("PA;PA;PA");
		for (IndustrialProduct industrialProduct : industrialProductList) {
			industrialProduct.setMroStatus(status);
			if (ProductStatusEnum.PROD_ONLINE.value().equals(status)) {
				industrialProduct.setState(1);
			} else {
				industrialProduct.setState(0);
			}
			industrialProduct.setUpdateTime(new DateTime());
			this.industrialProductDao.updateProdByShopId(industrialProduct, industrialProduct.getShopId());
			if (hasIndex) {
				indexId = industrialProduct.getShopId() + StrUtil.COLON + industrialProduct.getSkuId();
				outerIndustrialProductProducerService.deleteIndustrialIndex(indexId);
			}
		}

		if (hasIndex) {
			outerIndustrialProductProducerService.updateIndustrialIndex(indexId);
		}

		return Boolean.TRUE;
	}

	@Override
	public Long getProductCount(Long shopId) {
		return industrialProductDao.getProductCountByShopId(null, shopId);
	}

	@Override
	public void updateProdViews(Long productId, Long shopId) {
		industrialProductDao.updateProdViewsByShopId(productId, shopId);
	}

	@Override
	public List<IndustrialProduct> queryProds(Map<Long,List<Long>> shopProdMap) {
		if(ObjectUtil.isNull(shopProdMap)){
			return new ArrayList<>();
		}
		List<IndustrialProduct> industrialProducts = new ArrayList<>();
		for (Long shopId : shopProdMap.keySet()) {
			industrialProducts.addAll(this.industrialProductDao.queryByShopIdAndIds(shopProdMap.get(shopId), shopId));
		}
		return industrialProducts;
	}

	private void getCategory(ProductBO productBO) {
		String[] categoryArray = productBO.getCategory().split(";");
		if (categoryArray.length == 1) {
			SupplierCategoryBindErpVo thirdCategoryByCode = industrialCategoryService.getThirdCategoryByCode(productBO.getCategory(), productBO.getShopId());
			if (ObjectUtil.isNotNull(thirdCategoryByCode)) {
				thridIndustrialProductConverter.first(thirdCategoryByCode, productBO);
				List<SupplierCategoryBindErpVo> children = thirdCategoryByCode.getChildren();
				if (CollectionUtils.isNotEmpty(children)) {
					thridIndustrialProductConverter.second(children.get(0), productBO);
//						children = children.get(0).getChildren();
//						if (CollectionUtils.isNotEmpty(children)) {
//							thridIndustrialProductConverter.third(children.get(0), productBO);
//						}
				}
			}
		} else {
			getMroCategory(productBO, categoryArray);
		}
	}

	private void calculatePrice(ProductBO productBO, IndustrialOuterShopConfig config, IndustrialProductDTO industrialProduct, String userId) {
		IndustrialSearchDocumentDTO industrialSearchDocumentDTO = new IndustrialSearchDocumentDTO();
		industrialSearchDocumentDTO.setProductCode(industrialProduct.getSkuId());
		industrialSearchDocumentDTO.setSupplierSalePrice(industrialProduct.getPrice());
		industrialSearchDocumentDTO.setTotalPrice(industrialProduct.getMarketPrice());
		industrialSearchDocumentDTO.setSupplier(config.getInnerCode());
		industrialSearchDocumentDTO.setMroCategoryId(industrialProduct.getCategoryMroSupplierId());
		this.calculatePrice(industrialSearchDocumentDTO, userId);
		productBO.setPrice(industrialSearchDocumentDTO.getAdjustedPrice().toString());

		List<SkuBO> skus = new ArrayList<>();
		SkuBO skuBO = industrialProductConverter.to16(industrialProduct);
		skuBO.setPrice(industrialSearchDocumentDTO.getAdjustedPrice());
		skus.add(skuBO);
		productBO.setSkuBOList(skus)
			.setSkuBOListLength(skus.size())
			.setSkuBoListJson(JSON.toJSONString(skus));
	}

	private void getMroCategory(ProductBO productBO, String[] categoryArray) {
		Category category = this.categoryDao.getByCategoryCode(categoryArray[0]);
		productBO.setGlobalFirstCatName(category.getName())
			.setGlobalFirstCatId(category.getId());

		Category secondCategory = this.categoryDao.getByCategoryCode(categoryArray[1]);
		productBO.setGlobalSecondCatName(secondCategory.getName())
			.setGlobalSecondCatId(secondCategory.getId());

		Category thirdCategory = this.categoryDao.getByCategoryCode(categoryArray[2]);
		productBO.setGlobalThirdCatName(thirdCategory.getName())
			.setGlobalThirdCatId(thirdCategory.getId());
	}


	@Nullable
	private R<String> inStoreCheck(OpenProductDetailDTO productDetailDTO, Long shopId, IndustrialOuterShopConfig config, IndustrialProduct industrialProduct) {
		boolean switchStatus = industrialOuterShopSwitchSettleDao.getSwitchStatus(shopId, OpenPlatformSwitchEnum.PRODUCT_AUDIT.getSwitchId());
		String prodId = null;
		String redissionLockKey = ProductServiceConstant.OUTER_INDUSTRIAL_UPDATE_LOCK_KEY + shopId + productDetailDTO.getSkuId();
		RLock lock = redissonClient.getLock(redissionLockKey);
		try {
			if (!lock.tryLock(2L, 10L, TimeUnit.SECONDS)) {
				throw new BusinessException("正在进行更新操作");
			}
		} catch (Exception e) {
			return R.fail("正在进行更新操作", "正在进行更新操作");
		}

		String[] categoryList = productDetailDTO.getCategory().split(";");
		if (categoryList.length < 3) {
			lock.unlock();
			return R.fail("请按照这个格式填写类目数据---670;729;4837");
		}

		Category mroCategoryCode = this.categoryDao.getCategoryByCategoryCode(categoryList[2]);
		if (ObjectUtil.isEmpty(mroCategoryCode)) {
			lock.unlock();
			return R.fail("类目不存在，请检查类目是否正确");
		}
		if (ObjectUtil.isEmpty(industrialProduct)) {
			industrialProduct = industrialProductConverter.to6(productDetailDTO);
			industrialProduct.setCategoryMroSupplierId(mroCategoryCode.getId());
			industrialProduct.setCreateTime(new DateTime());
			industrialProduct.setUpdateTime(new DateTime());
			industrialProduct.setMroStatus(1);
			industrialProduct.setUpdateFlag(1);
			industrialProduct.setShopId(shopId);
			industrialProduct.setBuys(0);
			industrialProduct.setViews(0);
			industrialProduct.setComments(0);

			if (switchStatus) {
				industrialProduct.setState((OFF_SHELF.getMroStatus()));
			}

			Long savedId = this.industrialProductDao.saveProdByShopId(industrialProduct, industrialProduct.getShopId());
			prodId = savedId + "";

			// 新增商品创建相似商品更新任务
			similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(2).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, shopId, savedId))));
		} else {
			this.industrialProductConverter.to2(productDetailDTO, industrialProduct);
			industrialProduct.setCategoryMroSupplierId(mroCategoryCode.getId());
			industrialProduct.setUpdateTime(new DateTime());
			industrialProduct.setUpdateFlag(1);
			industrialProduct.setShopId(shopId);
			industrialProduct.setUpdateFlag(IndexUpdateFlagEnum.UNUPDATE.getValue());
			this.industrialProductDao.updateProdByShopId(industrialProduct, industrialProduct.getShopId());
			// 绑定商品通知采购商:更新商品信息
			messagePoolProductService.pushBindProductInfoChange(shopId, Collections.singletonList(industrialProduct.getSkuId()));
			prodId = industrialProduct.getId() + "";

			// 根据更新的商品状态区分是创建相似商品更新任务还是删除任务
			if (industrialProduct.getState().equals(OFF_SHELF.getMroStatus())) {
				similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(3).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, shopId, industrialProduct.getId()))));
			} else if (industrialProduct.getState().equals(ON_SHELF.getMroStatus())) {
				similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(2).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, shopId, industrialProduct.getId()))));
			}

			//订单服务下单会获取这个价格，需要把这个价格也更新
			if (industrialProduct.getMroSkuId() != null) {
				Sku sku = skuDao.getSkuById(industrialProduct.getMroSkuId());
				if (ObjectUtil.isNotEmpty(sku)) {
					sku.setPrice(industrialProduct.getPrice().doubleValue());
					skuDao.update(sku);
				}
			}
		}

		//解锁
		lock.unlock();

		//MQ处理入prod库（这里不是重复操作，是入库到另一个表），MQ处理完成后会自动更新ES
		outerIndustrialProductProducerService.updateProduct(industrialProductConverter.to4(industrialProduct));
		return R.ok(prodId, "商品推送成功");
	}


	private List<PreSearchQueryDTO> getPreSearchQueryDTOS(List<IndustrialProduct> industrialProducts) {
		List<PreSearchQueryDTO> preSearchQueryDTOS = industrialProductConverter.toList3(industrialProducts);
		preSearchQueryDTOS.forEach(e -> {
			IndustrialProduct industrialProduct = industrialProducts.stream().filter(k -> k.getSkuId().equals(e)).findFirst().get();
			e.setSupplier(ThirdIndustrialEnum.findByShopId(industrialProduct.getShopId()).getSupplierCode());
			try {
				e.setSkuName(URLEncoder.encode(Optional.ofNullable(e.getSkuName()).orElse(null), CharsetUtil.UTF_8))
					.setSpecificParam(URLEncoder.encode(Optional.ofNullable(e.getSpecificParam()).orElse(null), CharsetUtil.UTF_8))
					.setBrandName(URLEncoder.encode(Optional.ofNullable(e.getBrandName()).orElse(null), CharsetUtil.UTF_8))
					.setSaleUnit(URLEncoder.encode(Optional.ofNullable(e.getSaleUnit()).orElse(null), CharsetUtil.UTF_8));
			} catch (UnsupportedEncodingException unsupportedEncodingException) {
				unsupportedEncodingException.printStackTrace();
			}
		});
		return preSearchQueryDTOS;
	}

	private void productInfoPack(IndustrialProduct industrialProduct, DateTime dateTime, ShopDetail shopdetailInfo, CommonIndustrialProdDTO product, IndustrialOuterShopConfig config) {
		product.setProdId(industrialProduct.getProdId());
		product.setSupplierProdId(industrialProduct.getId());
		product.setProdType("P");
		product.setKeyWord(config.getNickName());
		product.setVersion(1);
		product.setUserName(config.getShopName());

		product.setViews(0);
		product.setBuys(0L);
		product.setUserParameter("[]");
		product.setComments(0L);
		product.setAuditOpinion("同意");
		product.setReviewScores(100);
		product.setStockCounting(2);
		product.setStore(0);
		if (StrUtil.isNotBlank(industrialProduct.getImagePath()) && industrialProduct.getImagePath().split(StrUtil.COMMA).length > 0) {
			String[] split = industrialProduct.getImagePath().split(StrUtil.COMMA);
			product.setPic(split[0]);
		}
		product.setPrice(industrialProduct.getMarketPrice().doubleValue());
		product.setCash(industrialProduct.getPrice().doubleValue());
		product.setUnit(industrialProduct.getSaleUnit());
		product.setStocks(10000);
		product.setName(industrialProduct.getName());
		product.setRecDate(dateTime);
		product.setModifyDate(dateTime);
		product.setStartDate(dateTime);
		product.setUserName(config.getShopName());
		product.setUserId(shopdetailInfo.getUserId());
		product.setShopId(shopdetailInfo.getShopId());
		product.setStatus(industrialProduct.getState());
		product.setPublishStatus(industrialProduct.getState());
		product.setOutId(String.valueOf(industrialProduct.getSkuId()));
		product.setExpressTransFee(0.00);
		product.setPaymentMethod(PayMannerEnum.ONLINE_PAY.value());
		product.setMetaTitle(Optional.ofNullable(industrialProduct.getParam()).orElse(null));
		product.setMetaDesc(Optional.ofNullable(industrialProduct.getParam()).orElse(null));


		String[] split = industrialProduct.getCategory().split(";");

		Category categoryCode = categoryDao.getByCategoryCode(split[2]);
		if (categoryCode.getGrade() == 1) {
			product.setCategoryId(categoryCode.getId());
		}
	}

	private Long prodInStore(CommonIndustrialProdDTO commonIndustrialProdDTO) {
		Product product = new Product();
		product.setProdId(commonIndustrialProdDTO.getProdId());
		product.setProdType(commonIndustrialProdDTO.getProdType());
		product.setKeyWord(commonIndustrialProdDTO.getKeyWord());
		product.setVersion(commonIndustrialProdDTO.getVersion());
		product.setUserName(commonIndustrialProdDTO.getUserName());
		product.setCategoryId(commonIndustrialProdDTO.getCategoryId());
		product.setViews(commonIndustrialProdDTO.getViews());
		product.setBuys(commonIndustrialProdDTO.getBuys());
		product.setUserParameter(commonIndustrialProdDTO.getUserParameter());
		product.setComments(commonIndustrialProdDTO.getComments());
		product.setAuditOpinion(commonIndustrialProdDTO.getBrief());
		product.setReviewScores(commonIndustrialProdDTO.getReviewScores());
		product.setStockCounting(commonIndustrialProdDTO.getStockCounting());
		product.setStore(commonIndustrialProdDTO.getStore());
		product.setPic(commonIndustrialProdDTO.getPic());
		product.setPrice(commonIndustrialProdDTO.getPrice());
		product.setCash(commonIndustrialProdDTO.getCash());
		product.setUnit(commonIndustrialProdDTO.getUnit());
		product.setStocks(commonIndustrialProdDTO.getStocks());
		product.setName(commonIndustrialProdDTO.getName());
		product.setRecDate(commonIndustrialProdDTO.getRecDate());
		product.setModifyDate(commonIndustrialProdDTO.getModifyDate());
		product.setStartDate(commonIndustrialProdDTO.getStartDate());
		product.setUserName(commonIndustrialProdDTO.getUserName());
		product.setUserId(commonIndustrialProdDTO.getUserId());
		product.setShopId(commonIndustrialProdDTO.getShopId());
		product.setShopThirdCatId(commonIndustrialProdDTO.getShopThirdCatId());
		product.setStatus(commonIndustrialProdDTO.getStatus());
		product.setPublishStatus(commonIndustrialProdDTO.getPublishStatus());
		product.setIndustrialProdType(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());
		product.setIsMroSelf(IsMroSelfEnum.THIRD_PROD.getType());
		product.setExpressTransFee(commonIndustrialProdDTO.getExpressTransFee());
		product.setPaymentMethod(commonIndustrialProdDTO.getPaymentMethod());
		return productDao.saveOrUpdate(product);
	}

	private Long skuInfoInStore(CommonIndustrialProdDTO commonIndustrialProdDTO, Long prodId) {
		Sku sku;
		sku = skuDao.getByOuterId(commonIndustrialProdDTO.getOutId());
		if (AppUtils.isBlank(sku)) {
			sku = new Sku();
		}
		sku.setProdId(prodId);
		sku.setPrice(commonIndustrialProdDTO.getPrice());
		sku.setName(commonIndustrialProdDTO.getName());
		sku.setStocks(Long.valueOf(commonIndustrialProdDTO.getStocks()));
		sku.setActualStocks(Long.valueOf(commonIndustrialProdDTO.getStocks()));
		sku.setStatus(1);
		sku.setOuterId(commonIndustrialProdDTO.getOutId());
		sku.setModifyDate(commonIndustrialProdDTO.getModifyDate());
		sku.setRecDate(commonIndustrialProdDTO.getRecDate());
		sku.setSkuType("P");
		sku.setTenderSku(Boolean.FALSE);
		sku.setPic(commonIndustrialProdDTO.getPic());
		return this.skuDao.saveOrUpdate(sku);
	}
}
