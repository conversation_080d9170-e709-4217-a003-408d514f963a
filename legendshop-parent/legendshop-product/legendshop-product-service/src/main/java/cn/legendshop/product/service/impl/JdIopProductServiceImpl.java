package cn.legendshop.product.service.impl;

import static cn.legendshop.product.api.enums.IndexUpdateFlagEnum.*;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.search.api.client.SimilarityProductTaskClient;
import cn.legendshop.search.api.dto.ProductSimilarityTaskDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.legendshop.common.data.cache.util.RedisUtil;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageParams;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.lambda.LambdaEntityCriterion;
import com.legendshop.dao.support.update.LambdaUpdate;
import com.legendshop.model.constant.PayMannerEnum;
import com.legendshop.model.entity.Product;
import com.legendshop.model.entity.ShopDetail;
import com.legendshop.model.entity.Sku;
import com.legendshop.util.AppUtils;
import com.xxl.job.core.log.XxlJobLogger;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Page;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.constants.OpenPlatformShopSwitchTypeEnum;
import cn.legendshop.common.core.config.ThirdParameterConfig;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.IsMroSelfEnum;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.SqlUtil;
import cn.legendshop.common.dao.IndustrialOuterShopSwitchSettleDao;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.jd.iop.sdk.client.ProductClient;
import cn.legendshop.jd.iop.sdk.constants.ProductApi;
import cn.legendshop.jd.iop.sdk.enums.ErrorKeyEnum;
import cn.legendshop.jd.iop.sdk.enums.JdProductStateEnum;
import cn.legendshop.jd.iop.sdk.enums.ProductCheckExtsEnum;
import cn.legendshop.jd.iop.sdk.enums.ProductDetailQueryExtsEnum;
import cn.legendshop.jd.iop.sdk.model.request.CommodityPoolNumRequest;
import cn.legendshop.jd.iop.sdk.model.request.GetMessageRequest;
import cn.legendshop.jd.iop.sdk.model.request.PoolRequest;
import cn.legendshop.jd.iop.sdk.model.request.PriceRequest;
import cn.legendshop.jd.iop.sdk.model.request.ProductCheckRequest;
import cn.legendshop.jd.iop.sdk.model.request.ProductDetailTipsRequest;
import cn.legendshop.jd.iop.sdk.model.request.ProductQueryRequest;
import cn.legendshop.jd.iop.sdk.model.request.ProductTotalCheckNewRequest;
import cn.legendshop.jd.iop.sdk.model.request.SearchProductRequest;
import cn.legendshop.jd.iop.sdk.model.request.ShipNeedTimeRequest;
import cn.legendshop.jd.iop.sdk.model.response.AreaSaleLimitResponse;
import cn.legendshop.jd.iop.sdk.model.response.CheckSalabilityResponse;
import cn.legendshop.jd.iop.sdk.model.response.CommodityPoolNumResponse;
import cn.legendshop.jd.iop.sdk.model.response.CommonResponse;
import cn.legendshop.jd.iop.sdk.model.response.GetMessageResponse;
import cn.legendshop.jd.iop.sdk.model.response.OnSaleResponse;
import cn.legendshop.jd.iop.sdk.model.response.PoolResponse;
import cn.legendshop.jd.iop.sdk.model.response.PriceResponse;
import cn.legendshop.jd.iop.sdk.model.response.ProductDetailTipsResponse;
import cn.legendshop.jd.iop.sdk.model.response.ProductGiftResponse;
import cn.legendshop.jd.iop.sdk.model.response.ProductImagesResponse;
import cn.legendshop.jd.iop.sdk.model.response.ProductTotalCheckNewResponse;
import cn.legendshop.jd.iop.sdk.model.response.SearchProductResponse;
import cn.legendshop.jd.iop.sdk.model.response.productDetailResponse;
import cn.legendshop.jd.iop.sdk.util.JdAccessTokenUtil;
import cn.legendshop.jd.iop.sdk.util.JdMessageUtil;
import cn.legendshop.jd.iop.sdk.util.JdRequestUtils;
import cn.legendshop.product.api.constant.ProdChangeConstant;
import cn.legendshop.messagePool.api.client.MessagePoolClient;
import cn.legendshop.messagePool.api.dto.MessagePoolMessageDTO;
import cn.legendshop.messagePool.api.dto.prod.MaterialProductPriceChangeMessageDTO;
import cn.legendshop.messagePool.api.dto.prod.MaterialProductStateChangeMessageDTO;
import cn.legendshop.messagePool.api.enums.MessageTypeEnum;
import cn.legendshop.order.client.v2.QuotationClient;
import cn.legendshop.product.api.dto.*;
import cn.legendshop.product.api.entity.*;
import cn.legendshop.product.api.enums.*;
import cn.legendshop.product.api.vo.*;
import cn.legendshop.product.dao.*;
import cn.legendshop.product.mq.producer.IndustrialProductProducerService;
import cn.legendshop.product.mq.producer.JdProductProducerService;
import cn.legendshop.product.mq.producer.ProdChangeLogProducerService;
import cn.legendshop.product.service.JdIopProductService;
import cn.legendshop.product.service.MessagePoolProductService;
import cn.legendshop.product.service.MsCategoryService;
import cn.legendshop.product.service.ProdPriceContactsService;
import cn.legendshop.product.service.convert.JdIopProductConverter;
import cn.legendshop.search.api.client.IndustrialProductIndexClient;
import cn.legendshop.search.api.mq.param.IndustrialProductActionParam;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.legendshop.common.data.cache.util.RedisUtil;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageParams;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.lambda.LambdaEntityCriterion;
import com.legendshop.dao.support.update.LambdaUpdate;
import com.legendshop.model.constant.PayMannerEnum;
import com.legendshop.model.entity.Product;
import com.legendshop.model.entity.ShopDetail;
import com.legendshop.model.entity.Sku;
import com.legendshop.util.AppUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.legendshop.product.api.enums.IndexUpdateFlagEnum.UNUPDATE;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.OFF_SHELF;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.ON_SHELF;

@Service
@AllArgsConstructor
@Slf4j
public class JdIopProductServiceImpl implements JdIopProductService {

	private final IndustrialProductProducerService industrialProductProducerService;

	private final JdAccessTokenUtil jdAccessTokenUtil;

	private final JdIopProductConverter jdIopProductConverter;

	private final JdProductDao jdProductDao;

	private final JdProductHisitoryDao jdProductHisitoryDao;

	private final CategoryMroSupplierDao categoryMroSupplierDao;

	private final ProductDao productDao;

	private final SkuDao skuDao;

	private final JdCategoryDao jdCategoryDao;

	private final ThirdParameterConfig thirdParameterConfig;

	private final MaterialProductRelDao materialProductRelDao;

	private final ShopDetailDao shopDetailDao;

	private final RedisUtil redisUtil;

	private final JdProductProducerService jdProductProducerService;

	private final IndustrialProductIndexClient industrialProductIndexClient;

	private final IndustrialProductDao industrialProductDao;

	private final MsCategoryService msCategoryService;


	private final OpenMaterialProductRelDao openMaterialProductRelDao;


	private final MessagePoolProductService messagePoolProductService;

	private final IndustrialOuterShopSwitchSettleDao industrialOuterShopSwitchSettleDao;

	private final ProdChangeLogProducerService prodChangeProducerService;

	private final QuotationClient quotationClient;

	private final SimilarityProductTaskClient similarityProductTaskClient;

	private final IndustrialOuterShopConfigService configService;

	private final ProdPriceContactsService prodPriceContactsService;

	@Override
	public List<PriceResponse> priceCheck(String skuId) {

		String queryExts = "price,taxPrice,containsTax";
		PriceRequest priceRequest = new PriceRequest();
//		priceRequest.setToken(jdAccessTokenUtil.getIOPAccessToken())
		priceRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		priceRequest.setSku(skuId)
			.setQueryExts(queryExts);

		CommonResponse<List<PriceResponse>> response;
		int flag = 3;
		do {
			response = JdRequestUtils.sendPostForList(ProductApi.PRODUCT_GET_PRICE, priceRequest, PriceResponse.class);
			flag--;
			if (flag == 0) {
				break;
			}
		} while (response.getResultCode().equals(ErrorKeyEnum.TRY_AGAIN.getStatus()));
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + response.getResultCode());
		}

		return response.getResult();
	}

	@Override
	public Boolean checkPriceChange(List<String> skuIds) {
//		商品编号，请以，(英文逗号)分割。例如：129408,129409(最高支持100个商品)
		String skuIdsStr = String.join(",", skuIds.stream().map(String::valueOf).toArray(String[]::new));
		List<PriceResponse> priceResponses = this.priceCheck(skuIdsStr);
		if (CollUtil.isEmpty(priceResponses)) {
			return Boolean.FALSE;
		}
		Map<Long, PriceResponse> responseMap = priceResponses.stream().collect(Collectors.toMap(PriceResponse::getSkuId, Function.identity()));

		List<IndustrialProduct> jdProducts = industrialProductDao.queryByShopIdAndSkuId(skuIds, thirdParameterConfig.getJdIOPSetting().getShopId());
		Map<String, IndustrialProduct> jdProductMap = jdProducts.stream().collect(Collectors.toMap(IndustrialProduct::getSkuId, Function.identity()));
		List<IndustrialProduct> updateList = new ArrayList<>();
		List<String> updateIndexList = new ArrayList<>();
		//为空 尝试导入商品
		if (ObjectUtil.isEmpty(jdProducts)) {
			//非入库商品不需要处理！
			return Boolean.TRUE;
		}

		// 用于存储价格变更记录
		List<ProdChangeLogDTO> priceChangeRecords = new ArrayList<>();

		for (Map.Entry<String, IndustrialProduct> entry : jdProductMap.entrySet()) {
			PriceResponse priceResponse = responseMap.get(Long.parseLong(entry.getKey()));
			if (ObjectUtil.isEmpty(priceResponse)) {
				continue;
			}
			IndustrialProduct jdProduct = entry.getValue();

			// 记录变更前的价格
			BigDecimal beforePrice = jdProduct.getPrice();

			jdIopProductConverter.toIndustrial(priceResponse, jdProduct);

			log.info("商品价格更新：商品sku为:{}",jdProduct.getSkuId());
			// 计算变更后的价格和价格变化比率
			BigDecimal afterPrice = jdProduct.getPrice();
			BigDecimal priceChangeRate = calculatePriceChangeRate(beforePrice, afterPrice);
			if(priceChangeRate.compareTo(BigDecimal.ZERO) != 0) {
				// 创建价格变更记录
				ProdChangeLogDTO record = new ProdChangeLogDTO();
				record.setShopId(thirdParameterConfig.getJdIOPSetting().getShopId());
				record.setShopName(thirdParameterConfig.getJdIOPSetting().getShopName());
				record.setSkuId(jdProduct.getSkuId());  // 商品sku
				record.setBeforePrice(beforePrice);  //变更前价格
				record.setAfterPrice(afterPrice);  //变更后价格
				record.setPriceChangeRate(priceChangeRate);  // 价格变化比率
				record.setRequestDate(new Date());  // 当前请求日期
				priceChangeRecords.add(record); // 将记录添加到列表
			}

			updateIndexList.add(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + jdProduct.getSkuId());
			updateList.add(jdProduct);
		}

		// 更新商品信息
		industrialProductDao.updateProdsByShopId(updateList, thirdParameterConfig.getJdIOPSetting().getShopId());
		// 建立索引
		if (ObjectUtil.isNotEmpty(updateIndexList)) {
			IndustrialProductActionParam industrialProductActionParam = new IndustrialProductActionParam();
			industrialProductActionParam.setIndexIds(updateIndexList);
			industrialProductProducerService.updateBatchIndustrialIndex(industrialProductActionParam);
		}
		// 通知采购方
		for (IndustrialProduct product : updateList) {
			String skuId = product.getSkuId();
			// 通知南钢erp
			MaterialProductRel rel = this.materialProductRelDao.getByProperties(new EntityCriterion().eq("supplier", ThirdIndustrialEnum.JD.getCode()).eq("productCode", product.getSkuId()).eq("isSured", 1).limit(1));
			if (ObjectUtil.isNotEmpty(rel)) {
				jdProductProducerService.noticeErpProductInfoUpdate(Long.parseLong(skuId));
			}
		}
		// 通知采购商
		messagePoolProductService.pushBindProductPriceChange(thirdParameterConfig.getJdIOPSetting().getShopId(), updateList.stream().map(IndustrialProduct::getSkuId).collect(Collectors.toList()));

		// 发送MQ消息以进行异步处理
		if(priceChangeRecords != null && !priceChangeRecords.isEmpty()) {
			priceChangeRecords.forEach(record -> {
				prodChangeProducerService.sendPriceChangeMessage(record);// 发送每个价格变更记录到MQ

				// 判断该商品是否需要提醒
				List<ProdPriceContacts> priceLogList = prodPriceContactsService.getBySkuAndShopId(record.getShopId(), record.getSkuId());
				log.info("需要进行价格变化公众号提醒的商品:{}",JSON.toJSONString(priceLogList));
				if(AppUtils.isNotBlank(priceLogList)) {
					for (ProdPriceContacts priceLog : priceLogList) {
						ProdPriceContactsVo vo = new ProdPriceContactsVo();
						vo.setShopId(priceLog.getShopId());
						vo.setSkuId(priceLog.getSkuId());
						vo.setShopName(record.getShopName());
						vo.setMaterialCode(priceLog.getMaterialCode());
						vo.setBeforePrice(record.getBeforePrice());
						vo.setAfterPrice(record.getAfterPrice());
						vo.setBidPrice(priceLog.getBidPrice());
						vo.setContacts(priceLog.getContacts());
						log.info("指定商品提醒的联系人:{}",priceLog.getContacts());
						vo.setPriceChangeRate(record.getPriceChangeRate());
						prodChangeProducerService.sendProdPriceWXMessage(vo);// 发送价格变更微信公众号提醒MQ
					}
				}
			});
		}
		return Boolean.TRUE;
	}

	/**
	 * 计算价格变化率
	 */
	private BigDecimal calculatePriceChangeRate(BigDecimal beforePrice, BigDecimal afterPrice) {
		if (beforePrice == null || beforePrice.compareTo(BigDecimal.ZERO) == 0) {
			return BigDecimal.ZERO;
		}
		return afterPrice.subtract(beforePrice)
				.divide(beforePrice, 4, RoundingMode.HALF_UP);
	}


	@Override
	public void saveJdProduct(List<Long> skuIds) {
		// 查询商品审核开关
		boolean switchStatus = industrialOuterShopSwitchSettleDao.getSwitchStatus(ThirdIndustrialEnum.JD.getShopId(), OpenPlatformShopSwitchTypeEnum.SHOP_AUDIT.getValue());

		//0失败 1成功 2商品表已存在
		log.debug("接到商品id为:  " + skuIds);
		List<String> skuIdStrings = skuIds.stream().map(String::valueOf).collect(Collectors.toList());

		List<IndustrialProduct> industrialProducts = this.industrialProductDao.queryByShopIdAndSkuId(skuIdStrings, thirdParameterConfig.getJdIOPSetting().getShopId());
//		List<String> existSkuIds = industrialProducts.stream().map(IndustrialProduct::getSkuId).collect(Collectors.toList());

		List<JdProductHisitory> jdProductHisitories = this.jdProductHisitoryDao.queryByProperties(new LambdaEntityCriterion<>(JdProductHisitory.class).in(JdProductHisitory::getSkuId, skuIds).eq(JdProductHisitory::getStatus, 2));

		StringBuilder sb = new StringBuilder();
		Map<String, productDetailResponse> productDetailMap = new HashMap<>();
		//6.查询商品详情页话术,获取商品预计发货日期
		Map<String, ProductDetailTipsResponse> productDetailTipsMap = new HashMap<>();
		// 并行获取商品详情

        // 等待所有查询完成
		CompletableFuture.allOf(skuIdStrings.stream()
                .map(querySkuId -> CompletableFuture.runAsync(() -> {
                    // 1、查商品详情
                    productDetailResponse productDetailResponse = this.queryProductDetail(Long.parseLong(querySkuId));

                    if (ObjectUtil.isEmpty(productDetailResponse)) {
                        JdProductHisitory jdProductHisitory = jdProductHisitories.stream().filter(u -> u.getSkuId().equals(Long.parseLong(querySkuId))).findFirst().orElse(null);
                        if (jdProductHisitory != null) {
                            jdProductHisitory.setStatus(ThirdProductImportStatusEnum.IN_STORE_ERROR.getNum());
                            jdProductHisitory.setMessage("查不到商品详情！");
                        }
                    } else {
                        sb.append(querySkuId).append(StrUtil.COMMA);
                        // 部分参数转化为我们的格式
                        productParamTransform(productDetailResponse);
                        ProductDetailTipsResponse productDetailTips = getProductDetailTips(querySkuId);
                        // 放到Map里面，下面使用
                        synchronized (productDetailMap) {
                            productDetailMap.put(querySkuId, productDetailResponse);
                            productDetailTipsMap.put(querySkuId, productDetailTips);
                        }
                    }
                })).toArray(CompletableFuture[]::new)).join();

		//所有记录都找不到！
		if (productDetailMap.size() == 0) {
			jdProductHisitoryDao.update(jdProductHisitories);
			return;
		}


		//3、查询商品上下架状态
		List<OnSaleResponse> onSaleResponses = this.checkOnSaleState(sb.toString());
		Map<Long, OnSaleResponse> onsaleStatusMap = onSaleResponses.stream().collect(Collectors.toMap(OnSaleResponse::getSku, e -> e));

		//4、价格
		List<PriceResponse> priceResponses = this.priceCheck(sb.toString());
		Map<Long, PriceResponse> priceMap = priceResponses.stream().collect(Collectors.toMap(PriceResponse::getSkuId, e -> e));

		//5、可售型和售后
		List<CheckSalabilityResponse> checkSalabilities = this.checkOnSaleStatus(sb.toString(),
			ProductCheckExtsEnum.IS_SELF.getValue() + StrUtil.COMMA + ProductCheckExtsEnum.NO_REASON_TO_RETURN.getValue() + StrUtil.COMMA +
				ProductCheckExtsEnum.THWA.getValue() + StrUtil.COMMA + ProductCheckExtsEnum.IS_JD_LOGISTICS.getValue());
		Map<Long, CheckSalabilityResponse> afterSaleMap = checkSalabilities.stream().collect(Collectors.toMap(CheckSalabilityResponse::getSkuId, e -> e));

		//保存到数据库

		List<IndustrialProduct> saveList = new ArrayList<>();
		List<IndustrialProduct> updateList = new ArrayList<>();

		for (String skuId : productDetailMap.keySet()) {
			try {
				productDetailResponse productDetailResponse = productDetailMap.get(skuId);
				OnSaleResponse onSaleResponse = onsaleStatusMap.get(Long.parseLong(skuId));
				PriceResponse priceResponse = priceMap.get(Long.parseLong(skuId));
				//价格没有就不保存
				if (ObjectUtil.isEmpty(priceResponse) || new BigDecimal(13).compareTo(priceResponse.getTax()) != 0) {
					JdProductHisitory jdProductHisitory = jdProductHisitories.stream().filter(u -> u.getSkuId().equals(Long.parseLong(skuId))).findFirst().orElse(null);
					if (ObjectUtil.isNotEmpty(jdProductHisitory)) {
						jdProductHisitory.setMessage("查不到商品价格！可能已下架或税率不等于13，不允许上架");
						jdProductHisitory.setStatus(ThirdProductImportStatusEnum.IN_STORE_SUCCESS.getNum());
					}
					continue;
				}

				CheckSalabilityResponse checkSalabilityResponse = afterSaleMap.get(Long.parseLong(skuId));

				IndustrialProduct jdProduct = jdIopProductConverter.toIndustrial(productDetailResponse, checkSalabilityResponse, onSaleResponse, priceResponse);
				jdProduct.setShopId(thirdParameterConfig.getJdIOPSetting().getShopId());
				jdProduct.setMroStatus(MroIndustrialProdStatusEnum.UNDERCARRIAGE.getNum());
				jdProduct.setUpdateFlag(UNUPDATE.getValue());

				//根据skuId获取京东商品详情页话术返回结果
				ProductDetailTipsResponse productDetailTipsResponse  = productDetailTipsMap.get(skuId);
				if(productDetailTipsResponse != null) {
					//获取每个skuId对应的预计发货天数
					Integer preDeliveryDayNum = productDetailTipsResponse.getDeliveryDays();
					if(preDeliveryDayNum != null) {
						jdProduct.setPreDeliveryDayNum(preDeliveryDayNum);
					}
				}
                JdProductHisitory jdProductHisitory = jdProductHisitories.stream().filter(u -> u.getSkuId().equals(Long.parseLong(skuId))).findFirst().orElse(null);
				jdProductHisitory.setStatus(ThirdProductImportStatusEnum.IN_STORE_SUCCESS.getNum());

				//如果Product已存在，则更新
				IndustrialProduct jdProductExist = industrialProducts.stream().filter(u -> u.getSkuId().equals(skuId)).findFirst().orElse(null);
				if (jdProductExist != null) {

					jdProduct.setUpdateTime(new DateTime());

					jdProduct.setId(jdProductExist.getId());
					//prodId也要set进去！！！！！
					if (jdProductExist.getProdId() != null) {
						jdProduct.setProdId(jdProductExist.getProdId());
					}

					updateList.add(jdProduct);
				} else {

					//新增导入时查询调价id，把调价id放入redis缓存
					SupplierCategoryBindErpVo jdCategory = this.msCategoryService.getJdCategory(jdProduct.getCategory());
					if (ObjectUtil.isNotEmpty(jdCategory)) {
						CategoryMroSupplier categoryMroSupplier = this.categoryMroSupplierDao.getBySupplierId(jdCategory.getId(), thirdParameterConfig.getJdIOPSetting().getShopId());
						if (ObjectUtil.isNotEmpty(categoryMroSupplier)) {
							jdProduct.setCategoryMroSupplierId(categoryMroSupplier.getId());
						}
					}

					jdProduct.setCreateTime(new DateTime());
					jdProduct.setUpdateTime(new DateTime());
					jdProduct.setMroStatus(ON_SHELF.getMroStatus());
					jdProduct.setUpdateFlag(UNUPDATE.getValue());
					jdProduct.setBuys(0);
					jdProduct.setViews(0);
					jdProduct.setComments(0);

					if (switchStatus){
						jdProduct.setState(OFF_SHELF.getMroStatus());
					}
					//保存到数组
					saveList.add(jdProduct);
				}

			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		if (CollUtil.isNotEmpty(saveList)) {
			List<Long> ids = industrialProductDao.saveProdByShopId(saveList, thirdParameterConfig.getJdIOPSetting().getShopId());
			// 修改上架状态到询价单明细
			saveList.forEach(e ->quotationClient.updateQuotationProduct(e.getSkuId()));
			saveList.forEach(e -> industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + e.getSkuId()));
			// 创建相似商品更新任务
			similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(2).setShopIdAndProdIdSet(ids.stream().map(id -> StrUtil.join(StrUtil.UNDERLINE, thirdParameterConfig.getJdIOPSetting().getShopId(), id)).collect(Collectors.toSet())));
		}
		if (CollUtil.isNotEmpty(updateList)) {
			industrialProductDao.updateProdsByShopId(updateList, thirdParameterConfig.getJdIOPSetting().getShopId());
			updateList.forEach(e -> industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + e.getSkuId()));
			// 根据更新的商品状态区分是创建相似商品更新任务还是删除任务
			updateList.forEach(e -> {
				if (e.getState().equals(OFF_SHELF.getMroStatus())) {
					similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(3).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, thirdParameterConfig.getJdIOPSetting().getShopId(), e.getId()))));
				}
				if (e.getState().equals(ON_SHELF.getMroStatus())) {
					similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(2).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, thirdParameterConfig.getJdIOPSetting().getShopId(), e.getId()))));
				}
			});
		}
		if (CollUtil.isNotEmpty(jdProductHisitories)) {
			jdProductHisitoryDao.update(jdProductHisitories);
		}

	}

	@Override
	public void deleteIndexByCategoryMroSupplierId(Long categoryMroSupplierId) {
		XxlJobLogger.log("开始重试定时任务");
		Integer pageSize = 20;
		Integer pageCur = 1;
		Boolean flag = false;

		do {
			PageSupport<IndustrialProduct> industrialPageSupport = this.industrialProductDao.pageQueryByMroSupplierIdByShopId(pageCur, pageSize, categoryMroSupplierId, thirdParameterConfig.getJdIOPSetting().getShopId());
			List<IndustrialProduct> resultList = industrialPageSupport.getResultList();

			if (CollUtil.isNotEmpty(resultList)) {
				resultList.forEach(e -> e.setCategoryMroSupplierId(null));
				this.industrialProductDao.updateProdsByShopId(resultList, thirdParameterConfig.getJdIOPSetting().getShopId());

				resultList.forEach(e -> {
					industrialProductProducerService.bindJdCategoryAndImportProduct(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + e.getSkuId());
				});

				pageCur++;
			} else {
				flag = true;
			}
		} while (!flag);
	}

	private void recordResult(Long skuId, Boolean flag, String message, String page_num) {
		JdProductHisitory productHisitory = this.jdProductHisitoryDao.getBySkuId(skuId);
		Integer status;
		if (flag) {
			status = ThirdProductImportStatusEnum.IN_STORE_SUCCESS.getNum();
		} else {
			status = ThirdProductImportStatusEnum.ALREADY_EXIST.getNum();
		}
//		List<Long> existSkudIds = jdProductHisitories.stream().map(JdProductHisitory::getSkuid).collect(Collectors.toList());
		//处理历史记录被
		if (ObjectUtil.isEmpty(productHisitory)) {
			DateTime dateTime = new DateTime();
			JdProductHisitory jdProductHisitory2 = new JdProductHisitory();
			Long id = this.jdProductHisitoryDao.createId();
			jdProductHisitory2.setId(id);
			jdProductHisitory2.setSkuId(skuId);
			jdProductHisitory2.setProductPool(Optional.ofNullable(page_num).orElse(""));
			jdProductHisitory2.setCreateTime(dateTime);
			jdProductHisitory2.setUpdateTime(dateTime);
			jdProductHisitory2.setStatus(ThirdProductImportStatusEnum.IN_STORE_SUCCESS.getNum());
			jdProductHisitory2.setMessage(message);
			this.jdProductHisitoryDao.save(jdProductHisitory2);
			return;
		}

		productHisitory.setStatus(status);
		productHisitory.setProductPool(Optional.ofNullable(page_num).orElse(""));
		productHisitory.setMessage(Optional.ofNullable(message).orElse(""));
		productHisitory.setUpdateTime(DateUtil.date());
		this.jdProductHisitoryDao.update(productHisitory);
	}

	/**
	 * 批量版
	 *
	 * @param skuIds
	 * @param flag
	 * @param message
	 * @param page_num
	 */
	private void recordResult2(List<Long> skuIds, Boolean flag, String message, String page_num) {
		List<JdProductHisitory> saveJdProductHisitoryList = new ArrayList<>();
		List<JdProductHisitory> jdProductHisitories = this.jdProductHisitoryDao.queryByProperties(new LambdaEntityCriterion<>(JdProductHisitory.class).in(JdProductHisitory::getSkuId, skuIds));
		List<JdProductHisitory> updateLists = new ArrayList<>();
		if (CollUtil.isNotEmpty(jdProductHisitories)) {
			updateLists = jdProductHisitories.stream()
				.filter(e -> skuIds.stream()
					.anyMatch(k -> k.equals(e.getSkuId())))
				.collect(Collectors.toList());
		}

		//set一下状态
		Integer status;
		if (flag) {
			status = ThirdProductImportStatusEnum.IN_STORE_SUCCESS.getNum();
		} else {
			status = ThirdProductImportStatusEnum.ALREADY_EXIST.getNum();
		}

		//不存在，就直接保存到历史表
		log.info("开始保存历史记录");
		if (CollUtil.isEmpty(updateLists)) {
			for (Long skuId : skuIds) {
				DateTime dateTime = new DateTime();
				JdProductHisitory jdProductHisitory2 = new JdProductHisitory();
				jdProductHisitory2.setSkuId(skuId);
				jdProductHisitory2.setProductPool(Optional.ofNullable(page_num).orElse(""));
				jdProductHisitory2.setCreateTime(dateTime);
				jdProductHisitory2.setUpdateTime(dateTime);
				jdProductHisitory2.setStatus(ThirdProductImportStatusEnum.IN_STORE_SUCCESS.getNum());
				jdProductHisitory2.setMessage(message);
				saveJdProductHisitoryList.add(jdProductHisitory2);
			}

			this.jdProductHisitoryDao.save(saveJdProductHisitoryList);
			return;
		}

		log.info("部分记录不为空");
		//先更新
		for (JdProductHisitory jdProductHisitory : jdProductHisitories) {
			jdProductHisitory.setStatus(status);
			jdProductHisitory.setMessage(Optional.ofNullable(message).orElse(""));
			jdProductHisitory.setUpdateTime(DateUtil.date());
		}
		log.info("执行更新操作");
		this.jdProductHisitoryDao.update(jdProductHisitories);


		List<Long> noneMatchSkuIds = skuIds.stream()
			.filter(e -> jdProductHisitories.stream()
				.noneMatch(k -> e.equals(k.getSkuId())))
			.collect(Collectors.toList());

		for (Long noneMatchSkuId : noneMatchSkuIds) {
			DateTime dateTime = new DateTime();
			JdProductHisitory jdProductHisitory2 = new JdProductHisitory();
			Long id = this.jdProductDao.createId();
			jdProductHisitory2.setId(id);
			jdProductHisitory2.setSkuId(noneMatchSkuId);
			jdProductHisitory2.setProductPool(Optional.ofNullable(page_num).orElse(""));
			jdProductHisitory2.setCreateTime(dateTime);
			jdProductHisitory2.setUpdateTime(dateTime);
			jdProductHisitory2.setStatus(ThirdProductImportStatusEnum.IN_STORE_SUCCESS.getNum());
			jdProductHisitory2.setMessage(message);
			saveJdProductHisitoryList.add(jdProductHisitory2);
		}
		log.info("执行保存操作");
		this.jdProductHisitoryDao.save(saveJdProductHisitoryList);
	}

	@Override
	public SearchProductResponse searchJdProduct(SearchProductRequest searchProductRequest) {

		CommonResponse<SearchProductResponse> response = JdRequestUtils.sendPost(ProductApi.PRODUCT_SEARCH, searchProductRequest, SearchProductResponse.class);
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + response.getResultCode());
		}

		return response.getResult();
	}

	@Override
	public List<GetMessageResponse> MessageCheck(Integer type) {

		GetMessageRequest productQueryRequest = new GetMessageRequest();
		productQueryRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		productQueryRequest.setType(type.toString());

		CommonResponse<List<GetMessageResponse>> listCommonResponse = JdRequestUtils.sendPostForList(ProductApi.MESSAGE_GET, productQueryRequest, GetMessageResponse.class);

		return listCommonResponse.getResult();
	}

	@Override
	public List<CommodityPoolNumResponse> queryProductPoolNum(String queryExts) {
		//1、搜索

		CommodityPoolNumRequest commodityPoolNumRequest = new CommodityPoolNumRequest();
		commodityPoolNumRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());

		if (StrUtil.isNotBlank(queryExts)) {
			commodityPoolNumRequest.setQueryExts(queryExts);
		}

		CommonResponse<List<CommodityPoolNumResponse>> response = JdRequestUtils.sendPostForList(ProductApi.PRODUCT_GET_PAGE_NUM, commodityPoolNumRequest, CommodityPoolNumResponse.class);
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + response.getResultCode());
		}

		return response.getResult();
	}

	@Override
	public PoolResponse querySkusInPool(String pageNum, Integer pageSize, Long offset) {

		PoolRequest poolRequest = new PoolRequest();
		poolRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		poolRequest.setPageSize(pageSize)
			.setPageNum(pageNum)
			.setOffset(offset);

		CommonResponse<PoolResponse> response = JdRequestUtils.sendPost(ProductApi.PRODUCT_POOL_ALL_SKUIDS, poolRequest, PoolResponse.class);
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + response.getResultCode());
		}

		return response.getResult();
	}

	@Override
	public productDetailResponse queryProductDetail(Long sku) {

		//todo 暂时不添加微信小程序描述
		ProductQueryRequest productQueryRequest = new ProductQueryRequest();
		productQueryRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
//		productQueryRequest.setToken(jdAccessTokenUtil.getIOPAccessToken())
		productQueryRequest.setSku(sku.toString())
			.setQueryExts(ProductDetailQueryExtsEnum.SEO_MODEL.getValue() + StrUtil.COMMA + ProductDetailQueryExtsEnum.IS_FACTORY_SHIP.getValue() + StrUtil.COMMA +
					ProductDetailQueryExtsEnum.SIZE_DESC.getValue() + StrUtil.COMMA + ProductDetailQueryExtsEnum.CATEGORY_ATTRS.getValue() + StrUtil.COMMA +
					ProductDetailQueryExtsEnum.CAPACITY.getValue() + StrUtil.COMMA + ProductDetailQueryExtsEnum.LOWEST_BUY.getValue() + StrUtil.COMMA +
					ProductDetailQueryExtsEnum.TAX_CODE.getValue() + StrUtil.COMMA + ProductDetailQueryExtsEnum.PARAM_DETAIL_JSON.getValue());
		CommonResponse<productDetailResponse> response;
		//刷3次京东接口，因为大概率报5001重试
		int flag = 3;
		do {
			response = JdRequestUtils.sendPost(ProductApi.PRODUCT_GET_DETAIL, productQueryRequest, productDetailResponse.class);
			flag--;
			if (flag == 0) {
				break;
			}
			if (ObjectUtil.isEmpty(response)) {
				return null;
			}
		} while (response.getResultCode().equals(ErrorKeyEnum.TRY_AGAIN.getStatus()));

		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			log.info("错误状态码为" + response.getResultCode());
			XxlJobLogger.log("错误状态码为" + response.getResultCode());
			return null;
		}

		return response.getResult();
	}

	@Override
	public List<ProductImagesResponse> queryProductImages(Long sku) {
		//todo 需要测试 queryExts是否会影响

		ProductQueryRequest productQueryRequest = new ProductQueryRequest();
		productQueryRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
//		productQueryRequest.setToken(jdAccessTokenUtil.getIOPAccessToken());
		productQueryRequest.setSku(sku.toString());

		CommonResponse<List<ProductImagesResponse>> listCommonResponse = JdRequestUtils.sendPostForList(ProductApi.PRODUCT_SKU_IMAGE, productQueryRequest, ProductImagesResponse.class);
		if (!listCommonResponse.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + listCommonResponse.getResultCode());
		}

		return listCommonResponse.getResult();
	}

	@Override
	public List<OnSaleResponse> checkOnSaleState(String sku) {

		ProductQueryRequest productQueryRequest = new ProductQueryRequest();
//		productQueryRequest.setToken(jdAccessTokenUtil.getIOPAccessToken());
		productQueryRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		productQueryRequest.setSku(sku);
		CommonResponse<List<OnSaleResponse>> response;
		int flag = 3;
		do {
			response = JdRequestUtils.sendPostForList(ProductApi.PRODUCT_SKU_STATE, productQueryRequest, OnSaleResponse.class);
			flag--;
			if (flag == 0) {
				break;
			}
		} while (response.getResultCode().equals(ErrorKeyEnum.TRY_AGAIN.getStatus()));
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			log.error("错误状态码为： " + response.getResultCode());
		}

		return response.getResult();
	}

	@Override
	public List<CheckSalabilityResponse> checkOnSaleStatus(String skuIds, String queryExts) {
		ProductCheckRequest productQueryRequest = new ProductCheckRequest();
//		productQueryRequest.setToken(jdAccessTokenUtil.getIOPAccessToken());
		productQueryRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		productQueryRequest.setSkuIds(skuIds);
		productQueryRequest.setQueryExts(queryExts);

		CommonResponse<List<CheckSalabilityResponse>> response;
		int flag = 3;
		do {
			response = JdRequestUtils.sendPostForList(ProductApi.PRODUCT_CHECK, productQueryRequest, CheckSalabilityResponse.class);
			flag--;
			if (flag == 0) {
				break;
			}
		} while (response.getResultCode().equals(ErrorKeyEnum.TRY_AGAIN.getStatus()));
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + response.getResultCode());
		}
		log.info(response.getResult().toString());
		return response.getResult();
	}

	//获取预计发货天数
	private ProductDetailTipsResponse getProductDetailTips(String skuId) {
		ProductDetailTipsRequest productDetailTipsRequest = new ProductDetailTipsRequest();
		CommonResponse<ProductDetailTipsResponse> response;

		productDetailTipsRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		productDetailTipsRequest.setSkuId(skuId); // 设置SKU ID
		productDetailTipsRequest.setProvince(12);
		productDetailTipsRequest.setCity(904);
		productDetailTipsRequest.setCounty(908);
		productDetailTipsRequest.setTown(0);
		productDetailTipsRequest.setNum("1");
		// 直接将 skuId 和响应结果放入 Map
		int flag = 3;
		do {
			response = JdRequestUtils.sendPost(ProductApi.PRODUCT_GET_DETAIL_TIPS, productDetailTipsRequest, ProductDetailTipsResponse.class);
			flag--;
			if (flag == 0) {
				break;
			}
		} while (response.getResultCode().equals(ErrorKeyEnum.TRY_AGAIN.getStatus()));
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + response.getResultCode());
		}
		if(response.getResult() == null){
			return null;
		}
		log.info(response.getResult().toString());

		return response.getResult();
	}

	@Override
	public AreaSaleLimitResponse checkSaleLimit(JdSkuAddressDTO jdSkuAddressDTO) {
		CommonResponse<AreaSaleLimitResponse> response = JdRequestUtils.sendPost(ProductApi.PRODUCT_CHECK_AREA_LIMIT, jdSkuAddressDTO, AreaSaleLimitResponse.class);
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + response.getResultCode());
		}

		return response.getResult();
	}

	@Override
	public ProductGiftResponse queryProductSkuGift(JdSkuAddressDTO jdSkuAddressDTO) {
		CommonResponse<ProductGiftResponse> response = JdRequestUtils.sendPost(ProductApi.PRODUCT_GET_SKU_GIFT, jdSkuAddressDTO, ProductGiftResponse.class);
		if (!response.getResultCode().equals(ErrorKeyEnum.NORMAL.getStatus())) {
			throw new BusinessException("错误状态码为" + response.getResultCode());
		}

		return response.getResult();
	}


	@Override
	public CommonSupplierProductDTO getJdIOProductById(Long prodId) {
		JdProduct jdProduct = jdProductDao.getById(prodId);
		return jdIopProductConverter.toCommonSupplierProductDTO(jdProduct);
	}


	@Override
	public List<CommonSupplierProductDTO> queryUpdateProd(Integer updateFlag, Integer currentPage, Integer pageSize) {
		List<JdProduct> jdProducts = jdProductDao.queryUpdateProd(updateFlag, currentPage, pageSize);
		return jdIopProductConverter.toCommonSupplierProductDTOList(jdProducts);
	}

	@Override
	public List<CommonSupplierProductDTO> queryProdByStatus(Integer status, Integer currentPage, Integer pageSize) {
		List<JdProduct> jdProducts = jdProductDao.queryProdByStatus(status, currentPage, pageSize);
		return jdIopProductConverter.toCommonSupplierProductDTOList(jdProducts);
	}


	@Override
	public Boolean updateStatus(List<updataListVo> updataListVos, Integer flag) {
		if (MroIndustrialProdStatusEnum.findByCode(flag).getNum() == null) {
			return Boolean.FALSE;
		}
		List<Long> prodId = updataListVos.stream().map(updataListVo::getId).collect(Collectors.toList());
		return SqlUtil.retBool(this.jdProductDao.updateProperties(new LambdaUpdate<>(JdProduct.class).in(JdProduct::getId, prodId).set(JdProduct::getStatus, flag)));
	}

	@Override
	public Long getMroCategoryId(long parseLong) {

		//先从京东商品表拿出关联表id
		LambdaEntityCriterion<JdProduct> eq = new LambdaEntityCriterion<>(JdProduct.class).eq(JdProduct::getSkuId, parseLong);
		JdProduct jdProduct = this.jdProductDao.getByProperties(eq);

		//再拿出关联的mro类目id
		LambdaEntityCriterion<CategoryMroSupplier> eq1 = new LambdaEntityCriterion<>(CategoryMroSupplier.class).eq(CategoryMroSupplier::getId, jdProduct.getCategoryMroSupplierId());
		CategoryMroSupplier categoryMroSupplier = this.categoryMroSupplierDao.getByProperties(eq1);
		if (ObjectUtil.isNotEmpty(categoryMroSupplier)) {
			return categoryMroSupplier.getMroId();
		} else {
			return 0L;
		}
	}

	@Override
	public Boolean updateOnSaleStatus(Long skuId, Long state) {
		IndustrialProduct jdProduct = this.industrialProductDao.getByShopIdAndSkuId(skuId.toString(), thirdParameterConfig.getJdIOPSetting().getShopId());
		if (ObjectUtil.isEmpty(jdProduct)) {
			if (Objects.equals(state, OFF_SHELF.getMroStatus().longValue())) {
				log.info("不需要处理");
				return Boolean.TRUE;
			} else {
				//查不到说明没入库
				log.info("返回false保存历史表：{}", skuId);
				return Boolean.FALSE;
			}
		}

		//state 是下架，下架并且删除索引
		if (state == OFF_SHELF.getMroStatus().longValue()) {
			//更新mro状态
			log.info("更新mro状态：{}", skuId);
			jdProduct.setMroStatus(MroIndustrialProdStatusEnum.UNDERCARRIAGE.getNum());
			jdProduct.setState(OFF_SHELF.getMroStatus());
//			//更新prod表的status
//			if (jdProduct.getProdId() != null) {
//				Product product = this.productDao.getById(jdProduct.getProdId());
//				product.setStatus(MroIndustrialProdStatusEnum.UNDERCARRIAGE.getNum());
//				this.productDao.update(product);
//			}
			//删除索引
			log.info("删除京东商品索引：{}", skuId);
			industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + skuId);
			this.industrialProductDao.updateProdByShopId(jdProduct, thirdParameterConfig.getJdIOPSetting().getShopId());
		}

		//state是上架，但jd商品是下架状态，但绑定id不为空(mro商城可以上架)，则把商品上架
		if (state == ON_SHELF.getMroStatus().longValue()) {

			log.info("更新mro状态：{}", skuId);
			//如果已经导入商品表，则设prod表的status和jd商品表的mro_status和status为上架状态
			jdProduct.setState(ON_SHELF.getMroStatus());

			if (jdProduct.getCategoryMroSupplierId() != null) {
				jdProduct.setMroStatus(ON_SHELF.getMroStatus());
			}
			this.industrialProductDao.updateProdByShopId(jdProduct, thirdParameterConfig.getJdIOPSetting().getShopId());

			//把此商品添加到索引
			IndustrialProductActionParam industrialProductActionParam = new IndustrialProductActionParam();
			List<String> index = Stream.of(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + jdProduct.getSkuId()).collect(Collectors.toList());
			industrialProductActionParam.setIndexIds(index);
			log.info("新增京东商品索引：{}", skuId);
			index.forEach(industrialProductProducerService::bindJdCategoryAndImportProduct);
		}
		// 通知采购商
		messagePoolProductService.pushBindProductStateChange(thirdParameterConfig.getJdIOPSetting().getShopId(), Collections.singletonList(String.valueOf(skuId)), state.intValue());
		return Boolean.TRUE;
	}

	@Override
	public void updateProductDetail(Long skuId) {
		Long shopId = thirdParameterConfig.getJdIOPSetting().getShopId();
		IndustrialProduct jdProduct = this.industrialProductDao.getByShopIdAndSkuId(skuId.toString(), shopId);
		if (ObjectUtil.isEmpty(jdProduct)) {
			//非推送商品，不需要处理
			log.debug("非推送商品，不需要处理!");
			return;
		}

		//查询商品详情
		productDetailResponse productDetailResponse = this.queryProductDetail(skuId);
		if (ObjectUtil.isEmpty(productDetailResponse)) {
			return;
		}
		productParamTransform(productDetailResponse);

		jdIopProductConverter.toIndustrialDetail(productDetailResponse, jdProduct);

		if (!jdProduct.getImagePath().contains("http://img13.360buyimg.com/n0/")) {
			jdProduct.setImagePath("http://img13.360buyimg.com/n0/" + jdProduct.getImagePath());
		}

		this.industrialProductDao.updateProdByShopId(jdProduct, shopId);


		// 绑定商品通知采购商:更新商品信息
		messagePoolProductService.pushBindProductInfoChange(shopId, Collections.singletonList(String.valueOf(skuId)));
		//更新索引
		industrialProductProducerService.updateIndustrialIndex(shopId + StrUtil.COLON + jdProduct.getSkuId());
	}

	private void productParamTransform(productDetailResponse productDetailResponse) {
		if (!productDetailResponse.getImagePath().contains("http://img13.360buyimg.com/n0/")) {

			productDetailResponse.setImagePath("http://img13.360buyimg.com/n0/" + productDetailResponse.getImagePath());
		}
		//转换param参数
		System.out.println(JSON.toJSONString(productDetailResponse));
		String detailJson = productDetailResponse.getParamDetailJson();
		//转化为我们的格式
		if (!StringUtils.isBlank(detailJson) && !"[]".equals(detailJson)) {
//			JdParamDetailJsonDTO
			List<CommonParam> commonParams = new ArrayList<>();
			List<JdParamDetailJsonDTO> jdParamDetailJsonDTOS = JSONObject.parseArray(detailJson, JdParamDetailJsonDTO.class);
			for (JdParamDetailJsonDTO jdParamDetailJsonDTO : jdParamDetailJsonDTOS) {
				CommonParam commonParam = new CommonParam();
				commonParam.setParamGroup(jdParamDetailJsonDTO.getGroupName());
//					System.out.println("before" + JSON.toJSONString(jdParamDetailJsonDTO));

				List<CommonParamValue> commonParamValues = new ArrayList<>();
				for (JdSpecifyDTO att : jdParamDetailJsonDTO.getAtts()) {
					CommonParamValue commonParamValue = new CommonParamValue();
					commonParamValue.setParamName(att.getAttrName());

					// 安全处理vals字段，可能是JSON数组字符串，也可能是简单字符串
					String valsStr = att.getVals();
					String paramValue;
					if (StringUtils.isBlank(valsStr)) {
						paramValue = "";
					} else {
						try {
							// 尝试作为JSON数组解析
							List<String> valStrings = JSON.parseArray(valsStr, String.class);
							paramValue = String.join(StrUtil.SPACE, valStrings);
						} catch (Exception e) {
							// 如果解析失败，直接使用原始字符串
							paramValue = valsStr;
						}
					}

					commonParamValue.setParamValue(paramValue);
					commonParamValues.add(commonParamValue);
				}
				commonParam.setValue(commonParamValues);
				commonParams.add(commonParam);
//					System.out.println("after" + JSON.toJSONString(jdParamDetailJsonDTO));
			}
			productDetailResponse.setParamDetailJson(JSON.toJSONString(commonParams));
		}
	}

//	private void updateProd(IndustrialProduct jdProduct) {
//		Product product = this.productDao.getProductById(jdProduct.getProdId());
//		product.setPic(jdProduct.getImagePath());
//		product.setPrice(jdProduct.getJdPrice().doubleValue());
//		product.setCash(jdProduct.getPrice().doubleValue());
//		product.setUnit(jdProduct.getSaleUnit());
//		product.setStocks(10000);
//		product.setName(jdProduct.getJdProductName());
//		product.setModifyDate(new DateTime());
//		product.setUserName(ThirdIndustrialEnum.JD.getName());
//		product.setShopThirdCatId(Long.getLong(jdProduct.getCategory()));
//		product.setStatus(ThirdProductOnSaleStatusEnum.findByJdStatus(jdProduct.getStatus()).getMroStatus());
//		product.setPublishStatus(jdProduct.getStatus() == 10 ? 1 : 0);
//		this.productDao.update(product);
//	}


	@Override
	public PageSupport<IndustrialProduct> queryJdIopProduct(PageParams pageParams) {

//		return this.jdProductDao.queryByPage(pageParams);
		return this.industrialProductDao.queryByPageByShopId(pageParams, null, ON_SHELF.getMroStatus(), null, thirdParameterConfig.getJdIOPSetting().getShopId());
	}

	@Override
	public IndustrialProduct getJdProductBySkuIds(Long skuId) {

		IndustrialProduct industrialProduct = this.industrialProductDao.getByShopIdAndSkuId(skuId.toString(), thirdParameterConfig.getJdIOPSetting().getShopId());
		return industrialProduct;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void checkJDPriceAndOnSaleStatus(List<IndustrialProduct> jdProducts) {

		List<String> indexIds = new ArrayList<>();
		String skuIds = jdProducts.stream().map(IndustrialProduct::getSkuId).map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA));
		List<String> updateList = new ArrayList<>();
		List<String> deleteList = new ArrayList<>();


		//查询价格
		List<PriceResponse> priceResponses = this.priceCheck(skuIds);
		if (CollUtil.isEmpty(priceResponses)) {
			log.info("价格查询失败");
		}
		//查询上下架
		List<OnSaleResponse> onSaleResponses = this.checkOnSaleState(skuIds);
		if (CollUtil.isEmpty(onSaleResponses)) {
			log.info("价格查询失败");
		}
		Map<Long, OnSaleResponse> stateMap = onSaleResponses.stream().collect(Collectors.toMap(OnSaleResponse::getSku, e -> e));
		Map<Long, PriceResponse> priceMaps = priceResponses.stream().collect(Collectors.toMap(PriceResponse::getSkuId, e -> e));

		for (IndustrialProduct jdProduct : jdProducts) {
			//先找e的价格
			PriceResponse priceResponse = priceMaps.get(jdProduct.getSkuId());
			if (ObjectUtil.isEmpty(priceResponse)) {
				log.info("查到价格为空");
			} else {

				if (priceResponse.getPrice() == null || priceResponse.getPrice().compareTo(BigDecimal.ZERO) < 0) {
					log.info("暂无报价");

					//价格更新
				} else if (priceResponse.getPrice().compareTo(Optional.ofNullable(jdProduct.getPrice()).orElse(BigDecimal.ZERO)) != 0 ||
					priceResponse.getJdPrice().compareTo(jdProduct.getMarketPrice()) != 0 ||
					priceResponse.getTax().compareTo(jdProduct.getTaxRate()) != 0 ||
					priceResponse.getNakedPrice().compareTo(jdProduct.getUntaxedPrice()) != 0) {

					//只有以上内容有变化才改变
					jdIopProductConverter.toIndustrialProductChange(priceResponse, jdProduct);
					log.info("更新的商品skuId为" + jdProduct.getSkuId());
					updateList.add(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + jdProduct.getSkuId());
				}
			}
//===============================价格更新==========================================================================================================
			OnSaleResponse onSaleResponse = stateMap.get(jdProduct.getSkuId());
			if (ObjectUtil.isEmpty(onSaleResponse)) {
				continue;
			}
			//state 是下架，但京东商品是上架状态，就把商品下架
			if (onSaleResponse.getState() == OFF_SHELF.getMroStatus().longValue()) {
				//更新mro状态
				jdProduct.setMroStatus(MroIndustrialProdStatusEnum.UNDERCARRIAGE.getNum());
				jdProduct.setState(OFF_SHELF.getMroStatus());
				//更新索引
				updateList.add(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + jdProduct.getSkuId());
			}

			//state是上架，但jd商品是下架状态，但绑定id不为空(mro商城可以上架)，则把商品上架
			if (onSaleResponse.getState() == ON_SHELF.getMroStatus().longValue()) {
				//如果已经导入商品表，则设prod表的status和jd商品表的mro_status和status为上架状态
				jdProduct.setState(ON_SHELF.getThirdStatus());
				jdProduct.setMroStatus(ON_SHELF.getMroStatus());
				indexIds.add(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + jdProduct.getSkuId());
			}
			// 绑定商品通知采购商:更新上下架状态
			messagePoolProductService.pushBindProductStateChange(thirdParameterConfig.getJdIOPSetting().getShopId(), Collections.singletonList(jdProduct.getSkuId()), onSaleResponse.getState());
		}
		this.industrialProductDao.updateProdsByShopId(jdProducts, thirdParameterConfig.getJdIOPSetting().getShopId());
		// 绑定商品通知采购商:更新价格
		messagePoolProductService.pushBindProductPriceChange(thirdParameterConfig.getJdIOPSetting().getShopId(), jdProducts.stream().map(IndustrialProduct::getSkuId).map(String::valueOf).collect(Collectors.toList()));


		//事务完成后，提交成功后，进行修改
		TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
			@Override
			public void afterCommit() {
				log.info("-----------productUpdate committed afterCommit-------");
//				deleteList.forEach(industrialProductProducerService::deleteProductIndex);
				industrialProductProducerService.sendThirdPartyLogMQ("JD", "商品上下架状态更新", deleteList.stream().collect(Collectors.joining(StrUtil.COMMA)));
				//上架索引
				log.info("上架索引");
				indexIds.forEach(industrialProductProducerService::bindJdCategoryAndImportProduct);

				//更新索引
				updateList.forEach(industrialProductProducerService::updateIndustrialIndex);
			}
		});
		log.info("查询完毕，更新京东商品表");
	}

	@Override
	public void saveJdProductBycommodityPool(CommodityPoolNumResponse commodityPoolNumRespons, Long offset, Long total) {
		Long flag = null;
//		log.info("进来保存历史记录");
		PoolRequest poolRequest = new PoolRequest();
//		poolRequest.setToken(jdAccessTokenUtil.getIOPAccessToken())
		poolRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		poolRequest.setPageSize(200)
			.setPageNum(commodityPoolNumRespons.getPage_num())
			.setOffset(offset);
		CommonResponse<PoolResponse> response = new CommonResponse<>();
		response = JdRequestUtils.sendPost(ProductApi.PRODUCT_POOL_ALL_SKUIDS, poolRequest, PoolResponse.class);

		if (response.getResultCode().equals(ErrorKeyEnum.RETURN_EMPTY.getStatus())) {
			log.info("商品池：{}，总数量为： {}", commodityPoolNumRespons.getPage_num(), total);
			String totalCount = redisUtil.getObject("legendshop.JD-Iop.totalCount").toString();
			if (AppUtils.isBlank(totalCount)) {
				redisUtil.setObject("legendshop.JD-Iop.totalCount", 0, 2L, TimeUnit.HOURS);
			} else {
				redisUtil.setObject("legendshop.JD-Iop.totalCount", Long.parseLong(totalCount) + total, 2L, TimeUnit.HOURS);
			}

			log.info("错误状态码为" + response.getResultCode());
			return;
		}

		//处理5001重试情况
		if (response.getResultCode().equals(ErrorKeyEnum.TRY_AGAIN.getStatus())) {
			do {
				response = JdRequestUtils.sendPost(ProductApi.PRODUCT_POOL_ALL_SKUIDS, poolRequest, PoolResponse.class);
			} while (response.getResultCode().equals(ErrorKeyEnum.TRY_AGAIN.getStatus()));
		}

		List<Long> skuIds = response.getResult().getSkus();

		if (CollUtil.isEmpty(skuIds)) {
			log.info("skuIds为空");
			return;
		}

		flag = skuIds.get(skuIds.size() - 1);

		skuIds.forEach(u -> {
//			JdHistoryVo jdHistoryVo = new JdHistoryVo();
//			jdHistoryVo.setPoolNum(commodityPoolNumRespons.getPage_num());
//			jdHistoryVo.setSkuId(u);
//			productProducerService.saveProductHistory(jdHistoryVo);

				try {
					int count = jdProductHisitoryDao.getCount(u.toString(), commodityPoolNumRespons.getPage_num());
					if (count == 0) {
						JdProductHisitory jdProductHisitory = new JdProductHisitory();
						jdProductHisitory.setProductPool(commodityPoolNumRespons.getPage_num());
						jdProductHisitory.setCreateTime(new DateTime());
						jdProductHisitory.setSkuId(u);
						jdProductHisitory.setStatus(ThirdProductImportStatusEnum.UN_IN_STORE.getNum());
						jdProductHisitory.setUpdateTime(new DateTime());
						this.jdProductHisitoryDao.save(jdProductHisitory);
						log.info("Sku添加成功！skuId:{}", u);
					} else {
						log.info("Sku已存在，不需要重复导入！skuId:{}", u);
					}
				} catch (Exception ex) {
					log.error("Sku添加失败！skuId:{},错误信息:{}", u, ex.getMessage());
				}
			}
		);
		total += skuIds.size();

//		log.info("继续调接口查询");
		if (!response.getResultCode().equals(ErrorKeyEnum.RETURN_EMPTY.getStatus())) {
			this.saveJdProductBycommodityPool(commodityPoolNumRespons, flag, total);
		}
	}

	@Override
	public void checkCategoryInIndex(String categoryCode) {
		log.info("进来更新新导入商品的索引");
		List<String> indexIds = new ArrayList<>();
		JdCategory jdCategory = this.jdCategoryDao.getByCategoryCode(categoryCode);
		log.info("查询");
		CategoryMroSupplier categoryMroSupplier = this.categoryMroSupplierDao.getBySupplierId(jdCategory.getId(), ThirdIndustrialEnum.JD.getCode());
		List<JdProduct> jdProducts = this.jdProductDao.queryByProperties(new LambdaEntityCriterion<>(JdProduct.class).eq(JdProduct::getCategory, jdCategory.getCategoryCode()).eq(JdProduct::getCategoryMroSupplierId, null));
		log.info("进来循环遍历发mq");
		jdProducts.forEach(e -> {
			indexIds.add(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + e.getSkuId());
		});
		indexIds.forEach(industrialProductProducerService::bindJdCategoryAndImportProduct);
		log.info("发送完毕，准备批量保存");
		this.jdProductDao.update(jdProducts);
	}

	@Override
	public void saleStatusCheck(Long skuId) {
		log.info("进来更新可售性" + skuId);
		List<CheckSalabilityResponse> checkSalabilities = this.checkOnSaleStatus(skuId.toString(), ProductCheckExtsEnum.IS_SELF.getValue() + StrUtil.COMMA + ProductCheckExtsEnum.NO_REASON_TO_RETURN.getValue() + StrUtil.COMMA +
			ProductCheckExtsEnum.THWA.getValue() + StrUtil.COMMA + ProductCheckExtsEnum.IS_JD_LOGISTICS.getValue());
		CheckSalabilityResponse checkSalabilityResponse = checkSalabilities.stream().findFirst().get();
		if (ObjectUtil.isEmpty(checkSalabilityResponse)) {
			log.info("查询结果为空，结束！！");
			return;
		}
		log.info("获取商品表信息");
		JdProduct jdProduct = this.jdProductDao.getBySkuId(skuId);
		jdIopProductConverter.to4(checkSalabilityResponse, jdProduct);
		log.info("变更完毕，开始更新数据库");
		this.jdProductDao.update(jdProduct);
		if (jdProduct.getSaleStatus().equals(OFF_SHELF.getMroStatus())) {
			industrialProductProducerService.deleteProductIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + skuId);
		}
		industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + skuId);
	}

	@Override
	public void updateProductByType(List<IndustrialChangeDataDTO> industrialChangeDataDTOList, Integer type) {

		List<String> outerIds = industrialChangeDataDTOList.stream().map(IndustrialChangeDataDTO::getSkuId).collect(Collectors.toList());
//		List<JdProduct> jdProductList = jdProductDao.queryByskuId(outerIds);
		List<IndustrialProduct> jdProductList = industrialProductDao.queryByShopIdAndSkuId(outerIds, thirdParameterConfig.getJdIOPSetting().getShopId());
//		List<Sku> skuList = skuDao.queryByOuterId(outerIds);
//		List<Long> prodIds = skuList.stream().map(Sku::getProdId).collect(Collectors.toList());
//		List<Product> productList = productDao.queryAllByIds(prodIds);

//		Map<String, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getOuterId, Function.identity()));
		Map<String, IndustrialProduct> jdProductMap = jdProductList.stream().collect(Collectors.toMap(e -> e.getSkuId().toString(), Function.identity()));
//		Map<Long, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getProdId, Function.identity()));

		for (IndustrialChangeDataDTO industrialChangeDataDTO : industrialChangeDataDTOList) {
			IndustrialProduct jdProduct = jdProductMap.get(industrialChangeDataDTO.getSkuId());
//			Sku sku = skuMap.get(industrialChangeDataDTO.getSkuId());
//			Product product = productMap.get(sku.getProdId());

			//上下架
			if (IndustrialChangeTypeEnum.UP_LOWER_SHELVES.getCode().equals(type)) {
				if (industrialChangeDataDTO.getStatus().equals(JdProductStateEnum.OFFLINE.value())) {
					jdProduct.setState(OFF_SHELF.getMroStatus());
				}
				if (industrialChangeDataDTO.getStatus().equals(JdProductStateEnum.ONLINE.value())) {
					jdProduct.setState(ON_SHELF.getMroStatus());
				}
//				product.setStatus(industrialChangeDataDTO.getStatus());
//				sku.setStatus(industrialChangeDataDTO.getStatus());
			}

			//可售性
//			if (IndustrialChangeTypeEnum.SALES_STATUS.getCode().equals(type)) {
//				if (industrialChangeDataDTO.getSaleStatus().equals(JdProductStateEnum.OFFLINE.value())) {
//					jdProduct.setState(ON_SHELF.getMroStatus());
//				}
//				if (industrialChangeDataDTO.getSaleStatus().equals(JdProductStateEnum.ONLINE.value())) {
//					jdProduct.setState(OFF_SHELF.getMroStatus());
//				}
//			}

			//价格
			if (IndustrialChangeTypeEnum.PRICE.getCode().equals(type)) {
				jdProduct.setPrice(industrialChangeDataDTO.getPrice());
				jdProduct.setUntaxedPrice(industrialChangeDataDTO.getUntaxedPrice());
				jdProduct.setTaxRate(industrialChangeDataDTO.getTax());
//				jdProduct.setTaxPrice(industrialChangeDataDTO.getTaxPrice());
				jdProduct.setMarketPrice(industrialChangeDataDTO.getPlatformPrice());

//				product.setPrice(industrialChangeDataDTO.getPlatformPrice().doubleValue());
//				product.setCash(industrialChangeDataDTO.getPrice().doubleValue());
//				sku.setPrice(industrialChangeDataDTO.getPrice().doubleValue());
			}
		}


		industrialProductDao.updateProdsByShopId(jdProductList, thirdParameterConfig.getJdIOPSetting().getShopId());
//		productDao.update(productList);
//		skuDao.update(skuList);


		log.debug("-----------修改完成后更新索引-------");
		industrialChangeDataDTOList.forEach(e -> industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + e.getSkuId()));
	}

	@Override
	public CommonProdInfoDTO updateProductByProdId(IndustrialProduct jdProduct) {

		CommonProdInfoDTO commonProdInfoDTO = new CommonProdInfoDTO();

		//获取更新前的价格
		BigDecimal beforePrice = jdProduct.getPrice();
		IndustrialOuterShopConfig config = configService.getByShopId(jdProduct.getShopId());

		jdProduct.setUpdateTime(new Date());

		//获取京东价格
		List<PriceResponse> priceResponses = this.priceCheck(jdProduct.getSkuId());
		Map<Long, PriceResponse> priceResponseMap = priceResponses.stream().collect(Collectors.toMap(PriceResponse::getSkuId, Function.identity()));

		//获取商品上下架状态
		List<OnSaleResponse> onSaleResponses = this.checkOnSaleState(jdProduct.getSkuId());
		Map<Long, OnSaleResponse> onSaleResponseMap = onSaleResponses.stream().collect(Collectors.toMap(OnSaleResponse::getSku, Function.identity()));

		//更新价格
		PriceResponse priceResponse = priceResponseMap.get(Long.parseLong(jdProduct.getSkuId()));
		//如果下架后，没有返回时会出现NULL的情况
		if (priceResponse != null) {
			commonProdInfoDTO.setPrice(priceResponse.getPrice());
			commonProdInfoDTO.setThirdSalePrice(priceResponse.getJdPrice());
			commonProdInfoDTO.setTaxRate(priceResponse.getTax());
			commonProdInfoDTO.setUntaxedPrice(priceResponse.getNakedPrice());
			jdIopProductConverter.toIndustrialProductChange(priceResponse, jdProduct);
		} else {
			commonProdInfoDTO.setStatus(ThridProductStatusEnum.PROD_OFFLINE.getValue());
		}

		//更新商品上下架状态
		OnSaleResponse onSaleResponse = onSaleResponseMap.get(Long.parseLong(jdProduct.getSkuId()));
		if (onSaleResponse.getState().equals(JdProductStateEnum.OFFLINE.value())) {
			log.info(onSaleResponse.getSku() + " JD商品已下架");
			commonProdInfoDTO.setStatus(ThridProductStatusEnum.PROD_OFFLINE.getValue());
			jdProduct.setState(OFF_SHELF.getMroStatus());
		} else {
			commonProdInfoDTO.setStatus(ThridProductStatusEnum.PROD_ONLINE.getValue());
			jdProduct.setState(ON_SHELF.getMroStatus());
		}

		//更新数据库
		industrialProductDao.updateProdByShopId(jdProduct, jdProduct.getShopId());

		log.debug("-----------修改完成后更新索引-------");
		industrialProductIndexClient.updateIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + jdProduct.getSkuId(), SecurityConstants.FROM_IN);

		// 用于存储价格变更记录
		List<ProdChangeLogDTO> priceChangeRecords = new ArrayList<>();

		// 计算变更后的价格和价格变化比率
		BigDecimal afterPrice = jdProduct.getPrice();
		BigDecimal priceChangeRate = calculatePriceChangeRate(beforePrice, afterPrice); // 调用价格变化率计算方法

		if(priceChangeRate.compareTo(BigDecimal.ZERO) != 0) {
			// 创建价格变更记录
			ProdChangeLogDTO record = new ProdChangeLogDTO();
			record.setShopId(jdProduct.getShopId());
			record.setShopName(config.getShopName());
			record.setSkuId(jdProduct.getSkuId());  // 商品sku
			record.setBeforePrice(beforePrice);  //变更前价格
			record.setAfterPrice(afterPrice);  //变更后价格
			record.setPriceChangeRate(priceChangeRate);  // 价格变化比率
			record.setRequestDate(new Date());  // 当前请求日期
			priceChangeRecords.add(record); // 将记录添加到列表
		}

		// 发送MQ消息以进行异步处理
		if(priceChangeRecords != null && !priceChangeRecords.isEmpty()) {
			priceChangeRecords.forEach(record -> {
				prodChangeProducerService.sendPriceChangeMessage(record);// 发送每个价格变更记录到MQ

				// 判断该商品是否需要提醒
				List<ProdPriceContacts> priceLogList = prodPriceContactsService.getBySkuAndShopId(record.getShopId(), record.getSkuId());
				log.info("需要进行价格变化公众号提醒的商品:{}",JSON.toJSONString(priceLogList));
				if(AppUtils.isNotBlank(priceLogList)) {
					for (ProdPriceContacts priceLog : priceLogList) {
						ProdPriceContactsVo vo = new ProdPriceContactsVo();
						vo.setShopId(priceLog.getShopId());
						vo.setSkuId(priceLog.getSkuId());
						vo.setShopName(record.getShopName());
						vo.setMaterialCode(priceLog.getMaterialCode());
						vo.setBeforePrice(record.getBeforePrice());
						vo.setAfterPrice(record.getAfterPrice());
						vo.setBidPrice(priceLog.getBidPrice());
						vo.setContacts(priceLog.getContacts());
						log.info("指定商品提醒的联系人:{}",priceLog.getContacts());
						vo.setPriceChangeRate(record.getPriceChangeRate());
						prodChangeProducerService.sendProdPriceWXMessage(vo);// 发送价格变更微信公众号提醒MQ
					}
				}
			});
		}

		return commonProdInfoDTO;
	}

	@Override
	public void ackMessage(List<String> ids) {
		JdMessageUtil.deleteMessageInfo(jdAccessTokenUtil.getIOPAccessToken(), ids);
	}

	@Override
	public void checkMroIndustrialBindedIndexForJd() {

		List<Long> skuIds = this.jdProductDao.queryMroIndustrialIndexUnUpdated();

		if (CollUtil.isEmpty(skuIds)) {
			log.info("结果为空，不需要更新");
			return;
		}

		//发送mq重建索引
		skuIds.forEach(e -> industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopUserId() + StrUtil.COLON + e));
	}

	@Override
	public void checkMroIndustrialBindedForNewInStoreProduct() {
		List<CategoryMroSupplier> categoryMroSuppliers = this.categoryMroSupplierDao.queryBySupplier(ThirdIndustrialEnum.JD.getCode());
		if (CollUtil.isEmpty(categoryMroSuppliers)) {
			log.info("类目绑定关系为空");
			return;
		}
		Map<Long, CategoryMroSupplier> categoryMroSupplierMap = categoryMroSuppliers.stream().collect(Collectors.toMap(CategoryMroSupplier::getSupplierId, e -> e));
		List<Long> categoryIds = categoryMroSuppliers.stream().map(CategoryMroSupplier::getSupplierId).collect(Collectors.toList());
		List<JdCategory> jdCategories = this.jdCategoryDao.queryByProperties(new LambdaEntityCriterion<>(JdCategory.class).in(JdCategory::getId, categoryIds));
		for (JdCategory jdCategory : jdCategories) {
			if (jdCategory.getCategoryLevel() == 2) {
				List<JdCategory> childList = this.jdCategoryDao.queryByProperties(new LambdaEntityCriterion<>(JdCategory.class).eq(JdCategory::getParentId, jdCategory.getId()));
				if (CollUtil.isEmpty(childList)) {
					continue;
				}
				for (JdCategory category : childList) {
					log.info("二级");
					if (ObjectUtil.isEmpty(categoryMroSupplierMap.get(category.getId()))) {
						log.info("类目不是三级");
						CategoryMroSupplier categoryMroSupplier = categoryMroSupplierMap.get(jdCategory.getId());
						importAction(category.getCategoryCode(), categoryMroSupplier.getId());
					} else {
						log.info("类目是三级");
						CategoryMroSupplier categoryMroSupplier = categoryMroSupplierMap.get(category.getId());
						importAction(category.getCategoryCode(), categoryMroSupplier.getId());
					}

				}
			} else {
				//不等于 就是3级
				log.info("3级");
				CategoryMroSupplier categoryMroSupplier = categoryMroSupplierMap.get(jdCategory.getId());
				importAction(jdCategory.getCategoryCode(), categoryMroSupplier.getId());
			}
		}
	}

	private void importAction(String cateCode, Long id) {
		Page page = new Page(1, 20);
		boolean flag = false;
		int a = 0;
		do {

			PageSupport<Long> longPageSupport = this.jdProductDao.queryIndustrialBindedIsNullButBindExist(cateCode, page.getPageSize(), page.getPageNumber());

			if (CollUtil.isEmpty(longPageSupport.getResultList())) {
				XxlJobLogger.log("已经遍历完了");
				flag = true;
			} else {
				List<Long> resultList = longPageSupport.getResultList();
				this.jdProductDao.updateProperties(new LambdaUpdate<>(JdProduct.class)
					.in(JdProduct::getSkuId, resultList)
					.set(JdProduct::getCategoryMroSupplierId, id)
					.set(JdProduct::getMroStatus, 1));
//				log.info("发mq入库1");
//				resultList.forEach(industrialProductProducerService::jdProductInMroStore);
			}
			a++;
			page.setPageNumber(page.getPageNumber() + 1);
		} while (!flag);
	}

	@Override
	public void inMroStore(Long skuId) {
		log.info("进来入库");
		JdProduct jdProduct = this.jdProductDao.getBySkuId(skuId);
		DateTime dateTime = new DateTime();
		ShopDetail shopdetailInfo = this.shopDetailDao.getById(thirdParameterConfig.getJdIOPSetting().getShopId());
		CategoryMroSupplier categoryMroSupplier = this.categoryMroSupplierDao.getById(jdProduct.getCategoryMroSupplierId());

		CommonIndustrialProdDTO product = new CommonIndustrialProdDTO();
		log.info("入prod库");
		productInfoPack(jdProduct, dateTime, shopdetailInfo, categoryMroSupplier, product);

		Long prodId = prodInStore(product);
		log.info("入sku库：{}", prodId);
		skuInfoInStore(product, prodId);
		jdProduct.setProdId(prodId);
		log.info("保存京东商品表：{}", prodId);
		this.jdProductDao.update(jdProduct);
		log.info("发mq重建索引");
		industrialProductProducerService.updateIndustrialIndex(thirdParameterConfig.getJdIOPSetting().getShopId() + StrUtil.COLON + jdProduct.getSkuId());
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long inMroStore(CommonSupplierProductDTO commonSupplierProductDTO) {
		log.info("进来入库");
		JdProduct jdProduct = this.jdProductDao.getBySkuId(Long.parseLong(commonSupplierProductDTO.getSupplierSkuId()));
		DateTime dateTime = new DateTime();
		ShopDetail shopdetailInfo = this.shopDetailDao.getById(thirdParameterConfig.getJdIOPSetting().getShopId());
		CategoryMroSupplier categoryMroSupplier = this.categoryMroSupplierDao.getById(commonSupplierProductDTO.getCategoryMroSupplierId());

		CommonIndustrialProdDTO product = new CommonIndustrialProdDTO();
		log.info("入prod库");
		productInfoPack(jdProduct, dateTime, shopdetailInfo, categoryMroSupplier, product);

		Long prodId = prodInStore(product);
		log.info("入sku库：{}", prodId);
		skuInfoInStore(product, prodId);
		return prodId;

	}

	@Override
	public PageSupport<ReturnBindedProductInfoDTO> queryAllBindedProductInfo(ErpBindedMessageQueryVo erpBindedMessageQueryVo) {
		return this.jdProductDao.queryBindedProductInfo(erpBindedMessageQueryVo);
	}

	@Override
	public String totalSaleCheck(JdSaleableCheckVo jdSaleableCheckVo) {

//		String token = jdAccessTokenUtil.getIOPAccessToken();
		ShipNeedTimeRequest addressRequest = new ShipNeedTimeRequest();
		addressRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		addressRequest
			.setProvince(new Integer(jdSaleableCheckVo.getProvince()))
			.setCity(new Integer(jdSaleableCheckVo.getCity()))
			.setCounty(new Integer(jdSaleableCheckVo.getTown()))
//			.setTown(new Integer(jdSaleableCheckVo.getTown()))
			.setSkuId(jdSaleableCheckVo.getSkuIds());
		String result = JdRequestUtils.sendPostReturnString(ProductApi.PROMISE_TIPS, addressRequest, String.class);
		if (StrUtil.isBlank(result)) {
			ProductTotalCheckNewRequest productTotalCheckNewRequest = jdIopProductConverter.to6(jdSaleableCheckVo);
			productTotalCheckNewRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
			CommonResponse<List<ProductTotalCheckNewResponse>> listCommonResponse = ProductClient.totalCheckNew(productTotalCheckNewRequest);
			if (listCommonResponse.getResult().get(0).getCanPurchase()) {
				return "可以下单，非现货-采购周期7-15天，着急勿拍";
			}
		}
		return result;
	}


	private void skuInfoInStore(CommonIndustrialProdDTO commonIndustrialProdDTO, Long prodId) {
		Sku sku;
		sku = skuDao.getByOuterId(commonIndustrialProdDTO.getOutId());
		if (AppUtils.isBlank(sku)) {
			sku = new Sku();
		}
		sku.setProdId(prodId);
		sku.setPrice(commonIndustrialProdDTO.getPrice());
		sku.setName(commonIndustrialProdDTO.getName());
		sku.setStocks(Long.valueOf(commonIndustrialProdDTO.getStocks()));
		sku.setActualStocks(Long.valueOf(commonIndustrialProdDTO.getStocks()));
		sku.setStatus(1);
		sku.setOuterId(commonIndustrialProdDTO.getOutId());
		sku.setModifyDate(commonIndustrialProdDTO.getModifyDate());
		sku.setRecDate(commonIndustrialProdDTO.getRecDate());
		sku.setSkuType("P");
		sku.setTenderSku(Boolean.FALSE);
		sku.setPic(commonIndustrialProdDTO.getPic());
		this.skuDao.saveOrUpdate(sku);
	}

	private Long prodInStore(CommonIndustrialProdDTO commonIndustrialProdDTO) {
		Product product = new Product();
		product.setProdId(commonIndustrialProdDTO.getProdId());
		product.setProdType(commonIndustrialProdDTO.getProdType());
		product.setKeyWord(commonIndustrialProdDTO.getKeyWord());
		product.setVersion(commonIndustrialProdDTO.getVersion());
		product.setUserName(commonIndustrialProdDTO.getUserName());
		product.setCategoryId(commonIndustrialProdDTO.getCategoryId());
		product.setViews(commonIndustrialProdDTO.getViews());
		product.setBuys(commonIndustrialProdDTO.getBuys());
		product.setUserParameter(commonIndustrialProdDTO.getUserParameter());
		product.setComments(commonIndustrialProdDTO.getComments());
		product.setAuditOpinion(commonIndustrialProdDTO.getBrief());
		product.setReviewScores(commonIndustrialProdDTO.getReviewScores());
		product.setStockCounting(commonIndustrialProdDTO.getStockCounting());
		product.setStore(commonIndustrialProdDTO.getStore());
		product.setPic(commonIndustrialProdDTO.getPic());
		product.setPrice(commonIndustrialProdDTO.getPrice());
		product.setCash(commonIndustrialProdDTO.getCash());
		product.setUnit(commonIndustrialProdDTO.getUnit());
		product.setStocks(commonIndustrialProdDTO.getStocks());
		product.setName(commonIndustrialProdDTO.getName());
		product.setRecDate(commonIndustrialProdDTO.getRecDate());
		product.setModifyDate(commonIndustrialProdDTO.getModifyDate());
		product.setStartDate(commonIndustrialProdDTO.getStartDate());
		product.setUserName(commonIndustrialProdDTO.getUserName());
		product.setUserId(commonIndustrialProdDTO.getUserId());
		product.setShopId(commonIndustrialProdDTO.getShopId());
		product.setShopThirdCatId(commonIndustrialProdDTO.getShopThirdCatId());
		product.setStatus(commonIndustrialProdDTO.getStatus());
		product.setPublishStatus(commonIndustrialProdDTO.getPublishStatus());
		product.setIndustrialProdType(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());
		product.setIsMroSelf(IsMroSelfEnum.THIRD_PROD.getType());
		product.setExpressTransFee(commonIndustrialProdDTO.getExpressTransFee());
		product.setPaymentMethod(commonIndustrialProdDTO.getPaymentMethod());
		return productDao.saveOrUpdate(product);
	}

	private void productInfoPack(JdProduct jdProduct, DateTime dateTime, ShopDetail shopdetailInfo, CategoryMroSupplier categoryMroSupplier, CommonIndustrialProdDTO product) {
		product.setProdId(jdProduct.getProdId());
		product.setSupplierProdId(jdProduct.getId());
		product.setProdType("P");
		product.setKeyWord("jd");
		product.setVersion(1);
		product.setUserName(shopdetailInfo.getUserName());
		product.setCategoryId(categoryMroSupplier.getMroId());
		product.setViews(0);
		product.setBuys(0L);
		product.setUserParameter("[]");
		product.setComments(0L);
		product.setAuditOpinion("同意");
		product.setReviewScores(100);
		product.setStockCounting(2);
		product.setStore(0);
		product.setPic(jdProduct.getImagePath());
		product.setPrice(jdProduct.getJdPrice().doubleValue());
		product.setCash(jdProduct.getPrice().doubleValue());
		product.setUnit(jdProduct.getSaleUnit());
		product.setStocks(10000);
		product.setName(jdProduct.getJdProductName());
		product.setRecDate(dateTime);
		product.setModifyDate(dateTime);
		product.setStartDate(dateTime);
		product.setUserName(ThirdIndustrialEnum.JD.getName());
		product.setUserId(shopdetailInfo.getUserId());
		product.setShopId(shopdetailInfo.getShopId());
		product.setShopThirdCatId(Long.getLong(jdProduct.getCategory()));
		product.setStatus(ThirdProductOnSaleStatusEnum.findByJdStatus(jdProduct.getStatus()).getMroStatus());
		product.setPublishStatus(jdProduct.getStatus() == 10 ? 1 : 0);
		product.setOutId(String.valueOf(jdProduct.getSkuId()));
		product.setExpressTransFee(0.00);
		product.setPaymentMethod(PayMannerEnum.ONLINE_PAY.value());
	}
}
