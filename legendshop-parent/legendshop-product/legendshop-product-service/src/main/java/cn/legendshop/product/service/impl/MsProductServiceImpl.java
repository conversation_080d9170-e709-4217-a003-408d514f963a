package cn.legendshop.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.config.ThirdParameterConfig;
import cn.legendshop.common.core.constant.HttpStatusConstants;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.*;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.common.dao.AreaDao;
import cn.legendshop.common.dao.CityDao;
import cn.legendshop.common.dao.IndustrialOuterShopSettleConfigDao;
import cn.legendshop.common.dao.ProvinceDao;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.model.IndustrialOuterShopSettleConfig;
import cn.legendshop.common.model.entity.Area;
import cn.legendshop.common.model.entity.City;
import cn.legendshop.common.model.entity.Province;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.open.dto.CommonSimilarSkuDTO;
import cn.legendshop.product.api.dto.*;
import cn.legendshop.product.api.dto.product.BoundThirdPlatformProductParam;
import cn.legendshop.product.api.dto.product.ErpExclusionObeiProductDto;
import cn.legendshop.product.api.dto.product.SkuIdDTO;
import cn.legendshop.product.api.entity.*;
import cn.legendshop.product.api.enums.IndustrialProdTypeEnum;
import cn.legendshop.product.api.enums.MroIndustrialProdStatusEnum;
import cn.legendshop.product.api.query.CommonProdQuery;
import cn.legendshop.product.api.query.PromiseDayQuery;
import cn.legendshop.product.api.query.ShopQuery;
import cn.legendshop.product.api.vo.IndexCountVo;
import cn.legendshop.product.api.vo.StockCheckVo;
import cn.legendshop.product.api.vo.ThirdProductSearchRequestVo;
import cn.legendshop.product.dao.*;
import cn.legendshop.product.mq.producer.ConsumerProductProducerService;
import cn.legendshop.product.mq.producer.IndustrialProductProducerService;
import cn.legendshop.product.service.*;
import cn.legendshop.product.service.convert.*;
import cn.legendshop.product.strategy.ThirdConsumerStrategyContext;
import cn.legendshop.product.strategy.ThirdIndustrialStrategy;
import cn.legendshop.product.strategy.ThirdIndustrialStrategyContext;
import cn.legendshop.search.api.client.ProductSearchClient;
import cn.legendshop.search.api.client.SimilarityProductClient;
import cn.legendshop.search.api.client.ThirdProductSearchClient;
import cn.legendshop.search.api.dto.*;
import cn.legendshop.search.api.entity.ProductSimilarity;
import cn.legendshop.search.api.enums.SearchIndexIndustrialTypeEnum;
import cn.legendshop.search.api.request.IndustrialSearchRequest;
import cn.legendshop.search.api.vo.SearchResult;
import cn.legendshop.user.api.client.AccountPeriodClient;
import cn.legendshop.user.api.client.ShopDetailServiceClient;
import cn.legendshop.user.api.client.v2.AccountBookClient;
import cn.legendshop.user.api.client.v2.AccountBookCompanyClient;
import cn.legendshop.user.api.client.v2.AccountBookDetailClient;
import cn.legendshop.user.api.client.v2.UserAddressClient;
import cn.legendshop.user.api.dto.BasicShopDetailDTO;
import cn.legendshop.user.api.dto.UserAddressDTO;
import cn.legendshop.user.api.enums.AccountBookStatusEnum;
import cn.legendshop.user.api.vo.AccountBookCompanyVO;
import cn.legendshop.user.api.vo.AccountBookDetailVO;
import cn.legendshop.user.api.vo.AccountBookVO;
import cn.legendshop.user.api.vo.AccountPeriodVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageParams;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.lambda.LambdaEntityCriterion;
import com.legendshop.model.constant.ProductStatusEnum;
import com.legendshop.model.dto.request.ThirdProductSearchRequest;
import com.legendshop.model.entity.*;
import com.legendshop.util.AppUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static cn.legendshop.product.api.constant.ProductConstant.MRO_PLATFORM_ID;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.ON_SHELF;
import static cn.legendshop.product.api.enums.ThridProductStatusEnum.PROD_ONLINE;

@Slf4j
@Service
@AllArgsConstructor
public class MsProductServiceImpl implements MsProductService {

    private final ProductDao productDao;
    private final ObeiProductDao obeiProductDao;
    private final JdProductDao jdProductDao;
    private final JdVopProductDao jdVopProductDao;
    private final SimilarityProductClient similarityProductClient;
    private final IndustrialProductDao industrialProductDao;
    private final MaterialProductRelDao materialProductRelDao;
    private final MaterialProductRelConverter materialProductRelConverter;
    private final IndustrialProductService industrialProductService;
    private final ProductSearchClient productSearchClient;
    private final ThirdProductSearchClient thirdProductSearchClient;
    private final IndustrialProductProducerService industrialProductProducerService;
    private final ProductDateConverter productDateConverter;
    private final ObeiProductEntityConverter obeiProductEntityConverter;
    private final ThirdPartyProductSearchIndexConverter thirdPartyProductSearchIndexConverter;
    private final ThirdIndustrialStrategyContext thirdIndustrialStrategyContext;
    private final ThirdParameterConfig thirdParameterConfig;
    private final ShopDetailServiceClient shopDetailServiceClient;
    private final SkuDao skuDao;
    private final MsSpecificationService msSpecificationService;
    private final TenderSkuDao tenderSkuDao;
    private final MsCategoryService categoryService;
    private final ThirdConsumerStrategyContext thirdConsumerStrategyContext;
    private final ConsumerProductProducerService consumerProductProducerService;
    private final IndustrialOuterShopConfigService industrialOuterShopConfigService;
    private final ThirdPartyProductAdjustmentService thirdPartyProductAdjustmentService;
    private final ErpProductService erpProductService;
    private final IndustrialProductConverter industrialProductConverter;
    private final UserAddressService userAddressService;
    private final ThirdIndustrialProductService thirdIndustrialProductService;
    private final ProvinceDao provinceDao;
    private final CityDao cityDao;
    private final AreaDao areaDao;
    private final UserAddressClient userAddressClient;
    private final IndustrialOuterShopSettleConfigDao industrialOuterShopSettleConfigDao;
    private final AccountBookDetailClient bookDetailClient;
    private final OpenMaterialProductRelDao openMaterialProductRelDao;
    private final OpenMaterialProductRecommendDao openMaterialProductRecommendDao;
    private final AccountBookClient bookClient;
    private final AccountBookCompanyClient bookCompanyClient;
    private final AccountPeriodClient accountPeriodClient;
    private final OpenMaterialService openMaterialService;

    @Override
    public PageSupport<Product> queryProductsByOnLine(int curPageNO, int pageSize) {
        // 查询所有在线商品 (消费品)
        return productDao.queryProductsByOnLine(curPageNO, pageSize, IndustrialTypeEnum.CONSUMER_PRODUCTS.getCode(),
                20);
    }

    @Override
    public PageSupport<ObeiProduct> queryObeiProductsByOnLine(int curPageNO, int pageSize) {
        // 查询所有在线商品
        PageSupport<ObeiProduct> obeiProductPageSupport = obeiProductDao.queryAllObeiProductCode(curPageNO, pageSize);
        List<ObeiProduct> resultList = obeiProductPageSupport.getResultList();
        for (ObeiProduct obeiProduct : resultList) {
            obeiProduct.setCommodityDesc("");
        }
        obeiProductPageSupport.setResultList(resultList);
        return obeiProductPageSupport;
    }

    @Override
    public PageSupport<JdProduct> queryJdProductsByOnLine(int curPageNO, int pageSize) {
        // 查询所有在线商品
        return jdProductDao.queryJdProductsByOnLine(curPageNO, pageSize);
    }

    // 根据Id查询商品详情
    @Override
    public Product getProductById(Long prodId) {
        return productDao.getProductById(prodId);
    }

    @Override
    public ObeiProduct getObeiProductById(Long prodId) {
        return obeiProductDao.getByProperties(new EntityCriterion().eq("prodId", prodId));
    }

    @Override
    public List<ObeiProduct> getObeiProductByIds(List<Long> prodIds) {
        return obeiProductDao.queryAllByIds(prodIds);
    }

    @Override
    public List<Product> getProductByIds(List<Long> prodIds) {
        return productDao.getProductListByProdIds(prodIds);
    }

    // TODO 根据商店Id查询商品
    @Override
    public List<Product> getProductListByShopId(Long shopId) {
        return productDao.getProductListByProdIds(new ArrayList<>());
    }

    // 查询所有在线商品
    @Override
    public List<Product> queryProductAllByOnLine() {
        return productDao.queryProductAllByOnLine();
    }

    @Override
    public SearchResult<ThirdProductDocumentDTO> searchBindThirdPlatformProduct(
            ThirdProductSearchRequestVo thirdProductSearchRequest) {
        if (Objects.nonNull(thirdProductSearchRequest.getDescending())
                && ObjectUtil.isEmpty(thirdProductSearchRequest.getSortBy())) {
            thirdProductSearchRequest.setSortBy("supplierSalePrice");
        }

        // 查询所有物料编码可采购的电商平台
        if(StringUtils.isBlank(thirdProductSearchRequest.getThirdPlatform())) {
            List<String> shopIds = openMaterialService.getShopIds(thirdProductSearchRequest.getMaterialCode());
            if(CollectionUtils.isNotEmpty(shopIds)){
                StringBuilder thirdPlatform = new StringBuilder();
                for (String shopId : shopIds) {
                    IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(Long.valueOf(shopId));
                    if (Objects.nonNull(config)) {
                        thirdPlatform.append(config.getInnerCode()).append(",");
                    }
                }
                if (thirdPlatform.length() > 0) {
                    thirdPlatform.setLength(thirdPlatform.length() - 1);
                }
                thirdProductSearchRequest.setThirdPlatform(thirdPlatform.toString());
            }
        }

        ThirdProductSearchRequest convertVo = productDateConverter.to(thirdProductSearchRequest);
        convertVo.setForMro(false);
        R<SearchResult<ThirdProductDocumentDTO>> search = thirdProductSearchClient.search(convertVo);
        if (Objects.nonNull(search.getResult()) && CollectionUtils.isNotEmpty(search.getResult().getResultList())) {
            SearchResult<ThirdProductDocumentDTO> result = search.getResult();
            List<ThirdProductDocumentDTO> resultList = result.getResultList();
            // 设置绑定状态
            resultList.forEach(thirdProductDocumentDTO -> {
                thirdProductDocumentDTO.setBound(false);
                thirdProductDocumentDTO.setBoundMaterialCode(StrUtil.SPACE);
            });

            // 将resultList中已有绑定关系的商品，更新标记为已绑定并更新绑定料号
            Set<String> skuIdSet;
            if (AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()
                    .equals(thirdProductSearchRequest.getAccountBookId())) {
                List<MaterialProductRel> materialProductRels = materialProductRelDao
                        .queryBinding(thirdProductSearchRequest.getMaterialCode());
                skuIdSet = materialProductRels.stream().map(MaterialProductRel::getProductCode)
                        .collect(Collectors.toSet());
            } else {
                List<OpenMaterialProductRel> openMaterialProductRels = openMaterialProductRelDao
                        .queryByCompanyAndMaterialCode(thirdProductSearchRequest.getCompanyId(),
                                thirdProductSearchRequest.getMaterialCode());
                skuIdSet = openMaterialProductRels.stream().map(OpenMaterialProductRel::getSkuId)
                        .collect(Collectors.toSet());
            }
            if (ObjectUtil.isNotEmpty(skuIdSet)) {
                // 将resultList中已有绑定关系的商品，更新标记为已绑定并更新绑定料号
                resultList.forEach(thirdProductDocumentDTO -> {
                    if (skuIdSet.contains(thirdProductDocumentDTO.getProductCode())) {
                        thirdProductDocumentDTO.setBound(true);
                        thirdProductDocumentDTO.setBoundMaterialCode(thirdProductSearchRequest.getMaterialCode());
                    }
                });
            }
            return result;
        }
        return new SearchResult<>();
    }

    @Override
    public List<ObeiProduct> getObeiProductByProductCode(List<String> productCodes) {
        List<ObeiProduct> obeiProducts = obeiProductDao.queryByobeiProductCode(productCodes);
        for (ObeiProduct obeiProduct : obeiProducts) {
            obeiProduct.setCommodityDesc("");
        }
        return obeiProducts;
    }

    @Override
    public List<IndustrialProductForErpDocumentDTO> listRecommendThirdPlatformProduct(String companyId,
            String materialCode, Long accountBookId) {
        // 过滤掉不支持的店铺，并提取供应商代码列表
        List<IndustrialOuterShopConfig> outerShopConfigList = industrialOuterShopConfigService
                .queryAll(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());

        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId)
                .orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        List<String> vendorCodeList = listR.getSuccess() ? outerShopConfigList.stream()
                .filter(shopConfig -> listR.getResult().stream()
                        .noneMatch(detail -> detail.getShopId().equals(shopConfig.getShopId())))
                .map(IndustrialOuterShopConfig::getSupplierCode)
                .collect(Collectors.toList()) : Collections.emptyList();

        List<OpenMaterialProductRecommend> recommendList = openMaterialProductRecommendDao.queryRecommend(materialCode,
                vendorCodeList);

        // 根据供应商分类，分别查询
        Map<String, List<OpenMaterialProductRecommend>> recommendInfo = recommendList.stream()
                .collect(Collectors.groupingBy(OpenMaterialProductRecommend::getVendorCode));
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>();
        recommendInfo.forEach((k, v) -> {
            List<String> skuIds = v.stream().map(OpenMaterialProductRecommend::getSkuId).collect(Collectors.toList());
            IndustrialOuterShopConfig config = industrialOuterShopConfigService.getBySupplierCode(k);
            List<IndustrialProduct> industrialProducts = industrialProductDao.queryByShopIdAndSkuId(skuIds,
                    config.getShopId());
            industrialProducts = industrialProducts.stream()
                    .filter(product -> MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState()))
                    .collect(Collectors.toList());
            resultDTO.addAll(obeiProductEntityConverter.toOuterProductDocumentDTOList(industrialProducts));
        });
        // 获取到物料关联的sku。组成一个set.
        Set<String> finalSkuIds = Optional.ofNullable(materialProductRelDao.queryBinding(materialCode))
                .map(list -> list.stream()
                        .map(MaterialProductRel::getProductCode)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());

        resultDTO.forEach(documentDTO -> {
            // 取第一张图片,如果为空则不处理
            if (finalSkuIds.contains(documentDTO.getProductCode())) {
                documentDTO.setBound(true);
                documentDTO.setBoundMaterialCode(materialCode);
            }
        });

        resultDTO.sort(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice));
        return resultDTO;
    }

    @Override
    public List<MaterialProductRelDTO> getMaterialCodeBoundSupplierProduct(String productCode, String supplier) {
        List<MaterialProductRel> materialProductRels = materialProductRelDao.queryBinding(productCode, supplier);
        if (CollectionUtils.isEmpty(materialProductRels)) {
            return Collections.emptyList();
        }
        return materialProductRelConverter.to(materialProductRels);
    }

    @Override
    public PageSupport<IndustrialProductForErpDocumentDTO> getBoundThirdPlatformProduct(int curPageNO, int pageSize,
            String materialCode) {
        PageSupport<MaterialProductRel> boundThirdPlatformProductByCreateTime = materialProductRelDao
                .getBoundThirdPlatformProductByCreateTime(curPageNO, pageSize, materialCode);
        // 根据供应商分类，分别查询
        log.info("查询出绑定信息" + JSONObject.toJSONString(boundThirdPlatformProductByCreateTime));
        Map<String, List<MaterialProductRel>> boundInfo = boundThirdPlatformProductByCreateTime.getResultList().stream()
                .collect(Collectors.groupingBy(MaterialProductRel::getSupplier));
        PageSupport<IndustrialProductForErpDocumentDTO> obeiProductDocumentDTOPageSupport = new PageSupport<>();
        List<IndustrialProductForErpDocumentDTO> industrialProductForErpDocumentDTOS = new ArrayList<>();
        boundInfo.forEach((k, v) -> {
            List<String> skuIds = v.stream().map(MaterialProductRel::getProductCode).collect(Collectors.toList());
            // ThirdIndustrialEnum thirdIndustrialEnum = ThirdIndustrialEnum.findByCode(k);
            // if(ObjectUtil.isEmpty(thirdIndustrialEnum)){
            IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByInnerCode(k);
            List<IndustrialProduct> industrialProducts = industrialProductDao.queryByShopIdAndSkuId(skuIds,
                    config.getShopId());
            industrialProductForErpDocumentDTOS
                    .addAll(obeiProductEntityConverter.toOuterProductDocumentDTOList(industrialProducts));
            // }else {
            //
            // switch (thirdIndustrialEnum) {
            // case Obei:
            // List<ObeiProduct> obeiProducts =
            // obeiProductDao.queryByobeiProductCode(skuIds);
            // industrialProductForErpDocumentDTOS.addAll(obeiProductEntityConverter.toObeiProductDocumentDTO(obeiProducts));
            // break;
            // case JD:
            // List<JdProduct> jdProducts = this.jdProductDao.queryByskuId(skuIds);
            // industrialProductForErpDocumentDTOS.addAll(obeiProductEntityConverter.toJdProductDocumentDTOList(jdProducts));
            // break;
            // case Zkh:
            // List<ZkhProduct> zkhProducts = this.zkhProductDao.queryByskuId(skuIds);
            // industrialProductForErpDocumentDTOS.addAll(obeiProductEntityConverter.toZkhProductDocumentDTOList(zkhProducts));
            // break;
            // default:
            // break;
            // }
            // }
        });

        obeiProductDocumentDTOPageSupport.setCurPageNO(boundThirdPlatformProductByCreateTime.getCurPageNO());
        obeiProductDocumentDTOPageSupport.setResultList(industrialProductForErpDocumentDTOS);
        obeiProductDocumentDTOPageSupport.setPageSize(boundThirdPlatformProductByCreateTime.getPageSize());
        obeiProductDocumentDTOPageSupport.setPageCount(boundThirdPlatformProductByCreateTime.getPageCount());
        obeiProductDocumentDTOPageSupport.setTotal(boundThirdPlatformProductByCreateTime.getTotal());
        return obeiProductDocumentDTOPageSupport;
    }

    @Override
    public List<IndustrialProductForErpDocumentDTO> listBoundThirdPlatformProduct(String companyId, String materialCode,
            Long accountBookId) {
        // 过滤掉不支持的店铺，并提取供应商代码列表
        List<IndustrialOuterShopConfig> outerShopConfigList = industrialOuterShopConfigService
                .queryAll(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());

        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId)
                .orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        List<Long> supplierList = listR.getSuccess() ? outerShopConfigList.stream()
                .filter(shopConfig -> listR.getResult().stream()
                        .noneMatch(detail -> detail.getShopId().equals(shopConfig.getShopId())))
                .map(IndustrialOuterShopConfig::getPlatformId)
                .collect(Collectors.toList()) : Collections.emptyList();

        List<MaterialProductRel> boundList = materialProductRelDao.queryBinding(materialCode, supplierList);
        // 根据供应商分类，分别查询
        Map<String, List<MaterialProductRel>> boundListMap = boundList.stream()
                .collect(Collectors.groupingBy(MaterialProductRel::getSupplier));
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>();
        boundListMap.forEach((k, v) -> {
            List<String> skuIds = v.stream().map(MaterialProductRel::getProductCode).collect(Collectors.toList());
            IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByInnerCode(k);
            List<IndustrialProduct> industrialProducts = industrialProductDao.queryByShopIdAndSkuId(skuIds,
                    config.getShopId());
            industrialProducts = industrialProducts.stream()
                    .filter(product -> MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState()))
                    .collect(Collectors.toList());
            resultDTO.addAll(obeiProductEntityConverter.toOuterProductDocumentDTOList(industrialProducts));
        });

        resultDTO.forEach(documentDTO -> {
            documentDTO.setBound(true);
            documentDTO.setBoundMaterialCode(materialCode);
        });
        resultDTO.sort(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice));
        return resultDTO;
    }

    @Override
    public void submitBoundThirdPlatformProduct(BoundThirdPlatformProductParam boundThirdPlatformProductParam) {
        String materialCode = boundThirdPlatformProductParam.getMaterialCode();
        log.info("进到ProductService");
        if (MapUtils.isNotEmpty(boundThirdPlatformProductParam.getBoundProductIdsByPlatform())) {
            log.info("进入发送方法");
            Map<String, List<Long>> productIdsByPlatform = boundThirdPlatformProductParam
                    .getBoundProductIdsByPlatform();
            for (String key : productIdsByPlatform.keySet()) {
                handler(key, materialCode, boundThirdPlatformProductParam.getPlanNo(),
                        boundThirdPlatformProductParam.getBoundProductIdsByPlatform().get(key),boundThirdPlatformProductParam.getSource());
            }
        }
    }

    private void handler(String code, String materialCode, String planNo, List<Long> Ids,String source) {
        if (CollectionUtils.isNotEmpty(Ids)) {
            erpProductService.sendPickProductInfoToErp(materialCode, planNo, Ids,
                    industrialOuterShopConfigService.getByInnerCode(code).getShopId(),source);
        }
    }

    @NotNull
    private MaterialProductRel buildProduct(String materialCode, Date createTime,
            Map.Entry<String, ObeiProduct> stringObeiProductEntry) {
        MaterialProductRel materialProductRel;
        materialProductRel = new MaterialProductRel();
        materialProductRel.setMaterialCode(materialCode);
        materialProductRel.setProductCode(stringObeiProductEntry.getKey());
        materialProductRel.setSupplier(ThirdIndustrialEnum.Obei.getCode());
        materialProductRel.setCreateTime(createTime);
        materialProductRel.setUpdateTime(createTime);
        materialProductRel.setQueryKey(
                materialCode + ":" + stringObeiProductEntry.getKey() + ":" + ThirdIndustrialEnum.Obei.getCode());
        materialProductRel.setIsDeleted(0);
        materialProductRel.setIsSured(0);
        return materialProductRel;
    }

    /**
     * 排除已有的 商品code 并且更新修改时间
     *
     * @param materialCode
     * @param productCodes
     */
    private ErpExclusionObeiProductDto exclusionProductCodeAndExclusionUpdateTime(String materialCode,
            Map<String, ObeiProduct> productCodes, List<String> queryKey) { // productCode ObeiEntity
        ErpExclusionObeiProductDto erpExclusionObeiProductDto = new ErpExclusionObeiProductDto();
        // LambdaEntityCriterion<MaterialProductRel> entityCriterion = new
        // LambdaEntityCriterion<>(MaterialProductRel.class)
        // .eq(MaterialProductRel::getMaterialCode, materialCode)
        // .eq(MaterialProductRel::getSupplier, ThridIndustrialEnum.Obei.getCode())
        // .in(MaterialProductRel::getProductCode, productCodes.entrySet());
        // List<MaterialProductRel> materialProductRels =
        // materialProductRelDao.queryByProperties(entityCriterion);
        List<MaterialProductRel> newMaterialList = new ArrayList<>();
        List<Long> updateTimeIds = new ArrayList<>();
        List<MaterialProductRel> updateList = new ArrayList<>();
        List<MaterialProductRel> materialProductRels = materialProductRelDao.batchQueryByQueryKey(queryKey);
        if (CollectionUtils.isNotEmpty(materialProductRels)) { // 在关联表已存在
            log.info("");
            out: for (MaterialProductRel materialProductRel : materialProductRels) {
                if (productCodes.containsKey(materialProductRel.getProductCode())) {
                    productCodes.remove(materialProductRel.getProductCode());
                    updateList.add(materialProductRel);
                    newMaterialList.add(materialProductRel);
                    continue out;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateTimeIds)) {
            log.info("修改表已有数据的修改时间");
            DateTime dateTime = new DateTime();
            updateList.forEach(e -> e.setUpdateTime(dateTime));
            materialProductRelDao.updateProperties(updateList);
        }
        return erpExclusionObeiProductDto.setProductCodes(productCodes).setNewMaterialProductRelList(newMaterialList);

    }

    /**
     * 统计es和数据库索引数量（工业品）
     *
     * @return
     */
    @Override
    public List<IndexCountVo> reIndexCount() {

        ConcurrentHashMap<Long, IndexCountVo> indexHashMap = new ConcurrentHashMap<>();
        AtomicReference<IndustrialIndexCountDTO> industrialIndexCountDTO = new AtomicReference<>(
                new IndustrialIndexCountDTO());
        ExecutorService fixedThreadPool = Executors.newFixedThreadPool(10);

        // 异步执行索引查询，并处理异常
        CompletableFuture<Integer> cf1 = CompletableFuture.supplyAsync(() -> {
            try {
                R<IndustrialIndexCountDTO> searchReIndexCountDTOR = productSearchClient
                        .reIndexCount(new SearchReIndexCountDTO());
                if (searchReIndexCountDTOR.hasBody() && searchReIndexCountDTOR.isSuccess()) {
                    industrialIndexCountDTO.set(searchReIndexCountDTOR.getResult());
                }
            } catch (Exception e) {
                log.error("索引查询异常", e);
            }
            return 1;
        }, fixedThreadPool);

        List<IndustrialOuterShopConfig> industrialOuterShopConfigs = industrialOuterShopConfigService
                .queryAll(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());
        try {
            List<CompletableFuture<Void>> cfList = new ArrayList<>();

            industrialOuterShopConfigs.forEach(config -> {
                CompletableFuture<Void> future = cf1.thenAcceptAsync(ignored -> {
                    IndexCountVo indexCountVo = new IndexCountVo();
                    indexCountVo.setShopId(config.getShopId());
                    indexCountVo.setSort(config.getId());
                    indexCountVo.setDataBaseName(config.getInnerName() + "数量");
                    Long count = industrialProductDao.getProductOnLineCountByShopId(config.getShopId());
                    indexCountVo.setDataBaseCount(count != null ? count : 0);
                    indexHashMap.put(config.getShopId(), indexCountVo);
                }, fixedThreadPool);
                cfList.add(future);
            });

            // 等待所有异步操作完成
            CompletableFuture.allOf(cfList.toArray(new CompletableFuture[0])).join();

            // 处理完成后的数据
            industrialIndexCountDTO.get().getCountInfos().forEach(countInfo -> {
                IndexCountVo indexCountVo = indexHashMap.get(countInfo.getShopId());
                if (indexCountVo != null) {
                    indexCountVo.setEsName(countInfo.getName() + "数量");
                    indexCountVo.setEsCount(countInfo.getCount() != null ? countInfo.getCount() : 0);
                    indexCountVo.setTotalCount(industrialIndexCountDTO.get().getTotalCount());
                }
            });

            // // 暂时不包含MRO的数据。后续等自营模式上线后，再进行优化
            // Long mroShopId = 0L; // 确保这个ID是有效的，或者根据业务逻辑进行调整
            // IndexCountVo mroIndexCountVo = new IndexCountVo();
            // mroIndexCountVo.setShopId(mroShopId);
            // mroIndexCountVo.setSort(0L);
            // mroIndexCountVo.setEsName("MRO数量");
            // mroIndexCountVo.setEsCount(industrialIndexCountDTO.get().getMroTotalCount());
            // mroIndexCountVo.setTotalCount(industrialIndexCountDTO.get().getTotalCount());
            // indexHashMap.putIfAbsent(mroShopId, mroIndexCountVo); // 确保只添加一次
            // log.info("MRO数据记录: " + JSON.toJSONString(mroIndexCountVo));

        } catch (Exception e) {
            log.error("处理过程中出现异常", e);
        } finally {
            fixedThreadPool.shutdown(); // 确保线程池关闭
        }

        return new ArrayList<>(indexHashMap.values());
    }

    /**
     * 统计es和数据库索引数量（消费品）
     *
     * @return
     */
    @Override
    public ConsumerIndexCountDTO reIndexConsumerCount() throws ExecutionException, InterruptedException {

        ExecutorService fixedThreadPool = Executors.newFixedThreadPool(5);
        ConsumerIndexCountDTO consumerIndexCountDTO = new ConsumerIndexCountDTO();
        CompletableFuture<Integer> cf1 = CompletableFuture.supplyAsync(() -> {
            R<ConsumerIndexCountDTO> searchReIndexCountDTOR = productSearchClient
                    .consumerIndexCount(new ConsumerIndexCountDTO());
            if (searchReIndexCountDTOR.hasBody() && searchReIndexCountDTOR.isSuccess()) {
                thirdPartyProductSearchIndexConverter.to(searchReIndexCountDTOR.getResult(), consumerIndexCountDTO);
                log.info("索引查询结束");
            }
            return 1;
        }, fixedThreadPool);

        CompletableFuture<Integer> f2 = cf1.thenApplyAsync((result) -> {
            // 总数
            consumerIndexCountDTO.setMroConsumerProductCount(productDao.getDetailOnlineCount(
                    IndustrialTypeEnum.CONSUMER_PRODUCTS.getCode(), IsMroSelfEnum.MRO_SELF.getType()));
            log.info("MRO商品查询结束");
            return result += 2;
        }, fixedThreadPool);

        CompletableFuture<Integer> f4 = cf1.thenApplyAsync((result) -> {
            consumerIndexCountDTO.setMroJdVOPProductCount(productDao.getVopDetailOnlineCount());
            log.info("京东VOP商品查询结束");
            return result += 4;
        }, fixedThreadPool);

        Void unused = CompletableFuture.allOf(f2, f4).get();
        log.info("关闭线程池，返回结果");
        fixedThreadPool.shutdown();
        return consumerIndexCountDTO;
    }

    /**
     * 重建索引
     *
     * @param type {@link SearchIndexIndustrialTypeEnum}
     */
    @Override
    public R<String> reIndex(Integer type, Integer industrialType) {
        industrialProductProducerService.sendProductReIndex(type, industrialType);
        return R.ok();
    }

    /**
     * 重建消费品索引
     */
    @Override
    public R<String> reIndexTypeConsumer(String type, Integer industrialType) {
        switch (ThirdConsumerEnum.findByCode(type)) {
            case MRO:
                log.info("更新消费品全部索引");
                consumerProductProducerService.rebuildAllIndexByShopId("-1");
                break;
            case JDVOP:
                log.info("更新JD消费品全部索引");
                consumerProductProducerService.rebuildAllIndexByShopId(
                        thirdParameterConfig.getJdVOPSetting().getShopId() + StrUtil.COLON + "-1");
                break;
        }
        return R.ok();
    }

    // @Override
    // public List<IndustrialProduct> getJdProductByIds(List<Long> prodIds, Long
    // shopId) {
    // return null;
    // }

    // @Override
    // public PageSupport<ThirdPartyProductSearchIndexBO>
    // queryThirdPartyProductsByOnLine(int curPageNO, int pageSize, String supplier)
    // {
    // return null;
    // }

    // public PageSupport<ThirdPartyProductSearchIndexBO>
    // queryThirdPartyProductsByOnLine(int curPageNO, int pageSize, String supplier)
    // {
    // switch (ThirdIndustrialEnum.findByCode(supplier)) {
    // case Obei:
    // return
    // thirdPartyProductSearchIndexConverter.toObeiSearchIndex(obeiProductDao.queryAllObeiProductCode(curPageNO,
    // pageSize));
    // case JD:
    // return
    // thirdPartyProductSearchIndexConverter.toJDSearchIndex(jdProductDao.queryJdProductsByOnLine(curPageNO,
    // pageSize));
    // case SUNING:
    // break;
    // case Zkh:
    // break;
    // default:
    // return new PageSupport<>();
    // }
    // return new PageSupport<>();
    // }

    // public List<ThirdPartyProductSearchIndexBO>
    // queryThirdPartyProductByIds(List<Long> prodIds, String supplier) {
    // switch (ThirdIndustrialEnum.findByCode(supplier)) {
    // case Obei:
    // return
    // thirdPartyProductSearchIndexConverter.toObeiSearchIndex(obeiProductDao.queryAllByIds(prodIds));
    // case JD:
    // return
    // thirdPartyProductSearchIndexConverter.toJDSearchIndex(jdProductDao.queryAllByIds(prodIds));
    // case SUNING:
    // break;
    // case Zkh:
    // break;
    // default:
    // return new ArrayList<>();
    // }
    // return new ArrayList<>();
    // }

    // public List<JdProduct> getJdProductByProductCode(List<Long> productCodes) {
    //
    // List<JdProduct> jdProducts = jdProductDao.queryByProperties(new
    // LambdaEntityCriterion<>(JdProduct.class).in(JdProduct::getSkuId,
    // productCodes));
    // for (JdProduct jdProduct : jdProducts) {
    // jdProduct.setIntroduction("");
    // }
    // return jdProducts;
    // }

    // public List<IndustrialProduct> getJdProductByIds(List<Long> prodIds) {
    // return IndustrialProductService.
    // return .queryByProperties(new
    // LambdaEntityCriterion<>(JdProduct.c-lass).in(JdProduct::getProdId, prodIds));
    // }

    @Override
    public PageSupport<IndustrialSearchDocumentDTO> queryAllProducts(int curPageNO, int pageSize, String supplier) {
        PageSupport<IndustrialSearchDocumentDTO> erpSearchDocumentDTOPageSupport = new PageSupport<>();
        List<IndustrialSearchDocumentDTO> queryLists = new ArrayList<>();
        // ThirdIndustrialEnum thirdIndustrialEnum =
        // ThirdIndustrialEnum.findByCode(supplier);
        // if(ObjectUtil.isEmpty(thirdIndustrialEnum)) {
        IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByInnerCode(supplier);
        if (ObjectUtil.isNotEmpty(config)) {
            log.info("进来外部商品重建索引");
            PageSupport<SkuIdDTO> productDTOPageSupport = industrialProductDao.queryOnLineSkuIdsByShopId(curPageNO,
                    pageSize, config.getPlatformId().equals(MRO_PLATFORM_ID), config.getShopId());
            log.info("内部编码：" + supplier + " 本次查询结果: " + productDTOPageSupport.getResultList().size());
            if (CollUtil.isEmpty(productDTOPageSupport.getResultList())) {
                return null;
            }
            List<SkuIdDTO> outerSkuInfos = productDTOPageSupport.getResultList();

            try {

                List<IndustrialSearchDocumentDTO> industrialSearchDocumentDTO = industrialProductService
                        .buildIndexs(outerSkuInfos, config.getShopId());
                if (CollUtil.isNotEmpty(industrialSearchDocumentDTO)) {
                    queryLists.addAll(industrialSearchDocumentDTO);
                }
            } catch (Exception exception) {
                log.error("重建索引异常 供应商：{}", supplier);
                log.error("重建索引异常", exception);
            }

            erpSearchDocumentDTOPageSupport.setResultList(queryLists);
            erpSearchDocumentDTOPageSupport.setPageSize(productDTOPageSupport.getPageSize());
            erpSearchDocumentDTOPageSupport.setCurPageNO(productDTOPageSupport.getCurPageNO());
            erpSearchDocumentDTOPageSupport.setPageCount(productDTOPageSupport.getPageCount());
            erpSearchDocumentDTOPageSupport.setTotal(productDTOPageSupport.getTotal());
            log.info("查询完成，返回！");
            return erpSearchDocumentDTOPageSupport;
            // }
        }
        return null;
    }
    // else {
    // log.info("进入非第三方工业品进行组装");
    // PageSupport<Product> productPageSupport =
    // productDao.queryProductsByOnLine(curPageNO, pageSize,
    // IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode(), 20);
    // if (CollUtil.isEmpty(productPageSupport.getResultList())) {
    // return null;
    // }
    // List<Long> prodIds =
    // productPageSupport.getResultList().stream().map(Product::getProdId).collect(Collectors.toList());
    // prodIds.forEach(e -> {
    // try {
    // log.info("非第三方工业品进行组装" + e);
    // Long shopId = ThirdIndustrialEnum.ERP.getShopId();
    // IndustrialSearchDocumentDTO industrialSearchDocumentDTO =
    // thirdIndustrialStrategyContext.getStrategy(shopId).buildIndex(e, shopId);
    // if(ObjectUtil.isNotEmpty(industrialSearchDocumentDTO)) {
    // queryLists.add(industrialSearchDocumentDTO);
    // log.info("非第三方工业品进行组装" + e + "完成");
    // }
    // } catch (Exception exception) {
    // log.error("非第三方工业品组装异常", e);
    // }
    // });
    // erpSearchDocumentDTOPageSupport.setResultList(queryLists);
    // erpSearchDocumentDTOPageSupport.setPageSize(productPageSupport.getPageSize());
    // erpSearchDocumentDTOPageSupport.setCurPageNO(productPageSupport.getCurPageNO());
    // erpSearchDocumentDTOPageSupport.setPageCount(productPageSupport.getPageCount());
    // erpSearchDocumentDTOPageSupport.setTotal(productPageSupport.getTotal());
    // log.info("查询完成，返回！");
    // return erpSearchDocumentDTOPageSupport;
    // }

    // switch (thirdIndustrialEnum) {
    // case Obei:
    // log.info("进来欧贝");
    // PageSupport<ObeiProduct> obeiProductPageSupport =
    // obeiProductDao.queryAllObeiProductCode(curPageNO, pageSize);
    // if (CollUtil.isEmpty(obeiProductPageSupport.getResultList())) {
    // return null;
    // }
    // List<Long> obeiIds =
    // obeiProductPageSupport.getResultList().stream().map(ObeiProduct::getId).collect(Collectors.toList());
    // obeiIds.forEach(e -> {
    // try {
    // log.info("欧贝组装：" + e);
    // Long shopId = thirdParameterConfig.getObeiSetting().getShopId();
    // IndustrialSearchDocumentDTO industrialSearchDocumentDTO =
    // thridIndustrialStrategyContext.getStrategy(shopId).buildIndex(e, shopId);
    // if(ObjectUtil.isNotEmpty(industrialSearchDocumentDTO)){
    // queryLists.add(industrialSearchDocumentDTO);
    // log.info("组装：" + e + "完成");
    // }
    // } catch (Exception exception) {
    // log.error(exception.getMessage() + " " + e);
    // }
    // });
    // erpSearchDocumentDTOPageSupport.setResultList(queryLists);
    // erpSearchDocumentDTOPageSupport.setPageSize(obeiProductPageSupport.getPageSize());
    // erpSearchDocumentDTOPageSupport.setCurPageNO(obeiProductPageSupport.getCurPageNO());
    // erpSearchDocumentDTOPageSupport.setPageCount(obeiProductPageSupport.getPageCount());
    // erpSearchDocumentDTOPageSupport.setTotal(obeiProductPageSupport.getTotal());
    // log.info("查询完成，返回！");
    // return erpSearchDocumentDTOPageSupport;
    // case JD:
    // log.info("进来京东");
    // PageSupport<SkuIdDTO> jdproductDTOPageSupport =
    // jdProductDao.queryAllSkuIds(curPageNO, pageSize);
    // if (CollUtil.isEmpty(jdproductDTOPageSupport.getResultList())) {
    // return null;
    // }
    // pack(erpSearchDocumentDTOPageSupport, queryLists, jdproductDTOPageSupport,
    // "京东组装的id为：", thirdParameterConfig.getJdIOPSetting().getShopId());
    // log.info("查询完成，返回！");
    // return erpSearchDocumentDTOPageSupport;

    /// / case SUNING:
    /// / break;
    // case Zkh:
    // log.info("进来震坤行");
    // PageSupport<SkuIdDTO> zkhproductDTOPageSupport =
    // zkhProductDao.queryAllSkuIds(curPageNO, pageSize);
    // if (CollUtil.isEmpty(zkhproductDTOPageSupport.getResultList())) {
    // return null;
    // }
    // pack(erpSearchDocumentDTOPageSupport, queryLists, zkhproductDTOPageSupport,
    // "震坤行组装的id为：", thirdParameterConfig.getZkhSetting().getShopId());
    // log.info("查询完成，返回！");
    // return erpSearchDocumentDTOPageSupport;
    // case ERP:
    // log.info("进入非第三方工业品进行组装");
    // PageSupport<Product> productPageSupport =
    // productDao.queryProductsByOnLine(curPageNO, pageSize,
    // IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode(), 20);
    // if (CollUtil.isEmpty(productPageSupport.getResultList())) {
    // return null;
    // }
    // List<Long> prodIds =
    // productPageSupport.getResultList().stream().map(Product::getProdId).collect(Collectors.toList());
    // prodIds.forEach(e -> {
    // try {
    // log.info("非第三方工业品进行组装" + e);
    // Long shopId = ThirdIndustrialEnum.ERP.getShopId();
    // IndustrialSearchDocumentDTO industrialSearchDocumentDTO =
    // thridIndustrialStrategyContext.getStrategy(shopId).buildIndex(e, shopId);
    // if(ObjectUtil.isNotEmpty(industrialSearchDocumentDTO)) {
    // queryLists.add(industrialSearchDocumentDTO);
    // log.info("非第三方工业品进行组装" + e + "完成");
    // }
    // } catch (Exception exception) {
    // log.error("非第三方工业品组装异常", e);
    // }
    // });
    // erpSearchDocumentDTOPageSupport.setResultList(queryLists);
    // erpSearchDocumentDTOPageSupport.setPageSize(productPageSupport.getPageSize());
    // erpSearchDocumentDTOPageSupport.setCurPageNO(productPageSupport.getCurPageNO());
    // erpSearchDocumentDTOPageSupport.setPageCount(productPageSupport.getPageCount());
    // erpSearchDocumentDTOPageSupport.setTotal(productPageSupport.getTotal());
    // log.info("查询完成，返回！");
    // return erpSearchDocumentDTOPageSupport;
    // default:
    // return null;
    // }
    // return null;

    // private void pack(PageSupport<IndustrialSearchDocumentDTO>
    // erpSearchDocumentDTOPageSupport, List<IndustrialSearchDocumentDTO>
    // queryLists, PageSupport<SkuIdDTO> zkhproductDTOPageSupport, String s, Long
    // shopId) {
    // List<SkuIdDTO> zkhSkuInfos = zkhproductDTOPageSupport.getResultList();
    // for (SkuIdDTO skuIdDTO : zkhSkuInfos) {
    // try {
    // log.info(s + skuIdDTO.getId());
    // IndustrialSearchDocumentDTO industrialSearchDocumentDTO =
    // thirdIndustrialStrategyContext.getStrategy(shopId).buildIndex(skuIdDTO.getId(),
    // shopId);
    // if (ObjectUtil.isNotEmpty(industrialSearchDocumentDTO)) {
    // queryLists.add(industrialSearchDocumentDTO);
    // log.info("组装：" + industrialSearchDocumentDTO.getId() + "完成");
    // }
    // } catch (Exception exception) {
    // log.error(exception.getMessage() + "" + skuIdDTO.getId());
    // }
    // }
    // erpSearchDocumentDTOPageSupport.setResultList(queryLists);
    // erpSearchDocumentDTOPageSupport.setPageSize(zkhproductDTOPageSupport.getPageSize());
    // erpSearchDocumentDTOPageSupport.setCurPageNO(zkhproductDTOPageSupport.getCurPageNO());
    // erpSearchDocumentDTOPageSupport.setPageCount(zkhproductDTOPageSupport.getPageCount());
    // erpSearchDocumentDTOPageSupport.setTotal(zkhproductDTOPageSupport.getTotal());
    // }

    // @Override
    // public List<IndustrialSearchDocumentDTO> getERPAllProductByIds(String
    // supplier, List<Long> prodIds) {
    // log.info("开始组装");
    // List<IndustrialSearchDocumentDTO> queryLists = new ArrayList<>();
    // prodIds.forEach(e -> {
    // IndustrialOuterShopConfig config =
    // IndustrialOuterShopConfigService.getBySupplierCode(supplier);
    // IndustrialSearchDocumentDTO industrialSearchDocumentDTO =
    // thirdIndustrialStrategyContext.getStrategy(supplier).buildIndex(e,
    // config.getShopId());
    // if (ObjectUtil.isNotEmpty(industrialSearchDocumentDTO)) {
    // queryLists.add(industrialSearchDocumentDTO);
    // }
    // });
    // log.info("组装结束");
    // return queryLists;
    // }
    @Override
    public IndustrialSearchDocumentDTO getPreciseProductInfo(String id) {
        String[] split = id.split(StrUtil.COLON);
        Long entityId = 0L;
        // todo 优化枚举
        Long shopId = Long.parseLong(split[0]);

        IndustrialProduct outerProduct = industrialProductDao.getByShopIdAndSkuId(split[1], shopId);
        if (ObjectUtil.isEmpty(outerProduct)) {
            return null;
        }

        return industrialProductService.buildIndex(outerProduct);
    }

    @Override
    public ConsumerProductDocumentDTO getConsumerPreciseProductInfo(String id) {
        /**
         * entityId的作用：检查商品是否允许进索引，实际需要进索引的参数要表联查，尽量减少不必要的查表
         */
        // MRO document组装
        Long entityId = 0L;
        String[] split = id.split(StrUtil.COLON);
        if (split.length == 1) {
            // mro document组装
            Product product = productDao.getProductById(Long.parseLong(id));
            if (product.getIndustrialProdType().equals(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode())) {
                // 不重建工业品
                return null;
            }
            return mroConsumerIndexPack(product);
        }
        // 第三方 document组装
        switch (ThirdConsumerEnum.findByShopId(Long.parseLong(split[0]))) {
            case JDVOP:
                JdVopProduct jdVopProduct = this.jdVopProductDao.getBySkuId(Long.parseLong(split[1]));
                if (!Objects.equals(jdVopProduct.getStatus(), PROD_ONLINE.getValue())) {
                    // 不是上架状态不建索引
                    return null;
                }
                entityId = jdVopProduct.getId();
                break;
            default:
                return null;
        }
        return thirdConsumerStrategyContext.getStrategy(Long.parseLong(split[0]))
                .thridConsumerProductBuildIndex(entityId);
    }

    private ConsumerProductDocumentDTO mroConsumerIndexPack(Product product) {
        // 查询店铺是否存在
        Long shopId = product.getShopId();
        R<BasicShopDetailDTO> result = shopDetailServiceClient.getBasicShopDetailByShopId(shopId);
        if (!result.getStatus().equals(HttpStatusConstants.HTTP_RES_CODE_200) || result.getResult() == null
                || AppUtils.isBlank(result.getResult().getShopName())) {
            log.info("商品信息错误，查询不到商品商家信息，shopId为：{}", shopId);
            return null;
        }
        // 查询sku信息
        List<Sku> skus = this.skuDao.querySkuListByProductId(Collections.singletonList(product.getProdId()));
        // 查询商品分类信息
        List<String> categoryNames = this.categoryService
                .queryCategoryNamesByIds(Collections.singletonList(product.getCategoryId()));
        // 根据分类id查询参数的信息
        List<ProductProperty> productPropertyList = this.msSpecificationService
                .queryPropertyByCategoryId(product.getCategoryId());

        if (CollUtil.isEmpty(skus)) {
            log.info("创建商品索引错误：查询错误，商品没有sku或商品没有分类");
            return null;
        }

        // 商品是否为招标商品
        boolean isTenderSku = false;

        // 准备sku集合
        List<Map<String, Object>> skuList = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();

        // 准备价格集合
        Set<Double> price = new HashSet<>();
        // 排序价格
        Double sortPrice = 0d;
        for (Sku s : skus) {
            price.add(s.getPrice());
            sortPrice = skus.get(0).getPrice();
            Map<String, Object> sku = new HashMap<>();
            sku.put("id", s.getId());
            sku.put("price", s.getPrice());
            sku.put("image", StringUtils.isBlank(s.getPic()) ? "" : s.getPic());
            sku.put("title", s.getName());
            if (s.getTenderSku()) {
                List<TenderSkuDTO> tenderSkuDTOList = tenderSkuDao.getTenderSkuListBySkuId(s.getSkuId());
                List<TenderSkuDTO> collect = tenderSkuDTOList.stream()
                        .filter(tenderSkuDTO -> tenderSkuDTO.getEffectiveDate().getTime() > new Date().getTime()
                                && tenderSkuDTO.getSettlementDate().getTime() < new Date().getTime())
                        .collect(Collectors.toList());
                isTenderSku = !org.springframework.util.CollectionUtils.isEmpty(collect);
            }
            skuList.add(sku);
            skuIds.add(s.getId());
        }

        // 根据skuIds查询料号集合
        List<String> skuMaterialCodeRes = tenderSkuDao.getMaterialCodeBySkuIds(skuIds);

        // 商品sku的规格属性
        Map<String, List<Object>> specialSpecs = new LinkedHashMap<>();
        for (Sku sku : skus) {
            String cnProperties = sku.getCnProperties();
            if (AppUtils.isBlank(cnProperties)) {
                continue;
            }
            String[] split = cnProperties.split(";");
            if (AppUtils.isBlank(split)) {
                continue;
            }
            for (String str : split) {
                String[] sp = str.split(":");
                if (AppUtils.isBlank(sp) || sp.length < 2 || "".equals(sp[1])) {
                    continue;
                }
                if (specialSpecs.containsKey(sp[0])) {
                    specialSpecs.get(sp[0]).add(sp[1]);
                } else {
                    List<Object> obj = new ArrayList<>();
                    obj.add(sp[1]);
                    specialSpecs.put(sp[0], obj);
                }
            }
        }

        // 参数属性
        Map<String, Object> genericSpecs = new LinkedHashMap<>();
        JSONArray genericSpecsArray = JSON.parseArray(product.getParameter());
        if (genericSpecsArray == null || genericSpecsArray.size() <= 0) {
            // return null;
        } else {
            for (int i = 0; i < genericSpecsArray.size(); i++) {
                JSONObject jsonObject = genericSpecsArray.getJSONObject(i);
                String paramName = jsonObject.get("paramName").toString();
                String paramValueName = jsonObject.get("paramValueName").toString();
                if (AppUtils.isNotBlank(paramName) && AppUtils.isNotBlank(paramValueName)) {
                    genericSpecs.put(paramName, paramValueName);
                }
            }
        }

        Map<String, Object> specs = new HashMap<>();
        if (AppUtils.isNotBlank(productPropertyList)) {
            // 过滤规格模板，把所有可搜索的信息保存到Map中
            for (ProductProperty productProperty : productPropertyList) {
                if (productProperty.getIsForSearch()) {
                    if (1 == productProperty.getIsRuleAttributes()) {// 说明是规格属性
                        if (AppUtils.isNotBlank(genericSpecs.get(productProperty.getMemo()))) {
                            specs.put(productProperty.getMemo(), specialSpecs.get(productProperty.getMemo()));
                        }
                    } else if (2 == productProperty.getIsRuleAttributes()) {// 说明是参数属性

                        if (AppUtils.isNotBlank(genericSpecs.get(productProperty.getMemo()))) {
                            specs.put(productProperty.getMemo(), genericSpecs.get(productProperty.getMemo()));
                        }
                        // 判断是否为数值

                        /* specs */
                    }
                }
            }
        }

        ObjectMapper mapper = new ObjectMapper();
        ConsumerProductDocumentDTO productDocument = new ConsumerProductDocumentDTO();

        // 将规格属性筛选出写入keyword
        List<String> properties = skus.stream().map(Sku::getCnProperties).collect(Collectors.toList());
        List<String> filterProperty = new ArrayList<>();
        for (String property : properties) {
            if (AppUtils.isNotBlank(property)) {
                String[] split = property.split(":");
                if (split.length >= 2) {
                    filterProperty.add(split[1]);
                }
            }
        }
        String propertyArray = filterProperty.size() == 0 ? "" : StringUtils.join(filterProperty, " ");

        // 写入商品分类Id
        if (Optional.ofNullable(product.getCategoryId()).orElse(-999L) == -999L) {

        } else {
            Category category = categoryService.getCategoryById(product.getCategoryId());
            if (category != null) {
                log.info("查询的分类为空，不进行索引创建！分类Id为：{}", product.getCategoryId());
                Long oneCategoryId = categoryService.getParentIdByCategoryId(category.getParentId());
                productDocument.setFirstCategoryId(oneCategoryId);
                productDocument.setSecondCategoryId(category.getParentId());
                productDocument.setThirdCategoryId(category.getId());
            }
        }

        productDocument.setProductNameSuggest(product.getName());
        productDocument.setProdId(product.getProdId() + "");
        productDocument.setBrandId(product.getBrandId());
        productDocument.setBrief(product.getBrief());
        productDocument.setTenderSku(isTenderSku);
        productDocument.setSortPrice(sortPrice);
        productDocument.setSupplierSalePrice(new BigDecimal(sortPrice.toString()));
        productDocument.setAdjustedPrice(new BigDecimal(sortPrice.toString()));

        // 添加商家id和商家名称
        productDocument.setShopId(shopId);
        productDocument.setShopName(result.getResult().getShopName());

        productDocument.setProductName(product.getName());
        // 写入商品关键字 --（关键字组成：商品名称+商品分类名称+商品料号）
        log.info(" keyword : {},{},{},{},{}", product.getName(), categoryNames, skuMaterialCodeRes,
                result.getResult().getShopName(), propertyArray);
        productDocument.setKeyword(product.getName() + " " + StringUtils.join(categoryNames, " ") + " "
                + StringUtils.join(skuMaterialCodeRes, " ") + " "
                + result.getResult().getShopName() + " " + propertyArray);
        productDocument.setPrice(new ArrayList<>(price));
        try {
            if (!org.springframework.util.CollectionUtils.isEmpty(skuList) && skuList.size() >= 50) {
                skuList = skuList.subList(0, 50);
            }
            productDocument.setSkus(mapper.writeValueAsString(skuList));
        } catch (JsonProcessingException e) {
            log.info("productDocument设置setSkus错误");
        }
        productDocument.setSpecs(specs);
        productDocument.setViews(product.getViews());
        productDocument.setBuys(product.getBuys());
        productDocument.setComments(product.getComments());
        Product mroProduct = this.getProductById(product.getProdId());
        productDocument.setIsMroSelf(mroProduct.getIsMroSelf());
        productDocument.setId(product.getId() + "");
        productDocument.setTaxRate("0");
        productDocument.setBrandId(-1L);
        productDocument.setSeq(-1L);
        productDocument.setProductCode("");
        productDocument.setSeqSource(-1L);
        productDocument.setThirdFirstCategoryCode("");
        productDocument.setThirdFirstCategoryName("");
        productDocument.setThirdFirstCategoryId(null);
        productDocument.setThirdSecondCategoryId(null);
        productDocument.setThirdSecondCategoryName("");
        productDocument.setThirdSecondCategoryCode("");
        productDocument.setThirdThirdCategoryId(null);
        productDocument.setThirdThirdCategoryName("");
        productDocument.setThirdThirdCategoryCode("");
        productDocument.setSaleMeasureBaseChName("");
        productDocument.setSaleMeasureTypeUnitChName("");
        productDocument.setAdjustId(-1L);
        productDocument.setBrandName(product.getBrandName());
        productDocument.setBrandCode("");
        productDocument.setSupplierUntaxedPrice(new BigDecimal(0));
        productDocument.setSupplierSelfType(IsMroSelfEnum.MRO_SELF.getType());
        productDocument.setTotalPrice(new BigDecimal(0));
        productDocument.setIndustrialProdType(product.getIndustrialProdType());
        return productDocument;
    }

    @Override
    public List<IndustrialSearchDocumentDTO> queryPreciseProductInfo(List<String> ids) {
        List<IndustrialSearchDocumentDTO> industrialSearchDocumentDTOList = new ArrayList<>();
        for (String id : ids) {
            String[] split = id.split(StrUtil.COLON);
            Long shopId = Long.parseLong(split[0]);

            IndustrialProduct industrialProduct = this.industrialProductDao.getByShopIdAndSkuId(split[1], shopId);
            if (ObjectUtil.isEmpty(industrialProduct)) {
                log.debug("找不到该商品信息！ 索引ID为 ： " + id);
            }

            industrialSearchDocumentDTOList.add(industrialProductService.buildIndex(industrialProduct));
        }
        return industrialSearchDocumentDTOList;
    }

    @Override
    public List<ConsumerProductDocumentDTO> queryConsumerIndexInfoByPage(int pageNumber, int pageSize, String key) {
        // MRO商城的重建全部索引
        List<ConsumerProductDocumentDTO> consumerProductDocumentDTOS = new ArrayList<>();

        PageSupport<Product> productPageResult = productDao.pageQueryOnLineProduct(pageNumber, pageSize);
        List<Product> resultList = productPageResult.getResultList();
        if (CollUtil.isEmpty(resultList)) {
            log.info("查到的信息为空");
            return null;
        }
        for (Product product : resultList) {
            // 只加消费品
            if (IndustrialProdTypeEnum.PERSONAL_PROD.getType().equals(product.getIndustrialProdType())) {
                ConsumerProductDocumentDTO consumerProductDocumentDTO = mroConsumerIndexPack(product);
                if (ObjectUtil.isNotEmpty(consumerProductDocumentDTO)) {
                    consumerProductDocumentDTOS.add(consumerProductDocumentDTO);
                }
            }
        }
        return consumerProductDocumentDTOS;

    }

    @Override
    public List<ConsumerProductDocumentDTO> queryVopDocument(Long lastQueryId) {

        List<ConsumerProductDocumentDTO> consumerProductDocumentDTOS = new ArrayList<>();
        jdVopProductDao.pageQueryOnLineProduct2(lastQueryId);
        // 获取调价id不为空并且是上线状态的vop商品
        List<Long> resultList = jdVopProductDao.pageQueryOnLineProduct2(lastQueryId);
        if (CollUtil.isEmpty(resultList)) {
            log.info("查到的信息为空");
            return null;
        }
        resultList.forEach(e -> {
            consumerProductDocumentDTOS.add(thirdConsumerStrategyContext
                    .getStrategy(thirdParameterConfig.getJdVOPSetting().getShopId()).thridConsumerProductBuildIndex(e));
        });
        return consumerProductDocumentDTOS;
    }

    @Override
    public List<CommonSupplierProductDTO> queryByProdIds(List<Long> ids, String userId) {
        List<CommonSupplierProductDTO> returnList = new ArrayList<>();
        List<Product> products = this.productDao.queryAllByIds(ids);
        Map<Long, List<Product>> shopIdMap = products.stream().collect(Collectors.groupingBy(Product::getShopId));
        for (Long shopId : shopIdMap.keySet()) {
            List<Product> shopIdProducts = shopIdMap.get(shopId);
            if (CollUtil.isEmpty(shopIdProducts)) {
                continue;
            }
            List<Long> prodIds = shopIdProducts.stream().map(Product::getProdId).collect(Collectors.toList());

            // 里面有个supplier默认设置为JDVOP，如需拓展，请修改
            if (!ObjectUtil.isEmpty(ThirdConsumerEnum.findByShopId(shopId))) {
                List<CommonSupplierProductDTO> commonSupplierProductDTOS = thirdConsumerStrategyContext
                        .getStrategy(shopId).queryProdByProdIds(prodIds);
                calculateProductPrice(userId, returnList, commonSupplierProductDTOS);
            }

        }
        return returnList;
    }

    private List<CommonSupplierProductDTO> calculateProductPrice(String userId,
            List<CommonSupplierProductDTO> returnList, List<CommonSupplierProductDTO> commonSupplierProductDTOS) {
        Map<Long, CommonSupplierProductDTO> priceMap = commonSupplierProductDTOS.stream()
                .filter(u -> u.getProdId() != null)
                .collect(Collectors.toMap(CommonSupplierProductDTO::getProdId, e -> e));

        // 第三方工业品参与价格计算
        List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData> productPriceAdjustmentData1 = industrialProductConverter
                .toCalculateProductPrice(commonSupplierProductDTOS);
        productPriceAdjustmentData1.forEach(e -> e.setSupplier(ThirdConsumerEnum.JDVOP.getCode()));
        ProductPriceAdjustmentDTO productPriceAdjustmentDTO = new ProductPriceAdjustmentDTO()
                .setProdPriceAdjustmentData(productPriceAdjustmentData1);
        if (StrUtil.isNotBlank(userId)) {
            productPriceAdjustmentDTO.setUserId(userId);
        }
        log.info(productPriceAdjustmentDTO.toString());

        List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData> productPriceAdjustmentDataList = thirdPartyProductAdjustmentService
                .calculateProductPriceAdjustment(productPriceAdjustmentDTO);

        if (CollUtil.isNotEmpty(productPriceAdjustmentDataList)) {
            for (ProductPriceAdjustmentDTO.ProductPriceAdjustmentData productPriceAdjustmentData : productPriceAdjustmentDataList) {
                CommonSupplierProductDTO thirdProductDocumentDTO = priceMap
                        .get(productPriceAdjustmentData.getProductId());
                thirdProductDocumentDTO.setAdjustedPrice(productPriceAdjustmentData.getTotalPrice());
                returnList.add(thirdProductDocumentDTO);
            }
        }
        return returnList;
    }

    @Override
    public void rebuildProdIndexByShopId(Long shopId) {
        List<Product> products = productDao
                .queryByProperties(new LambdaEntityCriterion<>(Product.class).eq(Product::getShopId, shopId));
        for (Product product : products) {
            if (product.getIndustrialProdType().equals(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode())) {
                industrialProductProducerService.updateIndustrialIndex(
                        ThirdIndustrialEnum.ERP.getShopId() + StrUtil.COLON + product.getProdId());
            } else {
                consumerProductProducerService
                        .updateConsumerIndex(ThirdIndustrialEnum.ERP.getShopId() + StrUtil.COLON + product.getProdId());
            }
        }
    }

    @Override
    public String getPreDeliveryDay(AddressQueryDTO addressQueryDTO) {
        return thirdIndustrialStrategyContext.getStrategy(addressQueryDTO.getShopId())
                .checkAreaSaleRestriction(addressQueryDTO);
    }

    @Override
    public String getPreDeliveryDay(PromiseDayQuery promiseDayQuery) {

        AddressQueryDTO addressQueryDTO = new AddressQueryDTO();
        addressQueryDTO.setOuterId(promiseDayQuery.getSkuId());
        addressQueryDTO.setShopId(promiseDayQuery.getShopId());
        if (promiseDayQuery.getAddrId() == null) {
            addressQueryDTO.setProvince("江苏省").setCity("南京市").setArea("六合区");
        } else {
            UserAddressDTO userAddressDTO = userAddressClient.getAddressInfo(promiseDayQuery.getAddrId()).getResult();
            addressQueryDTO.setProvince(userAddressDTO.getProvince()).setCity(userAddressDTO.getCity())
                    .setArea(userAddressDTO.getArea());
        }

        return thirdIndustrialStrategyContext.getStrategy(addressQueryDTO.getShopId()).preDeliveryDay(addressQueryDTO);
    }

    @Override
    public CommonStockDTO getIndustrialProductCheck(StockCheckVo stockCheckVo) {
        ThirdIndustrialStrategy strategy = thirdIndustrialStrategyContext.getStrategy(stockCheckVo.getShopId());
        if (strategy != null) {
            // String userId = UserContextHolder.getInstance().getContext().getUserId();
            UserAddress defaultAddress = userAddressService.getById(stockCheckVo.getAddrId());
            CommonProdQuery commonProdQuery = new CommonProdQuery();
            commonProdQuery.setShopId(stockCheckVo.getShopId());
            Province province = provinceDao.getById(defaultAddress.getProvinceId());
            City city = cityDao.getById(defaultAddress.getCityId());
            Area area = areaDao.getById(defaultAddress.getAreaId());
            commonProdQuery.setProvince(province.getProvince()).setCity(city.getCity()).setArea(area.getArea())
                    .setAddress(defaultAddress.getSubAdds());
            SkuInfoDTO skuInfoDTO = new SkuInfoDTO();
            skuInfoDTO.setSkuId(stockCheckVo.getSkuId());
            skuInfoDTO.setSkuNum(stockCheckVo.getStock());
            commonProdQuery.setSkuInfoDTOS(Collections.singletonList(skuInfoDTO));
            // 真正调用
            return strategy.salableCheck(commonProdQuery);
        }
        return null;
    }

    @Override
    public List<CommonPriceDTO> queryByShopIdAndSkuId(List<Long> shopIds, List<String> productCodes, String userId) {

        List<CommonPriceDTO> commonPriceDTOS = new ArrayList<>();
        for (Long shopId : shopIds) {
            commonPriceDTOS.addAll(industrialProductService.getLatestPrice(shopId, productCodes, userId));
        }

        return commonPriceDTOS;
    }

    @Override
    public CommonStockDTO salableCheck(CommonProdQuery commonProdQuery) {
        if (ObjectUtil.isNotEmpty(commonProdQuery.getInnerCode())) {
            IndustrialOuterShopConfig shopConfig = industrialOuterShopConfigService
                    .getByInnerCode(commonProdQuery.getInnerCode());
            Optional.ofNullable(shopConfig).ifPresent(e -> commonProdQuery.setShopId(e.getShopId()));
        }

        return thirdIndustrialStrategyContext.getStrategy(commonProdQuery.getShopId()).salableCheck(commonProdQuery);

    }

    @Override
    public List<CommonProdInfoDTO> getIndustrialProductInfo(CommonProdQuery commonProdQuery) {
        IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(commonProdQuery.getShopId());
        List<IndustrialProduct> industrialProducts = industrialProductDao
                .queryByShopIdAndSkuId(commonProdQuery.getProductCodes(), commonProdQuery.getShopId());
        log.info("####################查找出来的结果" + JSON.toJSONString(industrialProducts));
        List<CommonProdInfoDTO> commonProdInfoDTOS = industrialProductConverter.toList18(industrialProducts);
        List<CommonPriceDTO> latestPrice = null;
        if (ObjectUtil.isNotEmpty(config)) {

            log.info("####################查找出来的结果" + "222查询价格");
            List<String> skuIds = industrialProducts.stream().map(IndustrialProduct::getSkuId)
                    .collect(Collectors.toList());
            latestPrice = industrialProductService.getLatestPrice(commonProdQuery.getShopId(), skuIds,
                    commonProdQuery.getUserId());
        }

        for (CommonProdInfoDTO commonProdInfoDTO : commonProdInfoDTOS) {
            // 1.设置平台ID
            commonProdInfoDTO.setPlatformId(
                    ObjectUtil.isNotEmpty(config) && config.getPlatformId() > 0 ? config.getPlatformId() : 0L);
            String[] imageArray = commonProdInfoDTO.getImage().split(StrUtil.COMMA);
            if (imageArray.length > 1) {
                commonProdInfoDTO.setImage(imageArray[0]);
            }

            if (CollUtil.isNotEmpty(latestPrice)) {
                CommonPriceDTO commonPriceDTO = latestPrice.stream()
                        .filter(e -> e.getSkuId().equals(commonProdInfoDTO.getProductCode())).findFirst().get();
                commonProdInfoDTO.setAdjustedPrice(commonPriceDTO.getAdjustedPrice());
            }

        }
        return commonProdInfoDTOS;
    }

    @Override
    public R<PageSupport<CommonProdInfoDTO>> pageGetShopProd(ShopQuery shopQuery) {
        ShopDetail shopDetail = shopDetailServiceClient.getShopDetailByShopId(shopQuery.getShopId()).getResult();

        if (AppUtils.isBlank(shopDetail)) {
            return R.fail(-1, "对不起,您要查看的店铺信息不存在!");
        }
        if (AppUtils.isNotBlank(shopDetail.getStatus())
                && shopDetail.getStatus() == 0) {
            return R.fail(-1, "对不起, 该店铺已不营业!");
        }

        String userId = UserContextHolder.getInstance().getContext().getUserId();
        PageSupport<CommonProdInfoDTO> commonProdInfoDTOPageSupport = new PageSupport<>();
        commonProdInfoDTOPageSupport.setPageSize(shopQuery.getPageSize());
        commonProdInfoDTOPageSupport.setPageCount(0);
        commonProdInfoDTOPageSupport.setCurPageNO(1);
        commonProdInfoDTOPageSupport.setResultList(new ArrayList<>());

        if (shopDetail.getEnterpriseType().equals(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getFlag())) {
            // 工业品
            PageParams pageParams = new PageParams();
            pageParams.setPageSize(shopQuery.getPageSize());
            pageParams.setCurPage(shopQuery.getCurPage());
            PageSupport<IndustrialProduct> industrialProductPageSupport = industrialProductDao.queryByPageByShopId(
                    pageParams, ON_SHELF.getMroStatus(), ON_SHELF.getMroStatus(), shopQuery.getSort(),
                    shopQuery.getShopId());
            List<IndustrialProduct> resultList = industrialProductPageSupport.getResultList();
            if (CollUtil.isEmpty(resultList)) {
                return R.ok(commonProdInfoDTOPageSupport);
            }

            List<CommonProdInfoDTO> commonProdInfoDTOS = industrialProductConverter.toList18(resultList);
            List<String> productCodes = commonProdInfoDTOS.stream().map(CommonProdInfoDTO::getProductCode)
                    .collect(Collectors.toList());

            IndustrialOuterShopSettleConfig shopSettleConfig = industrialOuterShopSettleConfigDao
                    .getByShopId(shopQuery.getShopId());
            if (ObjectUtil.isNotEmpty(shopSettleConfig) && shopSettleConfig.getPlatformId() > 0L) {
                List<CommonPriceDTO> priceLists = industrialProductService.getLatestPrice(shopQuery.getShopId(),
                        productCodes, userId);
                Map<String, CommonPriceDTO> priceMap = priceLists.stream()
                        .collect(Collectors.toMap(CommonPriceDTO::getSkuId, e -> e));
                commonProdInfoDTOS.stream().forEach(e -> {
                    CommonPriceDTO commonPriceDTO = priceMap.get(e.getProductCode());
                    e.setAdjustedPrice(commonPriceDTO.getAdjustedPrice());
                });
            }

            commonProdInfoDTOPageSupport.setPageCount(industrialProductPageSupport.getPageCount());
            commonProdInfoDTOPageSupport.setTotal(industrialProductPageSupport.getTotal());
            commonProdInfoDTOPageSupport.setCurPageNO(shopQuery.getCurPage());
            commonProdInfoDTOPageSupport.setResultList(commonProdInfoDTOS);
            return R.ok(commonProdInfoDTOPageSupport);
        } else {
            // productDao.pageQueryOnLineProduct()
            // 消费品

        }
        return null;
    }

    @Override
    public List<CommonProdInfoDTO> getPopular(Long shopId) {
        IndustrialSearchRequest industrialSearchRequest = new IndustrialSearchRequest();
        String userId = UserContextHolder.getInstance().getContext().getUserId();
        if (StrUtil.isNotBlank(userId)) {
            industrialSearchRequest.setUserId(userId);
        }

        if (shopId != null) {
            industrialSearchRequest.setShopId(shopId);
        }

        industrialSearchRequest.setPageSize(10);
        industrialSearchRequest.setCurPage(1);
        industrialSearchRequest.setStatus(10);

        SearchResult<ThirdProductDocumentDTO> result = thirdIndustrialProductService
                .searchBindThirdPartyProduct(industrialSearchRequest).getResult();
        List<ThirdProductDocumentDTO> resultList = result.getResultList();
        if (CollUtil.isEmpty(resultList)) {
            return new ArrayList<>();
        }

        return industrialProductConverter.toList19(resultList);
    }

    @Override
    public List<CommonSimilarSkuDTO> getSimilarSku(CommonProdQuery commonProdQuery) {
        if (ObjectUtil.isNotEmpty(commonProdQuery.getInnerCode())) {
            IndustrialOuterShopConfig shopConfig = industrialOuterShopConfigService
                    .getByInnerCode(commonProdQuery.getInnerCode());
            Optional.ofNullable(shopConfig).ifPresent(e -> commonProdQuery.setShopId(e.getShopId()));
        }
        Long shopId = commonProdQuery.getShopId();
        List<CommonSimilarSkuDTO> similarSkuDTOList = thirdIndustrialStrategyContext.getStrategy(shopId)
                .getSimilarSku(commonProdQuery.getProductCode(), shopId);
        if (ObjectUtil.isEmpty(similarSkuDTOList)) {
            return null;
        }
        List<String> skuIds = similarSkuDTOList.stream()
                .flatMap(dto -> dto.getSaleAttrList().stream())
                .flatMap(attr -> attr.getSkuIds().stream()).distinct().collect(Collectors.toList());
        if (ObjectUtil.isEmpty(skuIds)) {
            return null;
        }
        List<IndustrialProduct> productList = industrialProductDao.queryByShopIdAndSkuId(skuIds, shopId);
        List<IndustrialProduct> onlineProductList = productList.stream()
                .filter(prod -> ProductStatusEnum.PROD_ONLINE.value().equals(prod.getState()))
                .collect(Collectors.toList());

        if (ObjectUtil.isEmpty(onlineProductList)) {
            return null;
        }
        // similarSkuDTOList保留所有已上线的skuId
        Set<String> onlineSkuIds = onlineProductList.stream().map(IndustrialProduct::getSkuId)
                .collect(Collectors.toSet());

        // 过滤 similarSkuDTOList，保留包含已上线的 skuId
        return similarSkuDTOList.stream()
                .map(dto -> {
                    List<CommonSimilarSkuDTO.ProductSaleAttr> filteredAttrs = dto.getSaleAttrList().stream()
                            .map(attr -> {
                                Set<String> filteredSkuIds = attr.getSkuIds().stream()
                                        .filter(onlineSkuIds::contains)
                                        .collect(Collectors.toSet());
                                attr.setSkuIds(filteredSkuIds);
                                return attr;
                            })
                            .filter(attr -> !attr.getSkuIds().isEmpty())
                            .collect(Collectors.toList());

                    dto.setSaleAttrList(filteredAttrs);
                    return dto;
                })
                .filter(dto -> !dto.getSaleAttrList().isEmpty())
                .collect(Collectors.toList());
    }

    @Override
    public IndustrialProductDTO getByIdAndShopId(Long id, Long shopId) {
        IndustrialProduct industrialProduct = industrialProductDao.getByIdAndShopId(id, shopId);
        return industrialProductConverter.to4(industrialProduct);
    }

    @Override
    public IndustrialProductDTO queryByProductCodes(String skuId, Long shopId) {
        IndustrialProduct industrialProduct = industrialProductDao.getByShopIdAndSkuId(skuId, shopId);
        return industrialProductConverter.to4(industrialProduct);
    }

    @Override
    public IndustrialProductDTO getObeiEcologyRenewProduct() {
        IndustrialProduct industrialProduct = industrialProductDao.getByShopIdAndSkuId("obeiecology02",
                ThirdIndustrialEnum.ObeiEcology.getShopId());
        return industrialProductConverter.to4(industrialProduct);
    }

    private Long getAccountBookId(String userId) {
        Long defaultAccountBookId = AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId();

        if (ObjectUtil.isEmpty(userId)) {
            return defaultAccountBookId;
        }

        // 查询账期信息
        AccountPeriodVO accountPeriod = Optional
                .ofNullable(accountPeriodClient.getInfoByUserId(userId, SecurityConstants.FROM_IN)).orElse(R.fail())
                .getResult();
        if (AppUtils.isNotBlank(accountPeriod)) {
            return accountPeriod.getAccountBookId();
        }

        // 查询账套信息
        AccountBookVO accountBook = Optional.ofNullable(bookClient.getByUserId(userId)).orElse(new R<>()).getResult();
        if (AppUtils.isNotBlank(accountBook)) {
            return accountBook.getAccountBookId();
        }

        // 查询账套授权企业信息
        AccountBookCompanyVO bookCompany = Optional
                .ofNullable(bookCompanyClient.getByUserId(userId, AccountBookStatusEnum.AUTHORIZED.getCode()))
                .orElse(R.fail()).getResult();
        if (AppUtils.isNotBlank(bookCompany)) {
            return bookCompany.getAccountBookId();
        }

        return defaultAccountBookId;
    }


    @Override
    public R<List<IndustrialProductForErpDocumentDTO>> listSameProduct(Long shopId, Long prodId, String userId) {
        if (ObjectUtil.isEmpty(userId)) {
            return R.ok();
        }
        Long accountBookId = getAccountBookId(userId);

        // 过滤掉不支持的店铺，并提取供应商代码列表
        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId).orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        // 过滤掉不支持的店铺，并提取ShopId列表
        List<Long> noAuthShopIdList = listR.getSuccess() ? listR.getResult().stream()
                .map(AccountBookDetailVO::getShopId)
                .collect(Collectors.toList()) : Collections.emptyList();

        R<List<ProductSimilarity>> sameProductR = similarityProductClient.listSameProduct(shopId, prodId);
        if (!sameProductR.getSuccess()) {
            return R.fail(sameProductR.getMsg());
        }
        List<ProductSimilarity> similarProductList = sameProductR.getResult();

        // 只保留支持的店铺商品
        List<ProductSimilarity> authProductList = similarProductList.stream()
                .filter(productSimilarity -> !noAuthShopIdList.contains(productSimilarity.getTargetShopId()))
                .collect(Collectors.toList());
        // Get shopIds and prodIds from authProductList
        List<Long> shopIds = authProductList.stream().map(ProductSimilarity::getTargetShopId).collect(Collectors.toList());
        List<Long> ids = authProductList.stream().map(ProductSimilarity::getTargetProdId).collect(Collectors.toList());

        // Check if lists are empty
        if (CollUtil.isEmpty(shopIds) || CollUtil.isEmpty(ids)) {
            return R.ok(Collections.emptyList());
        }

        // Get industrial products and filter for online products
        List<IndustrialProduct> industrialProducts = industrialProductService.queryByShopIdAndId(shopIds, ids);
        if (CollUtil.isEmpty(industrialProducts)) {
            return R.ok(Collections.emptyList());
        }

        industrialProducts = industrialProducts.stream().filter(product -> product != null && MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState())).collect(Collectors.toList());

        // Convert to DTOs safely
        List<IndustrialProductForErpDocumentDTO> convertedList = obeiProductEntityConverter.toOuterProductDocumentDTOList(industrialProducts);

        if (CollUtil.isEmpty(convertedList)) {
            return R.ok(Collections.emptyList());
        }
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>(convertedList);

        // 价格从低到高排序
        resultDTO.sort(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice));
        return R.ok(resultDTO);
    }

    @Override
    public R<List<IndustrialProductForErpDocumentDTO>> listSimilarProduct(Long shopId, Long prodId, String userId) {
        if (ObjectUtil.isEmpty(userId)) {
            return R.ok();
        }
        Long accountBookId = getAccountBookId(userId);

        // 过滤掉不支持的店铺，并提取供应商代码列表
        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId).orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        // 过滤掉不支持的店铺，并提取ShopId列表
        List<Long> noAuthShopIdList = listR.getSuccess() ? listR.getResult().stream()
                .map(AccountBookDetailVO::getShopId)
                .collect(Collectors.toList()) : Collections.emptyList();

        R<List<ProductSimilarity>> similarProductR = similarityProductClient.listSimilarProduct(shopId, prodId);
        if (!similarProductR.getSuccess()) {
            return R.fail(similarProductR.getMsg());
        }
        // 如果相似度商品列表为空，则主动处理相似度查询、识别任务
        if (CollUtil.isEmpty(similarProductR.getResult())) {
            similarityProductClient.handleSingleProductSimilarity(shopId, prodId);
            similarProductR = similarityProductClient.listSimilarProduct(shopId, prodId);
            if (!similarProductR.getSuccess()) {
                return R.fail(similarProductR.getMsg());
            }
        }
        List<ProductSimilarity> similarProductList = similarProductR.getResult();

        // 只保留支持的店铺商品
        List<ProductSimilarity> authProductList = similarProductList.stream()
                .filter(productSimilarity -> !noAuthShopIdList.contains(productSimilarity.getTargetShopId()))
                .collect(Collectors.toList());
        // Get shopIds and prodIds from authProductList
        List<Long> shopIds = authProductList.stream().map(ProductSimilarity::getTargetShopId).collect(Collectors.toList());
        List<Long> ids = authProductList.stream().map(ProductSimilarity::getTargetProdId).collect(Collectors.toList());

        // Check if lists are empty
        if (CollUtil.isEmpty(shopIds) || CollUtil.isEmpty(ids)) {
            return R.ok(Collections.emptyList());
        }

        // Get industrial products and filter for online products
        List<IndustrialProduct> industrialProducts = industrialProductService.queryByShopIdAndId(shopIds, ids);
        if (CollUtil.isEmpty(industrialProducts)) {
            return R.ok(Collections.emptyList());
        }

        industrialProducts = industrialProducts.stream().filter(product -> product != null && MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState())).collect(Collectors.toList());

        // Convert to DTOs safely
        List<IndustrialProductForErpDocumentDTO> convertedList = obeiProductEntityConverter.toOuterProductDocumentDTOList(industrialProducts);

        if (CollUtil.isEmpty(convertedList)) {
            return R.ok(Collections.emptyList());
        }
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>(convertedList);

        // 创建一个映射，用于存储商品ID和店铺ID到相似度的映射关系
        Map<String, BigDecimal> similarityMap = authProductList.stream()
                .collect(Collectors.toMap(ps -> ps.getTargetShopId() + ":" + ps.getTargetProdId(),
                        ProductSimilarity::getSimilarity, (v1, v2) -> v1.compareTo(v2) > 0 ? v1 : v2));

        // 设置相似度值
        resultDTO.forEach(dto -> {
            String key = dto.getShopId() + ":" + dto.getProdId();
            dto.setSimilarity(similarityMap.getOrDefault(key, BigDecimal.ZERO));
        });

        // 根据相似度排序结果
        resultDTO.sort((o1, o2) -> {
            String key1 = o1.getShopId() + ":" + o1.getProdId();
            String key2 = o2.getShopId() + ":" + o2.getProdId();
            BigDecimal similarity1 = similarityMap.getOrDefault(key1, BigDecimal.ZERO);
            BigDecimal similarity2 = similarityMap.getOrDefault(key2, BigDecimal.ZERO);
            return similarity2.compareTo(similarity1); // 降序排序
        });

        return R.ok(resultDTO);
    }
}
