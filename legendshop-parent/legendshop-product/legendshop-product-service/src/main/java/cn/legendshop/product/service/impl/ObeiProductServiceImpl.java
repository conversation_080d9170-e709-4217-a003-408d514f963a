package cn.legendshop.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Page;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.constants.OpenPlatformShopSwitchTypeEnum;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.config.ThirdParameterConfig;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.dao.IndustrialOuterShopSwitchSettleDao;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyConstant;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.enums.ObeiProductMsgEnum;
import cn.legendshop.enums.ObeiSendDataEnum;
import cn.legendshop.jd.iop.sdk.enums.JdProductStateEnum;
import cn.legendshop.obei.RemoteObeiClient;
import cn.legendshop.product.api.constant.ProdChangeConstant;
import cn.legendshop.order.client.v2.QuotationClient;
import cn.legendshop.product.api.dto.*;
import cn.legendshop.product.api.dto.product.*;
import cn.legendshop.product.api.entity.MaterialProductRel;
import cn.legendshop.product.api.entity.*;
import cn.legendshop.product.api.enums.*;
import cn.legendshop.product.api.query.product.ThridIndustrialProductSimpleQuery;
import cn.legendshop.product.api.vo.*;
import cn.legendshop.product.dao.*;
import cn.legendshop.product.excel.ObeiProductSheetExcelModel;
import cn.legendshop.product.mq.producer.IndustrialProductProducerService;
import cn.legendshop.product.mq.producer.OuterIndustrialProductProducerService;
import cn.legendshop.product.mq.producer.ProdChangeLogProducerService;
import cn.legendshop.product.service.MessagePoolProductService;
import cn.legendshop.product.service.MsCategoryService;
import cn.legendshop.product.service.ObeiProductService;
import cn.legendshop.product.service.ProdPriceContactsService;
import cn.legendshop.product.service.convert.ObeiProductEntityConverter;
import cn.legendshop.search.api.client.IndustrialProductIndexClient;
import cn.legendshop.search.api.client.SimilarityProductTaskClient;
import cn.legendshop.search.api.dto.ProductSimilarityTaskDTO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.util.AppUtils;
import com.legendshop.util.StringUtil;
import com.obei.openapi.OPENAPIClient;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.legendshop.product.api.enums.IndexUpdateFlagEnum.UNUPDATE;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.OFF_SHELF;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.ON_SHELF;

@Slf4j
@Service("obeiProductService")
@AllArgsConstructor
public class ObeiProductServiceImpl implements ObeiProductService {

	private final CategoryErpSupplierDao categoryErpSupplierDao;

	private final MaterialProductRelDao materialProductRelDao;

	private final ObeiProductDao obeiProductDao;

	private final ErpCategoryDao erpCategoryDao;

	private MsCategoryService msCategoryService;

	private final ObeiCategoryDao obeiCategoryDao;

	private final IndustrialProductProducerService industrialProductProducerService;

	private final ObeiProductEntityConverter obeiProductEntityConverter;

	private final ObeiCommodityTaskDao obeiCommodityTaskDao;

	private ThirdParameterConfig thirdParameterConfig;

	private final RemoteObeiClient remoteObeiClient;

	private final ObeiProductHisitoryDao obeiProductHisitoryDao;

	private final CategoryMroSupplierDao categoryMroSupplierDao;

	private final IndustrialProductIndexClient industrialProductIndexClient;

	private final IndustrialProductDao industrialProductDao;

	private final IndustrialOuterShopConfigService configService;

	private final OuterIndustrialProductProducerService outerIndustrialProductProducerService;

	private final MessagePoolProductService messagePoolProductService;

	private final IndustrialOuterShopSwitchSettleDao industrialOuterShopSwitchSettleDao;
	private final QuotationClient quotationClient;

	private final ProdChangeLogProducerService prodChangeProducerService;

	private final ProdPriceContactsService prodPriceContactsService;

	private final SimilarityProductTaskClient similarityProductTaskClient;

	@Override
	public PageSupport<ReturnBindedProductInfoDTO> queryAllBindedProductInfo(ErpBindedMessageQueryVo query) {
		return this.obeiProductDao.queryBindedProductInfo(query);
	}

	@Override
	public PageSupport<String> queryBrandNameByInput(ErpBrandNameQueryVo erpBrandNameQueryVo) {
		return obeiProductDao.queryBrandNameByInputBrandName(erpBrandNameQueryVo);
	}

	@Override
	public Boolean isExistBrandName(ErpBrandNameQueryVo erpBrandNameQueryVo) {
		Integer is = obeiProductDao.isExistBrandName(erpBrandNameQueryVo.getBrandName());
		if(is == null) {
			return false;
		}else {
			return true;
		}
	}

	@Override
	public void checkObeiProduct() {
		List list = new ArrayList();
		EasyExcel.read("C:\\Users\\<USER>\\Desktop\\1.xls", ObeiProductSheetExcelModel.class, new AnalysisEventListener<ObeiProductSheetExcelModel>() {
			//重写子类方法
			@Override
			public void invoke(ObeiProductSheetExcelModel question1, AnalysisContext analysisContext) {
				list.add(question1);
			}

			//重写子类方法
			@Override
			public void doAfterAllAnalysed(AnalysisContext analysisContext) {

			}

			@Override
			public void invokeHeadMap(Map headMap, AnalysisContext context) {
				System.out.println(headMap);
			}
		}).excelType(ExcelTypeEnum.XLS).sheet("1").doRead();

		List<Long> a = new ArrayList<>();
		Integer count = 0;
		for (Object o : list) {
			ObeiProductSheetExcelModel question1 = (ObeiProductSheetExcelModel) o;
//			if(question1.getObeiProductCode() == "#VALUE!"){
//				continue;
//			}
			a.add(question1.getObeiProductCode());
			count++;
		}
		log.info(list.size() + "   " + count.toString());
		StringBuffer stringBuffer = new StringBuffer();
		for (Long s : a) {
			stringBuffer.append("\"" + s.toString() + "\"" + ",");
		}
		log.info(stringBuffer.toString());
	}

	@Override
	public CheckResultVo check(ProductCodeVo code) {
		List<ObeiProduct> obeiProducts = this.obeiProductDao.queryByobeiProductCode(code.getProductCode());
		List<String> noExist = new ArrayList<>();
		out:
		for (String s : code.getProductCode()) {
			for (ObeiProduct obeiProduct : obeiProducts) {
				if (s.equals(obeiProduct.getObeiProductCode())) {
					continue out;
				}
			}
			noExist.add(s);
		}
		CheckResultVo checkResultVo = new CheckResultVo();
		checkResultVo.setCount(noExist.size());
		checkResultVo.setNotExistCode(noExist);
		return checkResultVo;
	}


	@Override
	public IndustrialProductDetailForErpDTO getProductDetail(Long id) {
		ObeiProduct byId = this.obeiProductDao.getById(id);
		return obeiProductEntityConverter.to3(byId);
	}


	@Override
	public IndustrialProductDetailForErpDTO getProductDetailByCode(String productCode) {
		ObeiProduct byProductCode = this.obeiProductDao.getByobeiProductCode(productCode);
		return obeiProductEntityConverter.to3(byProductCode);
	}

	/**
	 * 批量导入
	 *
	 * @param supplier
	 * @param productDate
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public List<ReturnErpProductInfoDto> updateThirdSupplierDate(String supplier, List<ProductReceiveDateVo> productDate) throws UnsupportedEncodingException {
//		log.info("[ 批量导入料号与第三方工业品关系 ]");
//		List<String> failDate = new ArrayList<>();
//		List<String> successDate = new ArrayList<>();
//		HashSet<String> category = new HashSet<>();
//		List<ReturnErpProductInfoDto> returnInfo = new ArrayList<>();
//
//		/**
//		 * 返回成功ERP绑定类目successDate
//		 * 绑定失败ERP类目编码failDate
//		 * bindResult：已经装好失败结果的list
//		 * category2CategoryMap：类目和类目绑定编码
//		 */
//		AbstractReturnListDto abstractReturnListDto = this.abstractCheckMethod(supplier, productDate);
//		if (ObjectUtil.isEmpty(abstractReturnListDto)) {
//			List<ReturnErpProductInfoDto> returnErpProductInfoDtos = failHandle2(productDate);
//			return returnErpProductInfoDtos;
//		}
//
//		/**
//		 * 抽取返回一个class
//		 */
//		//做一些状态判断
//		if (ObjectUtil.isNotEmpty(abstractReturnListDto) && CollUtil.isEmpty(abstractReturnListDto.getBindResult())) {
//
//			if (!abstractReturnListDto.getSuccessDate().isEmpty()) {
//				successDate.addAll(abstractReturnListDto.getSuccessDate());
//			} else {
//				log.info("================能插入的分类为空=================");
//			}
//
//			if (!abstractReturnListDto.getSuccessDate().isEmpty()) {
//				failDate.addAll(abstractReturnListDto.getFailDate());
//			} else {
//				log.info("===============不能插入的分类为空===============");
//			}
//
//			if (!abstractReturnListDto.getCategory().isEmpty()) {
//				category.addAll(abstractReturnListDto.getCategory());
//			} else {
//				log.info("===============不能插入的分类为空===============");
//			}
//
//		}
//
////===========================================================================
////
//
//		ProductDataCheckDto productDataCheckDto = this.repeatInsertCheck(supplier, successDate, productDate, category);
//
//		List<String> newDsitinctCategory = successDate.stream().distinct().collect(Collectors.toList());
//
////======================================================================
//		/**
//		 * 第二种情况，既有成功绑定的也有绑定失败的
//		 * 先查成功的部分
//		 */
//		log.info(productDataCheckDto.getIsExistList().toString());
//		//执行保存绑定关系操作
//
//
//		if (CollUtil.isNotEmpty(newDsitinctCategory) && CollUtil.isNotEmpty(productDataCheckDto.getNewList())) {
//			List<ReturnErpProductInfoDto> returnErpProductInfoDtos = queryProductInfo(productDataCheckDto.getNewList());
//			//把当前查的结果加到总表
//			returnInfo.addAll(returnErpProductInfoDtos);
//			log.info("[ 开始插入传进来的部分数据 ]");
//			this.materialProductRelDao.batchSaveSuccessPart(supplier, productDataCheckDto.getNewList());
//			log.info("[ 插入传进来的部分数据结束 ]");
//		}
//		//处理已经逻辑删除的物料号和商品绑定关系
//		if(CollUtil.isNotEmpty(productDataCheckDto.getFilterList())){
//			handleUpdateRelStatus(productDataCheckDto.getFilterList(),supplier);
//			List<ReturnErpProductInfoDto> returnErpProductInfoDtos = queryProductInfo(productDataCheckDto.getFilterList());
//			thirdLogSendMsgUtil.sendThirdPartyLogMQ(new ThirdPartyLogDTO(CartServiceNameConstant.LEGENDSHOP_PRODUCT_SERVICE,
//				ThirdPartyConstant.ERP, "物料号绑定商品插入数据库动作(已经逻辑删除)", com.alibaba.fastjson.JSONObject.toJSONString(productDataCheckDto.getNewList()), HttpStatus.HTTP_OK, StrUtil.SPACE));
//			returnInfo.addAll(returnErpProductInfoDtos);
//		}
//		/**
//		 * 失败的部分，失败部分不保存绑定关系
//		 */
//		List<ReturnErpProductInfoDto> returnErpProductInfoDtos = this.failHandle2(productDataCheckDto.getIsExistList());
//		returnInfo.addAll(returnErpProductInfoDtos);
//		log.info("重建索引--------------------------！！！！！！！！！！！！！！！");
//		if(CollUtil.isNotEmpty(productDataCheckDto.getNewList())){
//			indexRebulid(productDataCheckDto.getNewList());
//		}
//		log.info("发送要入库的商品信息给mq");
//		amqpSendMsgUtil.convertAndSend(ThridIndustrialProductMQConstant.OBEI_IN_STORE_EXCHANGE,ThridIndustrialProductMQConstant.OBEI_IN_STORE_PRODUCT_ROUTING_KEY,new Message(JSONObject.toJSONBytes(productDataCheckDto.getNewList()), new MessageProperties()));
//		thirdLogSendMsgUtil.sendThirdPartyLogMQ(new ThirdPartyLogDTO(CartServiceNameConstant.LEGENDSHOP_PRODUCT_SERVICE,
//			ThirdPartyConstant.ERP, "物料号绑定商品插入数据库动作", com.alibaba.fastjson.JSONObject.toJSONString(productDataCheckDto.getNewList()), HttpStatus.HTTP_OK, StrUtil.SPACE));
//		log.info("发送成功");
//		return returnInfo;

		List<BindProductInfoSync> bindResult = new ArrayList<>();
		List<String> queryKeyList = productDate.stream().map(e -> e.getMaterialCode() + StrUtil.C_COLON + e.getThirdSkuId() + StrUtil.C_COLON + supplier).collect(Collectors.toList());

		//查询绑定关系表
		List<MaterialProductRel> materialProductRels = this.materialProductRelDao.batchQueryByQueryKey(queryKeyList);
		if (CollUtil.isEmpty(materialProductRels)) {
			log.info("全部没有绑定过，直接插入数据库");
			this.materialProductRelDao.batchSaveSuccessPart(supplier, productDate);
			List<BindProductInfoSync> bindProductInfoSyncs = returnToErpMsg(1, "", productDate);
			log.info("发送mq------1号");
//			industrialProductProducerService.obeiInStoreProduct(productDate);
			List<ReturnErpProductInfoDto> returnErpProductInfoDtos = queryProductInfo(productDate);
			if (CollUtil.isEmpty(returnErpProductInfoDtos)) {
				return null;
			}
			return encodeBeforeSendMessage(returnErpProductInfoDtos);
		}
		DateTime dateTime = new DateTime();
		//查出来的长度是否相同
		log.info("查出来的长度是否相同");
		if (materialProductRels.size() == queryKeyList.size()) {
			for (MaterialProductRel materialProductRel : materialProductRels) {
				materialProductRel.setIsDeleted(0);
				materialProductRel.setIsSured(1);
				materialProductRel.setUpdateTime(dateTime);
			}
			log.info("全部都是已经绑定，返回绑定成功，更新状态");
			this.materialProductRelDao.update(materialProductRels);
			log.info("发送mq------2号");
//			industrialProductProducerService.obeiInStoreProduct(productDate);
		} else {
			log.info("查出来的长度不相同");
			Map<String, MaterialProductRel> queryKeyMap = materialProductRels.stream().distinct().collect(Collectors.toMap(MaterialProductRel::getQueryKey, Function.identity()));
			log.info("开始处理两种情况");
			saveNewRel(queryKeyList, dateTime, queryKeyMap);
		}
		List<ReturnErpProductInfoDto> returnErpProductInfoDtos = queryProductInfo(productDate);
		if (CollUtil.isEmpty(returnErpProductInfoDtos)) {
			return null;
		}
		return encodeBeforeSendMessage(returnErpProductInfoDtos);
	}

	private List<ReturnErpProductInfoDto> encodeBeforeSendMessage(List<ReturnErpProductInfoDto> returnErpProductInfoDtos) throws UnsupportedEncodingException {
		for (ReturnErpProductInfoDto returnErpProductInfoDto : returnErpProductInfoDtos) {
			String skuName = URLEncoder.encode(returnErpProductInfoDto.getSkuName(), CharsetUtil.UTF_8);
			String brandName = URLEncoder.encode(returnErpProductInfoDto.getBrandName(), CharsetUtil.UTF_8);
			String imP = URLEncoder.encode(returnErpProductInfoDto.getImagePath(), CharsetUtil.UTF_8);

			if (StringUtils.isNotEmpty(returnErpProductInfoDto.getSeoModel())) {
				String seoModel = URLEncoder.encode(returnErpProductInfoDto.getSeoModel(), CharsetUtil.UTF_8);
				returnErpProductInfoDto.setSeoModel(seoModel);
			}
			if (StringUtils.isNotEmpty(returnErpProductInfoDto.getSpecificParam())) {
				String specificParam = URLEncoder.encode(returnErpProductInfoDto.getSpecificParam(), CharsetUtil.UTF_8);
				returnErpProductInfoDto.setSpecificParam(specificParam);
			}
			if (StringUtils.isNotEmpty(returnErpProductInfoDto.getSaleUnit())) {
				String saleUnit = URLEncoder.encode(returnErpProductInfoDto.getSaleUnit(), CharsetUtil.UTF_8);
				returnErpProductInfoDto.setSaleUnit(saleUnit);
			}
			if (StringUtils.isNotEmpty(returnErpProductInfoDto.getProductArea())) {
				String productArea = URLEncoder.encode(returnErpProductInfoDto.getProductArea(), CharsetUtil.UTF_8);
				returnErpProductInfoDto.setProductArea(productArea);
			}
			if (StringUtils.isNotEmpty(returnErpProductInfoDto.getWarrantDesc())) {
				String warrantDes = URLEncoder.encode(returnErpProductInfoDto.getWarrantDesc(), CharsetUtil.UTF_8);
				returnErpProductInfoDto.setWarrantDesc(warrantDes);
			}
			returnErpProductInfoDto.setSkuName(skuName);
			returnErpProductInfoDto.setBrandName(brandName);
			returnErpProductInfoDto.setImagePath(imP);
		}
		return returnErpProductInfoDtos;
	}

	/**
	 * 绑定商品库信息同步接口
	 *
	 * @param supplier
	 * @param productDate
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public List<BindProductInfoSync> updateBindingRel(String supplier, List<ProductReceiveDateVo> productDate) {

		List<BindProductInfoSync> bindResult = new ArrayList<>();

		List<String> queryKeyList = productDate.stream().map(e -> e.getMaterialCode() + StrUtil.C_COLON + e.getThirdSkuId() + StrUtil.C_COLON + supplier).collect(Collectors.toList());

		//查询绑定关系表
		List<MaterialProductRel> materialProductRels = this.materialProductRelDao.batchQueryByQueryKey(queryKeyList);
		if (CollUtil.isEmpty(materialProductRels)) {
			log.info("全部没有绑定过，直接插入数据库");
			List<ProductReceiveDateVo> filterData = new ArrayList<>();
			List<ProductReceiveDateVo> failData = new ArrayList<>();
			for (ProductReceiveDateVo productReceiveDateVo : productDate) {
				ObeiProduct byobeiProductCode = this.obeiProductDao.getByobeiProductCode(productReceiveDateVo.getThirdSkuId());
				if (ObjectUtil.isEmpty(byobeiProductCode)) {
					failData.add(productReceiveDateVo);
				} else {
					filterData.add(productReceiveDateVo);
				}
			}

			this.materialProductRelDao.batchSaveSuccessPart(supplier, filterData);
			List<BindProductInfoSync> bindProductInfoSyncs = new ArrayList<>();
			if (CollUtil.isNotEmpty(filterData)) {
				bindProductInfoSyncs.addAll(returnToErpMsg(1, StrUtil.EMPTY, filterData));
				log.info("发送mq------1号");
//				industrialProductProducerService.obeiInStoreProduct(filterData);
			}
			if (CollUtil.isEmpty(filterData)) {
				bindProductInfoSyncs.addAll(returnToErpMsg(0, "欧贝商品不存在", filterData));
			}
			return bindProductInfoSyncs;
		}
		DateTime dateTime = new DateTime();
		//查出来的长度是否相同
		log.info("查出来的长度是否相同");
		List<ProductReceiveDateVo> filterList1 = new ArrayList<>();
		if (materialProductRels.size() == queryKeyList.size()) {
			List<String> filterList = new ArrayList<>();

			for (ProductReceiveDateVo productReceiveDateVo : productDate) {
				ObeiProduct byobeiProductCode = this.obeiProductDao.getByobeiProductCode(productReceiveDateVo.getThirdSkuId());
				if (ObjectUtil.isEmpty(byobeiProductCode)) {
					filterList.add(productReceiveDateVo.getThirdSkuId());
					filterList1.add(productReceiveDateVo);
				}
			}
			out:
			for (MaterialProductRel materialProductRel : materialProductRels) {
				for (String s : filterList) {
					if (Objects.equals(s, materialProductRel.getProductCode())) {
						log.info(s + "不存在");
						continue out;
					}
				}
				materialProductRel.setIsDeleted(0);
				materialProductRel.setIsSured(1);
				materialProductRel.setUpdateTime(dateTime);
			}
			List<ProductReceiveDateVo> filterData = new ArrayList<>();
			List<ProductReceiveDateVo> failData = new ArrayList<>();

			log.info("全部都是已经绑定，返回绑定成功，更新状态");
			this.materialProductRelDao.update(materialProductRels);

		} else {

			log.info("查出来的长度不相同");
			Map<String, MaterialProductRel> queryKeyMap = materialProductRels.stream().distinct().collect(Collectors.toMap(MaterialProductRel::getQueryKey, Function.identity()));
			log.info("开始处理两种情况");
			saveNewRel(queryKeyList, dateTime, queryKeyMap);
		}

		List<BindProductInfoSync> bindProductInfoSyncs = returnToErpMsg(1, StrUtil.EMPTY, productDate);
		if (CollUtil.isNotEmpty(filterList1)) {
			bindProductInfoSyncs.addAll(returnToErpMsg(0, "欧贝商品不存在", filterList1));
		}
		return bindProductInfoSyncs;

	}

	private void saveNewRel(List<String> queryKeyList, DateTime dateTime, Map<String, MaterialProductRel> queryKeyMap) {

		List<ProductReceiveDateVo> productReceiveDates = new ArrayList<>();
		List<MaterialProductRel> materialProductRels = new ArrayList<>();
		List<String> queryKeys = new ArrayList<>();
		for (String queryKey : queryKeyList) {
			MaterialProductRel materialProductRel = queryKeyMap.get(queryKey);
			log.info("处理queryKey不存在的情况");
			if (ObjectUtil.isEmpty(materialProductRel)) {
				String[] queryKeySplit = queryKey.split(StrUtil.COLON);
				MaterialProductRel newEntity = new MaterialProductRel();
				ProductReceiveDateVo productReceiveDateVo = new ProductReceiveDateVo();
				newEntity.setMaterialCode(queryKeySplit[0]);
				newEntity.setProductCode(queryKeySplit[1]);
				newEntity.setSupplier(queryKeySplit[2]);
				newEntity.setCreateTime(dateTime);
				newEntity.setUpdateTime(dateTime);
				newEntity.setIsSured(1);
				newEntity.setIsDeleted(0);
				newEntity.setQueryKey(queryKey);
				materialProductRels.add(materialProductRel);
				productReceiveDateVo.setMaterialCode(queryKeySplit[0]);
				productReceiveDateVo.setThirdSkuId(queryKeySplit[1]);
				productReceiveDates.add(productReceiveDateVo);
			} else {
				//先把queryKey记录下来
				queryKeys.add(queryKey);
			}
		}
		//处理queryKey
		if (CollUtil.isNotEmpty(queryKeys)) {
			log.info("--------------queryKeys不为空-----------");
			queryKeys.forEach(e -> {
				MaterialProductRel materialProductRel = queryKeyMap.get(e);
				materialProductRel.setIsDeleted(0);
				materialProductRel.setIsSured(1);
				materialProductRel.setUpdateTime(dateTime);
				materialProductRels.add(materialProductRel);
			});

		}

		this.materialProductRelDao.save(materialProductRels);
	}

	private Boolean parentCategoryCheck(ProductReceiveDateVo productReceiveData) {
		log.info("进入parentCategoryCheck");
		//a的情况比较特殊 todo
		if (productReceiveData.getCategoryCode().charAt(0) != 'a') {
			if (productReceiveData.getCategoryCode().length() > 3) {
				//类目字段截取判断父类是否允许插入
				out:
				for (int i = productReceiveData.getCategoryCode().length(); i >= 5; i -= 2) {
					String parentCategory = productReceiveData.getCategoryCode().substring(0, productReceiveData.getCategoryCode().length() - 2);
					GetErpCategoryBindDTO obeiCategoryIdByCategoryCode = this.erpCategoryDao.getObeiCategoryIdByCategoryCode(parentCategory, productReceiveData.getThirdSkuId());
					if (ObjectUtil.isEmpty(obeiCategoryIdByCategoryCode)) {
						continue out;
					}
					//不为空 则说明允许绑定
					return true;
				}
			}

			return false;
		}

		return false;
	}

	private Boolean supplierCheck(String supplier, GetErpCategoryBindDTO categoryNobeiCategorys) {

		if (ObjectUtil.isEmpty(categoryNobeiCategorys)) {
			log.info("供应商不允许绑定");
			return false;
		}

		String[] midSupSplit = categoryNobeiCategorys.getMidSupplier().split(StrUtil.COMMA);

		String[] categorySplit = categoryNobeiCategorys.getErpAllowSupplier().split(StrUtil.COMMA);
		for (String categorySupplier : categorySplit) {
			for (String sup : midSupSplit) {
				if (sup.equals(supplier) && categorySupplier.equals(supplier)) {                           //supplier用，号区分
					//能对应上，就添加
					log.info("供应商允许绑定");
					return true;
				}

			}
		}

		return false;
	}

	private Map<String, List<String>> extractCategoryNthirdSkuId(List<ProductReceiveDateVo> productDate) {
		Map<String, List<String>> categoryMap = new HashMap<>();
		out:
		for (ProductReceiveDateVo productReceiveDateVo : productDate) {
			List<String> strings = categoryMap.get(productReceiveDateVo.getCategoryCode());
			if (CollUtil.isEmpty(strings)) {
				List<String> newStrs = new ArrayList<>();
				newStrs.add(productReceiveDateVo.getThirdSkuId());
				categoryMap.put(productReceiveDateVo.getCategoryCode(), newStrs);
				continue out;
			}
			strings.add(productReceiveDateVo.getThirdSkuId());
			categoryMap.put(productReceiveDateVo.getCategoryCode(), strings);
		}
		return categoryMap;
	}

	private AbstractReturnListDto abstractCheckMethod(String supplier, List<ProductReceiveDateVo> productDate) {
		List<String> failDate = new ArrayList<>();
		List<String> successDate = new ArrayList<>();
		List<BindProductInfoSync> bindResult = new ArrayList<>();
		/**
		 * 1.筛选ERP类目是否存在
		 */
		//筛选ERP类目名称  （不重复的类目编号List)
		List<String> distCategoryCode = productDate.stream().map(ProductReceiveDateVo::getCategoryCode).distinct().collect(Collectors.toList());
		if (CollUtil.isEmpty(distCategoryCode)) {
			log.info("===================筛选出来的 ERP 类目名称为空==================");
			return null;
		}
		//获取类目信息
		//==================================================================================================
		/**
		 * 根据erp类目编码筛选出重复的erp料号，把不存在的类目编码加进failDatalist
		 */
		List<String> thirdSkuId = productDate.stream().map(ProductReceiveDateVo::getThirdSkuId).distinct().collect(Collectors.toList());
		List<ObeiProduct> obeiProducts = this.obeiProductDao.queryObeiClassIdByproductCode(thirdSkuId);
		if (CollUtil.isEmpty(obeiProducts)) {
			log.info("obei商品信息不存在");
			for (ProductReceiveDateVo productDataVo : productDate) {
				BindProductInfoSync bindProductInfoSync = new BindProductInfoSync();
				bindProductInfoSync.setImportStatus(0);
				bindProductInfoSync.setImportMessages("欧贝商品不存在");
				bindProductInfoSync.setMaterialCode(productDataVo.getMaterialCode());
				bindProductInfoSync.setThirdSkuId(productDataVo.getThirdSkuId());
				bindResult.add(bindProductInfoSync);
			}
			failDate.addAll(distCategoryCode);
			AbstractReturnListDto returnListDto = new AbstractReturnListDto();
			returnListDto.setSuccessDate(successDate);
			returnListDto.setFailDate(failDate);
			returnListDto.setBindResult(bindResult);
			industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "物料号绑定商品", JSON.toJSONString(returnListDto));
			return returnListDto;
		}
		List<String> classCodes = obeiProducts.stream().map(ObeiProduct::getClassCode).distinct().collect(Collectors.toList());
		List<ObeiCategory> obeiCategories = this.obeiCategoryDao.queryCategoryByClassCode(classCodes);


		List<String> rmDuplicateErpCategoryCodes = new ArrayList<>();
		//取出第三方类目编码 + erp类目编号
		List<CategoryErpSupplierDTO> categoryErpSupplierDTOS = this.categoryErpSupplierDao.queryAllcategoryErpSupplier2(distCategoryCode);
		if (CollUtil.isEmpty(categoryErpSupplierDTOS)) {
			for (ProductReceiveDateVo productDataVo : productDate) {
				BindProductInfoSync bindProductInfoSync = new BindProductInfoSync();
				bindProductInfoSync.setImportStatus(0);
				bindProductInfoSync.setImportMessages("该类目不允许绑定此第三方商品");
				bindProductInfoSync.setMaterialCode(productDataVo.getMaterialCode());
				bindProductInfoSync.setThirdSkuId(productDataVo.getThirdSkuId());
				bindResult.add(bindProductInfoSync);
			}
			failDate.addAll(distCategoryCode);
			AbstractReturnListDto returnListDto = new AbstractReturnListDto();
			returnListDto.setSuccessDate(successDate);
			returnListDto.setFailDate(failDate);
			returnListDto.setBindResult(bindResult);
			industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "物料号绑定商品", JSON.toJSONString(returnListDto));
			return returnListDto;
		}

		List<String> enableInsertCategorys = new ArrayList<>();
		if (CollUtil.isNotEmpty(categoryErpSupplierDTOS)) {
			log.info("筛选出来的第三方不为空");
			out:
			for (CategoryErpSupplierDTO ces : categoryErpSupplierDTOS) {
				for (ObeiCategory obeiCategory : obeiCategories) {
					if (ces.getSupplierId().equals(obeiCategory.getId())) {
						enableInsertCategorys.add(ces.getCategoryCode());
						continue out;
					}
				}
			}
			if (CollUtil.isNotEmpty(enableInsertCategorys)) {
				log.info("存在可以插入的ERP类目");

				for (String categoryCode : distCategoryCode) {    //判定类目是否被允许插入
					out:
					for (String str : enableInsertCategorys) {
						if (str.equals(categoryCode)) {
							rmDuplicateErpCategoryCodes.add(str);
							break out;
						}
						failDate.add(str);
					}
				}
			}
		} else {
			AbstractReturnListDto returnListDto = new AbstractReturnListDto();
			failDate.addAll(distCategoryCode);
			returnListDto.setFailDate(failDate);
			return returnListDto;
		}

		//1.判断erp能否绑定此供应商
		List<ErpCategory> erpCategories = this.erpCategoryDao.queryBatchCategoryPermitByCategoryCode(rmDuplicateErpCategoryCodes);
		List<ErpCategory> newErpCategories = new ArrayList<>();
		//异常处理（如果ERP类目供应商supplier为空，则全部加到failData）
		out:
		for (ErpCategory erpCategory : erpCategories) {
			if (erpCategory.getSupplier() == null) {
				failDate.add(erpCategory.getCategoryCode());
				break out;
			}
			newErpCategories.add(erpCategory);
		}
		Map<String, String> catSupMap = newErpCategories.stream().collect(Collectors.toMap(ErpCategory::getCategoryCode, ErpCategory::getSupplier));
		for (ProductReceiveDateVo productDataVo : productDate) {
			String ResultSup = catSupMap.get(productDataVo.getCategoryCode());
			if (ResultSup == null) { //为空，则没有权限
				failDate.add(productDataVo.getCategoryCode());
				continue;
			}
			String[] supSplit = ResultSup.split(StrUtil.COMMA);
			for (String sup : supSplit) {
				if (sup.equals(supplier)) {                           //supplier用，号区分
					//能对应上，就添加
					successDate.add(productDataVo.getCategoryCode());
					break;
				}
				//对应不上说明没有权限 顺便删除拿出来
				failDate.add(productDataVo.getCategoryCode());
			}
		}

		List<String> distFailCategory = successDate.stream().distinct().collect(Collectors.toList());

		//2.筛选 erp类目和第三方商品类目 组一个Set,从数据库拿出
		HashSet<String> category = new HashSet<>();
		List<ErpMaterialRelDto> erpThirdCategoryRelDtos = this.erpCategoryDao.queryProductDataInfo(rmDuplicateErpCategoryCodes, supplier);
		for (String failCategory : distFailCategory) {
			out:
			for (ErpMaterialRelDto erpMaterialRelDto : erpThirdCategoryRelDtos) {
				if (!failCategory.equals(erpMaterialRelDto.getErpCategoryCode())) {
					continue out;
				}
				String str = erpMaterialRelDto.getErpCategoryCode() + StrUtil.COLON + erpMaterialRelDto.getThirdClassCode();
				category.add(str);
			}
		}

		AbstractReturnListDto returnListDto = new AbstractReturnListDto();
		returnListDto.setSuccessDate(successDate);
		returnListDto.setFailDate(distFailCategory);
		returnListDto.setBindResult(bindResult);
		returnListDto.setCategory(category);
		return returnListDto;
	}

	private void HandleIsExistStatus(List<ProductReceiveDateVo> isExistList, String supplier) {
		List<MaterialProductRel> newMaterialProductRelList = new ArrayList<>();
		List<String> queryKey = isExistList.stream().map(e -> e.getMaterialCode() + StrUtil.C_COLON + e.getThirdSkuId() + StrUtil.C_COLON + supplier).collect(Collectors.toList());

		List<MaterialProductRel> materialProductRels = this.materialProductRelDao.updateDeleteNSureStatusByQueryKey(queryKey, supplier);
		out:
		for (MaterialProductRel materialProductRel : materialProductRels) {
			if (BindRelationStatusEnum.IS_BINDED.value().equals(materialProductRel.getIsSured()) && LogicDeleteEnum.NOT_LOGIC_DELETE.value().equals(materialProductRel.getIsDeleted())) {
				continue out;
			}
			materialProductRel.setIsSured(BindRelationStatusEnum.IS_BINDED.value());
			materialProductRel.setIsDeleted(LogicDeleteEnum.NOT_LOGIC_DELETE.value());
			newMaterialProductRelList.add(materialProductRel);
		}
		this.materialProductRelDao.update(newMaterialProductRelList);
	}

	/**
	 * 把productDate传进来进行筛选，传两个List出去，一个是没有插入数据的，另一个是数据库已经存在数据的
	 *
	 * @param supplier
	 * @param checkedErpCategorys
	 * @param erpProductDate
	 * @return
	 */
	private ProductDataCheckDto repeatInsertCheck(String supplier,
												  List<String> checkedErpCategorys,
												  List<ProductReceiveDateVo> erpProductDate,
												  HashSet<String> category) {

		List<ProductReceiveDateVo> newList = new ArrayList<>();
		List<ProductReceiveDateVo> isExistList = new ArrayList<>();
		List<ProductReceiveDateVo> filterList = new ArrayList<>();
		List<String> queryKey = new ArrayList<>();

		/**
		 * 现在需要检查传过来的第三方商品能绑定
		 * 1.根据传过来的productData的categoryCode判断对应的第三方类目id
		 * 2.检查商品对应的类在不在category2CategoryMap
		 */
//		List<String> thirdSkuIds = new ArrayList<>();
//		for (ProductReceiveDateVo productReceiveDateVo : erpProductDate) {
//			String thirdSkuId = productReceiveDateVo.getThirdSkuId();
//			thirdSkuIds.add(thirdSkuId);
//		}
		List<String> thirdSkuIds = erpProductDate.stream().map(ProductReceiveDateVo::getThirdSkuId).collect(Collectors.toList());

		//获取欧贝商品信息，
		List<ObeiProduct> obeiProducts = this.obeiProductDao.queryObeiClassIdByproductCode(thirdSkuIds);
		if (CollUtil.isEmpty(obeiProducts)) {
			log.info("获取欧贝商品信息List为空");
		}
		for (ObeiProduct obeiProduct : obeiProducts) {
			out:
			for (ProductReceiveDateVo productReceiveDateVo : erpProductDate) {
				if (productReceiveDateVo.getThirdSkuId().equals(obeiProduct.getObeiProductCode()) && !category.contains(productReceiveDateVo.getCategoryCode() + StrUtil.C_COLON + obeiProduct.getClassCode())) {
					queryKey.add(productReceiveDateVo.getMaterialCode() + StrUtil.C_COLON + productReceiveDateVo.getThirdSkuId() + StrUtil.C_COLON + supplier);
					break out;
				}
			}
		}

		if (CollUtil.isEmpty(queryKey)) {
			log.info("没有能成功绑定的类目");
			isExistList.addAll(erpProductDate);
			ProductDataCheckDto productDataCheckDto = new ProductDataCheckDto();
			return productDataCheckDto.setIsExistList(isExistList);
		}
		if (CollUtil.isNotEmpty(checkedErpCategorys)) {
			log.info("根据传进来的值拼成queryKey检查是否已经插入");
			List<MaterialProductRel> resultList = this.materialProductRelDao.batchQueryByQueryKey(queryKey);
			log.info("检查完成，一共取得" + resultList.size() + "条数据");

			HashSet<String> queryKeySet = new HashSet<>();
			if (CollUtil.isNotEmpty(resultList)) {
				for (MaterialProductRel materialProductRel : resultList) {
					if (materialProductRel.getIsDeleted() == 1) {
						ProductReceiveDateVo productReceiveDateVo = new ProductReceiveDateVo();
						productReceiveDateVo.setThirdSkuId(materialProductRel.getProductCode());
						productReceiveDateVo.setMaterialCode(materialProductRel.getMaterialCode());
						filterList.add(productReceiveDateVo);
					}
					queryKeySet.add(materialProductRel.getQueryKey());
				}
			}

			out:
			for (ProductReceiveDateVo productReceiveDateVo : erpProductDate) {
				if (!queryKeySet.contains(productReceiveDateVo.getMaterialCode() + StrUtil.C_COLON + productReceiveDateVo.getThirdSkuId() + StrUtil.C_COLON + supplier)) {
					newList.add(productReceiveDateVo);
					continue out;
				}
				for (ProductReceiveDateVo productReceiveDateVo1 : filterList) {
					if (productReceiveDateVo.getThirdSkuId().equals(productReceiveDateVo1.getThirdSkuId()) && productReceiveDateVo.getMaterialCode().equals(productReceiveDateVo1.getMaterialCode())) {
						continue out;
					}
				}
				isExistList.add(productReceiveDateVo);
			}


			ProductDataCheckDto productDataCheckDto = new ProductDataCheckDto();
			productDataCheckDto.setNewList(newList);
			productDataCheckDto.setIsExistList(isExistList);
			productDataCheckDto.setFilterList(filterList);
			return productDataCheckDto;
		}
		ProductDataCheckDto productDataCheckDto = new ProductDataCheckDto();
		isExistList.addAll(erpProductDate);
		productDataCheckDto.setIsExistList(erpProductDate);
		return productDataCheckDto;
	}


	/**
	 * 根据类目对应的thirdSkuId查询商品信息
	 *
	 * @return
	 */
	public List<ReturnErpProductInfoDto> queryProductInfo(List<ProductReceiveDateVo> GetProuctData) {
		//获取有权限的商品skuId
		List<String> productCode = GetProuctData.stream().map(ProductReceiveDateVo::getThirdSkuId).collect(Collectors.toList());//这个地方NPE

		List<ObeiProduct> obeiProducts = this.obeiProductDao.queryObeiProdcutByObeiProductCode(productCode);
		if (CollUtil.isEmpty(obeiProducts)) {
			return Collections.emptyList();
		}
		return obeiProductEntityConverter.toList(obeiProducts);
	}


	/**
	 * 失败情况1
	 *
	 * @param failList
	 * @return
	 */
	public List<ReturnErpProductInfoDto> failHandle1(List<String> failList) {
		return failList.stream().map(k -> {
			ReturnErpProductInfoDto failInfo = new ReturnErpProductInfoDto();
			failInfo.setThirdSkuId(k);
			failInfo.setImportStatus(0);
			failInfo.setImportMessages("此类目不允许添加此商品");
			return failInfo;
		}).collect(Collectors.toList());
	}

	/**
	 * 失败情况2
	 *
	 * @param productDate
	 * @return
	 */
	public List<ReturnErpProductInfoDto> failHandle2(List<ProductReceiveDateVo> productDate) {
		return productDate.stream().map(k -> {
			ReturnErpProductInfoDto failInfo = new ReturnErpProductInfoDto();
			failInfo.setThirdSkuId(k.getThirdSkuId());
			failInfo.setImportStatus(0);
			failInfo.setImportMessages("此商品跟此料号已经绑定");
			return failInfo;
		}).collect(Collectors.toList());
	}

	/**
	 * 失败情况3
	 *
	 * @param productDate
	 * @return
	 */
	public List<BindProductInfoSync> failHandle3(List<ProductReceiveDateVo> productDate) {
		return productDate.stream().map(k -> {
			BindProductInfoSync failInfo = new BindProductInfoSync();
			failInfo.setThirdSkuId(k.getThirdSkuId());
			failInfo.setImportStatus(0);
			failInfo.setImportMessages("所给的商品信息无法绑定，请检查类目是否允许采购");
			failInfo.setMaterialCode(k.getMaterialCode());
			return failInfo;
		}).collect(Collectors.toList());
	}


	private List<BindProductInfoSync> returnToErpMsg(int status, String msg, List<ProductReceiveDateVo> productDate) {
		return productDate.stream().map(productDataVo -> {
			BindProductInfoSync bindProductInfoSync = new BindProductInfoSync();
			bindProductInfoSync.setImportStatus(status);
			bindProductInfoSync.setImportMessages(msg);
			bindProductInfoSync.setMaterialCode(productDataVo.getMaterialCode());
			bindProductInfoSync.setThirdSkuId(productDataVo.getThirdSkuId());
			return bindProductInfoSync;
		}).collect(Collectors.toList());
	}

	private void handleUpdateRelStatus(List<ProductReceiveDateVo> productReceiveData, String supplier) {
//		List<String> queryKey = new ArrayList<>();
//		for (ProductReceiveDateVo newList : productReceiveData) {
//			queryKey.add(newList.getMaterialCode() + StrUtil.C_COLON + newList.getThirdSkuId() + StrUtil.C_COLON + supplier);
//		}
		List<String> queryKey = productReceiveData.stream().map(newList -> newList.getMaterialCode() + StrUtil.C_COLON + newList.getThirdSkuId() + StrUtil.C_COLON + supplier).collect(Collectors.toList());
		//todo  handleUpdateRelStatus
		List<MaterialProductRel> materialProductRels = this.materialProductRelDao.batchQueryByQueryKey(queryKey);
		for (MaterialProductRel materialProductRel : materialProductRels) {
			DateTime dateTime = new DateTime();
			materialProductRel.setIsSured(BindRelationStatusEnum.IS_BINDED.value());
			materialProductRel.setIsDeleted(LogicDeleteEnum.NOT_LOGIC_DELETE.value());
			materialProductRel.setUpdateTime(dateTime);
		}
		this.materialProductRelDao.save(materialProductRels);

	}


	@Override
	public CommonSupplierProductDTO getObeiProductById(Long prodId) {
//		ObeiProduct obeiProduct = obeiProductDao.getById(prodId);
//		return obeiProductEntityConverter.toCommonSupplierProductDTO(obeiProduct);
		return null;
	}

	@Override
	public Boolean updateMroStatus(List<updataListVo> updataListVos, Integer flag) {

		if(MroIndustrialProdStatusEnum.findByCode(flag).getNum() == null){
			return Boolean.FALSE;
		}

		List<Long> prodIds = updataListVos.stream().map(updataListVo::getId).collect(Collectors.toList());
//		return	SqlUtil.retBool(this.obeiProductDao.updateProperties(new LambdaUpdate<>(ObeiProduct.class).in(ObeiProduct::getId,prodIds).set(ObeiProduct::getStatus,flag)));
		return Boolean.FALSE;
	}


	@Override
	public void compensateObeiInStore() {
		Integer pageSzie = 20;
		Integer pageCur = 1;
		Boolean flag = false;

		do{
			PageSupport<ObeiCommodityTask> obeiCommodityTaskPageSupport = this.obeiCommodityTaskDao.queryInStoreFailMessage(pageSzie, pageCur, DateUtil.lastMonth());
			List<ObeiCommodityTask> resultList = obeiCommodityTaskPageSupport.getResultList();

			if(CollUtil.isNotEmpty(resultList)){
				for (ObeiCommodityTask commodityTask : resultList) {
					ObeiCommodityInfoVo obeiCommodityInfoVo1 = JSONUtil.toBean(commodityTask.getCommodityTaskContent(), ObeiCommodityInfoVo.class);
					Boolean check = this.queryProductCallback(obeiCommodityInfoVo1);
					if (check) {
						//更新状态为处理成功 写入数据表obei_commodity_task，20 已处理，处理次数加1
						commodityTask.setStatus(ObeiProductProcessStatuEnum.PROCESSED.value());
					} else {
						commodityTask.setStatus(ObeiProductProcessStatuEnum.PROCESSED_FAIL.value());
						//更新状态为处理失败， 失败后定时任务重新发MQ重试 写入数据表obei_commodity_task，30 处理失败，处理次数加1
					}
					log.info("进入更新欧贝商品导入");
					commodityTask.setProcessCout(Optional.ofNullable(commodityTask.getProcessCout()).orElse(0L) + 1);
					obeiCommodityTaskDao.update(commodityTask);
				}
				pageCur ++;
			}else {
				flag = true;
			}
		}while (!flag);
	}

	@Override
	public List<CommonSupplierProductDTO> queryUpdateProd(Integer updateStatus, Integer currentPage, Integer pageSize) {
		List<ObeiProduct> obeiProducts = obeiProductDao.queryUpdateProd(updateStatus, currentPage, pageSize);
		return obeiProductEntityConverter.toCommonSupplierProductDTOList(obeiProducts);

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean queryProductCallback(ObeiCommodityInfoVo commodityInfo) {
		boolean switchStatus = industrialOuterShopSwitchSettleDao.getSwitchStatus(ThirdIndustrialEnum.Obei.getShopId(), OpenPlatformShopSwitchTypeEnum.SHOP_AUDIT.getValue());
		log.info("回调信息：{}", JSONUtil.toJsonStr(commodityInfo));
		if (ObjectUtil.isEmpty(commodityInfo)) {
			log.info("没有返回{}", commodityInfo);
			return Boolean.FALSE;
		}
		Map<String, Object> queryParam = Maps.newHashMap();
		queryParam.put("type", commodityInfo.getType());
		//组织代码
		queryParam.put("companyCode", thirdParameterConfig.getObeiSetting().getCompanyCode());
		Long obeiShopId = thirdParameterConfig.getObeiSetting().getShopId();
		if (CollUtil.isNotEmpty(commodityInfo.getCommodityCodes())) {

			//循环发送
			for (String commodityCode : commodityInfo.getCommodityCodes()) {
				queryParam.put("commodityCode", commodityCode);
				XxlJobLogger.log("正在执行：" + commodityCode);
				R<ObeiProductVo> data = queryRemoteObeiProductVo(commodityInfo.getType(), commodityCode, remoteObeiClient.getSupplier().get(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()).get(), queryParam);
				XxlJobLogger.log("查询结果：" + JSONUtil.toJsonStr(data));
				int i = 0;
				if (data.isSuccess() && data.hasBody()) {
					ObeiProductVo obeiProductVo = data.getResult();
					IndustrialProduct industrialProductFromDb = industrialProductDao.getByShopIdAndSkuId(commodityCode, obeiShopId);
					//针对欧贝商品已经下架的情况进行处理
					if(ObjectUtil.isNotEmpty(industrialProductFromDb) && Objects.equals(new Integer(obeiProductVo.getStatus()), OFF_SHELF.getThirdStatus())){
						industrialProductFromDb.setState(OFF_SHELF.getMroStatus());
						industrialProductDao.updateProdByShopId(industrialProductFromDb, obeiShopId);
						log.info("重建索引");
						industrialProductProducerService.afterCommitUpdateIndustrialIndex(industrialProductFromDb.getShopId() + StrUtil.COLON + industrialProductFromDb.getSkuId());

						// 创建相似商品删除任务
						similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(3).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, obeiShopId, industrialProductFromDb.getId()))));

						//商品更新完通知ERP下架
						notifyErp(commodityInfo, industrialProductFromDb);
						// 绑定商品通知采购商:更新上下架状态
						messagePoolProductService.pushBindProductStateChange(obeiShopId, Collections.singletonList(industrialProductFromDb.getSkuId()), OFF_SHELF.getMroStatus());
						return Boolean.TRUE;
					}


					if (Objects.equals(new Integer(obeiProductVo.getStatus()), OFF_SHELF.getThirdStatus())) {
						IndustrialProduct noticeProduct = new IndustrialProduct();
						noticeProduct.setSkuId(commodityCode);
						//商品更新完通知ERP下架
						notifyErp(commodityInfo, noticeProduct);
						// 绑定商品通知采购商:更新上下架状态
						messagePoolProductService.pushBindProductStateChange(obeiShopId, Collections.singletonList(commodityCode), OFF_SHELF.getMroStatus());
						continue;
					}

					// 工业品税率必须为13，否则直接跳过
					if (!String.valueOf(13).equals(obeiProductVo.getTaxRate())) {
						return Boolean.TRUE;
					}
					IndustrialProduct obeiProduct = buildObeiProduct(obeiProductVo);

					//查询面价
					Map<String, Object> queryParam2 = Maps.newHashMap();
					queryParam2.put("loginCode", thirdParameterConfig.getObeiSetting().getCompanyCode());
					queryParam2.put("commodityCode", commodityCode);
					R<ObeiPriceVo> obeiPrice = getObeiPrice(commodityCode, queryParam2);
					if(obeiPrice.getSuccess() && data.hasBody()){
						obeiProduct.setMarketPrice(obeiPrice.getResult().getFacePrice());
					}


					//新增导入时查询调价id，把调价id放入redis缓存
					SupplierCategoryBindErpVo obeiCategory = this.msCategoryService.getObeiCategory(obeiProduct.getCategory());
					if(ObjectUtil.isNotEmpty(obeiCategory)){
						CategoryMroSupplier categoryMroSupplier = this.categoryMroSupplierDao.getBySupplierId(obeiCategory.getId(), ThirdIndustrialEnum.Obei.getCode());
						if(ObjectUtil.isNotEmpty(categoryMroSupplier)){
							obeiProduct.setCategoryMroSupplierId(categoryMroSupplier.getId());
						}
					}

					//存在就更新，不存在就保存商品信息
					if (Objects.isNull(industrialProductFromDb)) {

						obeiProduct.setBuys(0);
						obeiProduct.setViews(0);
						obeiProduct.setComments(0);
						if (switchStatus){
							obeiProduct.setState(OFF_SHELF.getMroStatus());
						}

						Long prodId = industrialProductDao.saveProdByShopId(obeiProduct, obeiShopId);
						//更新询价单上架明细
						quotationClient.updateQuotationProduct(obeiProduct.getSkuId());

						// 创建相似商品更新任务
						similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(2).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, obeiShopId, prodId))));
					}else {

						log.info("商品价格更新：商品sku为:{}",industrialProductFromDb.getSkuId());
						// 记录变更前的价格
						BigDecimal beforePrice = industrialProductFromDb.getPrice();
						// 计算变更后的价格和价格变化比率
						BigDecimal afterPrice = obeiProduct.getPrice();
						BigDecimal priceChangeRate = calculatePriceChangeRate(beforePrice, afterPrice);
						if(priceChangeRate.compareTo(BigDecimal.ZERO) != 0) {
							// 创建价格变更记录
							ProdChangeLogDTO record = new ProdChangeLogDTO();
							record.setShopId(thirdParameterConfig.getObeiSetting().getShopId());
							record.setShopName(thirdParameterConfig.getObeiSetting().getSiteName());
							record.setSkuId(industrialProductFromDb.getSkuId());  // 商品sku
							record.setBeforePrice(beforePrice);  //变更前价格
							record.setAfterPrice(afterPrice);  //变更后价格
							record.setPriceChangeRate(priceChangeRate);  // 价格变化比率
							record.setRequestDate(new Date());  // 当前请求日期
							//发送商品价格变更mq
							prodChangeProducerService.sendPriceChangeMessage(record);

							// 判断该商品是否需要提醒
							List<ProdPriceContacts> priceLogList = prodPriceContactsService.getBySkuAndShopId(record.getShopId(), record.getSkuId());
							log.info("需要进行价格变化公众号提醒的商品:{}",JSON.toJSONString(priceLogList));
							if(AppUtils.isNotBlank(priceLogList)) {
								for (ProdPriceContacts priceLog : priceLogList) {
									ProdPriceContactsVo vo = new ProdPriceContactsVo();
									vo.setShopId(priceLog.getShopId());
									vo.setSkuId(priceLog.getSkuId());
									vo.setShopName(record.getShopName());
									vo.setMaterialCode(priceLog.getMaterialCode());
									vo.setBeforePrice(record.getBeforePrice());
									vo.setAfterPrice(record.getAfterPrice());
									vo.setBidPrice(priceLog.getBidPrice());
									vo.setContacts(priceLog.getContacts());
									log.info("指定商品提醒的联系人:{}",priceLog.getContacts());
									vo.setPriceChangeRate(record.getPriceChangeRate());
									prodChangeProducerService.sendProdPriceWXMessage(vo);// 发送价格变更微信公众号提醒MQ
								}
							}
						}
						obeiProduct.setId(industrialProductFromDb.getId());
						obeiProduct.setProdId(industrialProductFromDb.getProdId());
						obeiProduct.setUpdateFlag(UNUPDATE.getValue());
						if (!obeiProduct.getPrice().equals(industrialProductFromDb.getPrice())) {
							// 绑定商品通知采购商:更新价格
							messagePoolProductService.pushBindProductPriceChange(obeiShopId, Collections.singletonList(obeiProduct.getSkuId()));
						} else {
							// 绑定商品通知采购商:更新商品信息
							messagePoolProductService.pushBindProductInfoChange(obeiShopId, Collections.singletonList(obeiProduct.getSkuId()));

							// 根据更新的商品状态区分是创建相似商品更新任务还是删除任务
							if (obeiProduct.getState().equals(OFF_SHELF.getMroStatus())) {
								similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(3).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, obeiShopId, obeiProduct.getId()))));
							}
							if (obeiProduct.getState().equals(ON_SHELF.getMroStatus())) {
								similarityProductTaskClient.createTask(new ProductSimilarityTaskDTO().setTaskType(2).setShopIdAndProdIdSet(Collections.singleton(StrUtil.join(StrUtil.UNDERLINE, obeiShopId, obeiProduct.getId()))));
							}
						}
						industrialProductDao.updateProdByShopId(obeiProduct, obeiShopId);
					}
					industrialProductProducerService.afterCommitUpdateIndustrialIndex(obeiShopId + StrUtil.COLON + obeiProduct.getSkuId());
					saveObeiproductHistory(commodityInfo.getType(), "商品更新", commodityCode);


					//商品更新完通知ERP
					notifyErp(commodityInfo, obeiProduct);


					industrialProductProducerService.afterCommitUpdateIndustrialIndex(obeiShopId + StrUtil.COLON + obeiProduct.getSkuId());
				} else {
					//调用失败，返回
					log.info("调用失败，返回");
					return Boolean.FALSE;
				}
			}
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * 计算价格变化率
	 */
	private BigDecimal calculatePriceChangeRate(BigDecimal beforePrice, BigDecimal afterPrice) {
		if (beforePrice == null || beforePrice.compareTo(BigDecimal.ZERO) == 0) {
			return BigDecimal.ZERO;
		}
		return afterPrice.subtract(beforePrice)
				.divide(beforePrice, 4, RoundingMode.HALF_UP);
	}

	private void notifyErp(ObeiCommodityInfoVo commodityInfo, IndustrialProduct obeiProduct) {
		//发送mq通知erp更新商品
		MaterialProductRel rel = this.materialProductRelDao.getByProperties(new EntityCriterion().eq("supplier", configService.getByShopId(thirdParameterConfig.getObeiSetting().getShopId()).getInnerCode()).eq("productCode", obeiProduct.getSkuId()).limit(1));
		if (ObjectUtil.isNotEmpty(rel)) {
			outerIndustrialProductProducerService.sendProductUpdateErpNotice( thirdParameterConfig.getObeiSetting().getShopId() + StrUtil.COLON + obeiProduct.getSkuId());
			industrialProductProducerService.sendThirdPartyLogMQ(ThirdPartyConstant.ERP, "发送mq通知erp更新商品接口", JSON.toJSONString(commodityInfo));
		}
	}

	private R<ObeiPriceVo> getObeiPrice(String commodityCode, Map<String, Object> queryParam) {
		String stringQueryParam = JSON.toJSONString(queryParam);
		log.info(stringQueryParam);
		//String body = openapiClient.sendPostRequest("/jk/api/commodity/query", stringQueryParam);
		String body = remoteObeiClient.getSupplier().get(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()).get().sendPostRequest(ObeiSendDataEnum.JAQ.getCode(), stringQueryParam);
		JSONObject jsonObject = JSON.parseObject(body);
		log.info("请求返回内容message{}", jsonObject.get("message"));
		ObeiPriceVo data = null;
		try {
			List<ObeiPriceVo> obeiPriceVos = JSON.parseArray((String) jsonObject.get("data"), ObeiPriceVo.class);
			data = obeiPriceVos.get(0);
		}catch (Exception e){
			e.printStackTrace();
		}

//		log.info("data数据{}" , data);
		if (ObjectUtil.isEmpty(data)) {
			log.info("data数据为空{}", data);
			return ResResultManager.setResultError("返回数据为空");
		}
		return ResResultManager.setResultSuccess(data);
	}

	private R<ObeiProductVo> queryRemoteObeiProductVo(Integer type, String commodityCode, OPENAPIClient openapiClient, Map<String, Object> queryParam) {
		String stringQueryParam = JSON.toJSONString(queryParam);
		log.info(stringQueryParam);
		//String body = openapiClient.sendPostRequest("/jk/api/commodity/query", stringQueryParam);
		String body = openapiClient.sendPostRequest(ObeiSendDataEnum.JACQ.getCode(), stringQueryParam);
//		log.info(body);
		JSONObject jsonObject = JSON.parseObject(body);
		log.info("请求返回内容message{}", jsonObject.get("message"));
		if(jsonObject.get("message").toString().contains("未查询到商品")){
			ObeiProductVo obeiProductVo = new ObeiProductVo();
			obeiProductVo.setProductCode(commodityCode)
				.setStatus(OFF_SHELF.getThirdStatus().toString());
			return R.ok(obeiProductVo);
		}

		if (BooleanUtil.isFalse((Boolean) jsonObject.get("success"))) {
			log.info("请求调用失败{} 调用接口success", jsonObject.get("success"));
			saveObeiproductHistory(type, "找不到:" + "请求调用失败{}success" + jsonObject.get("success"), commodityCode);
			return ResResultManager.setResultError(null);
		}
		ObeiProductVo data = jsonObject.getObject("data", ObeiProductVo.class);

//		log.info("data数据{}" , data);
		if (ObjectUtil.isEmpty(data)) {
			return ResResultManager.setResultError("返回数据为空");
		}
		return ResResultManager.setResultSuccess(data);
	}

	/**
	 * 欧贝商品转换
	 *
	 * @param vo
	 * @return
	 */
	private IndustrialProduct buildObeiProduct(ObeiProductVo vo) {
		IndustrialProduct obeiProduct = new IndustrialProduct();
		obeiProduct.setCategory(vo.getClassCode());
		obeiProduct.setSkuId(vo.getCommodityCode());
		obeiProduct.setName(vo.getCommodityName());
		obeiProduct.setState(Integer.parseInt(vo.getStatus()) == ON_SHELF.getThirdStatus()? ON_SHELF.getMroStatus(): OFF_SHELF.getMroStatus());
		obeiProduct.setModel(vo.getTypeGauge());
//		obeiProduct.setStock(vo.getStock());
		obeiProduct.setMiniOrderQty(vo.getMiniOrderQty());
		obeiProduct.setSaleUnit(Optional.ofNullable(vo.getSaleMeasureTypeUnitChName()).orElse("件"));
		obeiProduct.setPrice(vo.getPrice());
		obeiProduct.setUntaxedPrice(vo.getUntaxedPrice());
		obeiProduct.setTaxRate(new BigDecimal(vo.getTaxRate()));

		obeiProduct.setBrandName(vo.getBrandName());
		obeiProduct.setPreDeliveryDayNum(vo.getPreDeliveryDayNum());
		obeiProduct.setParam(JSON.toJSONString(vo.getCommoditySpecific()));
		obeiProduct.setWeight(Optional.ofNullable(vo.getUnitWeight()).orElse(BigDecimal.ZERO).toString());
		obeiProduct.setShopId(thirdParameterConfig.getObeiSetting().getShopId());
		obeiProduct.setCreateTime(new DateTime());
		obeiProduct.setUpdateTime(new DateTime());
		obeiProduct.setMroStatus(ON_SHELF.getMroStatus());
		obeiProduct.setUpdateFlag(UNUPDATE.getValue());
		obeiProduct.setProductType(10);
		obeiProduct.setProductArea(vo.getProduceArea());

		//ObeiCommoditySpecificationVo
		List<ObeiCommoditySpecificationVo> commoditySpecific = vo.getCommoditySpecific();
		if(CollUtil.isNotEmpty(commoditySpecific)){
			CommonParam commonParam = new CommonParam();
			commonParam.setParamGroup("商品属性");
			List<CommonParamValue> commonParamValues = new ArrayList<>();
			for (ObeiCommoditySpecificationVo obeiCommoditySpecificationVo : commoditySpecific) {
				CommonParamValue commonParamValue = new CommonParamValue();
				commonParamValue.setParamName(obeiCommoditySpecificationVo.getParamName());
				commonParamValue.setParamValue(obeiCommoditySpecificationVo.getParamValue());
				commonParamValues.add(commonParamValue);
			}
			commonParam.setValue(commonParamValues);
			obeiProduct.setParam(JSON.toJSONString(Collections.singleton(commonParam)));
		}

		if(StrUtil.isNotBlank(vo.getPhotoAddress())){
			obeiProduct.setImagePath(vo.getPhotoAddress());
		}else if(ObjectUtil.isNotEmpty(vo.getPhotoAddressList())) {
			//把主图挑出来，一个商品只能有一个主图,主图放最前面
			String mainPhoto = "";
			String photoListStr = "";
			for(ObeiPhotoAddressVo obeiPhotoAddressVo:vo.getPhotoAddressList()) {
				if(obeiPhotoAddressVo.getIfMain() == 0) {
					if(StringUtil.isBlank(photoListStr)) {
						photoListStr = obeiPhotoAddressVo.getPhotoAddress();
					}else {
						photoListStr = photoListStr + StrUtil.COMMA + obeiPhotoAddressVo.getPhotoAddress();
					}
				}else {
					mainPhoto = obeiPhotoAddressVo.getPhotoAddress();
				}
			}
			if(StringUtil.isNotBlank(mainPhoto)) {
				photoListStr = mainPhoto  + StrUtil.COMMA + photoListStr;
			}
			obeiProduct.setImagePath(photoListStr);
		}else{

			Pattern pattern = Pattern.compile("src=\"(https://[^\"]+)\"");
			Matcher matcher = pattern.matcher(vo.getCommodityDesc());

			StringBuilder stringBuilder = new StringBuilder();
			while (matcher.find()) {
				String url = matcher.group(1);
				stringBuilder.append(url + StrUtil.COMMA);
			}
			stringBuilder.setLength(stringBuilder.length() - 1);

			obeiProduct.setImagePath(stringBuilder.toString());
		}


		//21845
		obeiProduct.setIntroduction(vo.getCommodityDesc().length() > 160000 ? vo.getCommodityDesc().substring(160000) + "..." : vo.getCommodityDesc());
		return obeiProduct;
	}

	/**
	 * 保存进欧贝商品历史
	 */
	private void saveObeiproductHistory(Integer status, String reason, String obeiProductCode) {
		ObeiProductHisitory obeiProductHisitory = new ObeiProductHisitory();
		obeiProductHisitory.setCreateTime(DateUtil.date());
		obeiProductHisitory.setStatus(status);
		obeiProductHisitory.setObeiProductCode(obeiProductCode);
		obeiProductHisitory.setMroStatus(ObeiProductMsgEnum.PRODUCT_CHANGE_MSG.value().equals(status.toString()) ? ObeiProductHisitoryStatusEnum.UPDATE.value() : ObeiProductHisitoryStatusEnum.NOT_UPDATE.value());
		obeiProductHisitory.setErpStatus(ObeiProductHisitoryStatusEnum.NOT_UPDATE.value());
		obeiProductHisitory.setReason(reason);
		obeiProductHisitoryDao.save(obeiProductHisitory);
	}

	/**
	 * @param commodityInfo 任务数据
	 *                      {@link cn.legendshop.product.mq.ThirdIndustrialProductListener#updateObeiProduct}
	 * @return
	 */
	@Override
	public Boolean queryProductCallbackMq(String commodityInfo){
		if (StrUtil.isEmpty(commodityInfo)) {
			return Boolean.FALSE;
		}
		Long taskId = buildObeiCommodityTask(commodityInfo, ObeiProductProcessStatuEnum.NOT_PROCESS.value());
		//发送MQ处理，
		industrialProductProducerService.obeiDealWithProduct(taskId);
		return Boolean.TRUE;
	}

	@Override
	public PageSupport<String> queryByUpdateFlag(Integer value, Page page) {
		return obeiProductDao.pageQueryByUpdateFlag(value, page);
	}

	@Override
	public CommonProdInfoDTO getPresentProductInfo(String productCode) {
		Map<String, Object> queryParam = Maps.newHashMap();
		queryParam.put("type", "1");
		//组织代码
		queryParam.put("companyCode", thirdParameterConfig.getObeiSetting().getCompanyCode());
		queryParam.put("commodityCode", productCode);
		queryParam.put("loginCode", thirdParameterConfig.getObeiSetting().getCompanyCode());
		// todo 账套id
		R<ObeiProductVo> data = queryRemoteObeiProductVo(1, productCode, remoteObeiClient.getSupplier().get(1L).get(), queryParam);
		R<ObeiPriceVo> obeiPrice = getObeiPrice(productCode, queryParam);
		CommonProdInfoDTO commonProdInfoDTO = new CommonProdInfoDTO();
		if (data.isSuccess() && data.hasBody()) {
			Date date = new Date();
			ObeiProductVo result = data.getResult();
			if(result.getPreDeliveryDayNum() != null) {
				date = DateUtil.offsetDay(new Date(), result.getPreDeliveryDayNum());
			}else {
				//默认7天
				date = DateUtil.offsetDay(new Date(),7);
			}
			commonProdInfoDTO.setProductCode(productCode)
				.setPrice(result.getPrice())
				.setThirdSalePrice(result.getPrice())
				.setTaxRate(Optional.ofNullable(result.getTaxRate()).map(BigDecimal::new).orElse(new BigDecimal("13")))
				.setUntaxedPrice(result.getUntaxedPrice())
				.setStatus("10".equals(result.getStatus())? ON_SHELF.getThirdStatus() : OFF_SHELF.getThirdStatus())
				.setPreDeliveryDay(date);
			if (obeiPrice.isSuccess()){
				commonProdInfoDTO.setThirdSalePrice(obeiPrice.getResult().getFacePrice());
			}
			System.out.println("欧贝查询结果1" + JSONObject.toJSON(data));
			System.out.println("欧贝查询结果2" + JSONObject.toJSON(obeiPrice));
			return  commonProdInfoDTO;
		}else {
			return commonProdInfoDTO.setProductCode(productCode);
		}
	}

	@Override
	public CommonProdInfoDTO updateProductByProdId(IndustrialProduct product) {

		log.info("更新一日未更新商品数据，prodid: {}", product.getId());

		//获取更新前的价格
		BigDecimal beforePrice = product.getPrice();
		IndustrialOuterShopConfig config = configService.getByShopId(product.getShopId());

		product.setUpdateTime(new Date());
		CommonProdInfoDTO commonProdInfoDTO = new CommonProdInfoDTO();

		CommonProdInfoDTO presentProductInfo = this.getPresentProductInfo(product.getSkuId());
		if (ObjectUtil.isEmpty(presentProductInfo) || Objects.nonNull(presentProductInfo.getPrice())) {

			if (!ObjectUtil.isEmpty(presentProductInfo)) {
				product.setState(presentProductInfo.getStatus() == 10 ? ON_SHELF.getMroStatus() : OFF_SHELF.getMroStatus());
				commonProdInfoDTO.setStatus(ON_SHELF.getThirdStatus().equals(presentProductInfo.getStatus()) ?ThridProductStatusEnum.PROD_ONLINE.getValue():ThridProductStatusEnum.PROD_OFFLINE.getValue());

				//更新价格
				product.setPrice(presentProductInfo.getPrice());
				product.setTaxRate(Optional.ofNullable(presentProductInfo.getTaxRate()).orElse(BigDecimal.valueOf(13)));
				product.setUntaxedPrice(presentProductInfo.getUntaxedPrice());
				product.setMarketPrice(presentProductInfo.getThirdSalePrice());

				//返回体打包价格
				commonProdInfoDTO.setPrice(presentProductInfo.getPrice());
				commonProdInfoDTO.setTaxRate(Optional.ofNullable(presentProductInfo.getTaxRate()).orElse(BigDecimal.valueOf(13)));
				commonProdInfoDTO.setUntaxedPrice(presentProductInfo.getUntaxedPrice());
				commonProdInfoDTO.setThirdSalePrice(presentProductInfo.getThirdSalePrice());
			} else {
				commonProdInfoDTO.setStatus(JdProductStateEnum.OFFLINE.value());
				product.setState(OFF_SHELF.getMroStatus());
			}
		}

		industrialProductDao.updateProdByShopId(product, product.getShopId());

		log.debug("-----------修改完成后更新索引-------");
		industrialProductIndexClient.updateIndex(product.getShopId() + StrUtil.COLON + product.getSkuId(), SecurityConstants.FROM_IN);

		// 用于存储价格变更记录
		List<ProdChangeLogDTO> priceChangeRecords = new ArrayList<>();

		// 计算变更后的价格和价格变化比率
		BigDecimal afterPrice = product.getPrice();
		BigDecimal priceChangeRate = calculatePriceChangeRate(beforePrice, afterPrice); // 调用价格变化率计算方法

		if(priceChangeRate.compareTo(BigDecimal.ZERO) != 0) {
			// 创建价格变更记录
			ProdChangeLogDTO record = new ProdChangeLogDTO();
			record.setShopId(product.getShopId());
			record.setShopName(config.getShopName());
			record.setSkuId(product.getSkuId());  // 商品sku
			record.setBeforePrice(beforePrice);  //变更前价格
			record.setAfterPrice(afterPrice);  //变更后价格
			record.setPriceChangeRate(priceChangeRate);  // 价格变化比率
			record.setRequestDate(new Date());  // 当前请求日期
			priceChangeRecords.add(record); // 将记录添加到列表
		}

		// 发送MQ消息以进行异步处理
		if(priceChangeRecords != null && !priceChangeRecords.isEmpty()) {
			priceChangeRecords.forEach(record -> {
				prodChangeProducerService.sendPriceChangeMessage(record);// 发送每个价格变更记录到MQ

				// 判断该商品是否需要提醒
				List<ProdPriceContacts> priceLogList = prodPriceContactsService.getBySkuAndShopId(record.getShopId(), record.getSkuId());
				log.info("需要进行价格变化公众号提醒的商品:{}",JSON.toJSONString(priceLogList));
				if(AppUtils.isNotBlank(priceLogList)) {
					for (ProdPriceContacts priceLog : priceLogList) {
						ProdPriceContactsVo vo = new ProdPriceContactsVo();
						vo.setShopId(priceLog.getShopId());
						vo.setSkuId(priceLog.getSkuId());
						vo.setShopName(record.getShopName());
						vo.setMaterialCode(priceLog.getMaterialCode());
						vo.setBeforePrice(record.getBeforePrice());
						vo.setAfterPrice(record.getAfterPrice());
						vo.setBidPrice(priceLog.getBidPrice());
						vo.setContacts(priceLog.getContacts());
						log.info("指定商品提醒的联系人:{}",priceLog.getContacts());
						vo.setPriceChangeRate(record.getPriceChangeRate());
						prodChangeProducerService.sendProdPriceWXMessage(vo);// 发送价格变更微信公众号提醒MQ
					}
				}
			});
		}

		return commonProdInfoDTO;
	}

	@Override
	public PageSupport<ThirdIndustrialProductDTO> getThridIndustrialProds(ThridIndustrialProductSimpleQuery query) {
		return industrialProductDao.getThridIndustrialProdsByShopId(query, thirdParameterConfig.getObeiSetting().getShopId());
	}

	private Long buildObeiCommodityTask(String commodityCode, Integer status) {
		ObeiCommodityTask commodityTask = new ObeiCommodityTask();
		commodityTask.setCommodityTaskContent(commodityCode);
		commodityTask.setStatus(status);
		commodityTask.setProcessCout(0L);
		commodityTask.setCreateTime(new DateTime());
		return obeiCommodityTaskDao.save(commodityTask);
	}
}
