package cn.legendshop.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.IndustrialTypeEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.dao.OpenPlatformBuyerDao;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.model.OpenPlatformBuyer;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.messagePool.api.client.MessagePoolClient;
import cn.legendshop.messagePool.api.dto.MessagePoolMessageDTO;
import cn.legendshop.messagePool.api.enums.MessageTypeEnum;
import cn.legendshop.order.enums.AlarmStatusEnum;
import cn.legendshop.product.api.dto.product.OpenBindThirdPlatformConfirmParam;
import cn.legendshop.product.api.dto.product.OpenBindThirdPlatformProductParam;
import cn.legendshop.product.api.dto.product.OpenMaterialDTO;
import cn.legendshop.product.api.entity.*;
import cn.legendshop.product.api.enums.IndustrialProdTypeEnum;
import cn.legendshop.product.api.enums.MroIndustrialProdStatusEnum;
import cn.legendshop.product.api.query.open.OpenBindProdPageQuery;
import cn.legendshop.product.api.query.open.OpenBindProdQuery;
import cn.legendshop.product.api.query.open.OpenProdQuery;
import cn.legendshop.product.api.query.open.OpenRecommendProdPageQuery;
import cn.legendshop.product.api.vo.open.OpenBaseIndustrialProdVo;
import cn.legendshop.product.api.vo.open.OpenBindProdVo;
import cn.legendshop.product.api.vo.open.OpenIndustrialProdVo;
import cn.legendshop.product.api.vo.open.OpenRecommendProdVo;
import cn.legendshop.product.dao.IndustrialProductDao;
import cn.legendshop.product.dao.OpenMaterialProductRecommendDao;
import cn.legendshop.product.dao.OpenMaterialProductRelDao;
import cn.legendshop.product.dao.OpenMaterialProductRelDelDao;
import cn.legendshop.product.dao.impl.OpenMaterialDaoImpl;
import cn.legendshop.product.service.IndustrialProductService;
import cn.legendshop.product.service.OpenMaterialService;
import cn.legendshop.product.service.OpenProductService;
import cn.legendshop.product.service.convert.IndustrialProductConverter;
import cn.legendshop.product.service.convert.ObeiProductEntityConverter;
import cn.legendshop.search.api.client.SimilarityProductClient;
import cn.legendshop.search.api.dto.IndustrialProductForErpDocumentDTO;
import cn.legendshop.search.api.entity.ProductSimilarity;
import cn.legendshop.user.api.client.v2.AccountBookCompanyClient;
import cn.legendshop.user.api.client.v2.AccountBookDetailClient;
import cn.legendshop.user.api.model.AccountBookCompany;
import cn.legendshop.user.api.vo.AccountBookDetailVO;
import com.alibaba.fastjson.JSONObject;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.dao.support.lambda.LambdaCriteriaQuery;
import com.legendshop.dao.support.lambda.LambdaEntityCriterion;
import com.legendshop.util.AppUtils;
import com.legendshop.util.BeanUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 开放平台商品服务
 * <AUTHOR>
 * @data 2023/11/8
 **/

@Service
@Slf4j
@AllArgsConstructor
public class OpenProdServiceImpl implements OpenProductService {

    private final IndustrialOuterShopConfigService industrialOuterShopConfigService;

    private final IndustrialProductService industrialProductService;
    private final IndustrialProductDao industrialProductDao;

    private final OpenMaterialProductRelDao openMaterialProductRelDao;

    private final OpenMaterialProductRecommendDao openMaterialProductRecommendDao;

    private final OpenMaterialProductRelDelDao openMaterialProductRelDelDaoDao;

    private final IndustrialProductConverter industrialProductConverter;

    private final MessagePoolClient messagePoolClient;

    private final AccountBookDetailClient bookDetailClient;

    private final ObeiProductEntityConverter obeiProductEntityConverter;

    private final OpenPlatformBuyerDao openPlatformBuyerDao;

    private final OpenMaterialDaoImpl openMaterialDao;

    private final SimilarityProductClient similarityProductClient;

    private final OpenMaterialService openMaterialService;

    private final AccountBookCompanyClient accountBookCompanyClient;

    @Override
    public R<OpenIndustrialProdVo> queryIndustrialProd(OpenProdQuery openProdQuery) {
        IndustrialOuterShopConfig industrialOuterShopConfig = industrialOuterShopConfigService
                .getBySupplierCode(openProdQuery.getVendorCode());
        if (AppUtils.isBlank(industrialOuterShopConfig)) {
            return R.fail("厂商信息不存在!");
        }
        // 第三方平台
        IndustrialProduct industrialProduct = industrialProductDao.getByShopIdAndSkuId(openProdQuery.getSkuId(),
                industrialOuterShopConfig.getShopId());
        if (AppUtils.isBlank(industrialProduct)) {
            return R.fail("商品信息不存在！");
        }
        OpenIndustrialProdVo openIndustrialProdVo = industrialProductConverter.to20(industrialProduct);
        openIndustrialProdVo.setSupplier(industrialOuterShopConfig.getInnerCode());
        return R.ok(openIndustrialProdVo);
    }

    @Override
    public R<List<OpenBaseIndustrialProdVo>> queryBaseIndustrialProd(OpenProdQuery openProdQuery) {
        IndustrialOuterShopConfig industrialOuterShopConfig = industrialOuterShopConfigService
                .getBySupplierCode(openProdQuery.getVendorCode());
        if (AppUtils.isBlank(industrialOuterShopConfig)) {
            return R.fail("厂商信息不存在!");
        }
        String[] skuIds = openProdQuery.getSkuId().split(",");
        if (skuIds.length > 100) {
            return R.fail("查询商品超过100个");
        }
        List<String> skuIdsList = Arrays.stream(skuIds).collect(Collectors.toList());
        // 第三方平台
        List<IndustrialProduct> industrialProduct = industrialProductDao.queryByShopIdAndSkuId(skuIdsList,
                industrialOuterShopConfig.getShopId());
        if (AppUtils.isBlank(industrialProduct)) {
            return R.fail("商品信息不存在！");
        }
        List<OpenBaseIndustrialProdVo> openBaseIndustrialProdVo = industrialProductConverter
                .toList21(industrialProduct);
        return R.ok(openBaseIndustrialProdVo);
    }

    @Override
    public R<OpenBindProdVo> queryBindInfo(OpenBindProdQuery openBindProdQuery) {
        OpenMaterialProductRel openMaterialProductRel = openMaterialProductRelDao
                .getById(openBindProdQuery.getBindId());
        if (AppUtils.isBlank(openMaterialProductRel)) {
            return R.fail("绑定信息不存在!");
        }
        if (!openMaterialProductRel.getApiKey().equals(openBindProdQuery.getApiKey())) {
            return R.fail("绑定用户消息异常!");
        }
        OpenBindProdVo openBindProdVo = new OpenBindProdVo();
        openBindProdVo.setMaterialCode(openMaterialProductRel.getMaterialCode());
        openBindProdVo.setVendorCode(openMaterialProductRel.getVendorCode());
        openBindProdVo.setSkuId(openMaterialProductRel.getSkuId());
        openBindProdVo.setState(openMaterialProductRel.getState());
        openBindProdVo.setCompanyId(openMaterialProductRel.getCompanyId());
        openBindProdVo.setPlanItemNo(openMaterialProductRel.getPlanItemNo());
        return R.ok(openBindProdVo);
    }

    @Override
    public R<PageSupport<OpenBindProdVo>> queryAllBindProd(OpenBindProdPageQuery bindProdPageQuery) {
        // 获取账套对应的apiKey
        if (AppUtils.isNotBlank(bindProdPageQuery.getAccountBookId())) {
            OpenPlatformBuyer buyer = openPlatformBuyerDao.getByAccountBookId(bindProdPageQuery.getAccountBookId());
            bindProdPageQuery.setApiKey(Optional.ofNullable(buyer).map(OpenPlatformBuyer::getApiKey)
                    .orElseThrow(() -> new BusinessException("根据账套id未找到buyer信息!请联系管理员")));
        }

        if (AppUtils.isBlank(bindProdPageQuery.getSupplier())) {
            return R.fail("请选择第三方工业品再查询！");
        }

        IndustrialOuterShopConfig config = industrialOuterShopConfigService
                .getByInnerCode(bindProdPageQuery.getSupplier());
        bindProdPageQuery.setVendorCode(Optional.ofNullable(config).map(IndustrialOuterShopConfig::getSupplierCode)
                .orElseThrow(() -> new BusinessException("未查询到第三方工业品outerConfig配置信息！")));
        PageSupport<OpenBindProdVo> openBindProdVoPageSupport = openMaterialProductRelDao
                .queryAllBindProd(bindProdPageQuery);
        for (OpenBindProdVo openBindProdVo : openBindProdVoPageSupport.getResultList()) {
            openBindProdVo.setPic(openBindProdVo.getPic().split(StrUtil.COMMA)[0]);
        }
        return R.ok(openBindProdVoPageSupport);
    }

    @Override
    public R<PageSupport<OpenRecommendProdVo>> queryAllRecommendProd(
            OpenRecommendProdPageQuery recommendProdPageQuery) {
        // 获取账套对应的apiKey
        if (AppUtils.isNotBlank(recommendProdPageQuery.getAccountBookId())) {
            OpenPlatformBuyer buyer = openPlatformBuyerDao
                    .getByAccountBookId(recommendProdPageQuery.getAccountBookId());
            recommendProdPageQuery.setApiKey(Optional.ofNullable(buyer).map(OpenPlatformBuyer::getApiKey)
                    .orElseThrow(() -> new BusinessException("根据账套id未找到buyer信息!请联系管理员")));
        }
        if (AppUtils.isNotBlank(recommendProdPageQuery.getSupplier())) {
            IndustrialOuterShopConfig config = industrialOuterShopConfigService
                    .getByInnerCode(recommendProdPageQuery.getSupplier());
            recommendProdPageQuery
                    .setVendorCode(Optional.ofNullable(config).map(IndustrialOuterShopConfig::getSupplierCode)
                            .orElseThrow(() -> new BusinessException("未查询到第三方工业品outerConfig配置信息！")));
        }
        // 厂商鑫采商城编码为空的话查全部
        PageSupport<OpenRecommendProdVo> openRecommendProdVoPageSupport = openMaterialProductRecommendDao
                .queryAllRecommendProd(recommendProdPageQuery);
        for (OpenRecommendProdVo openRecommendProdVo : openRecommendProdVoPageSupport.getResultList()) {
            IndustrialProduct product = industrialProductDao
                    .getBySupplierCodeAndSkuId(openRecommendProdVo.getVendorCode(), openRecommendProdVo.getSkuId());
            if (AppUtils.isNotBlank(product)) {
                openRecommendProdVo.setPrice(product.getPrice());
                openRecommendProdVo.setDisplayName(product.getName());
                openRecommendProdVo.setPic(product.getImagePath());
            }
            openRecommendProdVo.setPic(Optional.ofNullable(openRecommendProdVo.getPic())
                    .map(pic -> pic.split(StrUtil.COMMA)[0]).orElse(StrUtil.EMPTY));
        }
        return R.ok(openRecommendProdVoPageSupport);
    }

    @Override
    public R deleteRecommendProd(Long id) {
        return openMaterialProductRecommendDao.deleteById(id) > 0 ? R.ok() : R.fail("删除失败！");
    }

    @Override
    public List<IndustrialProductForErpDocumentDTO> getBoundThirdPlatformProduct(String companyId, String materialCode,
            Long accountBookId) {
        // 过滤掉不支持的店铺，并提取供应商代码列表
        List<IndustrialOuterShopConfig> outerShopConfigList = industrialOuterShopConfigService
                .queryAll(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());

        // 查找未授权的企业
        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId)
                .orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        // 设置物料编码可采购的电商平台
        List<String> purchaseShopIds = openMaterialService.getShopIds(materialCode);

        // 过滤未授权企业，提取支持的店铺供应商编码
        List<String> vendorCodeList = listR.getSuccess() ? outerShopConfigList.stream()
                .filter(shopConfig -> listR.getResult().stream()
                        .noneMatch(detail -> detail.getShopId().equals(shopConfig.getShopId())))
                .map(IndustrialOuterShopConfig::getSupplierCode)
                .collect(Collectors.toList()) : Collections.emptyList();

        // 查询所有供应商编码已绑定相关物料的商品
        List<OpenMaterialProductRel> boundList = openMaterialProductRelDao.queryBindData(materialCode, vendorCodeList);

        // 批量查询公司名称
        R<List<AccountBookCompany>> accountList = accountBookCompanyClient.queryByBookId(accountBookId);
        Map<String, String> companyNameMap = accountList.getSuccess() ?
                accountList.getResult().stream()
                        .collect(Collectors.toMap(
                                AccountBookCompany::getCompanyId,
                                AccountBookCompany::getCompany,
                                (existing, replacement) -> existing)) :
                Collections.emptyMap();

        // 创建sku到公司名称列表的映射
        Map<String, List<String>> skuToCompanyNamesMap = boundList.stream()
                .collect(Collectors.groupingBy(
                        OpenMaterialProductRel::getSkuId,
                        Collectors.mapping(
                                rel -> companyNameMap.getOrDefault(rel.getCompanyId(), "未知公司"),
                                Collectors.toList()
                        )
                ));

        // 根据供应商分类商品，收集sku集合，根据sku集合和店铺id查找到商品表所有商品，过滤掉下架商品，最后整合到resultDTO中
        Map<String, List<OpenMaterialProductRel>> boundListMap = boundList.stream()
                .collect(Collectors.groupingBy(OpenMaterialProductRel::getVendorCode));
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>();
        boundListMap.forEach((k, v) -> {
            List<String> skuIds = v.stream().map(OpenMaterialProductRel::getSkuId).collect(Collectors.toList());
            IndustrialOuterShopConfig config = industrialOuterShopConfigService.getBySupplierCode(k);
            List<IndustrialProduct> industrialProducts = industrialProductDao.queryByShopIdAndSkuId(skuIds,
                    config.getShopId());
            industrialProducts = industrialProducts.stream()
                    .filter(product -> MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState()))
                    .collect(Collectors.toList());
            resultDTO.addAll(obeiProductEntityConverter.toOuterProductDocumentDTOList(industrialProducts));
        });

        // 根据companyID和物料号获取到物料商品关联表中当前角色（比如编号GA的公司）对应的sku，组成一个set。
//        Set<String> finalSkuIds = Optional
//                .ofNullable(openMaterialProductRelDao.queryByCompanyAndMaterialCode(companyId, materialCode))
//                .map(list -> list.stream()
//                        .map(OpenMaterialProductRel::getSkuId)
//                        .collect(Collectors.toSet()))
//                .orElse(Collections.emptySet());

        // 从已查询的boundList中提取当前公司的SKU集合
        Set<String> finalSkuIds = boundList.stream()
                .filter(rel -> companyId.equals(rel.getCompanyId()))
                .map(OpenMaterialProductRel::getSkuId)
                .collect(Collectors.toSet());

        resultDTO.forEach(documentDTO -> {
            // 取第一张图片,如果为空则不处理
            documentDTO.setImage(Optional.ofNullable(documentDTO.getImage())
                    .map(images -> images.split(StrUtil.COMMA)[0]).orElse(StrUtil.EMPTY));
            // 设置关联的公司名称列表
            documentDTO.setBoundCompany(skuToCompanyNamesMap.getOrDefault(documentDTO.getProductCode(), Collections.emptyList()));
            // 是当前用户绑定的话，则设置为已绑定（bound为true）
            if (finalSkuIds.contains(documentDTO.getProductCode())) {
                documentDTO.setBound(true);
                documentDTO.setBoundMaterialCode(materialCode);
            }
        });

        return resultDTO.stream()
                .filter(dto -> purchaseShopIds.contains(dto.getShopId().toString()))
                .sorted(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice))
                .collect(Collectors.toList());
    }

    @Override
    public List<IndustrialProductForErpDocumentDTO> getRecommendThirdPlatformProduct(String companyId,
            String materialCode, Long accountBookId) {
        // 过滤掉不支持的店铺，并提取供应商代码列表
        List<IndustrialOuterShopConfig> outerShopConfigList = industrialOuterShopConfigService
                .queryAll(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());

        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId)
                .orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        // 设置物料编码可采购的电商平台
        List<String> shopIds = openMaterialService.getShopIds(materialCode);
        List<String> vendorCodeList = listR.getSuccess() ? outerShopConfigList.stream()
                .filter(shopConfig -> listR.getResult().stream()
                        .noneMatch(detail -> detail.getShopId().equals(shopConfig.getShopId())))
                .filter(shopConfig -> {
                    if (CollectionUtils.isEmpty(shopIds)) {
                        return true; // shopIds为空时保留所有
                    }
                    // 保留物料编码可采购的电商平台
                    return shopIds.contains(shopConfig.getShopId().toString());
                })
                .map(IndustrialOuterShopConfig::getSupplierCode)
                .collect(Collectors.toList()) : Collections.emptyList();

        List<OpenMaterialProductRecommend> recommendList = openMaterialProductRecommendDao.queryRecommend(materialCode,
                vendorCodeList);

        // 根据供应商分类，分别查询
        Map<String, List<OpenMaterialProductRecommend>> recommendInfo = recommendList.stream()
                .collect(Collectors.groupingBy(OpenMaterialProductRecommend::getVendorCode));
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>();
        recommendInfo.forEach((k, v) -> {
            List<String> skuIds = v.stream().map(OpenMaterialProductRecommend::getSkuId).collect(Collectors.toList());
            IndustrialOuterShopConfig config = industrialOuterShopConfigService.getBySupplierCode(k);
            List<IndustrialProduct> industrialProducts = industrialProductDao.queryByShopIdAndSkuId(skuIds,
                    config.getShopId());
            industrialProducts = industrialProducts.stream()
                    .filter(product -> MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState()))
                    .collect(Collectors.toList());
            resultDTO.addAll(obeiProductEntityConverter.toOuterProductDocumentDTOList(industrialProducts));
        });

        // 获取到物料关联的sku。组成一个set.
        Set<String> finalSkuIds = Optional
                .ofNullable(openMaterialProductRelDao.queryByCompanyAndMaterialCode(companyId, materialCode))
                .map(list -> list.stream()
                        .map(OpenMaterialProductRel::getSkuId)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());

        resultDTO.forEach(documentDTO -> {
            // 取第一张图片,如果为空则不处理
            documentDTO.setImage(Optional.ofNullable(documentDTO.getImage())
                    .map(images -> images.split(StrUtil.COMMA)[0]).orElse(StrUtil.EMPTY));
            if (finalSkuIds.contains(documentDTO.getProductCode())) {
                documentDTO.setBound(true);
                documentDTO.setBoundMaterialCode(materialCode);
            }
        });

        resultDTO.sort(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice));
        return resultDTO;
    }

    @Override
    public List<IndustrialProductForErpDocumentDTO> getSameThirdPlatformProduct(String companyId, String materialCode,
            Long accountBookId) {
        // 过滤掉不支持的店铺，并提取供应商代码列表
        List<IndustrialOuterShopConfig> outerShopConfigList = industrialOuterShopConfigService
                .queryAll(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());

        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId)
                .orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        List<String> vendorCodeList = listR.getSuccess() ? outerShopConfigList.stream()
                .filter(shopConfig -> listR.getResult().stream()
                        .noneMatch(detail -> detail.getShopId().equals(shopConfig.getShopId())))
                .map(IndustrialOuterShopConfig::getSupplierCode)
                .collect(Collectors.toList()) : Collections.emptyList();

        List<OpenMaterialProductRel> boundList = openMaterialProductRelDao.queryBindData(materialCode, vendorCodeList);
        // 根据供应商分类，分别查询
        Map<String, List<OpenMaterialProductRel>> boundListMap = boundList.stream()
                .collect(Collectors.groupingBy(OpenMaterialProductRel::getVendorCode));
        List<IndustrialProduct> boundProducts = new ArrayList<>();

        boundListMap.forEach((k, v) -> {
            List<String> skuIds = v.stream().map(OpenMaterialProductRel::getSkuId).collect(Collectors.toList());
            IndustrialOuterShopConfig config = industrialOuterShopConfigService.getBySupplierCode(k);
            List<IndustrialProduct> industrialProducts = industrialProductDao.queryByShopIdAndSkuId(skuIds,
                    config.getShopId());
            industrialProducts = industrialProducts.stream()
                    .filter(product -> MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState()))
                    .collect(Collectors.toList());
            boundProducts.addAll(industrialProducts);
        });

        // 根据已绑定的商品，查询相同商品
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>();
        boundProducts.forEach(product -> resultDTO
                .addAll(listSameProduct(product.getShopId(), product.getId(), accountBookId).getResult()));

        // 设置物料编码可采购的电商平台
        List<String> purchaseShopIds = openMaterialService.getShopIds(materialCode);

        // 去重且不要在已经存在的boundProducts中,并按价格排序
        return resultDTO.stream()
                .filter(product -> boundProducts.stream()
                        .noneMatch(boundProduct -> (boundProduct.getId().equals(product.getProdId()))))
                .filter(product -> purchaseShopIds.contains(product.getShopId().toString()))
                .sorted(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice))
                .collect(Collectors.toList());
    }

    @Override
    public List<IndustrialProductForErpDocumentDTO> getSimilarThirdPlatformProduct(String companyId,
            String materialCode, Long accountBookId) {
        // 过滤掉不支持的店铺，并提取供应商代码列表
        List<IndustrialOuterShopConfig> outerShopConfigList = industrialOuterShopConfigService
                .queryAll(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());

        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId)
                .orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        List<String> vendorCodeList = listR.getSuccess() ? outerShopConfigList.stream()
                .filter(shopConfig -> listR.getResult().stream()
                        .noneMatch(detail -> detail.getShopId().equals(shopConfig.getShopId())))
                .map(IndustrialOuterShopConfig::getSupplierCode)
                .collect(Collectors.toList()) : Collections.emptyList();

        List<OpenMaterialProductRel> boundList = openMaterialProductRelDao.queryBindData(materialCode, vendorCodeList);
        // 根据供应商分类，分别查询
        Map<String, List<OpenMaterialProductRel>> boundListMap = boundList.stream()
                .collect(Collectors.groupingBy(OpenMaterialProductRel::getVendorCode));
        List<IndustrialProduct> boundProducts = new ArrayList<>();

        boundListMap.forEach((k, v) -> {
            List<String> skuIds = v.stream().map(OpenMaterialProductRel::getSkuId).collect(Collectors.toList());
            IndustrialOuterShopConfig config = industrialOuterShopConfigService.getBySupplierCode(k);
            List<IndustrialProduct> industrialProducts = industrialProductDao.queryByShopIdAndSkuId(skuIds,
                    config.getShopId());
            industrialProducts = industrialProducts.stream()
                    .filter(product -> MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState()))
                    .collect(Collectors.toList());
            boundProducts.addAll(industrialProducts);
        });

        // 根据已绑定的商品查询相似商品
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>();
        boundProducts.forEach(product -> resultDTO
                .addAll(listSimilarProduct(product.getShopId(), product.getId(), accountBookId).getResult()));

        // 设置物料编码可采购的电商平台
        List<String> purchaseShopIds = openMaterialService.getShopIds(materialCode);

        // 去重且不要在已经存在的boundProducts中,并按价格排序
        return resultDTO.stream()
                .filter(product -> boundProducts.stream()
                        .noneMatch(boundProduct -> (boundProduct.getId().equals(product.getProdId()))))
                .filter(product -> purchaseShopIds.contains(product.getShopId().toString()))
                .sorted(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice))
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public R preBind(OpenBindThirdPlatformProductParam param) {
        log.info("进到预绑定接口");
        if (MapUtils.isNotEmpty(param.getBindData())) {
            log.info("进入绑定方法");
            List<IndustrialOuterShopConfig> industrialOuterShopConfigs = industrialOuterShopConfigService.queryAll();
            Map<String, List<String>> productIdsByPlatform = param.getBindData();
            for (String innerCode : productIdsByPlatform.keySet()) {
                // 插入/更新绑定记录
                List<String> thirdSkuIds = productIdsByPlatform.get(innerCode);
                thirdSkuIds.forEach((skuId) -> {
                    String vendorCode = industrialOuterShopConfigs.stream()
                            .filter((item1) -> innerCode.equals(item1.getInnerCode())).findFirst()
                            .map(IndustrialOuterShopConfig::getSupplierCode)
                            .orElseThrow(() -> new BusinessException("未找到对应的ShopConfig店铺配置,请联系管理员!"));

                    OpenMaterialProductRel relInfo = openMaterialProductRelDao.getRelInfo(param.getApiKey(),
                            param.getCompanyId(), param.getItemId(), skuId, vendorCode);

                    Date date = new Date();
                    if (ObjectUtil.isEmpty(relInfo)) {
                        OpenMaterialProductRel openMaterialProductRel = new OpenMaterialProductRel();
                        openMaterialProductRel.setApiKey(param.getApiKey());
                        if (param.getCompanyId().startsWith("G")) {
                            openMaterialProductRel.setQueryKey("G");
                        } else {
                            openMaterialProductRel.setQueryKey(param.getCompanyId());
                        }
                        openMaterialProductRel.setPlanItemNo(param.getPlanItemNo());
                        openMaterialProductRel.setCompanyId(param.getCompanyId());
                        openMaterialProductRel.setVendorCode(vendorCode);
                        openMaterialProductRel.setMaterialCode(param.getItemId());
                        openMaterialProductRel.setSkuId(skuId);
                        openMaterialProductRel.setCreateTime(date);
                        openMaterialProductRel.setUpdateTime(date);
                        openMaterialProductRel.setState(0);
                        openMaterialProductRel.setMessageFlag(false);
                        openMaterialProductRelDao.save(openMaterialProductRel);
                    } else {
                        relInfo.setPlanItemNo(param.getPlanItemNo());
                        relInfo.setUpdateTime(date);
                        relInfo.setMessageFlag(false);
                        openMaterialProductRelDao.update(relInfo);
                    }
                });
            }
            // 2. 通过消息池推送
            return R.ok(pushBindMessage());
        } else {
            return R.fail("绑定数据为空!");
        }

    }

    @Override
    public R confirmBind(OpenBindThirdPlatformConfirmParam param) {
        OpenMaterialProductRel relInfo = openMaterialProductRelDao.getRelInfo(param.getApiKey(), param.getCompanyId(),
                param.getMaterialCode(), param.getSkuId(), param.getVendorCode());
        if (AppUtils.isNotBlank(param.getConfirmStatus()) && param.getConfirmStatus()) {
            if (AppUtils.isNotBlank(relInfo)) {
                return R.ok("已绑定成功,无需重复绑定!");
            }
            if (AppUtils.isBlank(param.getCompanyId())) {
                return R.fail("绑定的分厂标识为空!");
            }
            IndustrialProduct industrialProduct = industrialProductDao.getBySupplierCodeAndSkuId(param.getVendorCode(),
                    param.getSkuId());
            if (AppUtils.isBlank(industrialProduct)) {
                return R.fail("绑定的商品信息不存在!");
            }

            relInfo = new OpenMaterialProductRel();
            relInfo.setCompanyId(param.getCompanyId());
            relInfo.setMaterialCode(param.getMaterialCode());
            relInfo.setVendorCode(param.getVendorCode());
            relInfo.setSkuId(param.getSkuId());
            relInfo.setApiKey(param.getApiKey());
            relInfo.setMessageFlag(true);
            relInfo.setState(1);
            if (param.getCompanyId().startsWith("G")) {
                relInfo.setQueryKey("G");
            } else {
                relInfo.setQueryKey(param.getCompanyId());
            }
            relInfo.setCreateTime(new Date());
            relInfo.setUpdateTime(new Date());
            openMaterialProductRelDao.save(relInfo);

        } else {
            if (AppUtils.isBlank(relInfo)) {
                return R.fail("绑定关系不存在！");
            }
            if (param.getBindingStatus()) {
                // 确认绑定
                relInfo.setState(1);
                relInfo.setUpdateTime(new Date());
                openMaterialProductRelDao.update(relInfo);
                // 绑定的料号后放入商品推荐列表，方便后续查询、比价
                OpenMaterialProductRecommend productRecommend = openMaterialProductRecommendDao
                        .getByUniqueKeys(relInfo.getMaterialCode(), relInfo.getSkuId(), relInfo.getVendorCode());
                if (AppUtils.isBlank(productRecommend)) {
                    productRecommend = new OpenMaterialProductRecommend();
                    productRecommend.setApiKey(relInfo.getApiKey());
                    productRecommend.setCompanyId(relInfo.getCompanyId());
                    productRecommend.setMaterialCode(relInfo.getMaterialCode());
                    productRecommend.setSkuId(relInfo.getSkuId());
                    productRecommend.setVendorCode(relInfo.getVendorCode());
                    productRecommend.setCreateTime(new Date());
                    openMaterialProductRecommendDao.save(productRecommend);
                }
            } else {
                openMaterialProductRelDao.delete(relInfo);
                OpenMaterialProductRelDel openMaterialProductRelDel = new OpenMaterialProductRelDel();
                BeanUtils.copyProperties(openMaterialProductRelDel, relInfo);
                // 保存解绑记录
                openMaterialProductRelDelDaoDao.save(openMaterialProductRelDel);
            }
        }

        return R.ok();
    }

    @Override
    public R pushBindMessage() {
        List<OpenMaterialProductRel> openMaterialProductRels = openMaterialProductRelDao.queryMessageRel();
        if (AppUtils.isNotBlank(openMaterialProductRels)) {
            for (OpenMaterialProductRel item : openMaterialProductRels) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("bindId", item.getId());
                jsonObject.put("state", item.getState());
                MessagePoolMessageDTO messagePoolMessageDTO = new MessagePoolMessageDTO();
                messagePoolMessageDTO.setAppkey(item.getApiKey());
                messagePoolMessageDTO.setContent(jsonObject.toJSONString());
                messagePoolMessageDTO.setTypeId(MessageTypeEnum.MATERIAL_PROD_BIND.getValue());
                R pushResult = messagePoolClient.pushMessage(messagePoolMessageDTO);
                if (pushResult.getSuccess()) {
                    openMaterialProductRelDao.updateMessageFlag(item.getId());
                }
            }
        }
        return R.ok();
    }

    @Override
    public R pushUnBindMessage() {
        return null;
    }

    @Override
    public List<OpenMaterialProductRecommend> downloadTemplate(String apikey) {
        List<OpenMaterialProductRecommend> recommendList = new ArrayList<>();
        OpenMaterialProductRecommend openMaterialProductRecommend = new OpenMaterialProductRecommend();
        openMaterialProductRecommend.setApiKey(apikey);
        openMaterialProductRecommend.setCompanyId("");
        List<IndustrialOuterShopConfig> shopConfigList = industrialOuterShopConfigService
                .queryAll(IndustrialProdTypeEnum.INDUSTRIAL_PROD.getType());
        // industrialOuterShopConfigs组装成：{"shopName":"vendorCode"}
        Map<String, String> shopConfigMap = shopConfigList.stream().collect(
                Collectors.toMap(IndustrialOuterShopConfig::getShopName, IndustrialOuterShopConfig::getSupplierCode));
        openMaterialProductRecommend.setVendorCode(JSONUtil.toJsonStr(shopConfigMap));
        recommendList.add(openMaterialProductRecommend);
        return recommendList;
    }

    @Override
    public void batchSaveRecommendProds(List<OpenMaterialProductRecommend> recommendList) {

        // 进来先去重 按照vendorCode_materialCode_skuId唯一键
        List<OpenMaterialProductRecommend> deduplicatedList = new ArrayList<>(
                recommendList.stream().collect(Collectors.toMap(
                        recommend -> recommend.getVendorCode() + "_" + recommend.getMaterialCode() + "_"
                                + recommend.getSkuId(),
                        recommend -> recommend,
                        (existing, replacement) -> existing)).values());
        // 批量删除旧数据
        // 将recommendList按照vendorCode_materialCode_skuId唯一键
        Set<String> uniqueKeySet = deduplicatedList.stream()
                .map(recommend -> recommend.getVendorCode() + "_" + recommend.getMaterialCode() + "_"
                        + recommend.getSkuId())
                .collect(Collectors.toSet());

        List<String> materialCodeList = deduplicatedList.stream().map(OpenMaterialProductRecommend::getMaterialCode)
                .collect(Collectors.toList());
        // 根据物料编码获取已存在的推荐数据
        List<OpenMaterialProductRecommend> existRecommendList = openMaterialProductRecommendDao
                .queryByProperties(new LambdaEntityCriterion<>(OpenMaterialProductRecommend.class)
                .in(OpenMaterialProductRecommend::getMaterialCode, materialCodeList));
        // 记录数据库中按照vendorCode_materialCode_skuId分组的数据，如果与唯一键重复，则批量删除
        List<OpenMaterialProductRecommend> oldList = new ArrayList<>();
        for (OpenMaterialProductRecommend existRecommend : existRecommendList) {
            String key = existRecommend.getVendorCode() + "_" + existRecommend.getMaterialCode() + "_"
                    + existRecommend.getSkuId();
            if (uniqueKeySet.contains(key)) {
                oldList.add(existRecommend);
            }
        }
        openMaterialProductRecommendDao.delete(oldList);
        Date date = new Date();
        List<OpenMaterialProductRecommend> updateList = deduplicatedList.stream()
                .peek(recommend -> recommend.setCreateTime(date)).collect(Collectors.toList());
        openMaterialProductRecommendDao.save(updateList);
    }

    @Override
    public void calculateRecommendPriceRatio(String param) {
        // 判断param是否是自然数
        if (ObjectUtil.isNotEmpty(param) && !param.matches("[0-9]+")) {
            throw new BusinessException("参数错误,param请填写自然数");
        }

        List<String> vendorCodeList = industrialOuterShopConfigService.queryAll()
                .stream()
                .filter(e -> e.getType().equals(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode()))
                .map(IndustrialOuterShopConfig::getSupplierCode)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());

        ExecutorService service = Executors.newCachedThreadPool();
        // 多线程实现
        for (String vendorCode : vendorCodeList) {
            service.execute(() -> {
                Integer pageSize = 500;
                int curPageNO = 1;
                List<OpenMaterialProductRecommend> resultList;
                do {
                    PageSupport<OpenMaterialProductRecommend> pageRecommendList = openMaterialProductRecommendDao
                            .queryPage(
                            new LambdaCriteriaQuery<>(OpenMaterialProductRecommend.class, pageSize, curPageNO)
                                    .eq(OpenMaterialProductRecommend::getVendorCode, vendorCode)
                                    .addDescOrder(OpenMaterialProductRecommend::getId));
                    resultList = pageRecommendList.getResultList();
                    if (ObjectUtil.isNotEmpty(resultList)) {
                        updateRecommendProds(resultList);
                    }
                    curPageNO++;
                    if (ObjectUtil.isNotEmpty(param) && curPageNO >= Integer.parseInt(param)) {
                        break;
                    }
                } while (ObjectUtil.isNotEmpty(resultList));
            });

        }
    }

    @Override
    public void uploadJdCategory(List<JDCategoryUpLoad> categoryList) {
        for (JDCategoryUpLoad jdCategoryUpLoad : categoryList) {
            System.out.println(jdCategoryUpLoad.toString());
        }
    }

    @Override
    public R<String> updateMaterial(OpenMaterialDTO openMaterialDTO) {
        List<OpenMaterial> openMaterials = openMaterialDTO.getOpenMaterials();
        for (OpenMaterial openMaterialNew : openMaterials) {
            String materialCode = openMaterialNew.getMaterialCode();
            if (AppUtils.isBlank(materialCode)) {
                return R.fail("物料代码不能为空");
            }
            OpenMaterial openMaterial = openMaterialDao.getByMaterialCode(materialCode);
            // 更新或者新增
            if ("1".equals(openMaterialDTO.getType())) {
                if (AppUtils.isNotBlank(openMaterial)) {
                    BeanUtil.copyProperties(openMaterialNew, openMaterial);
                    openMaterialDao.update(openMaterialNew);
                } else {
                    openMaterialNew.setApiKey(openMaterialDTO.getApiKey());
                    // 新增
                    openMaterialDao.save(openMaterialNew);
                }
            } else {
                // 删除
                if (openMaterial == null) {
                    return R.fail("删除失败,物料代码不存在");
                }
                openMaterialDao.delete(openMaterial);
            }
        }
        return R.ok();
    }

    @Override
    public R<List<IndustrialProductForErpDocumentDTO>> listSameProduct(Long shopId, Long prodId, Long accountBookId) {
        // 过滤掉不支持的店铺，并提取供应商代码列表
        R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId)
                .orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        // 过滤掉不支持的店铺，并提取ShopId列表
        List<Long> noAuthShopIdList = listR.getSuccess() ? listR.getResult().stream()
                .map(AccountBookDetailVO::getShopId)    
                .collect(Collectors.toList()) : Collections.emptyList();

        // 从同款组中获取数据
        R<List<ProductSimilarity>> sameProductListR = similarityProductClient.listSameProduct(shopId, prodId);
        if (!sameProductListR.getSuccess()) {
            return R.fail(sameProductListR.getMsg());
        }

        // 如果相同商品列表为空，则主动处理相似度查询、识别任务
        if (CollUtil.isEmpty(sameProductListR.getResult())) {
            similarityProductClient.handleSingleProductSimilarity(shopId, prodId);
            sameProductListR = similarityProductClient.listSameProduct(shopId, prodId);
            if (!sameProductListR.getSuccess()) {
                return R.fail(sameProductListR.getMsg());
            }
        }

        List<ProductSimilarity> sameProductList = sameProductListR.getResult();
        // 只保留支持的店铺商品
        List<ProductSimilarity> authProductList = sameProductList.stream()
                .filter(productSimilarity -> !noAuthShopIdList.contains(productSimilarity.getTargetShopId()))
                .collect(Collectors.toList());
        // Get shopIds and prodIds from authProductList
        List<Long> shopIds = authProductList.stream().map(ProductSimilarity::getTargetShopId)
                .collect(Collectors.toList());
        List<Long> ids = authProductList.stream().map(ProductSimilarity::getTargetProdId).collect(Collectors.toList());

        // Check if lists are empty
        if (CollUtil.isEmpty(shopIds) || CollUtil.isEmpty(ids)) {
            return R.ok(Collections.emptyList());
        }

        // Get industrial products and filter for online products
        List<IndustrialProduct> industrialProducts = industrialProductService.queryByShopIdAndId(shopIds, ids);
        if (CollUtil.isEmpty(industrialProducts)) {
            return R.ok(Collections.emptyList());
        }

        industrialProducts = industrialProducts.stream()
                .filter(product -> product != null
                        && MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState()))
                .collect(Collectors.toList());

        // Convert to DTOs safely
        List<IndustrialProductForErpDocumentDTO> convertedList = obeiProductEntityConverter
                .toOuterProductDocumentDTOList(industrialProducts);
        if (CollUtil.isEmpty(convertedList)) {
            return R.ok(Collections.emptyList());
        }
        convertedList.sort(Comparator.comparing(IndustrialProductForErpDocumentDTO::getSupplierSalePrice));
        return R.ok(convertedList);
    }

    @Override
    public R<List<IndustrialProductForErpDocumentDTO>> listSimilarProduct(Long shopId, Long prodId,
            Long accountBookId) {
                // 过滤掉不支持的店铺，并提取供应商代码列表
                R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(Optional.ofNullable(accountBookId)
                .orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));

        // 过滤掉不支持的店铺，并提取ShopId列表
        List<Long> noAuthShopIdList = listR.getSuccess() ? listR.getResult().stream()
                .map(AccountBookDetailVO::getShopId)    
                .collect(Collectors.toList()) : Collections.emptyList();

        R<List<ProductSimilarity>> similarProductR = similarityProductClient.listSimilarProduct(shopId, prodId);
        if (!similarProductR.getSuccess()) {
            return R.fail(similarProductR.getMsg());
        }
        // 如果相似度商品列表为空，则主动处理相似度查询、识别任务
        if (CollUtil.isEmpty(similarProductR.getResult())) {
            similarityProductClient.handleSingleProductSimilarity(shopId, prodId);
            similarProductR = similarityProductClient.listSimilarProduct(shopId, prodId);
            if (!similarProductR.getSuccess()) {
                return R.fail(similarProductR.getMsg());
            }
        }
        List<ProductSimilarity> similarProductList = similarProductR.getResult();
        // 只保留支持的店铺商品
        List<ProductSimilarity> authProductList = similarProductList.stream()
                .filter(productSimilarity -> !noAuthShopIdList.contains(productSimilarity.getTargetShopId()))
                .collect(Collectors.toList());
        List<Long> shopIds = authProductList.stream().map(ProductSimilarity::getTargetShopId)
                .collect(Collectors.toList());
        List<Long> ids = authProductList.stream().map(ProductSimilarity::getTargetProdId).collect(Collectors.toList());

        // Check if lists are empty
        if (CollUtil.isEmpty(shopIds) || CollUtil.isEmpty(ids)) {
            return R.ok(Collections.emptyList());
        }

        // Get industrial products and filter for online products
        List<IndustrialProduct> industrialProducts = industrialProductService.queryByShopIdAndId(shopIds, ids);
        if (CollUtil.isEmpty(industrialProducts)) {
            return R.ok(Collections.emptyList());
        }

        industrialProducts = industrialProducts.stream()
                .filter(product -> product != null
                        && MroIndustrialProdStatusEnum.ONLINE.getNum().equals(product.getState()))
                .collect(Collectors.toList());

        // Convert to DTOs safely
        List<IndustrialProductForErpDocumentDTO> convertedList = obeiProductEntityConverter
                .toOuterProductDocumentDTOList(industrialProducts);
        if (CollUtil.isEmpty(convertedList)) {
            return R.ok(Collections.emptyList());
        }
        List<IndustrialProductForErpDocumentDTO> resultDTO = new ArrayList<>(convertedList);

        // 创建一个映射，用于存储商品ID和店铺ID到相似度的映射关系
        Map<String, BigDecimal> similarityMap = authProductList.stream()
                .collect(Collectors.toMap(ps -> ps.getTargetShopId() + ":" + ps.getTargetProdId(),
                        ProductSimilarity::getSimilarity, (v1, v2) -> v1.compareTo(v2) > 0 ? v1 : v2));

        // 设置相似度值
        resultDTO.forEach(dto -> {
            String key = dto.getShopId() + ":" + dto.getProdId();
            dto.setSimilarity(similarityMap.getOrDefault(key, BigDecimal.ZERO));
        });

        // 根据相似度排序结果
        resultDTO.sort((o1, o2) -> {
            String key1 = o1.getShopId() + ":" + o1.getProdId();
            String key2 = o2.getShopId() + ":" + o2.getProdId();
            BigDecimal similarity1 = similarityMap.getOrDefault(key1, BigDecimal.ZERO);
            BigDecimal similarity2 = similarityMap.getOrDefault(key2, BigDecimal.ZERO);
            return similarity2.compareTo(similarity1); // 降序排序
        });

        return R.ok(resultDTO);
    }

    private void updateRecommendProds(List<OpenMaterialProductRecommend> recommendList) {
        System.out.println("进入updateRecommendProds方法");
        String vendorCode = recommendList.get(0).getVendorCode();
        List<String> skuIdList = recommendList.stream().map(OpenMaterialProductRecommend::getSkuId)
                .collect(Collectors.toList());
        Map<String, IndustrialProduct> industrialProductMap = industrialProductDao
                .queryBySupplierCodeAndSkuId(skuIdList, vendorCode).stream()
                .collect(Collectors.toMap(IndustrialProduct::getSkuId, Function.identity()));

        for (OpenMaterialProductRecommend recommend : recommendList) {
            XxlJobLogger.log("recommend skuId=" + recommend.getSkuId());
            IndustrialProduct industrialProduct = industrialProductMap.get(recommend.getSkuId());
            if (industrialProduct == null) {
                XxlJobLogger.log("recommend skuId=" + recommend.getSkuId() + " not found");
                continue;
            }
            // 更新推荐表的价格和价格比例
            if (ObjectUtil.isEmpty(recommend.getInitialPrice())) {
                recommend.setInitialPrice(industrialProduct.getPrice());
            }
            recommend.setPrice(industrialProduct.getPrice());
            BigDecimal priceRatio = BigDecimal.ZERO;
            if (ObjectUtil.isNotEmpty(recommend.getInitialPrice())
                    && ObjectUtil.isNotEmpty(industrialProduct.getPrice())) {
                priceRatio = industrialProduct.getPrice().divide(recommend.getInitialPrice(), 2, RoundingMode.HALF_UP);
            }
            recommend.setPriceRatio(priceRatio);
            // 判断priceRatio在AlarmEnum哪个阈值范围内，设置报警等级
            recommend.setAlarm(AlarmStatusEnum.getAlarmStatus(priceRatio));
            XxlJobLogger.log("recommend skuId=" + recommend.getSkuId() + " finished");
        }
        openMaterialProductRecommendDao.update(recommendList);
    }

}
