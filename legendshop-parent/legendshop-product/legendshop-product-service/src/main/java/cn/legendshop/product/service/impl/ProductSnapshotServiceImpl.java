/*
 *
 * LegendShop 多用户商城系统
 *
 *  版权所有,并保留所有权利。
 *
 */
package cn.legendshop.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.order.client.SubItemServiceClient;
import cn.legendshop.order.client.SubServiceClient;
import cn.legendshop.order.dto.SubDTO;
import cn.legendshop.product.api.dto.IndustrialProductDetailForErpDTO;
import cn.legendshop.product.api.entity.IndustrialProduct;
import cn.legendshop.product.dao.*;
import cn.legendshop.product.service.IndustrialProductService;
import cn.legendshop.product.service.MsProductService;
import cn.legendshop.product.service.ProductSnapshotService;
import cn.legendshop.product.service.convert.SameProductSnapshotConverter;
import cn.legendshop.product.strategy.ThirdIndustrialStrategyContext;
import cn.legendshop.product.vo.PurchaseComparisonVO;
import cn.legendshop.search.api.dto.IndustrialProductForErpDocumentDTO;
import cn.legendshop.search.api.entity.ProductSameMember;
import com.legendshop.model.dto.KeyValueDto;
import com.legendshop.model.dto.ProdParamDto;
import com.legendshop.model.entity.*;
import com.legendshop.uploader.AttachmentManager;
import com.legendshop.util.AppUtils;
import com.legendshop.util.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.legendshop.common.core.enums.UserSourceTypeEnum.PC;

/**
 * 商品快照 service impl
 */
@Slf4j
@Service("productSnapshotService")
@AllArgsConstructor
public class ProductSnapshotServiceImpl  implements ProductSnapshotService {

    private final ProductSnapshotDao productSnapshotDao;
    private final ProductDao productDao;
    private final BrandDao brandDao;
	private final SameProductSnapshotDao sameProductSnapshotDao;
	private final ProductSameMemberDao productSameMemberDao;
    private final AttachmentManager attachmentManager;
    private final IndustrialProductService industrialProductService;
    private final SubServiceClient subServiceClient;
    private final AfterSaleDao afterSaleDao;
    private final IndustrialOuterShopConfigService industrialOuterShopConfigService;

	private final SubItemServiceClient subItemServiceClient;

	private final ThirdIndustrialStrategyContext thirdIndustrialStrategyContext;

	private final SameProductSnapshotConverter sameProductSnapshotConverter;
	
	//最好不要这样写，很容易会导致循环依赖
	private final MsProductService msProductService;
	
	private final PurchaseComparisonDao purchaseComparisonDao;



	@Override
    public ProductSnapshot getProductSnapshot(Long id) {
        return productSnapshotDao.getProductSnapshot(id);
    }

    /**
     * 保存商品快照
     */
	@Override
	public void saveProdSnapshot(SubItem subItem) {
		log.info("=======进入保存快照方法=============");
		Long finalId = null;
		SubDTO subDTO = subServiceClient.getSubBySubNumber(subItem.getSubNumber(), SecurityConstants.FROM_IN).getResult();
		Long snapshotId = null;
		if(subItem.getProdId() == null || ObjectUtil.isNotEmpty(industrialOuterShopConfigService.getByShopId(subDTO.getShopId()))){
			log.info("保存商品快照1");
			IndustrialProduct industrialProduct = industrialProductService.getByShopIdAndSkuId(subDTO.getShopId(), subItem.getThirdSkuId());
			//ProductSnapshot productSnapshot = productSnapshotDao.getSnapShotByThirdSkuIdAndPrice(industrialProduct.getShopId(),industrialProduct.getSkuId(), subItem.getCash());
			ProductSnapshot productSnapshot = new ProductSnapshot();
			// 获取 industrialProduct 的主图（imagePath）和 productDetail 的图片列表（photos）
			String mainImage = industrialProduct.getImagePath();
			IndustrialProductDetailForErpDTO productDetail = thirdIndustrialStrategyContext.getStrategy(industrialProduct.getShopId()).getProductDetail(industrialProduct.getSkuId(), industrialProduct.getShopId(), PC.getValue());
			List<String> detailPhotos = new ArrayList<>();
			if (AppUtils.isNotBlank(productDetail)) {
				if (CollUtil.isNotEmpty(productDetail.getPhotos())) {
					detailPhotos = productDetail.getPhotos();
				}
			}
			// 合并图片（确保主图在前，详情图在后，并用逗号分隔）
			StringBuilder combinedImages = new StringBuilder();
			// 1. 添加主图（如果存在）
			if (StrUtil.isNotBlank(mainImage)) {
				combinedImages.append(mainImage);
			}

			// 2. 添加详情图（如果存在）
			if (CollUtil.isNotEmpty(detailPhotos)) {
				if (combinedImages.length() > 0) {
					combinedImages.append(StrUtil.COMMA); // 主图和详情图之间加逗号
				}
				combinedImages.append(String.join(StrUtil.COMMA, detailPhotos));
			}

			// 设置合并后的图片到 productSnapshot
			productSnapshot.setPic(combinedImages.toString());
			snapshotId = productSnapshotDao.createProductSnapshotId();
			productSnapshot.setSnapshotId(snapshotId);
			productSnapshot.setProdId(subItem.getProdId());
			productSnapshot.setSkuId(subItem.getSkuId());
			productSnapshot.setThirdSkuId(subItem.getThirdSkuId());
			productSnapshot.setShopId(industrialProduct.getShopId());
			productSnapshot.setShopName(subDTO.getShopName());
			productSnapshot.setName(industrialProduct.getName());
			productSnapshot.setCarriage(subItem.getFreightAmount());
			productSnapshot.setProdType("P");
			productSnapshot.setRecDate(new Date());
			//处理商品详情
			productSnapshot.setContent(null);

			//通知图片服务器 ，图片主图copy一份  TODO  改成异步 这里如果有问题会延长事务时间 还有容易报错。
			//String snapShotImg =  attachmentManager.saveProdSnapshotImg(subItem.getPic(),snapshotId);
			//String[] imageArray = industrialProduct.getImagePath().split(StrUtil.COMMA);
			//productSnapshot.setPic(imageArray.length > 1?imageArray[0] : industrialProduct.getImagePath());

			//品牌
			productSnapshot.setBrandName(industrialProduct.getBrandName());
			//获取 属性 和 价格
			productSnapshot.setCash(subItem.getCash());
			productSnapshot.setActualCount(subItem.getActualCount());
			productSnapshot.setProductTotalAmount(BigDecimal.valueOf(subItem.getProductTotalAmount()));
			productSnapshot.setAttribute(subItem.getAttribute());
			productSnapshot.setHasGuarantee(0);
			productSnapshot.setVersion(0);
			//引用msProductService方法,最好不要这样写，很容易会导致循环依赖
			log.info("查询相同商品快照");
			R<List<IndustrialProductForErpDocumentDTO>> industrialProductForErpDocumentDTOSR = msProductService.listSameProduct(subDTO.getShopId(),subItem.getProdId(),subDTO.getUserId());
			if(industrialProductForErpDocumentDTOSR.isSuccess() && ObjectUtil.isNotEmpty(industrialProductForErpDocumentDTOSR.getResult())){
				Boolean lowestPrice = true;
				List<SameProductSnapshot> sameProductSnapshotList  = sameProductSnapshotConverter.toList(industrialProductForErpDocumentDTOSR.getResult());
				for(SameProductSnapshot sameProductSnapshot:sameProductSnapshotList) {
					sameProductSnapshot.setSnapshotId(snapshotId);
					if(sameProductSnapshot.getPrice().compareTo(BigDecimal.valueOf(subItem.getPrice()))<0){
						lowestPrice  = false;
					}
					sameProductSnapshot.setCreateTime(new Date());
				}
				productSnapshot.setSameProductLowestPrice(lowestPrice);
				sameProductSnapshotDao.save(sameProductSnapshotList);
			}
			productSnapshotDao.saveProductSnapshotWithId(productSnapshot);
			finalId = snapshotId;
		}else {
			//查询商品详细信息
			log.info("保存商品快照2");
			Product product = productDao.getProductById(subItem.getProdId());

			//根据prodId, skuid 和 version 先从快照表里找
			//ProductSnapshot productSnapshot = productSnapshotDao.getProductSnapshot(subItem.getProdId(),subItem.getSkuId(),product.getVersion());
			//快照表里没有找到时，保存到快照表

			// 直接保存到快照表
			ProductSnapshot productSnapshot = new ProductSnapshot();
			snapshotId = productSnapshotDao.createProductSnapshotId();
			productSnapshot = new ProductSnapshot();
			productSnapshot.setSnapshotId(snapshotId);
			productSnapshot.setProdId(product.getProdId());
			productSnapshot.setSkuId(subItem.getSkuId());
			productSnapshot.setVersion(product.getVersion());
			productSnapshot.setShopId(product.getShopId());
			productSnapshot.setShopName(subDTO.getShopName());
			productSnapshot.setName(product.getName());
			productSnapshot.setCarriage(subItem.getFreightAmount());
			productSnapshot.setBrief(product.getBrief());
			productSnapshot.setHasGuarantee(product.getHasGuarantee());
			productSnapshot.setRejectPromise(product.getRejectPromise());
			productSnapshot.setServiceGuarantee(product.getServiceGuarantee());
			productSnapshot.setProdType(product.getProdType());
			productSnapshot.setRecDate(new Date());
			//处理商品详情
			productSnapshot.setContent(handleContentPics(product.getContent(),snapshotId));
			//通知图片服务器 ，图片主图copy一份  TODO  改成异步 这里如果有问题会延长事务时间 还有容易报错。
			String snapShotImg =  attachmentManager.saveProdSnapshotImg(subItem.getPic(),snapshotId);
			productSnapshot.setPic(snapShotImg);
			//品牌
			productSnapshot.setBrandName(getBrandName(product.getBrandId()));
			//售后服务
			productSnapshot.setAfterSaleService(getAfterSale(product.getAfterSaleId()));
			//获取 属性 和 价格
			productSnapshot.setCash(subItem.getCash());
			productSnapshot.setActualCount(subItem.getActualCount());
			productSnapshot.setProductTotalAmount(BigDecimal.valueOf(subItem.getProductTotalAmount()));
			productSnapshot.setAttribute(subItem.getAttribute());
			//获取参数
			List<KeyValueDto> paramKeyValList = getParamKeyValList(product.getParameter(),product.getUserParameter());
			if(paramKeyValList.size()>0){
				productSnapshot.setParameter(JSONUtil.getJson(paramKeyValList));
			}
			//保存
			productSnapshotDao.saveProductSnapshotWithId(productSnapshot);
			productSnapshot.setSnapshotId(snapshotId);
			finalId = snapshotId;

		}
//
//		//更新订单项的 快照id
		subItemServiceClient.updateProdSnapshot(subItem.getSubItemId(),finalId,SecurityConstants.FROM_IN);

	}

	@Override
	public List<ProductSnapshot> queryByIds(List<Long> snapShotIds) {
		return productSnapshotDao.queryByIds(snapShotIds);
	}

	@Override
	public List<IndustrialProductForErpDocumentDTO> getSameProductSnapShot(Long snapShotId) {
		List<SameProductSnapshot> sameProductSnapshotList = sameProductSnapshotDao.queryBySnapshotId(snapShotId);
		return sameProductSnapshotConverter.fromList(sameProductSnapshotList);
	}


	/**
	 * 处理商品详情的图片, 不再支持拷贝详情的图片,因为太多了, 没有什么必要性
	 * @deprecated
	 * @param content
	 */
	private String handleContentPics(String content,Long snapshotId) {
		return null;
//		List<String> picPathList = HtmlUtil.getImgsSrc(content);//得到所有图片的路径
//		String newContent = new String(content);
//		String photoServer = ResourcePathUtil.getPhotoServer();
//		String pathPrefix = ResourcePathUtil.getPhotoPathPrefix();
//		String snapPathPrefix = ResourcePathUtil.getSnapPhotoPathPrefix();
//		for (String path : picPathList) {
//			String pic = null;
//			if(path.startsWith(pathPrefix)){
//				pic = path.replaceAll(pathPrefix, "");
//				newContent = newContent.replaceAll(path, snapPathPrefix+pic);
//			}else if(path.startsWith(photoServer +pathPrefix)){
//				pic = path.replaceAll(photoServer +pathPrefix, "");
//				newContent = newContent.replaceAll(path, photoServer+snapPathPrefix+pic);
//			}
//			if(AppUtils.isNotBlank(pic)){
//				//通知图片服务器 ，图片copy一份
//				attachmentManager.saveProdSnapshotImg(pic,snapshotId);
//			}
//		}
//		return newContent;
	}

	/**
	 * 把 参数 和 自定义参数 组合成 keyValueDto的列表
	 * @param param
	 * @param userParam
	 * @return
	 */
	private List<KeyValueDto> getParamKeyValList(String param, String userParam) {
		List<KeyValueDto> paramKeyValList = new ArrayList<KeyValueDto>();
		//获取参数
		if(AppUtils.isNotBlank(param)){
			List<ProdParamDto> params =  JSONUtil.getArray(param, ProdParamDto.class);
			for (ProdParamDto prodParamDto :params) {
				if(AppUtils.isNotBlank(prodParamDto.getParamValueName())){
					KeyValueDto keyValueDto = new KeyValueDto();
					keyValueDto.setKey(prodParamDto.getParamName());
					keyValueDto.setValue(prodParamDto.getParamValueName());
					paramKeyValList.add(keyValueDto);
				}
			}
		}
		//获取自定义参数
		if(AppUtils.isNotBlank(userParam)){
			List<KeyValueDto> kvs = JSONUtil.getArray(userParam, KeyValueDto.class);
			paramKeyValList.addAll(kvs);
		}
		return paramKeyValList;
	}

//	/**
//	 * 根据 sku 的属性id组合 获取 属性值的字符串
//	 * @param properties
//	 * @return
//	 */
//	private String getAttributeByProp(String properties) {
//		String attribute = "";
//		List<KeyValueEntity> keyValueEntities=splitJoinSku(properties,";");
//		for (KeyValueEntity entity:keyValueEntities) {
//			ProductProperty productProperty = productPropertyDao.getProductProperty(Long.valueOf(entity.getKey()));
//			ProductPropertyValue  productPropertyValue  = productPropertyValueDao.getProductPropertyValue(Long.valueOf(entity.getValue()));
//			if(productProperty != null && productPropertyValue != null){
//				attribute += productProperty.getMemo()+":"+productPropertyValue.getName()+";";
//			}
//		}
//		return attribute;
//	}

	/**
	 * 根据品牌id 获取品牌名称
	 * @param brandId
	 * @return
	 */
	private String getBrandName(Long brandId) {
		if(AppUtils.isNotBlank(brandId)){
			Brand brand = brandDao.getById(brandId);
			if(brand!=null){
				return brand.getBrandName();
			}
		}
		return null;
	}

	/**
	 * 根据id 获取售后服务说明
	 * @param afterSaleId
	 * @return
	 */
	private String getAfterSale(Long afterSaleId) {
		if(AppUtils.isNotBlank(afterSaleId)){
			AfterSale afterSale = afterSaleDao.getById(afterSaleId);
			if(afterSale!=null){
				return afterSale.getContent();
			}
		}
		return null;
	}

//	/**
//	 * 分解出SKU的属性和属性值ID
//	 * @param properties
//	 * @param expr
//	 * @return
//	 */
//	private List<KeyValueEntity> splitJoinSku(String properties, String expr) {
//		List<KeyValueEntity>  skuKVs=new ArrayList<KeyValueEntity>();
//		if (properties != null && properties != "") {
//			String[] proValue = properties.split(expr);
//			if (proValue != null && proValue.length > 0) {
//				int length = proValue.length;
//				for (int i = 0; i < length; i++) {
//					String[] valeus = proValue[i].split(":");
//					if (valeus != null && valeus.length > 1) {
//						KeyValueEntity kv = new KeyValueEntity();
//						kv.setKey(valeus[0]);
//						kv.setValue(valeus[1]);
//						skuKVs.add(kv);
//					}
//				}
//			}
//		}
//		return skuKVs;
//	}

	@Override
	public PurchaseComparisonVO getPurchaseComparisonInfo(String subNumber, String thirdSkuId) {
		log.info("获取采购比价信息, subNumber={}, thirdSkuId={}", subNumber, thirdSkuId);
		
		try {
			// 1. 获取采购比价基本信息
			PurchaseComparisonVO purchaseComparisonVO = purchaseComparisonDao.getPurchaseComparisonInfo(subNumber);
			if (purchaseComparisonVO == null) {
				log.error("未找到采购比价信息, subNumber={}", subNumber);
				return new PurchaseComparisonVO();
			}
			
			// 2. 获取所有商品比价信息
			List<PurchaseComparisonVO.ProductComparisonVO> productList = purchaseComparisonDao.getProductComparisonInfo(subNumber, null);
			
			// 3. 为每个商品获取报价信息
			if (productList != null && !productList.isEmpty()) {
				for (PurchaseComparisonVO.ProductComparisonVO product : productList) {
					// 获取该商品的所有报价信息
					List<PurchaseComparisonVO.QuotationVO> quotations = purchaseComparisonDao.getProductQuotationInfo(subNumber, product.getEpItemId());
					product.setQuotations(quotations);
					
					// 标记当前查看的商品
					if (thirdSkuId != null && thirdSkuId.equals(product.getProductId())) {
						product.setCurrentProduct(true);
					} else {
						product.setCurrentProduct(false);
					}
				}
			}
			
			purchaseComparisonVO.setProductList(productList);
			
			// 4. 获取平台比价汇总信息
			List<PurchaseComparisonVO.PlatformComparisonVO> platformList = purchaseComparisonDao.getPlatformComparisonInfo(subNumber);
			purchaseComparisonVO.setPlatformList(platformList);
			
			return purchaseComparisonVO;
		} catch (Exception e) {
			log.error("获取采购比价信息异常", e);
			return new PurchaseComparisonVO();
		}
	}
}
