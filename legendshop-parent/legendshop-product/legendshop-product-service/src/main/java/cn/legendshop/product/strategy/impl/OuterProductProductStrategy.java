package cn.legendshop.product.strategy.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.IndustrialTypeEnum;
import cn.legendshop.common.core.enums.IsMroSelfEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.model.entity.Area;
import cn.legendshop.common.model.entity.City;
import cn.legendshop.common.model.entity.Province;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.open.constants.OpenPlatformGlobalConstants;
import cn.legendshop.open.dto.CheckSaleResultDTO;
import cn.legendshop.open.dto.CommonSimilarSkuDTO;
import cn.legendshop.open.dto.SkuInfoDTO;
import cn.legendshop.open.dto.StockInfoDTO;
import cn.legendshop.open.enums.ResultCodeEnum;
import cn.legendshop.open.factory.OpenPlatformServiceFactory;
import cn.legendshop.open.request.product.SaleCheckRequest;
import cn.legendshop.open.request.product.SellPriceRequest;
import cn.legendshop.open.request.product.SimilarSkuRequest;
import cn.legendshop.open.request.product.StockRequest;
import cn.legendshop.open.response.CommonResponse;
import cn.legendshop.open.sevice.OpenPlatformProductService;
import cn.legendshop.product.api.dto.*;
import cn.legendshop.product.api.dto.product.ProductCheckItemDTO;
import cn.legendshop.product.api.dto.product.ThirdProductCheckDTO;
import cn.legendshop.product.api.entity.IndustrialProduct;
import cn.legendshop.product.api.entity.MaterialProductRel;
import cn.legendshop.product.api.entity.ProdPriceContacts;
import cn.legendshop.product.api.enums.IndexUpdateFlagEnum;
import cn.legendshop.product.api.query.CommonProdQuery;
import cn.legendshop.product.api.query.product.ErpProductQuery;
import cn.legendshop.product.api.vo.ProdPriceContactsVo;
import cn.legendshop.product.api.vo.SupplierCategoryBindErpVo;
import cn.legendshop.product.dao.IndustrialProductDao;
import cn.legendshop.product.mq.producer.ProdChangeLogProducerService;
import cn.legendshop.product.service.ProdPriceContactsService;
import cn.legendshop.product.service.convert.IndustrialProductConverter;
import cn.legendshop.product.service.convert.SkuConverter;
import cn.legendshop.product.strategy.ThirdIndustrialStrategy;
import cn.legendshop.search.api.client.IndustrialProductIndexClient;
import com.alibaba.fastjson.JSON;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.model.entity.Category;
import com.legendshop.model.entity.Product;
import com.legendshop.util.AppUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.legendshop.product.api.constant.ProductConstant.*;
import static cn.legendshop.product.api.enums.IndexUpdateFlagEnum.MUST_UPDATE;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.OFF_SHELF;
import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.ON_SHELF;

@Service("outerProductStrategy")
@AllArgsConstructor
@Slf4j
public class OuterProductProductStrategy extends BaseProductStrategy implements ThirdIndustrialStrategy {

	private final IndustrialProductDao industrialProductDao;
	
	private final IndustrialProductConverter industrialProductConverter;

	private final OpenPlatformServiceFactory openPlatformServiceFactory;

	private final IndustrialOuterShopConfigService industrialOuterShopConfigService;

	private final IndustrialProductIndexClient industrialProductIndexClient;

	private final IndustrialProductIndexClient userAddressService;

	private final SkuConverter skuConverter;

	private final IndustrialOuterShopConfigService configService;

	private final ProdChangeLogProducerService prodChangeProducerService;

	private final ProdPriceContactsService prodPriceContactsService;

	@Override
	public IndustrialSearchDocumentDTO buildIndex(Long entityId, Long shopId){
		List<String> skuMaterialCodeRes = new ArrayList<>();
		IndustrialSearchDocumentDTO industrialSearchDocumentDTO = new IndustrialSearchDocumentDTO();
		IndustrialOuterShopConfig config = industrialOuterShopConfigService.getByShopId(shopId);
		log.info("进入第三方平台商品策略开始组装索引");
		//初始化
		IndustrialProductDTO outerProduct = this.industrialProductDao.getInfoByIdAndShopId(entityId, shopId);
		if(outerProduct.getProdId() != null){
			Product product = this.productDao.getProductById(outerProduct.getProdId());
			outerProduct.setBuys(product.getBuys());
		}

		//组装最基本的索引信息
		outerProduct.setShopName(config.getShopName());
		thridIndustrialProductConverter.toProduct(outerProduct, industrialSearchDocumentDTO);

		//图片处理一下，只需要一张
		if(StrUtil.isNotBlank(industrialSearchDocumentDTO.getImage())){
			String[] split = industrialSearchDocumentDTO.getImage().split(StrUtil.COMMA);
			industrialSearchDocumentDTO.setImage(split[0]);
		}

		//找到该商家对应的枚举
		IndustrialOuterShopConfig outerShopConfig = industrialOuterShopConfigService.getByShopId(outerProduct.getShopId());
		industrialSearchDocumentDTO.setSupplier(outerShopConfig.getInnerCode());

		//erp 绑定关系设置
		List<MaterialProductRel> materialProductRels = materialProductRelDao.queryBinding(outerProduct.getSkuId().toString(), outerShopConfig.getInnerCode());
		if (CollUtil.isNotEmpty(materialProductRels)) {
			skuMaterialCodeRes.addAll(materialProductRels.stream().map(MaterialProductRel::getMaterialCode).distinct().collect(Collectors.toList()));
			String boundMaterialCode = materialProductRels.stream().map(MaterialProductRel::getMaterialCode).collect(Collectors.joining(StrUtil.COMMA));
			//设置ERP绑定的信息
			industrialSearchDocumentDTO.setBoundMaterialCode(boundMaterialCode);
			industrialSearchDocumentDTO.setBound(Boolean.TRUE);
		}
		log.info("绑定关系组装完成");


		// 写入商品关键字 --（关键字组成：商品名称+商品分类名称+商品料号）
		log.info(" keyword : {},{},{}",
			outerProduct.getSkuId(),
			outerProduct.getName(),
			skuMaterialCodeRes
		);

		industrialSearchDocumentDTO.setKeyword(outerShopConfig.getShopName() + " " + outerShopConfig.getNickName() + " " + outerProduct.getSkuId() + " " + outerProduct.getName() + " "
				+ outerProduct.getBrandName() + " " + outerProduct.getModel() + " "
				+ (CollectionUtils.isEmpty(skuMaterialCodeRes) ? "" : StringUtils.join(skuMaterialCodeRes, " "))
		);

//		CategoryMroSupplier categoryMroSupplier = this.categoryMroSupplierDao.getById(outerProduct.getCategoryMroSupplierId());

		//补充关键词
		String keyword = industrialSearchDocumentDTO.getKeyword();
		StringBuilder stringBuilder = new StringBuilder(keyword);

		industrialSearchDocumentDTO.setMroCategoryId(outerProduct.getCategoryMroSupplierId());
		log.info("组装调价id：{}", outerProduct.getCategoryMroSupplierId());
		// 根据关联的  进行添加一 二 三 级
		Long currentCategoryId = outerProduct.getCategoryMroSupplierId();
		Category currentCategory = null;
		do {
			currentCategory = categoryService.getCategoryById(currentCategoryId);
			if (AppUtils.isNotBlank(currentCategory)) {
				if (currentCategory.getGrade() == 1) {
					industrialProductConverter.first(currentCategory, industrialSearchDocumentDTO);
				} else if (currentCategory.getGrade() == 2) {
					industrialProductConverter.second(currentCategory, industrialSearchDocumentDTO);
				} else {
					industrialProductConverter.third(currentCategory, industrialSearchDocumentDTO);
				}
				stringBuilder.append(currentCategory.getName() + " ");
				currentCategoryId = currentCategory.getParentId();
			}
		} while (AppUtils.isNotBlank(currentCategory));
		log.info("类目组装完成");


		// 组装店铺信息

		stringBuilder.append(outerProduct.getShopName() + " " + industrialSearchDocumentDTO.categoryToString());

		//补充关键字
		industrialSearchDocumentDTO.setKeyword(stringBuilder.toString());
		industrialSearchDocumentDTO.setShopId(outerShopConfig.getShopId());
		industrialSearchDocumentDTO.setSupplierName(outerShopConfig.getInnerName());
		industrialSearchDocumentDTO.setProdType(IndustrialTypeEnum.INDUSTRIAL_PRODUCTS.getCode());
		industrialSearchDocumentDTO.setForMro(Boolean.TRUE);
		industrialSearchDocumentDTO.setBound(CollectionUtils.isEmpty(skuMaterialCodeRes) ? Boolean.FALSE : Boolean.TRUE);
		industrialSearchDocumentDTO.setId(shopId + StrUtil.COLON + outerProduct.getSkuId());
		industrialSearchDocumentDTO.setStatus(outerProduct.getState() == 1? ON_SHELF.getThirdStatus() : OFF_SHELF.getThirdStatus());
		industrialSearchDocumentDTO.setIsMroSelf(IsMroSelfEnum.THIRD_PROD.getType());

		//计算调价后的价格，目前只做排序使用
		if(Objects.nonNull(outerProduct.getCategoryMroSupplierId())){
			super.calculatePrice(industrialSearchDocumentDTO);
		}

		log.info("更新完索引标识!");
		if(outerProduct.getUpdateFlag() < MUST_UPDATE.getValue()){
			this.industrialProductDao.updateIndexFlagByShopId(outerProduct.getId(), IndexUpdateFlagEnum.UPDATED.getValue(), shopId);
		}
		return industrialSearchDocumentDTO;
	}

	@Override
	public SupplierCategoryBindErpVo getThirdCategoryByCode(String categoryCode) {
		return null;
	}


	@Override
	public Long getProductOnLineCount(Long shopId) {
		return industrialProductDao.getProductCountOnLineByShopId(shopId);
	}

	@Override
	public String checkAreaSaleRestriction(AddressQueryDTO addressQueryDTO) {
		OpenPlatformProductService productService = openPlatformServiceFactory.getProductServiceByShopId(addressQueryDTO.getShopId());
		SaleCheckRequest request = new SaleCheckRequest();

		Province province = this.provinceDao.getByProperties(new EntityCriterion().eq("province", addressQueryDTO.getProvince()));
		if (ObjectUtil.isEmpty(province)) {
			return null;
		}
		City city = this.cityDao.getByProperties(new EntityCriterion().eq("provinceid", province.getId()).eq("city", addressQueryDTO.getCity()));
		if (ObjectUtil.isEmpty(city)) {
			return null;
		}
		Area area = this.areaDao.getByProperties(new EntityCriterion().eq("cityid", city.getId()).eq("area", addressQueryDTO.getArea()));
		if (ObjectUtil.isEmpty(area)) {
			return null;
		}

		List<SkuInfoDTO> skus = new ArrayList<>();
		request.setProvinceId(province.getProvinceid())
			.setCityId(city.getCityid())
			.setCountyId(area.getAreaid())
			.setAddress(province.getProvince() + city.getCity() + area.getArea());

		SkuInfoDTO skuInfoDTO = new SkuInfoDTO();
		skuInfoDTO.setSkuId(addressQueryDTO.getOuterId())
			.setSkuNum(1);
		skus.add(skuInfoDTO);
		request.setSkus(skus);
		request.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		CommonResponse<List<CheckSaleResultDTO>> listCommonResponse = productService.saleCheck(request);
		if(listCommonResponse.getSuccess()){
			CheckSaleResultDTO checkSaleResultDTO = listCommonResponse.getResult().get(0);
			if(checkSaleResultDTO.getSaleState() == 1){
				return "此区域可以下单！";
			}else {
				return checkSaleResultDTO.getCause();
			}
		}

		return null;
	}


	@Override
	public String preDeliveryDay(AddressQueryDTO addressQueryDTO) {
		IndustrialProduct industrialProduct = industrialProductDao.getByShopIdAndSkuId(addressQueryDTO.getOuterId(), addressQueryDTO.getShopId());
		return industrialProduct.getPreDeliveryDayNum() == null? "999":industrialProduct.getPreDeliveryDayNum().toString();
	}

	@Override
	public R<CommonProdInfoDTO > dailyUpdateProdInfo(IndustrialProduct product) {
		try {
			CommonProdInfoDTO commonProdInfoDTO = new CommonProdInfoDTO();

			//获取更新前的价格
			BigDecimal beforePrice = product.getPrice();
			IndustrialOuterShopConfig config = configService.getByShopId(product.getShopId());

			log.info("更新一日未更新商品数据，prodid: {}", product.getId());
			OpenPlatformProductService openPlatformProductService = openPlatformServiceFactory.getProductServiceByShopId(product.getShopId());
			product.setUpdateTime(new Date());

			SellPriceRequest sellPriceRequest = new SellPriceRequest();
			sellPriceRequest.setSkuIds(Collections.singletonList(product.getSkuId()));
			sellPriceRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
			Map<String, SkuInfoDTO> skuPriceInfoMap = null;
			try {
				skuPriceInfoMap = openPlatformProductService.getSellPrice(sellPriceRequest).getResult().stream().collect(Collectors.toMap(SkuInfoDTO::getSkuId, java.util.function.Function.identity()));
			}catch (Exception e){
				return R.ok(commonProdInfoDTO.setStatus(OFF_SHELF.getThirdStatus()));
			}
			log.info("返回的数据" + JSON.toJSONString(skuPriceInfoMap));


			SkuInfoDTO skuInfoDTO = skuPriceInfoMap.get(product.getSkuId());

			//更新价格
			if (skuInfoDTO.getSellPrice().compareTo(product.getPrice()) != 0 || skuInfoDTO.getMarketPrice().compareTo(product.getMarketPrice()) != 0 || (ObjectUtil.isNotEmpty(skuInfoDTO.getTaxCode()) && !skuInfoDTO.getTaxCode().equals(product.getTaxCode()))) {
				product.setPrice(skuInfoDTO.getSellPrice());
				product.setMarketPrice(skuInfoDTO.getMarketPrice());
				product.setTaxRate(skuInfoDTO.getTaxRate());
				product.setTaxCode(skuInfoDTO.getTaxCode());
				product.setUntaxedPrice(skuInfoDTO.getSellPrice().divide(
								BigDecimal.ONE.add(
								Optional.ofNullable(skuInfoDTO.getTaxRate()).orElse(new BigDecimal(13)).divide(new BigDecimal(100))
						), 2, RoundingMode.HALF_UP));

				product.setUpdateTime(new DateTime());
				product.setUpdateFlag(IndexUpdateFlagEnum.UNUPDATE.getValue());
				industrialProductDao.updateProdByShopId(product, product.getShopId());
			}
			commonProdInfoDTO.setPrice(skuInfoDTO.getSellPrice());
			commonProdInfoDTO.setThirdSalePrice(skuInfoDTO.getMarketPrice());
			commonProdInfoDTO.setTaxRate(skuInfoDTO.getTaxRate());
			commonProdInfoDTO.setUntaxedPrice(product.getUntaxedPrice());

			log.debug("-----------修改完成后更新索引-------");
			industrialProductIndexClient.updateIndex(product.getShopId() + StrUtil.COLON + product.getSkuId(), SecurityConstants.FROM_IN);

			// 用于存储价格变更记录

			List<ProdChangeLogDTO> priceChangeRecords = new ArrayList<>();

			// 计算变更后的价格和价格变化比率
			BigDecimal afterPrice = product.getPrice();
			BigDecimal priceChangeRate = calculatePriceChangeRate(beforePrice, afterPrice); // 调用价格变化率计算方法

			if(priceChangeRate.compareTo(BigDecimal.ZERO) != 0) {
				// 创建价格变更记录
				ProdChangeLogDTO record = new ProdChangeLogDTO();
				record.setShopId(product.getShopId());
				record.setShopName(config.getShopName());
				record.setSkuId(product.getSkuId());  // 商品sku
				record.setBeforePrice(beforePrice);  //变更前价格
				record.setAfterPrice(afterPrice);  //变更后价格
				record.setPriceChangeRate(priceChangeRate);  // 价格变化比率
				record.setRequestDate(new Date());  // 当前请求日期
				priceChangeRecords.add(record); // 将记录添加到列表
			}

			// 发送MQ消息以进行异步处理
			if(priceChangeRecords != null && !priceChangeRecords.isEmpty()) {
				priceChangeRecords.forEach(record -> {
					prodChangeProducerService.sendPriceChangeMessage(record);// 发送每个价格变更记录到MQ

					// 判断该商品是否需要提醒
					List<ProdPriceContacts> priceLogList = prodPriceContactsService.getBySkuAndShopId(record.getShopId(), record.getSkuId());
					log.info("需要进行价格变化公众号提醒的商品:{}",JSON.toJSONString(priceLogList));
					if(AppUtils.isNotBlank(priceLogList)) {
						for (ProdPriceContacts priceLog : priceLogList) {
							ProdPriceContactsVo vo = new ProdPriceContactsVo();
							vo.setShopId(priceLog.getShopId());
							vo.setSkuId(priceLog.getSkuId());
							vo.setShopName(record.getShopName());
							vo.setMaterialCode(priceLog.getMaterialCode());
							vo.setBeforePrice(record.getBeforePrice());
							vo.setAfterPrice(record.getAfterPrice());
							vo.setBidPrice(priceLog.getBidPrice());
							vo.setContacts(priceLog.getContacts());
							log.info("指定商品提醒的联系人:{}",priceLog.getContacts());
							vo.setPriceChangeRate(record.getPriceChangeRate());
							prodChangeProducerService.sendProdPriceWXMessage(vo);// 发送价格变更微信公众号提醒MQ
						}
					}
				});
			}
			return R.ok(commonProdInfoDTO);
		}catch (Exception e){
			e.printStackTrace();
			return R.ok(null);
		}
	}

	/**
	 * 计算价格变化率
	 */
	private BigDecimal calculatePriceChangeRate(BigDecimal beforePrice, BigDecimal afterPrice) {
		if (beforePrice == null || beforePrice.compareTo(BigDecimal.ZERO) == 0) {
			return BigDecimal.ZERO;
		}
		return afterPrice.subtract(beforePrice)
				.divide(beforePrice, 4, RoundingMode.HALF_UP);
	}

//	/**
//	 * 根据传进来的条件分页查询第三方工业品商品数据
//	 * @param query
//	 * @return
//	 */
//	@Override
//	public PageSupport<ThridIndustrialProductDTO> pageQueryIndustrialInfo(ThridIndustrialProductQuery query){
//
//		PageSupport<ThridIndustrialProductDTO> pageSupport = new PageSupport<ThridIndustrialProductDTO>();
//		List<ThridIndustrialProductDTO> thridIndustrialProductDTOS = new ArrayList<>();
//		PageSupport<AdminProductInfoDTO> adminProductInfoDTOPageSupport = industrialProductDao.queryThridIndustrialProductListByShopId(query, IndustrialOuterShopConfigService.getByInnerCode(query.getSupplier()).getShopId());
//
//		List<AdminProductInfoDTO> adminProductInfoDTOS = adminProductInfoDTOPageSupport.getResultList();
//		adminProductInfoDTOS.stream().forEach(e->{
//			e.setImagePath(e.getImagePath().split(StrUtil.COMMA)[0]);
//			Category category = this.categoryService.getCategoryByCategoryCode(e.getCategoryCode().split(";")[2]);
//			e.setCategoryCode(category.getCategoryCode());
//			e.setCategoryName(category.getName());
//		});
//
//		List<ThridIndustrialProductDTO> thridIndustrialProductDTOS1 = industrialProductConverter.toList9(adminProductInfoDTOS);
//
//		System.out.println(JSON.toJSONString(thridIndustrialProductDTOS1));
//
//		if (ObjectUtil.isNotEmpty(adminProductInfoDTOS)) {
//			thridIndustrialProductDTOS1.forEach(e->{
//				e.setStatus(e.getStatus() == 1 ? 10 : 20);
//				e.setTaxRate(new BigDecimal(e.getTaxRate()).multiply(new BigDecimal(100L)).toString());
//			});
//			pageSupport.setResultList(thridIndustrialProductDTOS1);
//		}
//		pageSupport.setTotal(adminProductInfoDTOPageSupport.getTotal());
//		pageSupport.setOffset(adminProductInfoDTOPageSupport.getOffset());
//		pageSupport.setPageCount(adminProductInfoDTOPageSupport.getPageCount());
//		pageSupport.setPageSize(adminProductInfoDTOPageSupport.getPageSize());
//		pageSupport.setCurPageNO(adminProductInfoDTOPageSupport.getCurPageNO());
//		return pageSupport;
//
//	}



	@Override
	public ErpProductRriceDTO erpQueryErpProductPriceInfo(ErpProductQuery query){
		IndustrialProduct industrialProduct = this.industrialProductDao.getByShopIdAndSkuId(query.getThirdSkuId(), industrialOuterShopConfigService.getBySupplierCode(query.getSupplier()).getShopId());
		if (ObjectUtil.isEmpty(industrialProduct)) {
			throw new BusinessException("没有绑定关系的商品!" + query.getThirdSkuId());
		}

		ErpProductRriceDTO erpProductRriceDTO = industrialProductConverter.to8(industrialProduct);
		//商品库存查询
		OpenPlatformProductService openPlatformProductService = openPlatformServiceFactory.getProductServiceByShopId(industrialProduct.getShopId());
		StockRequest stockRequest = new StockRequest();
		stockRequest.setCountyId(LIU_HE_QU);
		stockRequest.setCityId(NAN_JING);
		stockRequest.setProvinceId(JIANG_SU);

		SkuInfoDTO skuInfoDTO1 = new SkuInfoDTO();
		skuInfoDTO1.setSkuId(industrialProduct.getSkuId());
		skuInfoDTO1.setSkuNum(1);


		stockRequest.setSkus(Collections.singletonList(skuInfoDTO1));
		stockRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		Map<String, StockInfoDTO> stockInfoDTOMap = openPlatformProductService.getStock(stockRequest).getResult().stream().collect(Collectors.toMap(StockInfoDTO::getSkuId, Function.identity()));
		Integer skuStock = stockInfoDTOMap.get(industrialProduct.getSkuId()).getSkuStock();
		if(skuStock > 0){
			erpProductRriceDTO.setSaleNum(new BigDecimal(skuStock));
			erpProductRriceDTO.setSkuState(ON_SHELF.getThirdStatus());
		}else if(skuStock == -1) {
			erpProductRriceDTO.setSaleNum(new BigDecimal(50));
			erpProductRriceDTO.setSkuState(ON_SHELF.getThirdStatus());
		}else {
			erpProductRriceDTO.setSaleNum(BigDecimal.ZERO);
			erpProductRriceDTO.setSkuState(OFF_SHELF.getThirdStatus());
		}
		//检查价格
		SellPriceRequest sellPriceRequest = new SellPriceRequest();
		sellPriceRequest.setSkuIds(Collections.singletonList(industrialProduct.getSkuId()));
		sellPriceRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		Map<String, SkuInfoDTO> skuPriceInfoMap = openPlatformProductService.getSellPrice(sellPriceRequest).getResult().stream().collect(Collectors.toMap(SkuInfoDTO::getSkuId, Function.identity()));
		SkuInfoDTO skuInfoDTO = skuPriceInfoMap.get(industrialProduct.getSkuId());
		erpProductRriceDTO.setSalePrice(skuInfoDTO.getSellPrice());
		erpProductRriceDTO.setThirdPlatformPrice(skuInfoDTO.getMarketPrice().toString());
		erpProductRriceDTO.setSupplierSalePrice(skuInfoDTO.getMarketPrice());
		erpProductRriceDTO.setUntaxedSalePrice(industrialProduct.getUntaxedPrice());
		erpProductRriceDTO.setStatus(industrialProduct.getState() == 1? 10 :20);

		//检查上下架状态
		return erpProductRriceDTO;
	}

	@Override
	public List<CommonProdInfoDTO> queryProdPriceNow(List<String> productCodes, Long shopId) {
		List<CommonProdInfoDTO> results = new ArrayList<>();
		SellPriceRequest sellPriceRequest = new SellPriceRequest();
		OpenPlatformProductService openPlatformProductService = openPlatformServiceFactory.getProductServiceByShopId(shopId);
		sellPriceRequest.setSkuIds(productCodes);
		sellPriceRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());
		Map<String, SkuInfoDTO> skuPriceInfoMap = openPlatformProductService.getSellPrice(sellPriceRequest).getResult().stream().collect(Collectors.toMap(SkuInfoDTO::getSkuId, Function.identity()));

		for (String productCode : productCodes) {
			SkuInfoDTO skuInfoDTO = skuPriceInfoMap.get(productCode);
			if(ObjectUtil.isEmpty(skuInfoDTO) || ObjectUtil.isEmpty(skuInfoDTO) ){
				log.info(productCode + "的价格查询结果为空");
				continue;
			}

			CommonProdInfoDTO commonProdInfoDTO = new CommonProdInfoDTO();
			commonProdInfoDTO.setProductCode(productCode)
				.setPrice(skuInfoDTO.getSellPrice())
				.setThirdSalePrice(skuInfoDTO.getMarketPrice())
				.setStatus(ON_SHELF.getThirdStatus());
			results.add(commonProdInfoDTO);
		}
		if(CollUtil.isEmpty(results)){
			throw new BusinessException("查询结果为空");
		}

		return results;
	}




	/**
	 * 下单前的检查库存操作
	 * @return
	 */
	@Override
	public R<String> checkStockBeforeCreateOrder(ThirdProductCheckDTO thirdProductCheckDTO){
		StringBuilder sb = new StringBuilder();
		Map<Long, List<ProductCheckItemDTO>> platformMap = thirdProductCheckDTO.getProductCheckItemDTOS().stream().collect(Collectors.groupingBy(ProductCheckItemDTO::getShopId));
		for (Long shopId : platformMap.keySet()) {
			List<ProductCheckItemDTO> productCheckItemDTOS = platformMap.get(shopId);
			Map<Long, Long> productNumMap = productCheckItemDTOS.stream().collect(Collectors.toMap(ProductCheckItemDTO::getProductId, ProductCheckItemDTO::getNum));
			List<IndustrialProduct> industrialProducts = industrialProductDao.queryByProdIdAndShopId(new ArrayList<>(productNumMap.keySet()), shopId);
			Map<String, IndustrialProduct> industrialProductMap = industrialProducts.stream().collect(Collectors.toMap(IndustrialProduct::getSkuId, e -> e));

			StockRequest stockRequest = new StockRequest();
			Province province = provinceDao.getById(thirdProductCheckDTO.getProvinceId());
			stockRequest.setProvinceId(province.getProvinceid());

			City city = cityDao.getById(thirdProductCheckDTO.getCityId());
			stockRequest.setCityId(city.getCityid());

			Area area = areaDao.getAreaById(thirdProductCheckDTO.getAreaId());
			stockRequest.setCountyId(area.getAreaid());

			List<SkuInfoDTO> skuInfoDTOS = new ArrayList<>();
			for (IndustrialProduct industrialProduct : industrialProducts) {
				SkuInfoDTO skuInfoDTO = new SkuInfoDTO();
				skuInfoDTO.setSkuNum(productNumMap.get(industrialProduct.getProdId()).intValue());
				skuInfoDTO.setSkuId(industrialProduct.getSkuId());
				skuInfoDTOS.add(skuInfoDTO);
			}
			stockRequest.setSkus(skuInfoDTOS);
			stockRequest.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId());


			OpenPlatformProductService openPlatformProductService = openPlatformServiceFactory.getProductServiceByShopId(thirdProductCheckDTO.getShopId());
			CommonResponse<List<StockInfoDTO>> stock = openPlatformProductService.getStock(stockRequest);
			for (StockInfoDTO stockInfoDTO : stock.getResult()) {
				if(stockInfoDTO.getStockState() >= 5){
					IndustrialProduct industrialProduct = industrialProductMap.get(stockInfoDTO.getSkuId());
					sb.append(industrialProduct.getName());
					sb.append("无货或已超出库存数量，无法购买，");
				}
			}

		}

		if (sb.length() != 0) {
			sb.setLength(sb.length() - 1);
		}

		return R.ok(sb.toString());
	}

	@Override
	public List<CommonSimilarSkuDTO> getSimilarSku(String skuId, Long shopId) {
		OpenPlatformProductService openPlatformProductService = openPlatformServiceFactory.getProductServiceByShopId(shopId);
		SimilarSkuRequest similarSkuRequest = new SimilarSkuRequest();
		similarSkuRequest.setSkuId(skuId);
		CommonResponse<List<CommonSimilarSkuDTO>> similarSkuResult = openPlatformProductService.getSimilarSku(similarSkuRequest);

		if (!Objects.equals(similarSkuResult.getResultCode(), ResultCodeEnum.NORMAL.getCode())) {
			throw new BusinessException("查询同类商品错误码：" + similarSkuResult.getResultCode());
		}
		return similarSkuResult.getResult();
	}

	@Override
	public CommonStockDTO salableCheck(CommonProdQuery commonProdQuery) {

		CommonStockDTO commonStockDTO = new CommonStockDTO();
		commonStockDTO.setPurchaseFlag(Boolean.TRUE);
		commonStockDTO.setTansFee(BigDecimal.ZERO);
		commonStockDTO.setShopId(commonProdQuery.getShopId());
		List<CommonStockDTO.SkuPurchaseStatus> skuPurchaseStatusList = new ArrayList<>();
		commonStockDTO.setSkuPurchaseStatuses(skuPurchaseStatusList);


		OpenPlatformProductService openPlatformProductService = openPlatformServiceFactory.getProductServiceByShopId(commonProdQuery.getShopId());
		List<SkuInfoDTO> skuInfoDTOS = skuConverter.toList3(commonProdQuery.getSkuInfoDTOS());

		// 京东地址转换成鑫采商城地址
		// 江苏省 南京市 六合区 大厂街道 province city county town
		// 天津市 东丽区 无瑕街道 province city county town
		Province province = provinceDao.getByName(commonProdQuery.getProvince());
		City city;
		Area area;
		city = cityDao.getByCityNameAndProvinceId(commonProdQuery.getCity(), province.getId());
		if (ObjectUtil.isEmpty(city)) {
			// 天津市 东丽区 无瑕街道 province city county town
			city = cityDao.getCityByProvinceid(province.getId()).get(0);
			area = areaDao.getByAreaNameAndCityId(commonProdQuery.getCity(), city.getId());
		} else {
			// 江苏省 南京市 六合区 大厂街道 province city county town
			area = areaDao.getByAreaNameAndCityId(commonProdQuery.getArea(), city.getId());
		}

		if (ObjectUtil.isEmpty(province)) {
			throw new BusinessException("未查询到鑫采商城地址!请核实用户填写地址province");
		}
		if (ObjectUtil.isEmpty(city)) {
			throw new BusinessException("未查询到鑫采商城地址!请核实用户填写地址city");
		}
		if (ObjectUtil.isEmpty(area)) {
			throw new BusinessException("未查询到鑫采商城地址!请核实用户填写地址area");
		}

		String address = commonProdQuery.getProvince() + commonProdQuery.getCity() + commonProdQuery.getArea() + Optional.ofNullable(commonProdQuery.getAddress()).orElse(StrUtil.EMPTY);
		// 可售性校验
		SaleCheckRequest saleCheckRequest = new SaleCheckRequest();
		saleCheckRequest.setProvinceId(province.getProvinceid());
		saleCheckRequest.setCityId(city.getCityid());
		saleCheckRequest.setCountyId(area.getAreaid());
		saleCheckRequest.setAddress(address);
		saleCheckRequest.setSkus(skuInfoDTOS);
		CommonResponse<List<CheckSaleResultDTO>> totalCheckResult = openPlatformProductService.saleCheck(saleCheckRequest);
		if (!Objects.equals(totalCheckResult.getResultCode(), ResultCodeEnum.NORMAL.getCode())) {
			throw new BusinessException("查询商品可售性错误状态码为" + totalCheckResult.getResultCode());
		}
		Map<String, CheckSaleResultDTO> totalCheckResultMap = totalCheckResult.getResult().stream().collect(Collectors.toMap(CheckSaleResultDTO::getSkuId, Function.identity()));

		// 库存校验
		StockRequest stockRequest = new StockRequest();
		stockRequest.setProvinceId(province.getProvinceid());
		stockRequest.setCityId(city.getCityid());
		stockRequest.setCountyId(area.getAreaid());
		stockRequest.setAddress(address);
		stockRequest.setSkus(skuInfoDTOS);
		CommonResponse<List<StockInfoDTO>> stockCheckResult = openPlatformProductService.getStock(stockRequest);

		if (!Objects.equals(stockCheckResult.getResultCode(), ResultCodeEnum.NORMAL.getCode())) {
			throw new BusinessException("查询商品库存错误状态码为" + stockCheckResult.getResultCode());
		}
		Map<String, StockInfoDTO> stockCheckResultMap = stockCheckResult.getResult().stream().collect(Collectors.toMap(StockInfoDTO::getSkuId, Function.identity()));


		// 处理校验结果
		StringBuilder errorMsg = new StringBuilder();
		for (cn.legendshop.product.api.dto.SkuInfoDTO skuInfoDTO : commonProdQuery.getSkuInfoDTOS()) {
			String skuId = skuInfoDTO.getSkuId();

			CheckSaleResultDTO totalCheck = totalCheckResultMap.get(skuId);
			StockInfoDTO stockCheck = stockCheckResultMap.get(skuId);

			boolean areaPurchaseFlag = totalCheck.getSaleState() == 1;
			boolean stockPurchaseFlag = OpenPlatformGlobalConstants.openHaveStock.contains(stockCheck.getStockState());

			CommonStockDTO.SkuPurchaseStatus skuPurchaseStatus = new CommonStockDTO.SkuPurchaseStatus();
			skuPurchaseStatus.setSkuId(skuId);
			skuPurchaseStatus.setAreaPurchaseFlag(areaPurchaseFlag);
			skuPurchaseStatus.setStockPurchaseFlag(stockPurchaseFlag);

			
			if (!areaPurchaseFlag) {
				errorMsg.append(String.format("商品%s不可售，%s；", skuId, totalCheck.getCause()));
			}
			if (!stockPurchaseFlag) {
				errorMsg.append(String.format("商品%s库存不满足购买数量，请联系运营人员处理；", skuId));
			}
			skuPurchaseStatusList.add(skuPurchaseStatus);
			commonStockDTO.setPurchaseFlag(areaPurchaseFlag && stockPurchaseFlag);
		}
		commonStockDTO.setMessage(errorMsg.toString());

		return commonStockDTO;

	}

	@Override
	public IndustrialProductDetailForErpDTO getProductDetail(String skuId, Long shopId, String source) {
		return super.getProductDetail(skuId, shopId, source);
	}

	@Override
	public Integer getDeliveryDay(IndustrialProduct industrialProduct, JdAddressDTO jdAddressDTO, Integer buyCount) {
		return null;
	}

	public JdAddressDTO getJDAddressFromAddress(String address) {
		return null;
	}

}
