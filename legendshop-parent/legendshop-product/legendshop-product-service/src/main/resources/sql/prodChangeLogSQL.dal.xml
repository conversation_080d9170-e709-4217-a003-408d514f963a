<?xml version="1.0" encoding="UTF-8"?>
<!--
 *  采用XML配置动态SQL用法
  * 动态参数替换原则
 * 1、每一行只有一对大括号{}，每个括号里有一个将要替换的参数
 * 2、用$$括起来的参数（key）将会用parameterMap中的value代替
 * 3、采用objectName.MethodName作为key放在parameterMap中
 * 4、用##括起来的参数(key)将会用parameterMap中的key对应的value代替，一直迭代到所有变量被替代完
 *
 * 动态参数替换用法
 * 1、{之后跟?,表示如果不传值的话这个条件忽略
 * 2、{之后紧跟!表示这个条件即使没有传值过来就采用默认值"",如果带有||则将||之前的作为默认值
 * 3、{之后直接是参数，有则整个替换，没有则忽略，例如： { $haveShop$ }h
 *
 -->

<DataAccessLayer>
	<BusinessObjects>
		<Object objectName="prodChangeLog">
			<Method name="createTable">
				<![CDATA[
					CREATE TABLE `{$logTableName$}` (
 						 `id` int(11) NOT NULL COMMENT '主键',
 						 `shop_id` int(11) DEFAULT NULL COMMENT '供应商ID',
  						 `shop_name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
 						 `sku_id` varchar(50) DEFAULT NULL COMMENT '商品sku',
 						 `before_price` DECIMAL(10, 2) DEFAULT NULL COMMENT '变更前价格',
    					 `after_price` DECIMAL(10, 2) DEFAULT NULL COMMENT '变更后价格',
    					 `price_change_rate` DECIMAL(10, 4) DEFAULT NULL COMMENT '价格变化比率（变更后价格 - 变更前价格）/ 变更前价格',
  						 `request_date` datetime DEFAULT NULL COMMENT '请求时间',
  						 PRIMARY KEY (`id`) USING BTREE
					) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
				]]>
			</Method>

			<Method name="page">
				<![CDATA[
				SELECT
					*
				FROM
					{$tableName$}
				WHERE
					1 = 1
					{? AND shop_id = $shopId$}
					{? AND sku_id LIKE CONCAT('%', $skuId$, '%')}
					{? AND price_change_rate >= $priceChangeRate$}
					{? AND request_date >= $beginTime$}
					{? AND request_date <= $endTime$}
				ORDER BY request_date DESC

				]]>
			</Method>

			<Method name="pageCount">
				<![CDATA[
				SELECT
					COUNT(*)
				FROM
					{$tableName$}
				WHERE
					1 = 1
					{? AND shop_id = $shopId$}
					{? AND sku_id LIKE CONCAT('%', $skuId$, '%')}
					{? AND price_change_rate >= $priceChangeRate$}
					{? AND request_date >= $beginTime$}
					{? AND request_date <= $endTime$}
				ORDER BY request_date DESC

				]]>
			</Method>

			<Method name="pageForTG">
				<![CDATA[
				SELECT
					*
				FROM
					{$tableName$}
				WHERE
					1 = 1
					AND sku_id in (select sku_id from test_mro.ls_prod_price_contacts where 1=1 {? AND account_book_id = $accountBookId$})
					{? AND shop_id = $shopId$}
					{? AND sku_id LIKE CONCAT('%', $skuId$, '%')}
					{? AND price_change_rate >= $priceChangeRate$}
					{? AND request_date >= $beginTime$}
					{? AND request_date <= $endTime$}
				ORDER BY request_date DESC

				]]>
			</Method>

			<Method name="pageForTGCount">
				<![CDATA[
				SELECT
					COUNT(*)
				FROM
					{$tableName$}
				WHERE
					1 = 1
					AND sku_id in (select sku_id from test_mro.ls_prod_price_contacts where 1=1 {? AND account_book_id = $accountBookId$})
					{? AND shop_id = $shopId$}
					{? AND sku_id LIKE CONCAT('%', $skuId$, '%')}
					{? AND price_change_rate >= $priceChangeRate$}
					{? AND request_date >= $beginTime$}
					{? AND request_date <= $endTime$}
				ORDER BY request_date DESC

				]]>
			</Method>

			<Method name="pageForUser">
				<![CDATA[
				SELECT
					*
				FROM
					{$tableName$}
				WHERE
					1 = 1
					{? AND shop_id = $shopId$}
					{? AND sku_id = $skuId$}
					{? AND request_date >= $beginTime$}
					{? AND request_date <= $endTime$}
				ORDER BY request_date

				]]>
			</Method>

			<Method name="pageForUserCount">
				<![CDATA[
				SELECT
					COUNT(*)
				FROM
					{$tableName$}
				WHERE
					1 = 1
					{? AND shop_id = $shopId$}
					{? AND sku_id = $skuId$}
					{? AND request_date >= $beginTime$}
					{? AND request_date <= $endTime$}
				ORDER BY request_date

				]]>
			</Method>
		</Object>
	</BusinessObjects>
</DataAccessLayer>
