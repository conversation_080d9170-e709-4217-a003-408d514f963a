package cn.legendshop.search.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.IsMroSelfEnum;
import cn.legendshop.common.core.enums.UserSourceTypeEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.dao.SystemParameterDao;
import cn.legendshop.common.model.entity.SystemParameter;
import cn.legendshop.common.rabbitmq.constants.ProductSearchMQConstant;
import cn.legendshop.common.rabbitmq.utils.AmqpSendMsgUtil;
import cn.legendshop.product.api.client.CategoryServiceClient;
import cn.legendshop.product.api.client.ThirdPartyProductPriceAdjustmentClient;
import cn.legendshop.product.api.dto.ProductPriceAdjustmentDTO;
import cn.legendshop.search.api.constant.SearchConstant;
import cn.legendshop.search.api.dto.CategoryDocumentDTO;
import cn.legendshop.search.api.dto.ResponseCategoryDTO;
import cn.legendshop.search.api.dto.SearchCategoryDTO;
import cn.legendshop.search.api.dto.ThirdProductDocumentDTO;
import cn.legendshop.search.api.enums.ThridProductStatusEnum;
import cn.legendshop.search.api.request.IndustrialCategorySearchRequest;
import cn.legendshop.search.api.request.IndustrialSearchRequest;
import cn.legendshop.search.api.vo.SearchResult;
import cn.legendshop.search.document.IndustrialProductDocument;
import cn.legendshop.search.document.IndustrialProductForErpDocument;
import cn.legendshop.search.repository.IndustrialProductRepository;
import cn.legendshop.search.service.IndustrialProductSearchService;
import cn.legendshop.search.service.converter.CategoryConverter;
import cn.legendshop.search.service.converter.IndustrialProductDocumentConverter;
import cn.legendshop.user.api.client.v2.AccountBookDetailClient;
import cn.legendshop.user.api.enums.AccountBookStatusEnum;
import cn.legendshop.user.api.vo.AccountBookDetailVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.legendshop.model.constant.OrderSourceEnum;
import com.legendshop.model.dto.request.ThirdProductSearchRequest;
import com.legendshop.model.entity.Category;
import com.legendshop.util.AppUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.*;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.ParsedComposite;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.UnmappedTerms;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.search.suggest.Suggest;
import org.elasticsearch.search.suggest.SuggestBuilder;
import org.elasticsearch.search.suggest.SuggestBuilders;
import org.elasticsearch.search.suggest.SuggestionBuilder;
import org.elasticsearch.search.suggest.completion.CompletionSuggestionBuilder;
import org.elasticsearch.search.suggest.completion.context.CategoryQueryContext;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.legendshop.product.api.enums.ThirdProductOnSaleStatusEnum.ON_SHELF;
import static cn.legendshop.search.api.constant.SearchConstant.*;
import static cn.legendshop.search.document.IndustrialProductDocument.*;

@Slf4j
@Service
@AllArgsConstructor
public class IndustrialProductSearchServiceImpl implements IndustrialProductSearchService {

	private final CategoryServiceClient categoryServiceClient;
	private final IndustrialProductDocumentConverter industrialProductDocumentConverter;
	private final ElasticsearchRestTemplate elasticsearchRestTemplate;
	private final IndustrialProductRepository industrialProductRepository;
	private final AmqpSendMsgUtil amqpSendMsgUtil;
	private final ThirdPartyProductPriceAdjustmentClient thirdPartyProductPriceAdjustmentClient;
	private final AccountBookDetailClient bookDetailClient;
	private final CategoryConverter categoryConverter;
	private final SystemParameterDao systemParameterDao;
	private static final int lowDistance = 6;
	private static final int highDistance = 18;

	@Override
	public SearchResult<ThirdProductDocumentDTO> ProductSearch(IndustrialSearchRequest request) {

		//simpleProductSearch用于匹配数据库与索引时使用，默认搜索不走这里。
		if (request.getFetchSourceFilter() != null) {
			return simpleProductSearch(request);
		}

		//默认搜只搜索ForMRO的商品
		request.setForMro(Boolean.TRUE);

		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(IndustrialProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			throw new BusinessException("索引不存在，请重建!");
		}
		log.debug("查询索引: {}", JSONUtil.toJsonStr(indexOperations.getSettings()));
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

		// 1、构建查询条件
		// 1.1.对搜索的结果进行过滤，返回页面需要参数

		//queryBuilder.withSourceFilter(new FetchSourceFilter(new String[]{"prodId", "productName", "brief", "skus", "brandId", "shopName", "sortPrice", "shopId"}, null));
		// 1.2.基本查询
		BoolQueryBuilder filterQueryBuilder = this.buildThirdPartyBasicQueryWithFilter(request);

		if (!UserSourceTypeEnum.IsMobileSource(request.getSource())) {
			// 1.3构建高亮查询
			List<String> highlightFieldList = CollUtil.toList("productName", "keyword");
			HighlightBuilder highlightBuilder = new HighlightBuilder();

			highlightFieldList.forEach(s -> {
				HighlightBuilder.Field field = new HighlightBuilder.Field(s).preTags("<em class='highlight text-cMain'>").postTags("</em>");
				highlightBuilder.field(field);
				highlightBuilder.requireFieldMatch(true); //如果该属性中有多个关键字 则都高亮
			});
			queryBuilder.withHighlightBuilder(highlightBuilder);// 名字高亮
		}


		// 1.4、品牌名称聚合
		String brandNameAgg = "brandNameAgg";
		TermsAggregationBuilder brandAgg = AggregationBuilders.terms(brandNameAgg).field("brandName.keyword").size(10000);
		queryBuilder.addAggregation(brandAgg);

		// 1.5、分类聚合
		String categoryAggName = "categoryAgg";
		queryBuilder.addAggregation(AggregationBuilders.terms(categoryAggName).field("mroCategoryId").size(10000));

		// 添加过滤条件，过滤账套不支持的shopId
		if (AppUtils.isNotBlank(request.getAccountBookId())) {
			R<List<AccountBookDetailVO>> listR = bookDetailClient.unauthorizedList(request.getAccountBookId());
			if (AppUtils.isNotBlank(listR) && listR.getSuccess() && AppUtils.isNotBlank(listR.getResult())) {
				filterQueryBuilder.mustNot(QueryBuilders.termsQuery("shopId", listR.getResult().stream().map(AccountBookDetailVO::getShopId).collect(Collectors.toList())));
			}
		}

		queryBuilder.withQuery(filterQueryBuilder);
		//排序
		buildSort(request, queryBuilder);

		// 1.4、分页
		queryBuilder.withPageable(PageRequest.of(request.getCurPage() - 1, request.getSize()));

		NativeSearchQuery searchQuery = queryBuilder.build();

		log.info("ProductSearch 搜索条件，{}", searchQuery.getQuery());
		log.info("ProductSearch 过滤条件，{}", searchQuery.getFilter());
		log.info("ProductSearch 高亮条件，{}", searchQuery.getHighlightBuilder());
		log.info("ProductSearch 排序，{}", searchQuery.getElasticsearchSorts());

		//放开ES搜索个数的限制
		searchQuery.setTrackTotalHits(Boolean.TRUE);
		log.debug("查询开始！date:{}", System.currentTimeMillis());
		SearchHits<IndustrialProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, IndustrialProductDocument.class);

		log.debug("查询结束！date:{}", System.currentTimeMillis());
		// 3、解析结果
		// 3.1、总条数和总页数
		long total = aggResult.getTotalHits();
		long totalPage = (total + request.getSize() - 1) / request.getSize();

		log.debug("查询总数，:共{} 条数据", total);
		if (total <= 0) {
			return new SearchResult<>();
		}

		Aggregations aggregations = aggResult.getAggregations();

		// 3.3、解析品牌
		assert aggregations != null;
		List<String> brandNames = getBrandNameAgg(aggregations.get(brandNameAgg));

		// 3.4、解析商品分类
		List<SearchCategoryDTO> categories = getCategoryAgg(aggregations.get(categoryAggName));

		// 3.5、对高亮的结果进行处理
		aggResult.getSearchHits().forEach(searchHit -> {
			Map<String, List<String>> highlightFieldsMap = searchHit.getHighlightFields();
			IndustrialProductDocument content = searchHit.getContent();
			if (CollUtil.isNotEmpty(highlightFieldsMap)) {
				// 高亮先去掉
				if (highlightFieldsMap.containsKey("productName")) {
					content.setProductName(highlightFieldsMap.get("productName").get(0));
				}
				if (highlightFieldsMap.containsKey("keyword")) {
					content.setKeyword(highlightFieldsMap.get("keyword").get(0));
				}
			}
		});

		//转换对象
		List<ThirdProductDocumentDTO> returnList = new ArrayList<>();
		List<ThirdProductDocumentDTO> thirdPartyProductDocumentList = aggResult.getSearchHits().stream()
			.map(SearchHit::getContent)
			.map(industrialProductDocumentConverter::to)
			.collect(Collectors.toList());
		Map<String, ThirdProductDocumentDTO> priceMap = thirdPartyProductDocumentList.stream().filter(u -> u.getProductCode() != null).collect(Collectors.toMap(ThirdProductDocumentDTO::getProductCode, e -> e));

		// 设置搜索结果的顺序1-thirdPartyProductDocumentList.size()
		AtomicInteger sortNo = new AtomicInteger(0);
		thirdPartyProductDocumentList.forEach(item -> {
			item.setSortNo(sortNo.getAndIncrement());
		});

		//商品和企业调价计算
		if (CollUtil.isNotEmpty(thirdPartyProductDocumentList)) {
			// 自营工业品不参与 价格计算
			List<ThirdProductDocumentDTO> thirdProductDocumentDTOS = thirdPartyProductDocumentList.stream().filter((item) -> !IsMroSelfEnum.THIRD_PROD.getType().equals(item.getIsMroSelf())).collect(Collectors.toList());
			returnList.addAll(thirdProductDocumentDTOS);
			//第三方工业品参与价格计算
			List<ThirdProductDocumentDTO> mroSelfThirdProductDocumentDTOS = thirdPartyProductDocumentList.stream().filter((item) -> IsMroSelfEnum.THIRD_PROD.getType().equals(item.getIsMroSelf())).collect(Collectors.toList());
			if (CollUtil.isNotEmpty(mroSelfThirdProductDocumentDTOS)) {
				ProductPriceAdjustmentDTO productPriceAdjustmentDTO = new ProductPriceAdjustmentDTO().setProdPriceAdjustmentData(industrialProductDocumentConverter.toCalculateProductPrice(mroSelfThirdProductDocumentDTOS));
				if (StrUtil.isNotBlank(request.getUserId())) {
					productPriceAdjustmentDTO.setUserId(request.getUserId());
				}
//				log.info(productPriceAdjustmentDTO.toString());
				R<List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData>> calculateProductPriceAdjustmentR = thirdPartyProductPriceAdjustmentClient.calculateProductPriceAdjustment(productPriceAdjustmentDTO);
				if (calculateProductPriceAdjustmentR.isSuccess() && CollUtil.isNotEmpty(calculateProductPriceAdjustmentR.getResult())) {
					List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData> result = calculateProductPriceAdjustmentR.getResult();
					for (ProductPriceAdjustmentDTO.ProductPriceAdjustmentData productPriceAdjustmentData : result) {
						ThirdProductDocumentDTO thirdProductDocumentDTO = priceMap.get(productPriceAdjustmentData.getSkuId());
//						log.info(productPriceAdjustmentData.getProductId() + "### supplierSalePrice " + thirdProductDocumentDTO.getTotalPrice());
						thirdProductDocumentDTO.setOriginalPrice(thirdProductDocumentDTO.getSupplierSalePrice());
						thirdProductDocumentDTO.setSupplierSalePrice(productPriceAdjustmentData.getTotalPrice());
						returnList.add(thirdProductDocumentDTO);
					}
				}
			}

		}

		// 结果按照搜索的初始顺序排序，防止处理后的结果顺序错乱
		returnList.sort(Comparator.comparing(ThirdProductDocumentDTO::getSortNo));

		try {
			sendSearchHistory(request);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		if (request.getDescending() != null) {
//			log.info("#################  returnList" + JSONUtil.toJsonStr(returnList));
			returnList.sort((o1, o2) -> {
				if (!request.getDescending()) {
					return o1.getSupplierSalePrice().compareTo(o2.getSupplierSalePrice());
				} else {
					return o2.getSupplierSalePrice().compareTo(o1.getSupplierSalePrice());
				}
			});
		}
		return new SearchResult<>(total, totalPage, returnList, categories, brandNames, request.getSize(), request.getCurPage());
	}


	/**
	 * 简单搜索，用于对比索引和数据库差异
	 *
	 * @param request
	 * @return
	 */
	private SearchResult<ThirdProductDocumentDTO> simpleProductSearch(IndustrialSearchRequest request) {
		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(IndustrialProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			throw new BusinessException("索引不存在，请重建!");
		}
		log.debug("查询索引: {}", JSONUtil.toJsonStr(indexOperations.getSettings()));
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

		// 1、构建查询条件
		// 1.1.对搜索的结果进行过滤，返回页面需要参数
		if (request.getFetchSourceFilter() != null) {
			queryBuilder.withSourceFilter(new FetchSourceFilter(request.getFetchSourceFilter(), null));
		}
		//queryBuilder.withSourceFilter(new FetchSourceFilter(new String[]{"prodId", "productName", "brief", "skus", "brandId", "shopName", "sortPrice", "shopId"}, null));
		// 1.2.基本查询
		BoolQueryBuilder filterQueryBuilder = this.buildThirdPartyBasicQueryWithFilter(request);

		queryBuilder.withQuery(filterQueryBuilder);
		//排序
		buildSort(request, queryBuilder);

		// 1.4、分页
		queryBuilder.withPageable(PageRequest.of(request.getCurPage() - 1, request.getSize()));

		NativeSearchQuery searchQuery = queryBuilder.build();
		//放开ES搜索个数的限制
		searchQuery.setTrackTotalHits(Boolean.TRUE);
		log.debug("查询开始！date:{}", System.currentTimeMillis());
		SearchHits<IndustrialProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, IndustrialProductDocument.class);

		log.debug("查询结束！date:{}", System.currentTimeMillis());
		// 3、解析结果
		// 3.1、总条数和总页数
		long total = aggResult.getTotalHits();
		long totalPage = (total + request.getSize() - 1) / request.getSize();

		log.debug("查询总数，:共{} 条数据", total);
		if (total <= 0) {
			return new SearchResult<>();
		}
		//转换对象
		List<ThirdProductDocumentDTO> thirdPartyProductDocumentList = aggResult.getSearchHits().stream()
			.map(SearchHit::getContent)
			.map(industrialProductDocumentConverter::to)
			.collect(Collectors.toList());

		try {
			sendSearchHistory(request);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return new SearchResult<>(total, totalPage, thirdPartyProductDocumentList, null, null, request.getSize(), request.getCurPage());
	}


	@Override
	public ThirdProductDocumentDTO views(IndustrialSearchRequest request) {
		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(IndustrialProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			throw new BusinessException("索引不存在，请重建!");
		}

		IndustrialProductDocument industrialProductDocument = null;
		if (request.getId() != null) {
			industrialProductDocument = industrialProductRepository.getById(request.getId());
		}

		if (request.getProdId() != null) {
			industrialProductDocument = industrialProductRepository.getByProdId(request.getProdId());
		}
		if (request.getProductCode() != null ){
			industrialProductDocument = industrialProductRepository.getByProductCode(request.getProductCode());
		}

		ThirdProductDocumentDTO thirdPartyProductDocument = industrialProductDocumentConverter.to(industrialProductDocument);

		if (ObjectUtil.isEmpty(thirdPartyProductDocument)) {
			log.info("转换失败，请检查： {}", request.getProdId());
		}

		//商品和企业调价计算
		if (ObjectUtil.isNotEmpty(thirdPartyProductDocument)) {
			thirdPartyProductDocument.setThirdPlatformPrice(thirdPartyProductDocument.getTotalPrice());
			ProductPriceAdjustmentDTO productPriceAdjustmentDTO = new ProductPriceAdjustmentDTO().setProdPriceAdjustmentData
				(CollUtil.newArrayList(industrialProductDocumentConverter.toCalculateProductPrice(thirdPartyProductDocument)));
			if (StrUtil.isNotBlank(request.getUserId())) {
				productPriceAdjustmentDTO.setUserId(request.getUserId());
			}
			R<List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData>> calculateProductPriceAdjustmentR = thirdPartyProductPriceAdjustmentClient.calculateProductPriceAdjustment(productPriceAdjustmentDTO);
			if (calculateProductPriceAdjustmentR.isSuccess() && CollUtil.isNotEmpty(calculateProductPriceAdjustmentR.getResult())) {

				List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData> result = calculateProductPriceAdjustmentR.getResult();
				ProductPriceAdjustmentDTO.ProductPriceAdjustmentData productPriceAdjustmentData1 = result.stream().findFirst().get();
				//调价之后的价格
				thirdPartyProductDocument.setPriceAdjustment(productPriceAdjustmentData1.getTotalPrice());
			}
		}
		return thirdPartyProductDocument;
	}

	@Override
	public SearchResult<ThirdProductDocumentDTO> ProductForErpSearch(ThirdProductSearchRequest request) {
		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(IndustrialProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			return null;
		}
		log.info("查询索引: {}", JSONUtil.toJsonStr(indexOperations.getSettings()));
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

		// 1、构建查询条件
		// 1.1.对搜索的结果进行过滤，返回页面需要参数
		//queryBuilder.withSourceFilter(new FetchSourceFilter(new String[]{"prodId", "productName", "brief", "skus", "brandId", "shopName", "sortPrice", "shopId"}, null));
		// 1.2.基本查询
		BoolQueryBuilder filterQueryBuilder = this.buildObeiBasicQueryWithFilter(request);

		//"should" : [
		//        {
		//          "match_phrase" : {"productName" : {"query" : "电机"}}
		//        },


		// 1.3构建高亮查询
		List<String> highlightFieldList = CollUtil.toList("productName", "keyword");
		HighlightBuilder highlightBuilder = new HighlightBuilder();

		highlightFieldList.forEach(s -> {
			HighlightBuilder.Field field = new HighlightBuilder.Field(s).preTags("<span class='highlight text-unadmin-main'>").postTags("</span>");
			highlightBuilder.field(field);
			highlightBuilder.requireFieldMatch(true); //如果该属性中有多个关键字 则都高亮
		});
		queryBuilder.withHighlightBuilder(highlightBuilder);// 名字高亮

		// 1.4、品牌名称聚合
		String brandNameAgg = "brandNameAgg";
		TermsAggregationBuilder brandAgg = AggregationBuilders.terms(brandNameAgg).field("brandName.keyword").size(10000);
		queryBuilder.addAggregation(brandAgg);

		//供货价
		buildFilter(request, filterQueryBuilder);

		queryBuilder.withQuery(filterQueryBuilder);
		//排序
		buildSort(request, queryBuilder);

		// 1.4、分页
		queryBuilder.withPageable(PageRequest.of(request.getPage() - 1, request.getSize()));

		NativeSearchQuery searchQuery = queryBuilder.build();
		log.info("ProductForErpSearch 搜索条件，{}", searchQuery.getQuery());
		log.info("ProductForErpSearch 过滤条件，{}", searchQuery.getFilter());
		log.info("ProductForErpSearch 高亮条件，{}", searchQuery.getHighlightBuilder());
		log.info("ProductForErpSearch 排序，{}", searchQuery.getElasticsearchSorts());
		//放开ES搜索个数的限制
		searchQuery.setTrackTotalHits(true);

		log.info("查询开始！date:{}", System.currentTimeMillis());
		SearchHits<IndustrialProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, IndustrialProductDocument.class);

		log.info("查询结束！date:{}", System.currentTimeMillis());
		// 3、解析结果
		// 3.1、总条数和总页数
		long total = aggResult.getTotalHits();
		long totalPage = (total + request.getSize() - 1) / request.getSize();

		log.info("查询总数，:共{} 条数据", total);
		if (total <= 0) {
			return null;
		}

		Aggregations aggregations = aggResult.getAggregations();

		// 3.3、解析品牌
		assert aggregations != null;
		List<String> brandNames = getBrandNameAgg(aggregations.get(brandNameAgg));


		//对高亮的结果进行处理
		aggResult.getSearchHits().forEach(searchHit -> {
			Map<String, List<String>> highlightFieldsMap = searchHit.getHighlightFields();
			IndustrialProductDocument content = searchHit.getContent();
			if (CollUtil.isNotEmpty(highlightFieldsMap)) {
				// 高亮先去掉
				if (highlightFieldsMap.containsKey("productName")) {
					content.setProductName(highlightFieldsMap.get("productName").get(0));
				}
				if (highlightFieldsMap.containsKey("keyword")) {
					content.setKeyword(highlightFieldsMap.get("keyword").get(0));
				}
			}
		});

		//转换对象
		List<ThirdProductDocumentDTO> industrialProductDocumentList = aggResult.getSearchHits().stream()
			.map(SearchHit::getContent)
			.map(industrialProductDocumentConverter::to)
			.collect(Collectors.toList());
		try {
			sendSearchHistory(request);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		return new SearchResult<>(total, totalPage, industrialProductDocumentList, null, null, brandNames, null, null, request.getSize(), request.getPage());
	}

	@Override
	public SearchResult<ThirdProductDocumentDTO> ProductSearchWithNoRestrictions(IndustrialSearchRequest request) {

		//simpleProductSearch用于匹配数据库与索引时使用，默认搜索不走这里。
		if (request.getFetchSourceFilter() != null) {
			return simpleProductSearch(request);
		}

		//默认搜只搜索ForMRO的商品
		request.setForMro(Boolean.TRUE);

		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(IndustrialProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			throw new BusinessException("索引不存在，请重建!");
		}
		log.info("查询索引: {}", JSONUtil.toJsonStr(indexOperations.getSettings()));
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

		// 1、构建查询条件
		// 1.1.对搜索的结果进行过滤，返回页面需要参数

		//queryBuilder.withSourceFilter(new FetchSourceFilter(new String[]{"prodId", "productName", "brief", "skus", "brandId", "shopName", "sortPrice", "shopId"}, null));
		// 1.2.基本查询
		BoolQueryBuilder filterQueryBuilder = this.baseQueryFilter(request);

		if (!UserSourceTypeEnum.IsMobileSource(request.getSource())) {
			// 1.3构建高亮查询
			List<String> highlightFieldList = CollUtil.toList("productName", "keyword");
			HighlightBuilder highlightBuilder = new HighlightBuilder();

			highlightFieldList.forEach(s -> {
				HighlightBuilder.Field field = new HighlightBuilder.Field(s).preTags("<em class='highlight text-cMain'>").postTags("</em>");
				highlightBuilder.field(field);
				highlightBuilder.requireFieldMatch(true); //如果该属性中有多个关键字 则都高亮
			});
			queryBuilder.withHighlightBuilder(highlightBuilder);// 名字高亮
		}


		// 1.4、品牌名称聚合
		String brandNameAgg = "brandNameAgg";
		TermsAggregationBuilder brandAgg = AggregationBuilders.terms(brandNameAgg).field("brandName.keyword").size(10000);
		queryBuilder.addAggregation(brandAgg);

		// 1.5、分类聚合
		String categoryAggName = "categoryAgg";
		queryBuilder.addAggregation(AggregationBuilders.terms(categoryAggName).field("mroCategoryId").size(10000));

		queryBuilder.withQuery(filterQueryBuilder);
		//排序
		buildSort(request, queryBuilder);

		// 1.4、分页
		queryBuilder.withPageable(PageRequest.of(request.getCurPage() - 1, request.getSize()));

		NativeSearchQuery searchQuery = queryBuilder.build();
		log.info("ProductSearchWithNoRestrictions 搜索条件，{}", searchQuery.getQuery());
		log.info("ProductSearchWithNoRestrictions 过滤条件，{}", searchQuery.getFilter());
		log.info("ProductSearchWithNoRestrictions 高亮条件，{}", searchQuery.getHighlightBuilder());
		log.info("ProductSearchWithNoRestrictions 排序，{}", searchQuery.getElasticsearchSorts());
		//放开ES搜索个数的限制
		searchQuery.setTrackTotalHits(Boolean.TRUE);
		log.info("查询开始！date:{}", System.currentTimeMillis());
		SearchHits<IndustrialProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, IndustrialProductDocument.class);

		log.info("查询结束！date:{}", System.currentTimeMillis());
		// 3、解析结果
		// 3.1、总条数和总页数
		long total = aggResult.getTotalHits();
		long totalPage = (total + request.getSize() - 1) / request.getSize();

		log.info("查询总数，:共{} 条数据", total);
		if (total <= 0) {
			return new SearchResult<>();
		}

		Aggregations aggregations = aggResult.getAggregations();

		// 3.3、解析品牌
		assert aggregations != null;
		List<String> brandNames = getBrandNameAgg(aggregations.get(brandNameAgg));

		// 3.4、解析商品分类
		List<SearchCategoryDTO> categories = getCategoryAgg(aggregations.get(categoryAggName));

		// 3.5、对高亮的结果进行处理
		aggResult.getSearchHits().forEach(searchHit -> {
			Map<String, List<String>> highlightFieldsMap = searchHit.getHighlightFields();
			IndustrialProductDocument content = searchHit.getContent();
			if (CollUtil.isNotEmpty(highlightFieldsMap)) {
				// 高亮先去掉
				if (highlightFieldsMap.containsKey("productName")) {
					content.setProductName(highlightFieldsMap.get("productName").get(0));
				}
				if (highlightFieldsMap.containsKey("keyword")) {
					content.setKeyword(highlightFieldsMap.get("keyword").get(0));
				}
			}
		});

		//转换对象
		List<ThirdProductDocumentDTO> returnList = new ArrayList<>();
		List<ThirdProductDocumentDTO> thirdPartyProductDocumentList = aggResult.getSearchHits().stream()
			.map(SearchHit::getContent)
			.map(industrialProductDocumentConverter::to)
			.collect(Collectors.toList());
		Map<Long, ThirdProductDocumentDTO> priceMap = thirdPartyProductDocumentList.stream().filter(u -> u.getProdId() != null).collect(Collectors.toMap(ThirdProductDocumentDTO::getProdId, e -> e));

		try {
			sendSearchHistory(request);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		if (request.getDescending() != null) {
			returnList.sort((o1, o2) -> {
				if (!request.getDescending()) {
					return o1.getSupplierSalePrice().compareTo(o2.getSupplierSalePrice());
				} else {
					return o2.getSupplierSalePrice().compareTo(o1.getSupplierSalePrice());
				}
			});
		}
		return new SearchResult<>(total, totalPage, returnList, categories, brandNames, request.getSize(), request.getCurPage());
	}

	@Override
	public CategoryDocumentDTO searchExistProductCategory(IndustrialCategorySearchRequest request) throws JsonProcessingException {
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

		// 1、构建查询条件
		// 1.1.对搜索的结果进行过滤，返回页面需要参数
		queryBuilder.withSourceFilter(new FetchSourceFilter(new String[]{"mroFirstCategoryId", "mroFirstCategoryName", "mroSecondCategoryId", "mroSecondCategoryName", "mroSecondCategoryCode", "mroThirdCategoryId", "mroThirdCategoryName", "mroThirdCategoryCode"}, null));

		BoolQueryBuilder filterQueryBuilder = QueryBuilders.boolQuery();

		//根据名称模糊搜索
		if (StrUtil.isNotBlank(request.getSearchCategoryName())) {
			if (Objects.nonNull(request.getLevel())) {
				//层级不为空，筛指定层级
				if (request.getLevel() >= SearchConstant.FIRST_FLOOR && request.getLevel() <= SearchConstant.THIRD_FLOOR) {
					String categoryField = getCategoryField(request.getLevel());
					addCategoryFilter(request.getSearchCategoryName(), filterQueryBuilder, categoryField, Boolean.TRUE);
				}
			} else {
				for (Long level = SearchConstant.FIRST_FLOOR; level <= SearchConstant.THIRD_FLOOR; level++) {
					String categoryField = getCategoryField(level);
					addCategoryFilter(request.getSearchCategoryName(), filterQueryBuilder, categoryField, Boolean.TRUE);
				}
			}
		}

		//根据类目ID精准搜索
		if (AppUtils.isNotBlank(request.getCategoryId())) {
			if (Objects.nonNull(request.getLevel())) {
				//层级不为空，筛指定层级
				if (request.getLevel() >= 0L && request.getLevel() <= SearchConstant.THIRD_FLOOR) {
					String categoryIdField = getCategoryIdField(request.getLevel());
					filterQueryBuilder.must(QueryBuilders.termQuery(categoryIdField, request.getCategoryId()));
					addCategoryFilter(request.getCategoryId(), filterQueryBuilder, categoryIdField, Boolean.FALSE);
				}
			} else {
				for (Long level = SearchConstant.FIRST_FLOOR; level <= SearchConstant.THIRD_FLOOR; level++) {
					String categoryIdField = getCategoryIdField(level);
					filterQueryBuilder.must(QueryBuilders.termQuery(categoryIdField, request.getCategoryId()));
					addCategoryFilter(request, filterQueryBuilder, categoryIdField, Boolean.FALSE);
				}
			}
		}

		//根据类目Code精准搜索 todo 直接调商品服务

		//父Id不为空
		if (AppUtils.isNotBlank(request.getParentId())) {

			//前端逻辑点击第一层，我们实际获取的是第二层
			filterQueryBuilder.must(QueryBuilders.termQuery(getCategoryIdField(request.getLevel()), request.getParentId()));
			request.setLevel(request.getLevel() + 1);
		}
		queryBuilder.withQuery(filterQueryBuilder);

		//装入店铺ID
		filterQueryBuilder.must(QueryBuilders.termQuery(SHOP_ID, request.getShopId()));
		filterQueryBuilder.must(QueryBuilders.termQuery("mroStatus", ON_SHELF.getMroStatus()));

		String categoryAgg = "unique_category";
		List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();
		sources.add(new TermsValuesSourceBuilder(MRO_FIRST_CATEGORY_NAME).field(MRO_FIRST_CATEGORY_NAME + SearchConstant.KEYWORD));
		sources.add(new TermsValuesSourceBuilder(MRO_SECOND_CATEGORY_NAME).field(MRO_SECOND_CATEGORY_NAME + SearchConstant.KEYWORD));
		sources.add(new TermsValuesSourceBuilder(MRO_THIRD_CATEGORY_NAME).field(MRO_THIRD_CATEGORY_NAME + SearchConstant.KEYWORD));
		sources.add(new TermsValuesSourceBuilder(MRO_FIRST_CATEGORY_Id).field(MRO_FIRST_CATEGORY_Id));
		sources.add(new TermsValuesSourceBuilder(MRO_SECOND_CATEGORY_Id).field(MRO_SECOND_CATEGORY_Id));
		sources.add(new TermsValuesSourceBuilder(MRO_THIRD_CATEGORY_Id).field(MRO_THIRD_CATEGORY_Id));
		sources.add(new TermsValuesSourceBuilder(MRO_FIRST_CATEGORY_CODE).field(MRO_FIRST_CATEGORY_CODE + SearchConstant.KEYWORD));
		sources.add(new TermsValuesSourceBuilder(MRO_SECOND_CATEGORY_CODE).field(MRO_SECOND_CATEGORY_CODE + SearchConstant.KEYWORD));
		sources.add(new TermsValuesSourceBuilder(MRO_THIRD_CATEGORY_CODE).field(MRO_SECOND_CATEGORY_CODE + SearchConstant.KEYWORD));
		queryBuilder.addAggregation(new CompositeAggregationBuilder(categoryAgg, sources));

		NativeSearchQuery searchQuery = queryBuilder.build();
		log.info("##################  查询开始！date:{}", System.currentTimeMillis());
		log.info("##################  搜索条件，{}", searchQuery.getQuery());
		log.info("##################  过滤条件，{}", searchQuery.getFilter());
		log.info("##################  排序，{}", searchQuery.getElasticsearchSorts());
		log.info(JSONUtil.toJsonStr(searchQuery));
		SearchHits<IndustrialProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, IndustrialProductDocument.class);

		log.info("查询结束！date:{}", System.currentTimeMillis());
		// 3、解析结果
		// 3.1、总条数和总页数
		long total = aggResult.getTotalHits();
		log.info("查询总数，:共{} 条数据", total);

//		aggResult.getAggregations().get(categoryAgg);
//		if(AppUtils.isBlank(aggregationMap.keySet())){
//			return new SearchResult<>();
//		}

		List<ResponseCategoryDTO> categoryAttr = getCategoryAttr(aggResult.getAggregations().get(categoryAgg));
		CategoryDocumentDTO categoryDocumentDTO = new CategoryDocumentDTO();
		categoryDocumentDTO.setLevel(request.getLevel());
		categoryDocumentDTO.setShopId(request.getShopId());
		if (CollUtil.isEmpty(categoryAttr)) {
			return categoryDocumentDTO;
		}
		if (AppUtils.isBlank(request.getLevel()) || request.getLevel().equals(0L)) {
			categoryDocumentDTO.setCategoryList(categoryAttr.stream().map(categoryConverter::toFisrt).distinct().collect(Collectors.toList()));
		}

		if (request.getLevel().equals(FIRST_FLOOR)) {
			categoryDocumentDTO.setCategoryList(categoryAttr.stream().map(categoryConverter::toFisrt).distinct().collect(Collectors.toList()));
		}

		if (AppUtils.isNotBlank(request.getLevel()) && request.getLevel().equals(SECOND_FLOOR)) {
			categoryDocumentDTO.setCategoryList(categoryAttr.stream().map(categoryConverter::toSecond).distinct().collect(Collectors.toList()));
		}

		if (AppUtils.isNotBlank(request.getLevel()) && request.getLevel().equals(THIRD_FLOOR)) {
			categoryDocumentDTO.setCategoryList(categoryAttr.stream().map(categoryConverter::toThird).distinct().collect(Collectors.toList()));
		}

		return categoryDocumentDTO;
	}

	/**
	 * 获取品牌聚合
	 *
	 * @param parsedComposite
	 * @return
	 */
	private List<ResponseCategoryDTO> getCategoryAttr(ParsedComposite parsedComposite) throws JsonProcessingException {

		//解析terms获取聚合的品牌名称
//		aggResult.getAggregations().aggregations.get(0);

		List<ResponseCategoryDTO> commonCategoryDTOS = new ArrayList<>();
		List<ParsedComposite.ParsedBucket> buckets = parsedComposite.getBuckets();
		for (ParsedComposite.ParsedBucket bucket : buckets) {
			String keyAsString = bucket.getKeyAsString();

			// 将字符串格式转换为 JSON 格式
			String jsonString = keyAsString.replace("{", "{\"").replace("=", "\":\"").replace(", ", "\",\"").replace("}", "\"}");

			ObjectMapper objectMapper = new ObjectMapper();
			// 转换成实体
			ResponseCategoryDTO responseCategoryDTO = objectMapper.readValue(jsonString, ResponseCategoryDTO.class);
			commonCategoryDTOS.add(responseCategoryDTO);
		}

		return commonCategoryDTOS;
	}

	private void addCategoryFilter(Object keyword, BoolQueryBuilder filterQueryBuilder, String categoryField, Boolean flag) {
		MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(categoryField, keyword);
		if (flag) {
			matchQueryBuilder.fuzziness("AUTO");
		}
		filterQueryBuilder.should(matchQueryBuilder);
	}

	private String getCategoryField(Long level) {
		switch (level.intValue()) {
			case 0:
			case 1:
				return MRO_FIRST_CATEGORY_NAME;
			case 2:
				return IndustrialProductDocument.MRO_SECOND_CATEGORY_NAME;
			case 3:
				return IndustrialProductDocument.MRO_THIRD_CATEGORY_NAME;
			default:
				throw new IllegalArgumentException("Invalid category level");
		}
	}

	private String getCategoryIdField(Long level) {
		switch (level.intValue()) {
			case 0:
			case 1:
				return IndustrialProductDocument.MRO_FIRST_CATEGORY_Id;
			case 2:
				return IndustrialProductDocument.MRO_SECOND_CATEGORY_Id;
			case 3:
				return IndustrialProductDocument.MRO_THIRD_CATEGORY_Id;
			default:
				throw new IllegalArgumentException("Invalid category level");
		}
	}

	private String getCategoryCodeField(Long level) {
		switch (level.intValue()) {
			case 0:
			case 1:
				return IndustrialProductDocument.MRO_FIRST_CATEGORY_CODE;
			case 2:
				return IndustrialProductDocument.MRO_SECOND_CATEGORY_CODE;
			case 3:
				return IndustrialProductDocument.MRO_THIRD_CATEGORY_CODE;
			default:
				throw new IllegalArgumentException("Invalid category level");
		}
	}


	private BoolQueryBuilder baseQueryFilter(IndustrialSearchRequest request) {
		BoolQueryBuilder filterQueryBuilder = QueryBuilders.boolQuery();
		// 基本查询条件

		//2023-05-25 过滤价格为负数的数据
		filterQueryBuilder.must(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).gt(BigDecimal.ZERO));

		if (StrUtil.isBlank(request.getKey())) {
			log.info("搜索全部商品");
			filterQueryBuilder.must(QueryBuilders.matchAllQuery());
		} else {
			// 多字段匹配且设置权重
			MultiMatchQueryBuilder multiMatchQuery = QueryBuilders.multiMatchQuery(request.getKey().trim(), IndustrialProductForErpDocument.PRODUCT_CODE, IndustrialProductForErpDocument.PRODUCT_NAME, IndustrialProductForErpDocument.KEYWORD)
					.field(IndustrialProductForErpDocument.PRODUCT_CODE, 3.0f)
					.field(IndustrialProductForErpDocument.PRODUCT_NAME, 1.0f)
					.field(IndustrialProductForErpDocument.KEYWORD, 1.0f)
					.operator(Operator.AND).fuzziness("AUTO" + StrUtil.COLON + lowDistance + StrUtil.COMMA + highDistance);

			// 添加查询到过滤器中
			filterQueryBuilder.must(multiMatchQuery);
//			Pattern pattern = Pattern.compile("^[a-zA-Z_0-9]{6,20}");
//			Matcher matcher = pattern.matcher(request.getKey());
//
//
//			if (matcher.matches() || request.getKey().trim().contains(StrUtil.SPACE)) {
//				log.info("商名名称有空格，分词匹配！");
//				filterQueryBuilder.must(QueryBuilders.matchQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey()).operator(Operator.AND));
//			} else {
//				log.info("商名名称没有空格，不分词精准匹配！");
//				MatchPhraseQueryBuilder productNameMatchPhraseQueryBuilder;
//				productNameMatchPhraseQueryBuilder = QueryBuilders.matchPhraseQuery("productName", request.getKey());
//				filterQueryBuilder.must(productNameMatchPhraseQueryBuilder);
//			}
//
//			// 短词查询
//			MatchPhraseQueryBuilder productName;
//			for (String s : request.getKey().split(StrUtil.SPACE)) {
//				productName = QueryBuilders.matchPhraseQuery("productName", s);
//				filterQueryBuilder.should(productName).boost(3f);
//			}

		}

		if (ObjectUtil.isNotNull(request.getStatus())) {
			filterQueryBuilder.filter(QueryBuilders.termQuery("status", request.getStatus()));
		}

		if (CollectionUtil.isNotEmpty(request.getProdIds())) {
			filterQueryBuilder.filter(QueryBuilders.termsQuery("prodId", request.getProdIds()));
		}

		//品牌名称
		if (CollUtil.isNotEmpty(request.getBrandNames())) {
			filterQueryBuilder.must(QueryBuilders.termsQuery("brandName.keyword", request.getBrandNames()));
		}

		// mro 类目匹配
		if (ObjectUtil.isNotNull(request.getCategoryId())) {
//			filterQueryBuilder.must(QueryBuilders.termQuery("mroCategoryId", request.getCategoryId()));
			filterQueryBuilder.must(QueryBuilders.multiMatchQuery(request.getCategoryId(), "mroFirstCategoryId", "mroSecondCategoryId", "mroThirdCategoryId"));
		}

		// 商家id
		if (ObjectUtil.isNotNull(request.getShopId())) {
			filterQueryBuilder.must(QueryBuilders.termQuery("shopId", request.getShopId()));
		}

		// 商家名称
		if (ObjectUtil.isNotNull(request.getShopName())) {
			filterQueryBuilder.must(QueryBuilders.matchQuery("shopName", request.getShopName()));
		}

		// 商品skuId精准搜索
		if (StrUtil.isNotBlank(request.getProductCode())) {
			filterQueryBuilder.must(QueryBuilders.termQuery(IndustrialProductForErpDocument.PRODUCT_CODE_KEYWORD, request.getProductCode()));
		}

		if (ObjectUtil.isNotNull(request.getProdType())) {
			filterQueryBuilder.must(QueryBuilders.termQuery("prodType", request.getProdType()));
		}

		if (ObjectUtil.isNotNull(request.getThirdPlatform())) {
			filterQueryBuilder.must(QueryBuilders.termQuery("supplier", request.getThirdPlatform()));
		}

		//价格匹配
		if (CollUtil.isNotEmpty(request.getPriceInterval())) {
			RangeQueryBuilder price = QueryBuilders.rangeQuery(ThirdProductDocumentDTO.SUPPLIER_SALE_PRICE);
			if (request.getPriceInterval().size() >= 1) {
				price.gte(request.getPriceInterval().get(0));
			}
			if (request.getPriceInterval().size() == 2) {
				price.lte(request.getPriceInterval().get(1));
			}


			BoolQueryBuilder priceBuilder = QueryBuilders.boolQuery();
			priceBuilder.should(price);
			filterQueryBuilder.must(priceBuilder);
		}

		return filterQueryBuilder;
	}

	public boolean containsChinese(String input) {
		return input.chars().anyMatch(c -> Character.toString((char) c).matches("[\\u4e00-\\u9fa5]"));
	}


	private BoolQueryBuilder buildThirdPartyBasicQueryWithFilter(IndustrialSearchRequest request) {
		BoolQueryBuilder filterQueryBuilder = QueryBuilders.boolQuery();

		// 基本查询条件

		//2023-05-25 过滤价格为负数的数据
		filterQueryBuilder.must(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).gt(BigDecimal.ZERO));

		// 过滤MRO 采购系统配置搜索关键词
		SystemParameter searchExcludedKeywordsMro = systemParameterDao.getSystemParameterByName("SEARCH_EXCLUDED_KEYWORDS_MRO");
		// 添加过滤条件：要求关键词中不包含 excludedKeywords 中的任何一个关键词
		if (ObjectUtil.isNotEmpty(searchExcludedKeywordsMro)) {
			// 逗号分隔，拆分成一个list<String>
			String value = searchExcludedKeywordsMro.getValue();
			if (StrUtil.isNotBlank(value)) {
				String[] excludedKeywords = value.split(StrUtil.COMMA);
				for (String excludedKeyword : excludedKeywords) {
					// 使用matchPhraseQuery排除包含特定关键词的搜索结果
					filterQueryBuilder.mustNot(QueryBuilders.matchPhraseQuery(IndustrialProductForErpDocument.KEYWORD, excludedKeyword.trim()));
				}
			}
		}


		if (StrUtil.isBlank(request.getKey())) {
			log.info("搜索全部商品");
			filterQueryBuilder.must(QueryBuilders.matchAllQuery());
		} else {
			MultiMatchQueryBuilder multiMatchQuery = QueryBuilders.multiMatchQuery(request.getKey().trim(), IndustrialProductForErpDocument.PRODUCT_CODE, IndustrialProductForErpDocument.PRODUCT_NAME, IndustrialProductForErpDocument.KEYWORD)
					.field(IndustrialProductForErpDocument.PRODUCT_CODE, 3.0f)
					.field(IndustrialProductForErpDocument.PRODUCT_NAME, 1.0f)
					.field(IndustrialProductForErpDocument.KEYWORD, 1.0f)
					.operator(Operator.AND).fuzziness("AUTO" + StrUtil.COLON + lowDistance + StrUtil.COMMA + highDistance);

			// 添加查询到过滤器中
			filterQueryBuilder.must(multiMatchQuery);
//				if (!request.getKey().trim().contains(StrUtil.SPACE)) {
//				log.info("商名名称没有空格，不分词精准匹配！");
//
//				//1. 判断是否包含中文
//				if (containsChinese(request.getKey())) {
//					log.info("包含中文，使用短词匹配");
//					filterQueryBuilder.must(QueryBuilders.matchPhraseQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey().trim()));
//				}
//
//				//(?!xxxx) 是正则表达式的负向零宽断言一种形式，标识预该位置后不是xxxx字符。
//				//^ 匹配一行的开头位置
//				//(?![0-9]+) 预测该位置后面不全是数字 (?![a-zA-Z]+) 预测该位置后面不全是字母
//				//[0-9A-Za-z] {8,16} 由8-16位数字或这字母组成
//				//^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,10}$
//				//这里用的是最简单的匹配，目前三家供应商最短是6位，所以展示先设6-20
//				//$ 匹配行结尾位置
//				//"^[a-zA-Z_0-9]+$"
//				Pattern pattern = Pattern.compile("^[a-zA-Z_0-9]{6,20}");
//				Matcher matcher = pattern.matcher(request.getKey().trim());
//				//只含字母
//				Pattern alphabetPattern = Pattern.compile("^[a-zA-Z]+$\n");
//				Matcher alphabetMatcher = alphabetPattern.matcher(request.getKey().trim());
//
//				Pattern numAlphabetPattern = Pattern.compile("^\\d.*\\d$|^\\d.*[a-zA-Z]$|^[a-zA-Z].*\\d$\n");
//				Matcher numAlphabetMatcher = numAlphabetPattern.matcher(request.getKey().trim());
//
//				BoolQueryBuilder mixBoolQuery = QueryBuilders.boolQuery();
//				if (matcher.matches()) {
//					log.info("只包含数字 查sku_id的情况/型号");
//					filterQueryBuilder.must(QueryBuilders.matchQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey()).operator(Operator.AND));
//				}else if(alphabetMatcher.matches()){
//					log.info("只包含字母");
//					String queryString = "*" + request.getKey() + "*";
////					filterQueryBuilder.must(QueryBuilders.wildcardQuery(IndustrialProductForErpDocument.KEYWORD, "*" + request.getKey() + "*"));
//					queryBuilder.withQuery(QueryBuilders.queryStringQuery(queryString).defaultField(IndustrialProductForErpDocument.KEYWORD));
//				} else if (numAlphabetMatcher.matches()) {
//					log.info("混合情况");
//					//condition A & condition B & （condition C || condition D）时，在ES中使用must与should组合可解决
//					//举个栗子
//					//{
//					//    "query":{
//					//        "bool":{
//					//            "must":[
//					//                {
//					//                    "bool":{
//					//                        "should":[
//					//                            {
//					//                                "match":{
//					//                                    "conditionA":{
//					//                                        "query":"A"
//					//                                    }
//					//                                }
//					//                            }
//					//                        ]
//					//                    }
//					//                }
//					//            ]
//					//        }
//					//    }
//					//}
//
//					mixBoolQuery.should(QueryBuilders.matchQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey().trim()));
//					mixBoolQuery.should(QueryBuilders.wildcardQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey().trim() + "*"));
//					filterQueryBuilder.must(mixBoolQuery);
//
////					filterQueryBuilder.must(mixBoolQuery.should(QueryBuilders.wildcardQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey().trim().replaceAll("[a-zA-Z]", "") + "*")));
//				}else {
//
//					mixBoolQuery.should(QueryBuilders.matchQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey().trim()));
//					mixBoolQuery.should(QueryBuilders.wildcardQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey().trim() + "*"));
//					filterQueryBuilder.must(mixBoolQuery);
//				}
//			} else {
//				// 短词查询
//				MatchPhraseQueryBuilder keywordQuery;
//				for (String s : request.getKey().trim().split(StrUtil.SPACE)) {
//					keywordQuery = QueryBuilders.matchPhraseQuery(IndustrialProductForErpDocument.KEYWORD, s);
//					filterQueryBuilder.should(keywordQuery).boost(3f);
//				}
//				filterQueryBuilder.minimumShouldMatch(request.getKey().trim().split(StrUtil.SPACE).length);
//			}
		}

		filterQueryBuilder.mustNot(QueryBuilders.termQuery("mroFirstCategoryId", "24000"));
		//判读是MRO搜索还是ERP搜索
		if (request.getForMro() != null) {
			filterQueryBuilder.must(QueryBuilders.termQuery("forMro", request.getForMro()));

			if (request.getForMro()) {
				//如果类目Id为-1说明 没有调价 或者 建索引时类目调价关系不存在但第三方商品表的categoryMroSupplierId字段不为空
				filterQueryBuilder.must(QueryBuilders.rangeQuery("mroCategoryId").gt(-1L));
			}
		}

		//mro状态必须为已经上架
		filterQueryBuilder.must(QueryBuilders.termQuery("mroStatus", ON_SHELF.getMroStatus()));

		//第三方供货价大于协议价不展示
		Script script = new Script("doc['supplierSalePrice'].value <= doc['thirdPlatformPrice'].value");
		filterQueryBuilder.must(QueryBuilders.scriptQuery(script));

		if (ObjectUtil.isNotNull(request.getStatus())) {
			filterQueryBuilder.filter(QueryBuilders.termQuery("status", request.getStatus()));
		}

		if (CollectionUtil.isNotEmpty(request.getProdIds())) {
			filterQueryBuilder.filter(QueryBuilders.termsQuery("prodId", request.getProdIds()));
		}

		if (StrUtil.isNotEmpty(request.getId())) {
			filterQueryBuilder.filter(QueryBuilders.termsQuery("id", request.getId()));
		}

		//品牌名称
		if (CollUtil.isNotEmpty(request.getBrandNames())) {
			filterQueryBuilder.must(QueryBuilders.termsQuery("brandName.keyword", request.getBrandNames()));
		}

		// mro 类目匹配
		if (ObjectUtil.isNotNull(request.getCategoryId())) {
//			filterQueryBuilder.must(QueryBuilders.termQuery("mroCategoryId", request.getCategoryId()));
			filterQueryBuilder.must(QueryBuilders.multiMatchQuery(request.getCategoryId(), "mroFirstCategoryId", "mroSecondCategoryId", "mroThirdCategoryId","firstCategoryId", "secondCategoryId", "thirdCategoryId"));
		}

		// 商家id
		if (ObjectUtil.isNotNull(request.getShopId())) {
			filterQueryBuilder.must(QueryBuilders.termQuery("shopId", request.getShopId()));
		}

		// 商家名称
		if (StrUtil.isNotBlank(request.getShopName())) {
			filterQueryBuilder.must(QueryBuilders.matchPhraseQuery("shopName", request.getShopName()));
		}

		// 商品skuId
		if (StrUtil.isNotBlank(request.getProductCode())) {
			filterQueryBuilder.must(QueryBuilders.termQuery(IndustrialProductForErpDocument.PRODUCT_CODE_KEYWORD, request.getProductCode()));
		}

		if (ObjectUtil.isNotNull(request.getProdType())) {
			filterQueryBuilder.must(QueryBuilders.termQuery("prodType", request.getProdType()));
		}

		if (StrUtil.isNotBlank(request.getThirdPlatform())) {
			filterQueryBuilder.must(QueryBuilders.termQuery("supplier", request.getThirdPlatform()));
		}

		//价格匹配
		if (CollUtil.isNotEmpty(request.getPriceInterval())) {
			RangeQueryBuilder price = QueryBuilders.rangeQuery(ThirdProductDocumentDTO.ADJUSTED_PRICE);
			if (request.getPriceInterval().size() >= 1) {
				price.gte(request.getPriceInterval().get(0));
			}
			if (request.getPriceInterval().size() == 2) {
				price.lte(request.getPriceInterval().get(1));
			}

			BoolQueryBuilder priceBuilder = QueryBuilders.boolQuery();
			priceBuilder.should(price);
			filterQueryBuilder.must(priceBuilder);
		}

		// 去除账套未授权的店铺
		R<List<AccountBookDetailVO>> listR = bookDetailClient.queryNotCreditByBookId(Optional.ofNullable(request.getAccountBookId()).orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));
		if (AppUtils.isNotBlank(listR) && listR.getSuccess() && AppUtils.isNotBlank(listR.getResult())) {
			filterQueryBuilder.mustNot(QueryBuilders.termsQuery(IndustrialProductForErpDocument.SHOP_ID, listR.getResult().stream().map(AccountBookDetailVO::getShopId).collect(Collectors.toList())));
		}

		return filterQueryBuilder;
	}


	/**
	 * 保存历史记录
	 *
	 * @param requestParams
	 */
	private void sendSearchHistory(Object requestParams) {
		log.info("准备发送搜索！：value : {}", JSONUtil.toJsonStr(requestParams));
		String searchInfo = "";
		if (requestParams instanceof ThirdProductSearchRequest) {
			ThirdProductSearchRequest searchRequest = (ThirdProductSearchRequest) requestParams;
			if (StrUtil.isNotBlank(searchRequest.getKey()) && StrUtil.isNotBlank(searchRequest.getKey().trim())) {
				searchInfo = "keyword." + searchRequest.getKey();
			} else if (StrUtil.isNotEmpty(searchRequest.getPrices())) {
				searchInfo = "prices." + searchRequest.getPrices();
			}
		} else if (requestParams instanceof IndustrialSearchRequest) {
			IndustrialSearchRequest searchRequest = (IndustrialSearchRequest) requestParams;
			if (StrUtil.isNotBlank(searchRequest.getKey()) && StrUtil.isNotBlank(searchRequest.getKey().trim())) {
				searchInfo = "keyword." + searchRequest.getKey();
			} else if (CollUtil.isNotEmpty(searchRequest.getPriceInterval())) {
				searchInfo = "priceInterval" + searchRequest.getPriceInterval().stream().map(BigDecimal::toString).collect(Collectors.joining(StrUtil.EMPTY));
			}
		}
		this.amqpSendMsgUtil.convertAndSend(ProductSearchMQConstant.PRODUCT_EXCHANGE, "search.create", searchInfo);
		log.info("发送成功！value :{}", JSONUtil.toJsonStr(requestParams));
	}

	/**
	 * 获取品牌聚合
	 *
	 * @param terms
	 * @return
	 */
	private List<String> getBrandNameAgg(Terms terms) {
		if (terms instanceof UnmappedTerms) {
			log.info("没有聚合到品牌");
			return new ArrayList<>();
		}
		//解析terms获取聚合的品牌名称
		List<String> brandNameList = terms.getBuckets().stream()
			.map(MultiBucketsAggregation.Bucket::getKeyAsString)
			.distinct()
			.sorted(Collator.getInstance(Locale.CHINA))    //按拼音首字母进行排序
			.collect(Collectors.toList());
		return CollectionUtil.isEmpty(brandNameList) ? Collections.emptyList() : brandNameList;
	}

	/**
	 * 排序
	 *
	 * @param searchRequest
	 * @param queryBuilder
	 */
	private void buildSort(Object searchRequest, NativeSearchQueryBuilder queryBuilder) {
		// 1.3 添加排序条件
		if (searchRequest instanceof IndustrialSearchRequest) {
			IndustrialSearchRequest request = (IndustrialSearchRequest) searchRequest;
			if (StrUtil.isNotEmpty(request.getSortBy())) {
				SortOrder sort = SortOrder.ASC;
				if (AppUtils.isNotBlank(request.getDescending()) && request.getDescending()) {
					sort = SortOrder.DESC;
				}
				queryBuilder.withSort(SortBuilders.fieldSort(request.getSortBy()).order(sort));
				// 添加默认排序 (权重)
				queryBuilder.withSort(SortBuilders.fieldSort("seqSource").order(sort));
				// 高亮显示
				queryBuilder.withHighlightFields();
			}
		} else if (searchRequest instanceof ThirdProductSearchRequest) {
			ThirdProductSearchRequest request = (ThirdProductSearchRequest) searchRequest;
			if (StrUtil.isNotBlank(request.getSortBy())) {
				SortOrder sort = SortOrder.ASC;
				if (AppUtils.isNotBlank(request.getDescending()) && request.getDescending()) {
					sort = SortOrder.DESC;
				}
				queryBuilder.withSort(SortBuilders.fieldSort(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).order(sort));
				// 添加默认排序
				queryBuilder.withSort(SortBuilders.fieldSort("seqSource").order(sort));
				// 高亮显示
				queryBuilder.withHighlightFields();
			}
		}
	}


	/**
	 * 获取分类聚合
	 *
	 * @param terms
	 * @return
	 */
	private List<SearchCategoryDTO> getCategoryAgg(ParsedLongTerms terms) {
		//解析terms获取聚合的分类ids
		List<Long> categoryIds = terms.getBuckets().stream()
			.map(Terms.Bucket::getKeyAsNumber)
			.map(Number::longValue)
			.collect(Collectors.toList());
		if (CollUtil.isEmpty(categoryIds)) {
			return Collections.emptyList();
		}
		// 获取分类名称
		R<List<Category>> resp = this.categoryServiceClient.findCategoryByIdsV2(categoryIds, SecurityConstants.FROM_IN);
		return !resp.hasBody() ? Collections.emptyList() : resp.getResult().stream().map(c -> new SearchCategoryDTO(c.getId(), c.getName())).collect(Collectors.toList());
	}


	private BoolQueryBuilder buildObeiBasicQueryWithFilter(ThirdProductSearchRequest request) {
		BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
		//有搜全部的选项
		if (StrUtil.isNotBlank(request.getThirdPlatform())) {
			if(request.getThirdPlatform().split(",").length>1) {
				List<String> supplierList = Arrays.asList(request.getThirdPlatform().split(","));
				queryBuilder.must(QueryBuilders.termsQuery("supplier", supplierList));
			} else {
				queryBuilder.must(QueryBuilders.termQuery("supplier", request.getThirdPlatform()));
			}
		}

		queryBuilder.filter(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).gt(0));

		//过滤MRO平台的商品( 后续考虑自营时再修改 )
//		List<IndustrialOuterShopConfig> allExcludeMroPlatform = industrialOuterShopConfigService.getAllExcludeMroPlatform();
//		List<Long> shopIds = allExcludeMroPlatform.stream().map(IndustrialOuterShopConfig::getShopId).collect(Collectors.toList());
//		queryBuilder.must(QueryBuilders.termsQuery(IndustrialProductForErpDocument.SHOP_ID, shopIds));

		// 只支持账套授权的店铺
//		R<List<AccountBookDetailVO>> listR = bookDetailClient.queryByBookId(Optional.ofNullable(request.getAccountBookId()).orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()));
//		if (AppUtils.isNotBlank(listR) && listR.getSuccess() && AppUtils.isNotBlank(listR.getResult())) {
////			List<AccountBookDetailVO> accountBookDetailList = listR.getResult().stream().filter(e-> !AccountBookStatusEnum.AUTHORIZED.getCode().equals(e.getStatus())).collect(Collectors.toList());
////			if(ObjectUtil.isNotEmpty(accountBookDetailList)) {
//				queryBuilder.filter(QueryBuilders.termsQuery(IndustrialProductForErpDocument.SHOP_ID, listR.getResult().stream().map(AccountBookDetailVO::getShopId).collect(Collectors.toList())));
////				queryBuilder.mustNot(QueryBuilders.termsQuery(IndustrialProductForErpDocument.SHOP_ID, accountBookDetailList.stream().map(AccountBookDetailVO::getShopId).collect(Collectors.toList())));
////			}
//		}
		Integer accountBookDetailType = request.getForMro()? OrderSourceEnum.MRO.value() : OrderSourceEnum.ERP.value();
		R<List<AccountBookDetailVO>> listR = bookDetailClient.queryAccountBookDetailByTypeAndAccountBookId(Optional.ofNullable(request.getAccountBookId()).orElse(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId()),accountBookDetailType);
		if (AppUtils.isNotBlank(listR) && listR.getSuccess() && AppUtils.isNotBlank(listR.getResult())) {
			queryBuilder.filter(QueryBuilders.termsQuery(IndustrialProductForErpDocument.SHOP_ID, listR.getResult().stream().map(AccountBookDetailVO::getShopId).collect(Collectors.toList())));
		}

		// 基本查询条件
		if (StrUtil.isBlank(request.getKey())) {
			log.info("搜索全部商品");
			queryBuilder.must(QueryBuilders.matchAllQuery());
		} else {
			MultiMatchQueryBuilder multiMatchQuery = QueryBuilders.multiMatchQuery(request.getKey().trim(), IndustrialProductForErpDocument.PRODUCT_CODE, IndustrialProductForErpDocument.PRODUCT_NAME, IndustrialProductForErpDocument.KEYWORD)
					.field(IndustrialProductForErpDocument.PRODUCT_CODE, 3.0f)
					.field(IndustrialProductForErpDocument.PRODUCT_NAME, 1.0f)
					.field(IndustrialProductForErpDocument.KEYWORD, 1.0f)
					.operator(Operator.AND).fuzziness("AUTO" + StrUtil.COLON + lowDistance + StrUtil.COMMA + highDistance);

			// 添加查询到过滤器中
			queryBuilder.must(multiMatchQuery);
//			//商品名称
//			if (StrUtil.isNotEmpty(request.getKey())) {
//				if (request.getKey().trim().contains(StrUtil.SPACE)) {
//					log.info("商名名称有空格，分词匹配！");
//					queryBuilder.must(QueryBuilders.matchQuery(IndustrialProductForErpDocument.KEYWORD, request.getKey()).operator(Operator.AND));
//				} else {
//					log.info("商名名称没有空格，不分词精准匹配！");
//					MatchPhraseQueryBuilder productNameMatchPhraseQueryBuilder;
//					productNameMatchPhraseQueryBuilder = QueryBuilders.matchPhraseQuery(IndustrialProductForErpDocument.PRODUCT_NAME_SUGGEST, request.getKey());
//					queryBuilder.must(productNameMatchPhraseQueryBuilder);
//				}
//
////				 //短词查询   之前对应欧贝productNameSuggest
////				MatchPhraseQueryBuilder productNameSuggest;
////				for (String s : request.getKey().split(StrUtil.SPACE)) {
////					productNameSuggest = QueryBuilders.matchPhraseQuery("productNameSuggest", s);
////					queryBuilder.should(productNameSuggest).boost(3f);
////				}
//				// 短词查询
//				MatchPhraseQueryBuilder productName;
//				for (String s : request.getKey().trim().split(StrUtil.SPACE)) {
//					productName = QueryBuilders.matchPhraseQuery(IndustrialProductForErpDocument.PRODUCT_NAME_SUGGEST, s);
//					queryBuilder.should(productName).boost(3f);
//				}
//			}
		}
		out:
		if (CollectionUtil.isNotEmpty(request.getThirdCategoryCodes())) {
			//
			for (String thirdCategoryCode : request.getThirdCategoryCodes()) {
				//String 转换成list String这里的判断特殊处理
				if (StrUtil.isEmpty(thirdCategoryCode)) {
					break out;
				}
			}
			//类目编码
			queryBuilder.must(QueryBuilders.termsQuery(IndustrialProductForErpDocument.THIRD_CATEGORY_CODE_KEYWORD, request.getThirdCategoryCodes()));
		}

		out:
		if (CollectionUtil.isNotEmpty(request.getSecondCategoryCodes())) {
			//
			for (String secondCategoryCode : request.getSecondCategoryCodes()) {
				//String 转换成list String这里的判断特殊处理
				if (StrUtil.isEmpty(secondCategoryCode)) {
					break out;
				}
			}
			//类目编码
			queryBuilder.must(QueryBuilders.termsQuery(IndustrialProductForErpDocument.SECOND_CATEGORY_CODE_KEYWORD, request.getSecondCategoryCodes()));
		}

		out:
		if (CollectionUtil.isNotEmpty(request.getFirstCategoryCodes())) {
			//
			for (String firstCategoryCode : request.getFirstCategoryCodes()) {
				//String 转换成list String这里的判断特殊处理
				if (StrUtil.isEmpty(firstCategoryCode)) {
					break out;
				}
			}
			//类目编码
			queryBuilder.must(QueryBuilders.termsQuery(IndustrialProductForErpDocument.FIRST_CATEGORY_CODE_KEYWORD, request.getFirstCategoryCodes()));
		}
//		if (ObjectUtil.isNotNull(request.getStatus())) {
//			queryBuilder.must(QueryBuilders.termQuery("status", request.getStatus()));
//		}
		if (StrUtil.isNotEmpty(request.getBrandName())) {
			//品牌名称
//			queryBuilder.must(QueryBuilders.termQuery("brandName.keyword", request.getBrandName()));
			queryBuilder.must(QueryBuilders.matchPhraseQuery("brandName", request.getBrandName().trim()));
		}

		//品牌名称
		if (CollUtil.isNotEmpty(request.getBrandNames())) {
			queryBuilder.must(QueryBuilders.termsQuery("brandName.keyword", request.getBrandNames()));
		}

		//是否绑定
		if (ObjectUtil.isNotEmpty(request.getBindFlag())) {
			queryBuilder.must(QueryBuilders.termQuery("bound", request.getBindFlag()));
		}
		//模糊搜索 规格和欧贝商品编码
		if (ObjectUtil.isNotEmpty(request.getProductCode())) {
			queryBuilder.must(QueryBuilders.wildcardQuery("productCode.keyword", "*" + request.getProductCode().trim() + "*"));
		}
		if (StrUtil.isNotBlank(request.getTypeGuage())) {
			queryBuilder.must(QueryBuilders.wildcardQuery("typeGauge.keyword", "*" + request.getTypeGuage().trim() + "*"));
		}

		if (AppUtils.isNotBlank(request.getLowValueConsumable()) && request.getLowValueConsumable() == 1) {
			//品牌名称
			queryBuilder.must(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).lte(BigDecimal.valueOf(5000L)));
		}

		queryBuilder.filter(QueryBuilders.termQuery("status", ThridProductStatusEnum.PROD_ONLINE.getValue()));

		return queryBuilder;
	}

	/**
	 * 商品价格
	 *
	 * @param request
	 * @param queryBuilder
	 */
	private void buildFilter(ThirdProductSearchRequest request, BoolQueryBuilder queryBuilder) {
		if (StringUtils.isNotEmpty(request.getPrices())) {
			if (request.getPrices().startsWith(StrUtil.DASHED)) {
				queryBuilder.filter(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).gte(0).lte(request.getPrices().substring(1)));
			} else {
				String[] p = request.getPrices().split(StrUtil.DASHED);
				if (p.length == ThirdProductSearchRequest.PRICE_FILTER_LENGTH) {
					if (StringUtils.isNotBlank(p[1])) {
						queryBuilder.filter(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).gte(p[0]).lte(p[1]));
					}
				} else {
					if (StringUtils.isNotBlank(p[0])) {
						queryBuilder.filter(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).gte(p[0]));
					}
				}
			}
		}
	}

	@Override
	public List<String> searchSuggestByKeyword(String keyword,Integer size) {
		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(IndustrialProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			throw new BusinessException("索引不存在，请重建!");
		}
		//构建搜索建议补全对象
		CompletionSuggestionBuilder completionSuggestionBuilder = SuggestBuilders.
				completionSuggestion("searchSuggestText"). // 使用fieldName入参进行标题联想(这里就是索引被@CompletionField注解标注的字段)
						prefix(keyword).                   // 关键字（参数传此）
						skipDuplicates(true)               // 重复过滤
				.size(size)                        // 匹配数量
				;
		//创建搜索提示对象 进行封装搜索补全
		SuggestBuilder suggestBuilder = new SuggestBuilder();
		//  completionSuggestionBuilder:随便起的搜索补全的名字
		suggestBuilder.addSuggestion("product-suggest", completionSuggestionBuilder);
		//查询es并反参
		SearchResponse searchResponse = elasticsearchRestTemplate.suggest(suggestBuilder, elasticsearchRestTemplate.getIndexCoordinatesFor(IndustrialProductDocument.class));
		//获取反参中的搜索补全结果
		Suggest.Suggestion<? extends Suggest.Suggestion.Entry<? extends Suggest.Suggestion.Entry.Option>> suggestionBuilder = searchResponse.getSuggest().getSuggestion("product-suggest");

		return suggestionBuilder.getEntries().stream().map(x -> x.getOptions().stream().map(y -> y.getText().toString()).collect(Collectors.toList())).findFirst().get();
	}
}
