package cn.legendshop.search.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.enums.IsMroSelfEnum;
import cn.legendshop.common.core.enums.ThirdIndustrialEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.model.IndustrialOuterShopConfig;
import cn.legendshop.common.rabbitmq.constants.ProductSearchMQConstant;
import cn.legendshop.common.rabbitmq.utils.AmqpSendMsgUtil;
import cn.legendshop.common.service.IndustrialOuterShopConfigService;
import cn.legendshop.common.utils.NumberUtils;
import cn.legendshop.product.api.client.BrandServiceClient;
import cn.legendshop.product.api.client.CategoryServiceClient;
import cn.legendshop.product.api.client.SpecificationServiceClient;
import cn.legendshop.product.api.client.ThirdPartyProductPriceAdjustmentClient;
import cn.legendshop.product.api.dto.ProductPriceAdjustmentDTO;
import cn.legendshop.product.api.enums.IndustrialProdTypeEnum;
import cn.legendshop.search.api.dto.*;
import cn.legendshop.search.api.request.IndustrialSearchRequest;
import cn.legendshop.search.api.vo.SearchRequest;
import cn.legendshop.search.api.vo.SearchResult;
import cn.legendshop.search.document.IndustrialProductDocument;
import cn.legendshop.search.document.IndustrialProductForErpDocument;
import cn.legendshop.search.document.ProductDocument;
import cn.legendshop.search.repository.IndustrialProductRepository;
import cn.legendshop.search.repository.ProductRepository;
import cn.legendshop.search.service.ProductSearchService;
import cn.legendshop.search.service.converter.IndustrialProductForErpDocumentConverter;
import cn.legendshop.search.service.converter.ProductDocumentConverter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.legendshop.model.dto.request.ThirdProductSearchRequest;
import com.legendshop.model.entity.Brand;
import com.legendshop.model.entity.Category;
import com.legendshop.model.entity.ProductProperty;
import com.legendshop.model.entity.ShopDetail;
import com.legendshop.util.AppUtils;
import com.legendshop.util.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.InternalHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.StringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.UnmappedTerms;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.search.suggest.Suggest;
import org.elasticsearch.search.suggest.SuggestBuilder;
import org.elasticsearch.search.suggest.SuggestBuilders;
import org.elasticsearch.search.suggest.completion.CompletionSuggestionBuilder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.legendshop.search.document.IndustrialProductForErpDocument.INDUSTRIAL_PROD_TYPE;
import static com.legendshop.util.AppUtils.isNotBlank;

@Slf4j
@Service
@AllArgsConstructor
public class ProductSearchServiceImpl implements ProductSearchService {


	private final AmqpSendMsgUtil amqpSendMsgUtil;
	private final ProductRepository productRepository;
	private final BrandServiceClient brandServiceClient;

	private final ElasticsearchRestTemplate elasticsearchRestTemplate;
	private final CategoryServiceClient categoryServiceClient;
	private final SpecificationServiceClient specificationServiceClient;
	private final ProductDocumentConverter productDocumentConverter;
	private final ThirdPartyProductPriceAdjustmentClient thirdPartyProductPriceAdjustmentClient;
	private final IndustrialOuterShopConfigService industrialOuterShopConfigService;

	private static final int lowDistance = 6;
	private static final int highDistance = 18;
	/**
	 * es搜索商品
	 *
	 * @param request the request
	 * @return SearchResult
	 */
	public SearchResult<ProductDocumentDTO> productSearch(SearchRequest request) {

		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(ProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			return null;
		}
		log.info("查询索引: {}", cn.hutool.json.JSONUtil.toJsonStr(indexOperations.getSettings()));
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
		// 1、构建查询条件
		// 1.1.对搜索的结果进行过滤，返回页面需要参数
		queryBuilder.withSourceFilter(new FetchSourceFilter(new String[]{"prodId", "productName", "brief", "skus", "brandId", "shopName", "sortPrice", "shopId","isMroSelf","totalPrice","supplierSalePrice",
			"thirdFirstCategoryId","thirdSecondCategoryId", "thirdThirdCategoryId", "productNameSuggest", "supplier", "adjustId", "mroCategoryId"}, null));
		// 1.2.基本查询
		QueryBuilder basicQuery = this.buildBasicQueryWithFilter(request);
		queryBuilder.withQuery(basicQuery);

		// 1.3 添加排序条件
		if (AppUtils.isNotBlank(request.getSortBy())) {
			SortOrder sort = SortOrder.ASC;
			if (AppUtils.isNotBlank(request.getDescending())) {
				if(request.getDescending()){
					sort = SortOrder.DESC;
				}
			}
			queryBuilder.withSort(SortBuilders.fieldSort(request.getSortBy()).order(sort));
			// 添加默认排序 (权重)
			queryBuilder.withSort(SortBuilders.fieldSort("seqSource").order(sort));
			// 高亮显示
			queryBuilder.withHighlightFields();
		}else {
			queryBuilder.withSort(SortBuilders.fieldSort("seqSource").order(SortOrder.DESC));
		}
		// 1.4、分页
		queryBuilder.withPageable(PageRequest.of(request.getPage() - 1, request.getSize()));

		// 1.5、分类聚合
		String categoryAggName = "categoryAgg";
		int count = 0;
		if (AppUtils.isNotBlank(request.getFilter())) {
			for (Map.Entry<String, String> entry : request.getFilter().entrySet()) {
				// 获取传递过来的key
				String key = entry.getKey();
				if ("firstCategoryId".equals(key) && count == 0) {
					queryBuilder.addAggregation(AggregationBuilders.terms(categoryAggName).field("firstCategoryId").size(5000));
					count++;
				} else if ("secondCategoryId".equals(key) && count == 0) {
					queryBuilder.addAggregation(AggregationBuilders.terms(categoryAggName).field("secondCategoryId").size(5000));
					count++;
				} else if (("thirdCategoryId".equals(key) || "brandId".equals(key)) && count == 0) {
					queryBuilder.addAggregation(AggregationBuilders.terms(categoryAggName).field("thirdCategoryId").size(5000));
					count++;
				}
			}
			if (count == 0) {
				queryBuilder.addAggregation(AggregationBuilders.terms(categoryAggName).field("thirdCategoryId").size(5000));
			}
		} else {
			queryBuilder.addAggregation(AggregationBuilders.terms(categoryAggName).field("thirdCategoryId").size(5000));
		}

		// 1.6、品牌聚合
		String brandAggName = "brandAgg";
		queryBuilder.addAggregation(AggregationBuilders.terms(brandAggName).field("brandId").size(1000));

		// 1.7、商家聚合
		String shopAggName = "shopAgg";
		queryBuilder.addAggregation(AggregationBuilders.terms(shopAggName).field("shopId").size(1000));

		NativeSearchQuery searchQuery = queryBuilder.build();
		log.info("搜索条件，{}", searchQuery.getQuery());
		log.info("过滤条件，{}", searchQuery.getFilter());
		log.info("高亮条件，{}", searchQuery.getHighlightBuilder());
		//放开ES搜索个数的限制
		searchQuery.setTrackTotalHits(true);
		log.info("查询开始！date:{}", System.currentTimeMillis());

		SearchHits<ProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, ProductDocument.class);

		log.info("查询结束！date:{}", System.currentTimeMillis());
		// 3、解析结果
		// 3.1、总条数和总页数
		long total = aggResult.getTotalHits();
		long totalPage = (total + request.getSize() - 1) / request.getSize();

		log.info("查询总数，:共{} 条数据", total);
		if (total <= 0) {
			return null;
		}
		Aggregations aggregations = aggResult.getAggregations();
		// 3.2、解析商品分类
		assert aggregations != null;
		List<Category> categories = getCategoryAgg(aggregations.get(categoryAggName));

		// 3.3、解析品牌
		List<Brand> brands = getBrandAgg(aggregations.get(brandAggName));

		//3.4、解析商家
		List<ShopDetail> shops = getShopAgg(aggregations.get(shopAggName));

		// 3.4、处理规格参数
		List<Map<String, Object>> specs = null;
		if (isNotBlank(categories) && categories.size() == 1) {
			//确定分类了可以去进行规格参数的聚合
			specs = getSpecifications1(categories.get(0).getId(), basicQuery);
		}
		//转换对象
		List<ProductDocumentDTO> returnList = new ArrayList<>();
		List<ProductDocumentDTO> productDocumentList = aggResult.getSearchHits().stream()
			.map(SearchHit::getContent)
			.map(productDocumentConverter::to)
			.collect(Collectors.toList());

		Map<Long, ProductDocumentDTO> priceMap = productDocumentList.stream().filter(u->u.getProdId() != null).collect(Collectors.toMap(ProductDocumentDTO::getProdId, e -> e));

		//商品和企业调价计算
		if (CollUtil.isNotEmpty(productDocumentList)) {
			// 自营工业品不参与 价格计算
			List<ProductDocumentDTO> thirdProductDocumentDTOS = productDocumentList.stream().filter((item) -> !IsMroSelfEnum.THIRD_PROD.getType().equals(item.getIsMroSelf())).collect(Collectors.toList());
			returnList.addAll(thirdProductDocumentDTOS);
			//第三方工业品参与价格计算
			List<ProductDocumentDTO> mroSelfThirdProductDocumentDTOS = productDocumentList.stream().filter((item) -> IsMroSelfEnum.THIRD_PROD.getType().equals(item.getIsMroSelf())).collect(Collectors.toList());
			if (CollUtil.isNotEmpty(mroSelfThirdProductDocumentDTOS)){
				ProductPriceAdjustmentDTO productPriceAdjustmentDTO = new ProductPriceAdjustmentDTO().setProdPriceAdjustmentData(productDocumentConverter.toCalculateProductPrice2(mroSelfThirdProductDocumentDTOS));
				if (StrUtil.isNotBlank(request.getUserId())) {
					productPriceAdjustmentDTO.setUserId(request.getUserId());
				}
				log.info(productPriceAdjustmentDTO.toString());
				R<List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData>> calculateProductPriceAdjustmentR = thirdPartyProductPriceAdjustmentClient.calculateProductPriceAdjustment(productPriceAdjustmentDTO);
				if (calculateProductPriceAdjustmentR.isSuccess() && CollUtil.isNotEmpty(calculateProductPriceAdjustmentR.getResult())) {
					List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData> result = calculateProductPriceAdjustmentR.getResult();
					for (ProductPriceAdjustmentDTO.ProductPriceAdjustmentData productPriceAdjustmentData : result) {
						ProductDocumentDTO productDocumentDTO = priceMap.get(productPriceAdjustmentData.getProductId());
						productDocumentDTO.setSortPrice(productPriceAdjustmentData.getTotalPrice().doubleValue());
						returnList.add(productDocumentDTO);
					}
				}
			}

		}

		try {
			sendSearchHistory(request);
		} catch (Exception e) {
			e.printStackTrace();
		}

		if(request.getDescending() != null){
			returnList.sort((o1, o2) -> {
				if(!request.getDescending()){
					return o1.getSortPrice().compareTo(o2.getSortPrice());
				}else {
					return o2.getSortPrice().compareTo(o1.getSortPrice());
				}
			});
		}
		return new SearchResult<>(total, totalPage, returnList, categories, brands, shops, specs, request.getSize(), request.getPage());
	}


	/**
	 * es搜索商品v2
	 *
	 * @param request the request
	 * @return SearchResult
	 */
	@Override
	public SearchResult<ThirdProductDocumentDTO> productSearchV2(IndustrialSearchRequest request) {

		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(ProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			return null;
		}
		log.info("查询索引: {}", cn.hutool.json.JSONUtil.toJsonStr(indexOperations.getSettings()));
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
		// 1、构建查询条件
		QueryBuilder basicQuery = this.buildObeiBasicQueryWithFilterV2(request);
		// 1.2.基本查询
		queryBuilder.withQuery(basicQuery);

		// 1.3 添加排序条件
		if (AppUtils.isNotBlank(request.getSortBy())) {
			SortOrder sort = Objects.equals(request.getDescending(), Boolean.TRUE) ? SortOrder.DESC : SortOrder.ASC;
			queryBuilder.withSort(SortBuilders.fieldSort(request.getSortBy()).order(sort));
		}else {
			queryBuilder.withSort(SortBuilders.fieldSort("seqSource").order(SortOrder.DESC));
		}
		// 1.4、分页
		queryBuilder.withPageable(PageRequest.of(request.getCurPage() - 1, request.getSize()));

		// 1.5、分类聚合
		String categoryAggName = "categoryAgg";

		queryBuilder.addAggregation(AggregationBuilders.terms(categoryAggName).field("thirdCategoryId").size(5000));


		// 1.6、品牌聚合
		String brandAggName = "brandAgg";
		queryBuilder.addAggregation(AggregationBuilders.terms(brandAggName).field("brandId").size(1000));

		// 1.7、商家聚合
		String shopAggName = "shopAgg";
		queryBuilder.addAggregation(AggregationBuilders.terms(shopAggName).field("shopId").size(1000));

		NativeSearchQuery searchQuery = queryBuilder.build();
		log.info("搜索条件，{}", searchQuery.getQuery());
		log.info("过滤条件，{}", searchQuery.getFilter());
		log.info("高亮条件，{}", searchQuery.getHighlightBuilder());
		//放开ES搜索个数的限制
		searchQuery.setTrackTotalHits(true);
		log.info("查询开始！date:{}", System.currentTimeMillis());

		SearchHits<ProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, ProductDocument.class);
		log.info("查询结束！date:{}", System.currentTimeMillis());
		// 3、解析结果
		// 3.1、总条数和总页数
		long total = aggResult.getTotalHits();
		long totalPage = (total + request.getSize() - 1) / request.getSize();

		log.info("查询总数，:共{} 条数据", total);
		if (total <= 0) {
			return new SearchResult<>();
		}
		Aggregations aggregations = aggResult.getAggregations();
		// 3.2、解析商品分类
		assert aggregations != null;
		List<SearchCategoryDTO> categories = getCategoryAggV2(aggregations.get(categoryAggName));

		// 3.3、解析品牌
		List<Brand> brands = getBrandAgg(aggregations.get(brandAggName));


		List<String> brandNames = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(brands)) {
			brandNames = brands.stream().map(Brand::getBrandName).collect(Collectors.toList());
		}

		//3.4、解析商家
		List<ShopDetail> shops = getShopAgg(aggregations.get(shopAggName));

		// 3.4、处理规格参数
		List<Map<String, Object>> specs = null;
		if (isNotBlank(categories) && categories.size() == 1) {
			//确定分类了可以去进行规格参数的聚合
			specs = getSpecifications1(categories.get(0).getCategoryId(), basicQuery);
		}
		//转换对象
		List<ThirdProductDocumentDTO> returnList = new ArrayList<>();
		List<ThirdProductDocumentDTO> productDocumentList = aggResult.getSearchHits().stream()
			.map(SearchHit::getContent)
			.map(productDocumentConverter::toIndustrialDocumentDTO)
			.collect(Collectors.toList());
		Map<Long, ThirdProductDocumentDTO> priceMap = productDocumentList.stream().filter(u->u.getProdId() != null).collect(Collectors.toMap(ThirdProductDocumentDTO::getProdId, e -> e));

		// 自营工业品不参与 价格计算
		List<ThirdProductDocumentDTO> mroSelfProductDocumentDTOS = productDocumentList.stream().filter((item) -> !IsMroSelfEnum.THIRD_PROD.getType().equals(item.getIsMroSelf())).collect(Collectors.toList());
		mroSelfProductDocumentDTOS.forEach(e->e.setSupplierSalePrice(BigDecimal.valueOf(e.getSortPrice())));
		if (CollUtil.isNotEmpty(mroSelfProductDocumentDTOS)){
			returnList.addAll(mroSelfProductDocumentDTOS);
		}

		//商品和企业调价计算
		if (CollUtil.isNotEmpty(productDocumentList)) {
			//第三方工业品参与价格计算
			List<ThirdProductDocumentDTO> thirdConsumerProductDocumentDTOS = productDocumentList.stream().filter((item) -> IsMroSelfEnum.THIRD_PROD.getType().equals(item.getIsMroSelf())).collect(Collectors.toList());
			if (CollUtil.isNotEmpty(thirdConsumerProductDocumentDTOS)){
				ProductPriceAdjustmentDTO productPriceAdjustmentDTO = new ProductPriceAdjustmentDTO().setProdPriceAdjustmentData(productDocumentConverter.toCalculateProductPrice(thirdConsumerProductDocumentDTOS));
				if (StrUtil.isNotBlank(request.getUserId())) {
					productPriceAdjustmentDTO.setUserId(request.getUserId());
				}
				log.info(productPriceAdjustmentDTO.toString());
				R<List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData>> calculateProductPriceAdjustmentR = thirdPartyProductPriceAdjustmentClient.calculateProductPriceAdjustment(productPriceAdjustmentDTO);
				if (calculateProductPriceAdjustmentR.isSuccess() && CollUtil.isNotEmpty(calculateProductPriceAdjustmentR.getResult())) {
					List<ProductPriceAdjustmentDTO.ProductPriceAdjustmentData> result = calculateProductPriceAdjustmentR.getResult();
					for (ProductPriceAdjustmentDTO.ProductPriceAdjustmentData productPriceAdjustmentData : result) {
						ThirdProductDocumentDTO thirdProductDocumentDTO = priceMap.get(productPriceAdjustmentData.getProductId());
						thirdProductDocumentDTO.setOriginalPrice(thirdProductDocumentDTO.getSupplierSalePrice());
						thirdProductDocumentDTO.setSupplierSalePrice(productPriceAdjustmentData.getTotalPrice());
						returnList.add(thirdProductDocumentDTO);
					}
				}
			}

		}

		try {
			sendSearchHistory2(request);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		return new SearchResult<>(total, totalPage, returnList, categories, brandNames, request.getSize(), request.getCurPage());
	}


	public SearchResult<ThirdProductDocumentDTO> productSearchV3(IndustrialSearchRequest request) {

		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(ProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			return null;
		}
		log.info("查询索引: {}", cn.hutool.json.JSONUtil.toJsonStr(indexOperations.getSettings()));
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
		// 1、构建查询条件
		// 1.1.对搜索的结果进行过滤，返回页面需要参数
		queryBuilder.withSourceFilter(new FetchSourceFilter(new String[]{"prodId","skus","isMroSelf"}, null));
		// 1.2.基本查询
		QueryBuilder basicQuery = this.buildObeiBasicQueryWithFilterV2(request);
		queryBuilder.withQuery(basicQuery);


		// 1.4、分页
		queryBuilder.withPageable(PageRequest.of(request.getCurPage() - 1, request.getSize()));



		NativeSearchQuery searchQuery = queryBuilder.build();
		log.info("搜索条件，{}", searchQuery.getQuery());
		log.info("过滤条件，{}", searchQuery.getFilter());
		log.info("高亮条件，{}", searchQuery.getHighlightBuilder());
		//放开ES搜索个数的限制
		searchQuery.setTrackTotalHits(true);
		log.info("查询开始！date:{}", System.currentTimeMillis());

		SearchHits<ProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, ProductDocument.class);
		log.info("查询结束！date:{}", System.currentTimeMillis());
		// 3、解析结果
		// 3.1、总条数和总页数
		long total = aggResult.getTotalHits();
		long totalPage = (total + request.getSize() - 1) / request.getSize();

		log.info("查询总数，:共{} 条数据", total);
		if (total <= 0) {
			return null;
		}
		Aggregations aggregations = aggResult.getAggregations();


		//转换对象
		List<ThirdProductDocumentDTO> productDocumentList = aggResult.getSearchHits().stream()
			.map(SearchHit::getContent)
			.map(productDocumentConverter::toIndustrialDocumentDTO)
			.collect(Collectors.toList());


		return new SearchResult<>(total, totalPage, productDocumentList, null, null, request.getSize(), request.getCurPage());
	}

	@Override
	public SearchResult<ThirdProductDocumentDTO> productSearchV4(IndustrialSearchRequest request) {

		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(ProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			return null;
		}
		log.info("查询索引: {}", cn.hutool.json.JSONUtil.toJsonStr(indexOperations.getSettings()));
		// 创建查询构建器
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
		// 1、构建查询条件
		// 1.1.对搜索的结果进行过滤，返回页面需要参数
		queryBuilder.withSourceFilter(new FetchSourceFilter(request.getFetchSourceFilter(), null));

		// 1.4、分页
		queryBuilder.withPageable(PageRequest.of(request.getCurPage() - 1, request.getSize()));

		// 1.2.基本查询
		QueryBuilder basicQuery = this.buildObeiBasicQueryWithFilterV2(request);
		queryBuilder.withQuery(basicQuery);


		NativeSearchQuery searchQuery = queryBuilder.build();
		log.info("搜索条件，{}", searchQuery.getQuery());
		log.info("过滤条件，{}", searchQuery.getFilter());
		log.info("高亮条件，{}", searchQuery.getHighlightBuilder());
		//放开ES搜索个数的限制
		searchQuery.setTrackTotalHits(true);
		log.info("查询开始！date:{}", System.currentTimeMillis());

		SearchHits<ProductDocument> aggResult = elasticsearchRestTemplate.search(searchQuery, ProductDocument.class);

		long total = aggResult.getTotalHits();
		long totalPage = (total + request.getSize() - 1) / request.getSize();

		//转换对象
		List<ThirdProductDocumentDTO> productDocumentList = new ArrayList<>();
			aggResult.getSearchHits().stream()
			.map(SearchHit::getContent).forEach(e->{
						ThirdProductDocumentDTO thirdProductDocumentDTO = new ThirdProductDocumentDTO();
						thirdProductDocumentDTO.setId(e.getId());
						thirdProductDocumentDTO.setProdId(e.getProdId());
						productDocumentList.add(thirdProductDocumentDTO);
		});

		return new SearchResult<>(total, totalPage, productDocumentList, null, null, request.getSize(), request.getCurPage());
	}

	/**
	 * 发送搜索结果
	 */
	private void sendSearchHistory2(IndustrialSearchRequest requestParams) {

		log.info("准备发送搜索！：value : {}", JSONUtil.getJson(requestParams));
		String searchInfo = "";
		if (StringUtils.isNotBlank(requestParams.getKey()) && StringUtils.isNotBlank(requestParams.getKey().trim())) {
			searchInfo = "keyword." + requestParams.getKey();
			this.amqpSendMsgUtil.convertAndSend(ProductSearchMQConstant.PRODUCT_EXCHANGE,"search.create", searchInfo);
			log.info("发送成功！value :{}", JSONUtil.getJson(requestParams));
		}
//		else if (CollectionUtils.isEmpty(requestParams.getFilter())) {
//			return;
//		} else {
//			List<String> keyIds = requestParams.getFilter().keySet().stream().filter(e -> e.toUpperCase().contains("CATEGORY")).collect(Collectors.toList());
//			if (CollectionUtils.isEmpty(keyIds)) {
//				return;
//			}
//			String categoryId = requestParams.getFilter().get(keyIds.get(0));
//			searchInfo = "category." + categoryId;
//		}
//		this.amqpSendMsgUtil.convertAndSend(ProductSearchMQConstant.PRODUCT_EXCHANGE,"search.create", searchInfo);
//		log.info("发送成功！value :{}", JSONUtil.getJson(requestParams));
	}

	/**
	 * 发送搜索结果
	 */
	private void sendSearchHistory(SearchRequest requestParams) {

		log.info("准备发送搜索！：value : {}", JSONUtil.getJson(requestParams));
		String searchInfo = "";
		if (StringUtils.isNotBlank(requestParams.getKey()) && StringUtils.isNotBlank(requestParams.getKey().trim())) {
			searchInfo = "keyword." + requestParams.getKey();
		} else if (CollectionUtils.isEmpty(requestParams.getFilter())) {
			return;
		} else {
			List<String> keyIds = requestParams.getFilter().keySet().stream().filter(e -> e.toUpperCase().contains("CATEGORY")).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(keyIds)) {
				return;
			}
			String categoryId = requestParams.getFilter().get(keyIds.get(0));
			searchInfo = "category." + categoryId;
		}
		this.amqpSendMsgUtil.convertAndSend(ProductSearchMQConstant.PRODUCT_EXCHANGE,"search.create", searchInfo);
		log.info("发送成功！value :{}", JSONUtil.getJson(requestParams));
	}

	private void sendObeiSearchHistory(ThirdProductSearchRequest requestParams) {
		log.info("准备发送搜索！：value : {}", JSONUtil.getJson(requestParams));
		String searchInfo = "";
		if (StringUtils.isNotBlank(requestParams.getKey()) && StringUtils.isNotBlank(requestParams.getKey().trim())) {
			searchInfo = "keyword." + requestParams.getKey();
		} else if (StringUtils.isEmpty(requestParams.getPrices())) {
			return;
		} else {
			searchInfo = "prices." + requestParams.getPrices();
		}
		this.amqpSendMsgUtil.convertAndSend(ProductSearchMQConstant.PRODUCT_EXCHANGE,"search.create", searchInfo);
		log.info("发送成功！value :{}", JSONUtil.getJson(requestParams));
	}

	/**
	 * 构建基本查询条件
	 *
	 * @param request the request
	 * @return QueryBuilder
	 */
	private QueryBuilder buildBasicQueryWithFilter(SearchRequest request) {
		BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
		// 基本查询条件

		//2023-05-25 过滤价格为负数的数据
		queryBuilder.must(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).gt(BigDecimal.ZERO));

		if (AppUtils.isBlank(request.getKey())) {
			queryBuilder.must(QueryBuilders.matchAllQuery());
		} else {

			Pattern pattern = Pattern.compile("^[a-zA-Z_0-9]{6,20}");
			Matcher matcher = pattern.matcher(request.getKey());

			if (matcher.matches() || request.getKey().trim().contains(StrUtil.SPACE)) {
				log.info("商名名称有空格，分词匹配！");
				queryBuilder.must(QueryBuilders.matchQuery(ProductDocument.KEYWORD, request.getKey()).operator(Operator.AND));
			} else {
//				log.info("商名名称没有空格，不分词精准匹配！");
//				MatchPhraseQueryBuilder productNameMatchPhraseQueryBuilder;
//				productNameMatchPhraseQueryBuilder = QueryBuilders.matchPhraseQuery(ProductDocument.PRODUCT_NAME, request.getKey());
//				queryBuilder.must(productNameMatchPhraseQueryBuilder);

				// 短词查询
				queryBuilder.should(QueryBuilders.matchPhraseQuery(ProductDocument.PRODUCT_NAME, request.getKey()).boost(3f));
				queryBuilder.should(QueryBuilders.matchPhraseQuery(ProductDocument.SHOP_NAME, request.getKey()).boost(3f));
				queryBuilder.minimumShouldMatch(2);

				MatchPhraseQueryBuilder keywordQuery;
				for (String s : request.getKey().trim().split(StrUtil.SPACE)) {
					queryBuilder.should(QueryBuilders.matchPhraseQuery(ProductDocument.PRODUCT_NAME, request.getKey()).boost(3f));
					queryBuilder.should(QueryBuilders.matchPhraseQuery(ProductDocument.SHOP_NAME, request.getKey()).boost(3f));
				}
				queryBuilder.minimumShouldMatch(request.getKey().trim().split(StrUtil.SPACE).length);
			}
		}

		// 过滤条件构建器
		Map<String, String> filter = request.getFilter();
		if (AppUtils.isNotBlank(filter) && filter.size() > 0) {
			BoolQueryBuilder filterQueryBuilder = QueryBuilders.boolQuery();
			// 整理过滤条件
			for (Map.Entry<String, String> entry : filter.entrySet()) {
				// 获取传递过来的key
				String key = entry.getKey();
				String value = entry.getValue();
				filterQueryBuilder.must(QueryBuilders.termQuery(key, value));
			}
			// 添加过滤条件
			queryBuilder.filter(filterQueryBuilder);
		}

		if (Objects.nonNull(request.getShopId())) {
			queryBuilder.must(QueryBuilders.matchQuery("shopId", request.getShopId()));
		}
		if (CollectionUtil.isNotEmpty(request.getShopIds())) {
			QueryBuilders.termsQuery("shopId", request.getShopIds());
			queryBuilder.filter(QueryBuilders.termsQuery("shopId", request.getShopIds()));
		}

		if (CollectionUtil.isNotEmpty(request.getProdIds())) {
			QueryBuilders.termsQuery("prodId", request.getProdIds());
			queryBuilder.filter(QueryBuilders.termsQuery("prodId", request.getProdIds()));
		}

		// 一级耳机类目过滤
		if (CollectionUtil.isNotEmpty(request.getCategoryIds())) {
			BoolQueryBuilder should = QueryBuilders.boolQuery().should(QueryBuilders.termsQuery("firstCategoryId", request.getCategoryIds()))
				.should(QueryBuilders.termsQuery("secondCategoryId", request.getCategoryIds()))
				.should(QueryBuilders.termsQuery("thirdCategoryId", request.getCategoryIds()));
			queryBuilder.filter(should);
		}

		// 排除shopId为4000的店铺
		queryBuilder.mustNot(QueryBuilders.termQuery("shopId", 4000L));

		return queryBuilder;
	}


	private BoolQueryBuilder buildObeiBasicQueryWithFilterV2(IndustrialSearchRequest request) {
		BoolQueryBuilder filterQueryBuilder = QueryBuilders.boolQuery();
		// 基本查询条件

		//2023-05-25 过滤价格为负数的数据
		filterQueryBuilder.must(QueryBuilders.rangeQuery(IndustrialProductForErpDocument.SUPPLIER_SALE_PRICE).gt(BigDecimal.ZERO));

		filterQueryBuilder.must(QueryBuilders.termQuery(INDUSTRIAL_PROD_TYPE, IndustrialProdTypeEnum.PERSONAL_PROD.getType()));

		if (StrUtil.isBlank(request.getKey())) {
			filterQueryBuilder.must(QueryBuilders.matchAllQuery());
		} else {
			// 多字段匹配且设置权重
			MultiMatchQueryBuilder multiMatchQuery = QueryBuilders.multiMatchQuery(request.getKey().trim(), IndustrialProductForErpDocument.PRODUCT_CODE, IndustrialProductForErpDocument.PRODUCT_NAME, IndustrialProductForErpDocument.KEYWORD)
					.field(IndustrialProductForErpDocument.PRODUCT_CODE, 3.0f)
					.field(IndustrialProductForErpDocument.PRODUCT_NAME, 1.0f)
					.field(IndustrialProductForErpDocument.KEYWORD, 1.0f)
					.operator(Operator.AND).fuzziness("AUTO" + StrUtil.COLON + lowDistance + StrUtil.COMMA + highDistance);

			// 添加查询到过滤器中
			filterQueryBuilder.must(multiMatchQuery);
		}


		if (CollectionUtil.isNotEmpty(request.getProdIds())) {
			filterQueryBuilder.filter(QueryBuilders.termsQuery("prodId", request.getProdIds()));
		}

		//品牌名称
		if (CollUtil.isNotEmpty(request.getBrandNames())) {
			filterQueryBuilder.must(QueryBuilders.termsQuery("brandName.keyword", request.getBrandNames()));
		}

		// mro 类目匹配
		if (ObjectUtil.isNotNull(request.getCategoryId())) {
//			filterQueryBuilder.must(QueryBuilders.termQuery("mroCategoryId", request.getCategoryId()));
			filterQueryBuilder.must(QueryBuilders.multiMatchQuery(request.getCategoryId(), "firstCategoryId", "secondCategoryId", "thirdCategoryId"));
		}

		// 商家id
		if (ObjectUtil.isNotNull(request.getShopId())) {
			filterQueryBuilder.must(QueryBuilders.termQuery("shopId", request.getShopId()));
		}

		// 是否第三方工业品
		if (request.getIsOnlySuppliers()) {
			filterQueryBuilder.must(QueryBuilders.termsQuery("shopId", Arrays.stream(ThirdIndustrialEnum.values()).map(ThirdIndustrialEnum::getShopId).collect(Collectors.toList())));
		}


		// 商家名称
		if (ObjectUtil.isNotNull(request.getShopName())) {
			filterQueryBuilder.must(QueryBuilders.matchPhraseQuery("shopName", request.getShopName()));
		}


		if (ObjectUtil.isNotNull(request.getThirdPlatform())) {
			filterQueryBuilder.must(QueryBuilders.termQuery("supplier", request.getThirdPlatform()));
		}

		//价格匹配
		if (CollUtil.isNotEmpty(request.getPriceInterval())) {
			RangeQueryBuilder price = QueryBuilders.rangeQuery("adjustedPrice");
			if (request.getPriceInterval().size() >= 1) {
				price.gte(request.getPriceInterval().get(0));
			}
			if (request.getPriceInterval().size() == 2) {
				price.lte(request.getPriceInterval().get(1));
			}

//			RangeQueryBuilder discountPrice = QueryBuilders.rangeQuery(ThirdPartyProductDocumentDTO.SUPPLIER_SALE_PRICE);
//			if (request.getPriceInterval().size() >= 1) {
//				discountPrice.gte(request.getPriceInterval().get(0));
//			}
//			if (request.getPriceInterval().size() == 2) {
//				discountPrice.lte(request.getPriceInterval().get(1));
//			}

			BoolQueryBuilder priceBuilder = QueryBuilders.boolQuery();
			priceBuilder.should(price);
//			priceBuilder.should(discountPrice);
			filterQueryBuilder.must(priceBuilder);
		}
		//

		// 排除shopId为4000的店铺
		filterQueryBuilder.mustNot(QueryBuilders.termQuery("shopId", 4000L));
		return filterQueryBuilder;
	}


	private List<Map<String, Object>> getSpecifications(Long id, QueryBuilder basicQuery) {
		// 1、根据分类查询规格
		R<List<ProductProperty>> specResp = this.specificationServiceClient.queryPropertyByCategoryId(id, SecurityConstants.FROM_IN);
		if (!specResp.hasBody()) {
			log.error("查询规格参数出错，cid={}", id);
			return null;
		}
		String jsonSpec = "";
		/*    List<ProductProperty> productPropertyList = specResp.getBody();*/
		// 将规格反序列化为集合
		List<Map<String, Object>> specs = null;
		try {
			specs = JSON.parseObject(jsonSpec, new TypeReference<List<Map<String, Object>>>() {
			});
		} catch (Exception e) {
			log.error("解析规格参数json出错，json={}", jsonSpec, e);
			return null;
		}


		// 2、过滤出可以搜索的哪些规格参数的名称，分成数值类型、字符串类型
		// 准备集合，保存字符串规格参数名
		Set<String> strSpec = new HashSet<>();
		// 准备map，保存数值规格参数名及单位
		Map<String, String> numericalUnits = new HashMap<>();
		// 解析规格
		for (Map<String, Object> spec : specs) {
			List<Map<String, Object>> params = (List<Map<String, Object>>) spec.get("params");
			for (Map<String, Object> param : params) {
				Boolean searchable = (Boolean) param.get("searchable");
				if (searchable) {
					// 判断是否是数值类型
					if (param.containsKey("numerical") && (boolean) param.get("numerical")) {
						numericalUnits.put(param.get("k").toString(), param.get("unit").toString());
					} else {
						strSpec.add(param.get("k").toString());
					}
				}
			}
		}

		// 3、聚合计算数值类型的interval
		Map<String, Double> numericalInterval = getNumericalInterval(id, numericalUnits.keySet());

		// 4、利用interval聚合计算数值类型的分段
		// 5、对字符串类型的参数进行聚合
		return this.aggForSpec(strSpec, numericalInterval, numericalUnits, basicQuery);
	}


	/**
	 * 聚合得到interval
	 *
	 * @param cid
	 * @param keySet
	 * @return
	 */
	private Map<String, Double> getNumericalInterval(Long cid, Set<String> keySet) {
		Map<String, Double> numbericalSpecs = new HashMap<>();
		// 准备查询条件
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
		// 不查询任何数据
		queryBuilder.withQuery(QueryBuilders.termQuery("thirdCategoryId", cid.toString()))
			.withSourceFilter(new FetchSourceFilter(new String[]{""}, null))
			.withPageable(PageRequest.of(0, 1));
		// 添加stats类型的聚合
		for (String key : keySet) {
			queryBuilder.addAggregation(AggregationBuilders.stats(key).field("specs." + key));
		}
//		Map<String, Aggregation> aggs = this.elasticsearchTemplate.query(queryBuilder.build(),
//			response -> response.getAggregations().asMap());
//
//		for (String key : keySet) {
//			InternalStats stats = (InternalStats) aggs.get(key);
//			double interval = this.getInterval(stats.getMin(), stats.getMax(), stats.getSum());
//			numbericalSpecs.put(key, interval);
//		}
		return numbericalSpecs;
	}

	/**
	 * 根据最小值，最大值，sum计算interval
	 *
	 * @param min
	 * @param max
	 * @param sum
	 * @return
	 */
	private double getInterval(double min, double max, Double sum) {
		double interval = (max - min) / 6.0d;
		// 判断是否是小数
		if (sum.intValue() == sum) {
			// 不是小数，要取整十、整百这样
			// 根据interval的整数位长度来判断位数
			int length = StringUtils.substringBefore(String.valueOf(interval), ".").length();
			double factor = Math.pow(10.0, length - 1);
			return Math.round(interval / factor) * factor;
		} else {
			// 是小数,我们只保留一位小数
			return NumberUtils.scale(interval, 1);
		}
	}


	/**
	 * 根据规格参数，聚合得出过滤条件
	 *
	 * @param strSpec
	 * @param numericalInterval
	 * @param numericalUnits
	 * @param query
	 * @return
	 */
	private List<Map<String, Object>> aggForSpec(Set<String> strSpec, Map<String, Double> numericalInterval,
												 Map<String, String> numericalUnits, QueryBuilder query) {
		List<Map<String, Object>> specs = new ArrayList<>();
		// 准备查询条件
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
		queryBuilder.withQuery(query);
		// 聚合数值类型
		for (Map.Entry<String, Double> entry : numericalInterval.entrySet()) {
			queryBuilder.addAggregation(
				AggregationBuilders.histogram(entry.getKey())
					.field("specs." + entry.getKey())
					.interval(entry.getValue())
					.minDocCount(1)
			);
		}
		// 聚合字符串
		for (String key : strSpec) {
			queryBuilder.addAggregation(
				AggregationBuilders.terms(key).field("specs." + key + ".keyword"));
		}

		// 解析聚合结果
		Map<String, Aggregation> aggs = null;

		// 解析数值类型
		for (Map.Entry<String, Double> entry : numericalInterval.entrySet()) {
			Map<String, Object> spec = new HashMap<>();
			String key = entry.getKey();
			spec.put("k", key);
			spec.put("unit", numericalUnits.get(key));
			// 获取聚合结果
			InternalHistogram histogram = (InternalHistogram) aggs.get(key);
			spec.put("options", histogram.getBuckets().stream().map(bucket -> {
				Double begin = (double) bucket.getKey();
				double end = begin + numericalInterval.get(key);
				// 对begin和end取整
				if (NumberUtils.isInt(begin) && NumberUtils.isInt(end)) {
					// 确实是整数，需要取整
					return begin.intValue() + "-" + (int) end;
				} else {
					// 小数，取2位小数
					begin = NumberUtils.scale(begin, 2);
					end = NumberUtils.scale(end, 2);
					return begin + "-" + end;
				}
			}));
			specs.add(spec);
		}

		// 解析字符串类型
		strSpec.forEach(key -> {
			Map<String, Object> spec = new HashMap<>();
			spec.put("k", key);
			StringTerms terms = (StringTerms) aggs.get(key);
			spec.put("options", terms.getBuckets().stream().map(StringTerms.Bucket::getKeyAsString));
			specs.add(spec);
		});
		return specs;
	}


	/**
	 * 获取品牌聚合
	 *
	 * @param terms
	 * @return
	 */
	private List<Brand> getBrandAgg(Terms terms) {
		if (terms instanceof UnmappedTerms) {
			log.info("没有聚合到品牌");
			return null;
		}

		//解析terms获取聚合的品牌ids
		List<Long> brandIds = terms.getBuckets()
			.stream().map(b -> b.getKeyAsNumber().longValue())
			.collect(Collectors.toList());

		if (AppUtils.isBlank(brandIds)) {
			return null;
		}
		R<List<Brand>> resp = this.brandServiceClient.queryBrandsByBrandIds(brandIds, SecurityConstants.FROM_IN);
		if (!resp.hasBody()) {
			return null;
		}
		return resp.getResult();
	}

	/**
	 * 获取分类聚合
	 *
	 * @param terms
	 * @return
	 */
	private List<Category> getCategoryAgg(ParsedLongTerms terms) {
		//解析terms获取聚合的分类ids
		List<Long> categoryIds = terms.getBuckets().stream()
			.map(b -> b.getKeyAsNumber().longValue())
			.collect(Collectors.toList());
		if (AppUtils.isBlank(categoryIds)) {
			return null;
		}

		// 获取分类名称
		R<List<Category>> resp = this.categoryServiceClient.findCategoryByIdsV2(categoryIds, SecurityConstants.FROM_IN);
		if (!resp.hasBody()) {
			return null;
		}
		List<Category> categoryList = resp.getResult();
		List<Category> categories = new ArrayList<>();

		for (Category category : categoryList) {
			Category c = new Category();
			c.setId(category.getId());
			c.setName(category.getName());
			categories.add(c);
		}
		return categories;
	}


	private List<SearchCategoryDTO> getCategoryAggV2(ParsedLongTerms terms) {
		//解析terms获取聚合的分类ids
		List<Long> categoryIds = terms.getBuckets().stream()
			.map(Terms.Bucket::getKeyAsNumber)
			.map(Number::longValue)
			.collect(Collectors.toList());
		if (CollUtil.isEmpty(categoryIds)) {
			return Collections.emptyList();
		}
		// 获取分类名称
		R<List<Category>> resp = this.categoryServiceClient.findCategoryByIdsV2(categoryIds, SecurityConstants.FROM_IN);
		return !resp.hasBody() ? Collections.emptyList() : resp.getResult().stream().map(c -> new SearchCategoryDTO(c.getId(), c.getName())).collect(Collectors.toList());
	}

	/**
	 * 获取商家聚合
	 *
	 * @param terms
	 * @return
	 */
	private List<ShopDetail> getShopAgg(Terms terms) {
		return null;
//		if (terms instanceof UnmappedTerms) {
//			log.info("没有聚合到商家");
//			return null;
//		}
//
//		//解析terms获取聚合的商家ids
//		List<Long> shopIds = terms.getBuckets()
//			.stream().map(b -> b.getKeyAsNumber().longValue())
//			.collect(Collectors.toList());
//
//		if (AppUtils.isBlank(shopIds)) {
//			return null;
//		}
//		R<List<ShopDetail>> result = shopDetailServiceClient.queryShopByshopIds(shopIds, SecurityConstants.FROM_IN);
//		if (!result.hasBody()) {
//			return null;
//		}
//		return result.getResult();
	}

	private List<Map<String, Object>> getSpecifications1(Long id, QueryBuilder basicQuery) {

		List<Map<String, Object>> specs = new ArrayList<>();
		// 1、根据分类查询规格参数
		R<List<ProductProperty>> specResp = this.specificationServiceClient.queryPropertyByCategoryId(id, SecurityConstants.FROM_IN);
		if (!specResp.hasBody()) {
			log.error("查询规格参数出错，cid={}", id);
			return null;
		}
		List<ProductProperty> productPropertyList = specResp.getResult();
		// 2、开始聚合
		NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
		// 3、在原有的搜索结果进行聚合
		queryBuilder.withQuery(basicQuery);


		for (ProductProperty prop : productPropertyList) {
			if (prop.getIsForSearch()) {
				queryBuilder.addAggregation(AggregationBuilders.terms(prop.getMemo()).field("specs." + prop.getMemo() + ".keyword"));
			}
		}

		// 4、获取聚合结果
		AggregatedPage<ProductDocument> aggResult =
			(AggregatedPage<ProductDocument>) this.productRepository.search(queryBuilder.build());

		Aggregations aggregations = aggResult.getAggregations();

		for (ProductProperty prop : productPropertyList) {
			if (prop.getIsForSearch()) {
				String memo = prop.getMemo();
				if (AppUtils.isBlank(memo)) {
					memo = prop.getPropName();
				}
				Aggregation aggregation = aggregations.get(memo);

				if (aggregation instanceof UnmappedTerms) {
					continue;
				}
				StringTerms stringTerms = (StringTerms) aggregation;
				if (stringTerms.getBuckets().size() == 0) {
					continue;
				}
				Map<String, Object> map = new HashMap<>();
				map.put("k", memo);
				map.put("options", stringTerms.getBuckets().stream().map(StringTerms.Bucket::getKeyAsString).collect(Collectors.toList()));

				specs.add(map);
			}
		}
		return specs;

	}

	/**
	 * 统计es和数据库索引数量
	 *
	 * @return
	 */
	public IndustrialIndexCountDTO reIndexCount(SearchReIndexCountDTO searchReIndexCount) {

		IndustrialIndexCountDTO industrialIndexCountDTO = new IndustrialIndexCountDTO();
		IndexOperations indexMroObeiProductOperations = elasticsearchRestTemplate.indexOps(IndustrialProductDocument.class);
		if (!indexMroObeiProductOperations.exists()) {
			log.error("索引不存在，请重建");
			indexMroObeiProductOperations.create();
		} else {
			List<IndustrialOuterShopConfig> industrialOuterShopConfigs = industrialOuterShopConfigService.queryAll();

			// 全部索引数量
			NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
			NativeSearchQuery searchQuery = queryBuilder.build();
			BoolQueryBuilder boolQuery3 = QueryBuilders.boolQuery();
			boolQuery3.filter(QueryBuilders.termQuery("status", "10"));
			queryBuilder.withFilter(boolQuery3);
			searchQuery.setTrackTotalHits(Boolean.TRUE);
			industrialIndexCountDTO.setTotalCount(elasticsearchRestTemplate.count(queryBuilder.build(), IndustrialProductDocument.class));

			// 非第三方的
			queryBuilder = new NativeSearchQueryBuilder();
			searchQuery = queryBuilder.build();
			BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
			boolQuery.must(QueryBuilders.termQuery("isMroSelf", IsMroSelfEnum.MRO_SELF.getType()));
			boolQuery.filter(QueryBuilders.termQuery("status", "10"));
			queryBuilder.withFilter(boolQuery);
			queryBuilder.withPageable(PageRequest.of(1, 1));
			searchQuery.setTrackTotalHits(Boolean.TRUE);
			SearchHits<IndustrialProductDocument> search = elasticsearchRestTemplate.search(queryBuilder.build(), IndustrialProductDocument.class);
			industrialIndexCountDTO.setMroTotalCount(search.getTotalHits());

			List<CountInfoDTO> countInfos = new ArrayList<>();
			for (IndustrialOuterShopConfig config : industrialOuterShopConfigs) {
				NativeSearchQueryBuilder queryBuilder1 = new NativeSearchQueryBuilder();
				BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
				if(AppUtils.isNotBlank(config.getShopId())){
					boolQuery1.must(QueryBuilders.termQuery("shopId", config.getShopId()));
				}
				boolQuery1.filter(QueryBuilders.termQuery("status", "10"));
				queryBuilder1.withFilter(boolQuery1);
				queryBuilder1.withPageable(PageRequest.of(1, 1));
				NativeSearchQuery searchQuery1 = queryBuilder1.build();
				//放开ES搜索个数的限制
				searchQuery1.setTrackTotalHits(Boolean.TRUE);
				SearchHits<IndustrialProductDocument> search1 = elasticsearchRestTemplate.search(searchQuery1, IndustrialProductDocument.class);
				CountInfoDTO countInfo = new CountInfoDTO();
				countInfo.setShopId(config.getShopId());
				countInfo.setName(config.getInnerName());
				countInfo.setCount(search1.getTotalHits());
				countInfos.add(countInfo);

			}
			industrialIndexCountDTO.setCountInfos(countInfos);

		}
		return industrialIndexCountDTO;
	}


	@Override
	public List<String> searchSuggestByKeyword(String keyword,Integer size) {
		final IndexOperations indexOperations = elasticsearchRestTemplate.indexOps(ProductDocument.class);
		if (!indexOperations.exists()) {
			log.error("索引不存在，请重建");
			throw new BusinessException("索引不存在，请重建!");
		}
		//构建搜索建议补全对象
		CompletionSuggestionBuilder completionSuggestionBuilder = SuggestBuilders.
				completionSuggestion("searchSuggestText"). // 使用fieldName入参进行标题联想(这里就是索引被@CompletionField注解标注的字段)
						prefix(keyword).                   // 关键字（参数传此）
						skipDuplicates(true)               // 重复过滤
				.size(size)                        // 匹配数量
				;
		//创建搜索提示对象 进行封装搜索补全
		SuggestBuilder suggestBuilder = new SuggestBuilder();
		//  completionSuggestionBuilder:随便起的搜索补全的名字
		suggestBuilder.addSuggestion("product-suggest", completionSuggestionBuilder);
		//查询es并反参
		SearchResponse searchResponse = elasticsearchRestTemplate.suggest(suggestBuilder, elasticsearchRestTemplate.getIndexCoordinatesFor(ProductDocument.class));
		//获取反参中的搜索补全结果
		Suggest.Suggestion<? extends Suggest.Suggestion.Entry<? extends Suggest.Suggestion.Entry.Option>> suggestionBuilder = searchResponse.getSuggest().getSuggestion("product-suggest");

		return suggestionBuilder.getEntries().stream().map(x -> x.getOptions().stream().map(y -> y.getText().toString()).collect(Collectors.toList())).findFirst().get();
	}
}
