package cn.legendshop.user.api.dto.accountBook;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class BookCompanySaveDTO {

	@NotNull(message = "账套ID不能为空")
	@ApiModelProperty(value = "账套ID", required = true)
	private Long accountBookId;

	@NotBlank(message = "授权企业用户ID不能为空")
	@ApiModelProperty(value = "子企业用户Id", required = true)
	private String userId;

	@ApiModelProperty(value = "合同")
	private String contract;

	@ApiModelProperty(value = "授权额度")
	private BigDecimal creditLine;

	@ApiModelProperty(value = "公司标识")
	private String companyId;

	@ApiModelProperty(value = "备注")
	private String remark;
}
