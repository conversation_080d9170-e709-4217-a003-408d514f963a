package cn.legendshop.user.api.dto.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UmsBnfInfo {

	//受益人姓名	String(20)	bnfList子元素
	private String bnfName;
	//受益人证件号	String(20)	bnfList子元素
	private String bnfCertno;
	//受益人证件开始日期	String(10)	bnfList子元素
	private String bnfCertBeginDate;
	//受益人证件有效期	String(10)	bnfList子元素
	private String bnfCertExpire;
	//受益人证件类型	String	String
	private String bnfCertType;
	//受益人家庭地址	String(60)	bnfList子元素
	private String bnfHomeAddr;
	//手机号格式 String（11）bnfList子元素 若上传受益人信息，则必填
	private String bnfMobile;

	//---------------------------------------------
	//以下字段为证件类型为非身份证时，必填

	//国籍 String(5)    bnfList子元素 0-中国
	private String bnfNationality;
	//性别 String  1-男性 2-女性
	private String bnfSex;
	//出生日期 格式:yyyy-MM-dd
	private String bnfBirthday;
	//受益人是否为特定自然人 1,是;0,否 特定自然人包括外国政要、国际组织高级管理人员，也包括其父母、配偶、子女等近亲属，及义务机构知道或者应当知道的通过工作、生活等产生共同利益关系的其他自然人
	private String bnfNatrlPrsn;
	//受益人图片列表 若上传受益人信息，则0042 受益人证件正面照和0043 受益人证件反面必填；当填写受益人姓名不包含在工商接口查询返回姓名列表内时，0041 受益人证明材料必传（商户类型为机关事业单位，0041 受益人证明材料选传）
    private List<UmsBnfPic> bnfPicList;
}
