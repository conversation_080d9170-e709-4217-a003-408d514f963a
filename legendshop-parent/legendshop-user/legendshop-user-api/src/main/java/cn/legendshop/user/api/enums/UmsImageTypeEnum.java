package cn.legendshop.user.api.enums;

import lombok.Getter;

@Getter
public enum UmsImageTypeEnum {

	LEGAL_ID_FRONT("0001","法人身份证"),

	LEGAL_ID_BACK("0011","身份证反面"),

	BUSINESS_LINCENSE("0002", "商户营业执照"),

	TAX_REGISTRATION("0003", "商户税务登记证"),

	ORGANIZATION_CODE("0004", "组织机构代码证"),

	ENTRANCE_IMAGE("0005", "门头照片"),

	ACCOUNT_LICENSE("0006", "开户许可证"),

	HAND_HOLD_ID_CARD_IMAGE("0007", "手持身份证自拍照"),

	AUXILIARY_IMAGE("0008", "辅助证明材料"),

	AUXILIARY_IMAGE_1("0013", "辅助证明材料1"),

	AUXILIARY_IMAGE_2("0014", "辅助证明材料2"),

	INNER_IMAGE("0015", "室内照片"),

	WEB_IMAGE("0034", "商户网站/APP截图"),

	OTHER("0099", "其他材料"),

	BANK_PIC_FRONT("0025" , "银行卡正面照"),

	BANK_PIC_BACK("0026" , "银行卡背面照"),

	NOT_ENTERPRISE_LICENSE("0032", "民办非登记证书"),

	BUSINESS_PROD_PIC("0021", "经营商品"),

	RENT_PIC("0016", "租赁协议"),

	PROPERTY_RIGHTS_PIC("0017", "产权证明"),

	PRACTICING_CERTIFICATE_PIC("0018", "执业资质证照"),

	THIRD_IDENTIFIER("0019", "第三方证明"),

	AUXILIARY_MATERIALS_PIC("0020", "其他小微商户证明材料"),

	BNF_ID_FRONT("0042", "受益人证件正面照"),

	BNF_ID_BACK("0043", "受益人证件反面"),

	BNF_PROOF_MATERIALS("0041", "受益人证明材料"),



	;


	private String type;

	private String name;

	UmsImageTypeEnum(String type, String name){
		this.type = type;
		this.name = name;
	}

}
