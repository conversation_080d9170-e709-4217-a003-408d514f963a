package cn.legendshop.user.api.model;

import com.legendshop.dao.persistence.*;
import com.legendshop.dao.support.GenericEntity;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "ls_account_book_company")
@NoArgsConstructor
@AllArgsConstructor
public class AccountBookCompany implements GenericEntity<Long> {

	private static final long serialVersionUID = 1L;

	@Column(name = "account_book_company_id")
	@GeneratedValue(strategy = GenerationType.TABLE, generator = "generator")
	@TableGenerator(name = "generator", pkColumnValue = "accountBookCompany_SEQ")
	@Id
	private Long id;

	@Column(name = "account_book_id")
	private Long accountBookId;

	@Column(name = "account_book_name")
	private String accountBookName;

	@Column(name = "user_id")
	private String userId;

	@Column(name = "user_name")
	private String userName;

	@Column(name = "company_id")
	private String companyId;

	@Column(name = "company")
	private String company;

	@Column(name = "account_code")
	private String accountCode;

	@Column(name = "contract")
	private String contract;

	@Column(name = "credit_line")
	private BigDecimal creditLine;

	@Column(name = "left_credit_line")
	private BigDecimal leftCreditLine;

	@Column(name = "status")
	private Integer status;

	@Column(name = "authorization_time")
	private Date authorizationTime;

	@Column(name = "operator")
	private String operator;

	@Column(name = "update_time")
	private Date updateTime;

	@Column(name = "create_time")
	private Date createTime;

	@Column(name = "remark")
	private String remark;

	public AccountBookCompany(Long accountBookId, String accountBookName, String userId, String company, String accountCode) {
		this.accountBookId = accountBookId;
		this.accountBookName = accountBookName;
		this.userId = userId;
		this.company = company;
		this.accountCode = accountCode;
	}

	@Override
	public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {
		this.id = id;
	}

	public Long getAccountBookId() {
		return accountBookId;
	}

	public void setAccountBookId(Long accountBookId) {
		this.accountBookId = accountBookId;
	}

	public String getAccountBookName() {
		return accountBookName;
	}

	public void setAccountBookName(String accountBookName) {
		this.accountBookName = accountBookName;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getContract() {
		return contract;
	}

	public void setContract(String contract) {
		this.contract = contract;
	}

	public BigDecimal getCreditLine() {
		return creditLine;
	}

	public void setCreditLine(BigDecimal creditLine) {
		this.creditLine = creditLine;
	}

	public BigDecimal getLeftCreditLine() {
		return leftCreditLine;
	}

	public void setLeftCreditLine(BigDecimal leftCreditLine) {
		this.leftCreditLine = leftCreditLine;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getAuthorizationTime() {
		return authorizationTime;
	}

	public void setAuthorizationTime(Date authorizationTime) {
		this.authorizationTime = authorizationTime;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
