package cn.legendshop.user.api.model;


import java.util.Date;
import com.legendshop.dao.support.GenericEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.legendshop.dao.persistence.*;

/**
 * 进件干系人表(ShopCertifyMans)实体类
 *
 * <AUTHOR> wu
 * @since 2022-12-05 11:07:32
 */
@Entity
@Table(name = "ls_ums_certify_mans")
public class UmsCertifyMans implements GenericEntity<Long>  {

    private static final long serialVersionUID = 947427196853511574L;
    
    /**
    * ID
    */    
    @Column(name = "id")
    @Id
	@GeneratedValue(strategy = GenerationType.TABLE, generator = "generator")
	@TableGenerator(name = "generator", pkColumnValue = "shopCertifyMans_SEQ")
    private Long id;

	/**
	 * 进件ID
	 */
	@Column(name = "shop_id")
	private Long shopId;

	@Column(name = "ums_certify_id")
	private Long umsCertifyId;
    
    /**
    * 干系人类型 见enum
    */    
    @Column(name = "man_type")
    private Integer manType;
    
    /**
    * 干系人姓名
    */    
    @Column(name = "man_name")
    private String manName;
    
    /**
    * 证件类型 默认 10-身份证
    */    
    @Column(name = "cert_type")
    private Integer certType;
    
    /**
    * 证件号码
    */    
    @Column(name = "cert_no")
    private String certNo;

	/**
	 * 证件开始日期
	 */
	@Column(name = "cert_begin_date")
	private String certBeginDate;

    
    /**
    * 证件失效日期
    */    
    @Column(name = "cert_expire_date")
    private String certExpireDate;

    
    /**
    * 创建时间
    */    
    @Column(name = "create_time")
    private Date createTime;

	/**
	 * 创建时间
	 */
	@Column(name = "update_time")
	private Date updateTime;

    
    /**
    * 身份证人像面照片
    */    
    @Column(name = "face_image_file")
    private String faceImageFile;
    
    /**
    * 身份证国徽面照片
    */    
    @Column(name = "national_image_file")
    private String nationalImageFile;

    @Column(name = "cert_addr")
    private String certAddr;

	/**
	 * 电话号码
	 */
	@Column(name = "phone_num")
	private String phoneNum;

	/**
	 * 受益人是否为特定自然人1,是;0,否
	 */
	@Column(name = "bnf_natrl_prsn")
	private Integer bnfNatrlPrsn;

	/**
	 * '受益人国籍 若上传受益人证件类型为非身份证，则必填'; 具体类型可以看前端
	 */
	@Column(name = "bnf_nationality")
	private Integer bnfNationality;

	/**
	 * '受益人性别 若上传受益人证件类型为非身份证，则必填';
	 */
	@Column(name = "bnf_sex")
	private Integer bnfSex;

	/**
	 * '受益人出生日期 若上传受益人证件类型为非身份证，则必填'
	 */
	@Column(name = "bnf_birthday")
	private String bnfBirthday;

	/**
	 * '受益人证明材料 当填写受益人姓名不包含在工商接口查询返回姓名列表内时，则必填'
	 */
	@Column(name = "bnf_pic_list")
	private String bnfPicList;



	public Long getUmsCertifyId() {
		return umsCertifyId;
	}

	public void setUmsCertifyId(Long umsCertifyId) {
		this.umsCertifyId = umsCertifyId;
	}

	public String getCertAddr() {
		return certAddr;
	}

	public void setCertAddr(String certAddr) {
		this.certAddr = certAddr;
	}

	@Override
	public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {
		this.id = id;
	}

	public String getPhoneNum() {
		return phoneNum;
	}

	public void setPhoneNum(String phoneNum) {
		this.phoneNum = phoneNum;
	}


	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}


	public Integer getManType() {
		return manType;
	}

	public void setManType(Integer manType) {
		this.manType = manType;
	}

	public String getManName() {
		return manName;
	}

	public void setManName(String manName) {
		this.manName = manName;
	}

	public Integer getCertType() {
		return certType;
	}

	public void setCertType(Integer certType) {
		this.certType = certType;
	}

	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	public String getCertExpireDate() {
		return certExpireDate;
	}

	public void setCertExpireDate(String certExpireDate) {
		this.certExpireDate = certExpireDate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getFaceImageFile() {
		return faceImageFile;
	}

	public void setFaceImageFile(String faceImageFile) {
		this.faceImageFile = faceImageFile;
	}

	public String getNationalImageFile() {
		return nationalImageFile;
	}

	public void setNationalImageFile(String nationalImageFile) {
		this.nationalImageFile = nationalImageFile;
	}

	public String getCertBeginDate() {
		return certBeginDate;
	}

	public void setCertBeginDate(String certBeginDate) {
		this.certBeginDate = certBeginDate;
	}

	public Integer getBnfNatrlPrsn() {
		return bnfNatrlPrsn;
	}

	public void setBnfNatrlPrsn(Integer bnfNatrlPrsn) {
		this.bnfNatrlPrsn = bnfNatrlPrsn;
	}

	public Integer getBnfNationality() {
		return bnfNationality;
	}

	public void setBnfNationality(Integer bnfNationality) {
		this.bnfNationality = bnfNationality;
	}

	public Integer getBnfSex() {
		return bnfSex;
	}

	public void setBnfSex(Integer bnfSex) {
		this.bnfSex = bnfSex;
	}

	public String getBnfBirthday() {
		return bnfBirthday;
	}

	public void setBnfBirthday(String bnfBirthday) {
		this.bnfBirthday = bnfBirthday;
	}

	public String getBnfPicList() {
		return bnfPicList;
	}

	public void setBnfPicList(String bnfPicList) {
		this.bnfPicList = bnfPicList;
	}
}
