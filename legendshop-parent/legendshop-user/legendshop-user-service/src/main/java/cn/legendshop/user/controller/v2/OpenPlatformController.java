package cn.legendshop.user.controller.v2;


import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.common.model.query.OpenPlatFormAppQuery;
import cn.legendshop.common.model.query.OpenPlatFormQuery;
import cn.legendshop.common.model.vo.IndustrialOpenPlatformAppVo;
import cn.legendshop.common.model.vo.IndustrialOuterPlatFormVo;
import cn.legendshop.user.api.dto.openPlatform.OpenPlatformDTO;
import cn.legendshop.user.service.AdminUserService;
import cn.legendshop.user.service.OpenPlatformService;
import com.legendshop.dao.support.PageSupport;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 开发平台controller
 */

@RestController
@RequestMapping("/v2/openPlatform")
@AllArgsConstructor
public class OpenPlatformController {


	private final OpenPlatformService openPlatformService;

	private final AdminUserService adminUserService;


	/**
	 * 创建工业品开发平台
	 *
	 * @param openPlatformDTO
	 * @return
	 */
	@PostMapping("/create")
	public R createOpenPlatform(@Validated  @RequestBody OpenPlatformDTO openPlatformDTO) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		if (adminUserService.getAdminUser(userId) == null) {
			return R.fail("非管理员用户，无权限创建");
		}
		return openPlatformService.createPlatform(openPlatformDTO);

	}



	/**
	 * 查询工业品开放平台
	 *
	 * @param IndustrialOpenPlatformAppVo
	 * @return
	 */
	@PostMapping("/queryAppList")
	public R<PageSupport<IndustrialOpenPlatformAppVo>> queryAppList(@RequestBody OpenPlatFormAppQuery openPlatFormAppQuery){
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		if (adminUserService.getAdminUser(userId) == null) {
			return R.fail("非管理员用户，无权限查询");
		}
		return R.ok(openPlatformService.queryAppList(openPlatFormAppQuery));
	}


	/**
	 * 查询工业品开放平台
	 *
	 * @param openPlatFormQuery
	 * @return
	 */
	@PostMapping("/queryList")
	public R<PageSupport<IndustrialOuterPlatFormVo>> queryList(@RequestBody OpenPlatFormQuery openPlatFormQuery) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		if (adminUserService.getAdminUser(userId) == null) {
			return R.fail("非管理员用户，无权限查询");
		}
		PageSupport<IndustrialOuterPlatFormVo> industrialOuterPlantFormVoPageSupport = openPlatformService.queryList(openPlatFormQuery);
		return R.ok(industrialOuterPlantFormVoPageSupport);
	}


	/**
	 * 更新工业品开放平台
	 *
	 * @param openPlatformDTO
	 * @return
	 */
	@PostMapping("/update")
	public R updatePlatform(@Validated @RequestBody OpenPlatformDTO openPlatformDTO) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		if (adminUserService.getAdminUser(userId) == null) {
			return R.fail("非管理员用户，无权限更新");
		}
		return openPlatformService.update(openPlatformDTO);
	}


	/**
	 * 删除工业品开放平台
	 */
	@GetMapping("/delete")
	public R delete(@RequestParam(name = "platformId") Long platformId) {
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		String userId = baseUserDetailDTO.getUserId();
		if (adminUserService.getAdminUser(userId) == null) {
			return R.fail("非管理员用户，无权限删除");
		}
		return openPlatformService.delete(platformId);
	}


}
