package cn.legendshop.user.controller.v2;

import cn.hutool.core.util.ObjectUtil;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.user.api.constant.UmsConstant;
import cn.legendshop.user.api.dto.UmsAccountVerifyDTO;
import cn.legendshop.user.api.dto.UmsAddressCodeDTO;
import cn.legendshop.user.api.dto.UmsAuditInfo;
import cn.legendshop.user.api.model.UmsMcc;
import cn.legendshop.user.api.vo.*;
import cn.legendshop.user.service.ShopCertifyService;
import cn.legendshop.user.service.ShopCertifyUmsService;
import cn.legendshop.user.service.UmsCertifyService;
import com.legendshop.common.data.cache.util.RedisUtil;
import com.legendshop.common.excel.util.EasyExcelUtils;
import com.legendshop.dao.support.PageSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/v2/shopCertify")
@AllArgsConstructor
@Api(tags = "V2银联进件")
public class UmsCertifyController {

	private final ShopCertifyService shopCertifyService;

	private final ShopCertifyUmsService shopCertifyUmsService;

	private final UmsCertifyService umsCertifyService;

	private final RedisUtil redisUtil;

	/**
	 * 进件页面打开
	 */
	@ApiModelProperty
	@PostMapping("shopCertifyApply")
	@ApiOperation(value = "打开进件页面获取信息", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<ShopCertifyVo> shopCertifyApply(){
		String shopId = UserContextHolder.getInstance().getContext().getShopId();
		ShopCertifyVo applyInfo = this.umsCertifyService.getApplyInfo(Long.parseLong(shopId));
		//返回的值为null则是第一次申请
		return R.ok(applyInfo);
	}

	/**
	 * 进件保存信息
	 * @param shopCertifyVo
	 * @return
	 */
	@PostMapping("/umsShopCertify")
	@ApiOperation(value = "保存进件信息", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> umsShopCertify(@RequestBody ShopCertifyVo shopCertifyVo){
		Object object = redisUtil.getObject(UmsConstant.UMS_CER_APPLY_LOCK + UserContextHolder.getInstance().getContext().getShopId());
		if(ObjectUtil.isEmpty(object)){
			Integer status = umsCertifyService.checkStatus(Long.parseLong(UserContextHolder.getInstance().getContext().getShopId()));
			if(status != 0){
				return R.ok();
			}
			umsCertifyService.applyUmsServiceSave(shopCertifyVo);
			return R.ok();
		}else {
			return R.fail("正在上传资料");
		}
	}

	/**
	 * 3.37  控股股东/受益人姓名查询接口
	 * @param beneficiaryNameVo
	 * @return
	 */
	@PostMapping("/umsBeneficiaryName")
	@ApiOperation(value = "控股股东/受益人姓名查询接口", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> umsBeneficiaryName(@RequestBody BeneficiaryNameVo beneficiaryNameVo){
		return umsCertifyService.umsBeneficiaryName(beneficiaryNameVo);
	}

	@PostMapping("/platformAuditList")
	@ApiOperation(value = "平台审核进件信息列表", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<PageSupport<UmsAuditInfo>> platformAuditList(@RequestBody PlatformAuditConditVo platformAuditConditVo){
		return this.umsCertifyService.getCertifyList(platformAuditConditVo);
	}

	@PostMapping("/exportAuditedInfo")
	@ApiOperation(value = "平台审核进件信息列表", produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportAuditedInfo(HttpServletResponse response, @RequestBody ExportAuditedVo exportAuditedVo) throws IOException {
		List<UmsAuditInfo> umsAuditInfos = this.umsCertifyService.exportAuditedInfo(exportAuditedVo);
		EasyExcelUtils.exportLocalByExcel(response, umsAuditInfos, "银联进件数据", UmsAuditInfo.class);
	}

	@PostMapping("/platformAuditInfo")
	@ApiOperation(value = "平台查看进件信息", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<ShopCertifyVo> platformAuditInfo(@RequestBody PlatformAuditConditVo platformAuditConditVo){
		return R.ok(this.umsCertifyService.getApplyInfo(platformAuditConditVo.getShopId()));
	}

	@PostMapping("/platformAudit")
	@ApiOperation(value = "平台审核进件信息", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> platformAudit(@RequestBody PlatformAuditVo platformAuditVo){
		return this.umsCertifyService.platformAudit(platformAuditVo);
	}

	@GetMapping("/resetApplyStatus")
	@ApiOperation(value = "重置进件状态", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> resetApplyStatus(@RequestParam Long shopId,@RequestParam Boolean onlyMerNo){
		return umsCertifyService.resetApplyStatus(shopId,onlyMerNo);
	}

	/**
	 * 发起对公验证
	 * @return
	 */
	@PostMapping("/bussinessAccountCheckApply")
	@ApiOperation(value = "发起对公验证", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<UmsAccountVerifyDTO> bussinessAccountCheckApply(){
		String shopId = UserContextHolder.getInstance().getContext().getShopId();
		return this.shopCertifyUmsService.bankAccountCheckApply(Long.parseLong(shopId));
	}

	/**
	 * 验证金额
	 * @param umsAccountCheckVo
	 * @return
	 */
	@PostMapping("/bussinessAccountCheck")
	@ApiOperation(value = "验证金额", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<UmsAccountVerifyDTO> bussinessAccountCheck(@RequestBody UmsAccountCheckVo umsAccountCheckVo){
		String shopId = UserContextHolder.getInstance().getContext().getShopId();
		return this.shopCertifyUmsService.bankAccountCheck(Long.parseLong(shopId), umsAccountCheckVo.getNum());
	}

	/**
	 * 拼接前台签约地址
	 * @return
	 */
	@PostMapping("/umsSign")
	@ApiOperation(value = "发起前台签约--返回拼接好的前台签约地址", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> umsSign(){
		String shopId = UserContextHolder.getInstance().getContext().getShopId();
		return this.shopCertifyUmsService.umsSign(Long.parseLong(shopId));
	}

	/**
	 * 商家信息变更
	 * @param umsInfoChangeVo
	 * @return
	 */
	@PostMapping("/umsInfoChange")
	@ApiOperation(value = "银行卡信息变更", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> umsInfoChange(@RequestBody UmsInfoChangeVo umsInfoChangeVo){
		return this.shopCertifyUmsService.changeReceiverApply(umsInfoChangeVo);
	}

		@PostMapping("/bankNoSearch")
	@ApiOperation(value = "支行编号查询", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<BankInfoVo>> bankNoSearch(@RequestBody UmsBankNameRequestVO bankName){
		return this.shopCertifyUmsService.bankNoSearch(bankName);
	}

	/**
	 * 申请状态回退
	 */
	@PostMapping("/reSign")
	public R<String> reSign(){
		String shopId = UserContextHolder.getInstance().getContext().getShopId();
		return this.shopCertifyUmsService.resign(Long.parseLong(shopId));
	}

	@PostMapping("/addressInfo")
	@ApiOperation(value = "省市区查询", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<UmsAddressCodeDTO>> getInfo(@RequestBody UmsAddressInfoVo umsAddressInfoVo){
		return R.ok(this.umsCertifyService.getAddressCode(umsAddressInfoVo));
	}

	@PostMapping("/mcc")
	@ApiOperation(value = "mcc编码查询", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<UmsMcc>> getMccInfo(@RequestBody UmsMccVo umsMccVo){
		return R.ok(this.umsCertifyService.getMccInfo(umsMccVo));
	}
}
