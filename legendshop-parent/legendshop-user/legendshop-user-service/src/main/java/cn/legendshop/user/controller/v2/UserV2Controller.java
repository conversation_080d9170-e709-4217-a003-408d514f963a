package cn.legendshop.user.controller.v2;


import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.common.utils.IPHelper;
import cn.legendshop.user.api.UpdateUserPasswordDTO;
import cn.legendshop.user.api.dto.MenuTree;
import cn.legendshop.user.api.dto.UserCheckDTO;
import cn.legendshop.user.api.dto.UserLoginFreeDTO;
import cn.legendshop.user.service.UserManagerService;
import com.legendshop.model.form.UserMobileForm;
import com.legendshop.model.vo.RegCompanyInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE;

@RestController
@AllArgsConstructor
@Api(tags = "新采购平台用户管理")
@RequestMapping("/v2/user")
public class UserV2Controller {


	@Autowired
	private UserManagerService userManagerService;


	/**
	 * 检查用户名是否合法
	 *
	 * @param userCheckDTO 用户名称
	 * @return
	 */
	@PostMapping("/checkUserName")
	@ApiOperation(value = "检查用户名", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<Boolean> checkUserName(@RequestBody UserCheckDTO userCheckDTO) {
		return userManagerService.checkUserName(userCheckDTO.getUserName());
	}


	/**
	 * 修改密码
	 */
	@PostMapping("/updatePassword")
	@ApiOperation(value = "修改密码", produces = MediaType.APPLICATION_JSON_VALUE)
	public R updatePassword(@RequestBody UpdateUserPasswordDTO updateUserPasswordDTO) {
		return userManagerService.updatePassword(updateUserPasswordDTO);
	}

	@PostMapping("/updatePaymentPassword")
	@ApiOperation(value = "修改支付密码", produces = MediaType.APPLICATION_JSON_VALUE)
	public R updatePaymentPassword(@RequestBody UpdateUserPasswordDTO updateUserPasswordDTO) {
		return userManagerService.updatePaymentPassword(updateUserPasswordDTO);
	}


	/**
	 * 判断用户的登录类型
	 *
	 * @param userCheckDTO 用户名称
	 * @return
	 */
	@PostMapping("/getUserType")
	@ApiOperation(value = "检查用户的登录类型", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> getUserType(@RequestBody UserCheckDTO userCheckDTO) {
		return userManagerService.checkUserLoginType(userCheckDTO.getUserName());
	}

	@PostMapping(value = "/getLoginFreeUrl")
	@ApiOperation(value = "获取登录免密url", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> getLoginFreeUrl(@RequestBody UserLoginFreeDTO userLoginFreeDTO) {
		return userManagerService.getLoginFreeUrl(userLoginFreeDTO);
	}



	/**
	 * 企业用户通过手机号注册
	 *
	 * @param userMobileForm
	 * @return
	 */
	@PostMapping("/regCompanyByPhone")
	@ApiOperation(value = "企业注册", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<String> userRegCompanyByPhone(@RequestBody UserMobileForm userMobileForm) {
		try {
			userMobileForm.setIp(IPHelper.getIp());
		} catch (Exception e) {
			userMobileForm.setIp("127.0.0.1");
		}
		if (userMobileForm.getMobileCode()== null || "".equals(userMobileForm.getMobileCode())) {
			return R.fail("请输入验证码");
		}
		R<String> registeredResult = userManagerService.regCompanyByPhone(userMobileForm);
		return registeredResult;
	}

	/**
	 * 获取企业用户信息(启信宝获取，用于注册时获取信息)
	 *
	 * @param uniformNo
	 * @return
	 */
	@GetMapping("/getCompanyInfo")
	@ApiOperation(value = "获取企业用户信息", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<RegCompanyInfo> getCompanyInfo(@RequestParam(required = false) String uniformNo) {
		return userManagerService.getCompanyInfo(uniformNo);
	}


	@GetMapping("/managerMenu")
	@ApiOperation(value = "获取用户登录的菜单", produces = MediaType.APPLICATION_JSON_VALUE)
	public R<List<MenuTree>> managerMenu(){
		BaseUserDetailDTO baseUserDetailDTO = UserContextHolder.getInstance().getContext();
		return 	R.ok(userManagerService.getManagerMenu(baseUserDetailDTO));
	}
}
