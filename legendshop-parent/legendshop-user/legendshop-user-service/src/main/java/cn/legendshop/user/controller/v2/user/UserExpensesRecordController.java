/*
 * 
* LegendShop 多用户商城系统
 * 
 *  版权所有,并保留所有权利。
 * 
 */
package cn.legendshop.user.controller.v2.user;

import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.core.utils.UserContextHolder;
import cn.legendshop.user.api.query.CancelQuery;
import cn.legendshop.user.api.query.DateQuery;
import cn.legendshop.user.service.ExpensesRecordService;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.model.entity.ExpensesRecord;
import com.legendshop.util.AppUtils;

/**
 * 消费记录控制器
 *
 */
@RestController
@RequestMapping("/v2/p")
@Api(tags = "消费记录")
@AllArgsConstructor
@Slf4j
public class UserExpensesRecordController {

	private final ExpensesRecordService expensesRecordService;

	@PostMapping("/expensesRecord")
	public R<PageSupport<ExpensesRecord>> query(@RequestBody DateQuery dateQuery) {
		String userId = UserContextHolder.getInstance().getContext().getUserId();
		if(AppUtils.isBlank(userId)) {
			throw new RuntimeException("请先登录后查看");
		}
		return R.ok(expensesRecordService.getExpensesRecordCount(dateQuery, userId));
	}

	/**
	 * 删除消费记录
	 * @param id
	 * @return
	 */
	@GetMapping(value = "/expensesRecord/delete/{id}")
	public R expensesRecordDel(@PathVariable Long id) {
		String userId = UserContextHolder.getInstance().getContext().getUserId();
		boolean result = expensesRecordService.deleteExpensesRecord(userId, id);
		if (result) {
			return R.ok();
		} else {
			return R.fail();
		}
	}

	/**
	 * 批量删除消费记录
	 */
	@PostMapping(value = "/deleteExpensesRecord")
	public R deleteExpensesRecord(@RequestBody CancelQuery cancelQuery) {
		String userId = UserContextHolder.getInstance().getContext().getUserId();
		if (userId == null) {
			return R.fail();
		}
		if (AppUtils.isNotBlank(cancelQuery.getIds())) {
			expensesRecordService.deleteExpensesRecord(userId, JSON.toJSONString(cancelQuery.getIds()));
		}
		return R.ok();
	}
}
