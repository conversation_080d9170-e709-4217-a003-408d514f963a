package cn.legendshop.user.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.legendshop.bestsign.api.client.BestSignClient;
import cn.legendshop.bestsign.api.dto.AuthAccountDTO;
import cn.legendshop.bestsign.api.dto.CompanyCredentialDTO;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.dto.BaseUserDetailDTO;
import cn.legendshop.common.core.enums.AccountBookEnum;
import cn.legendshop.common.core.enums.UserTypeEnum;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.rabbitmq.utils.ThirdLogSendMsgUtil;
import cn.legendshop.common.utils.IPHelper;
import cn.legendshop.common.utils.StringUtils;
import cn.legendshop.tender.api.client.UserCompanyVendorInfoClient;
import cn.legendshop.tender.api.dto.RegsUserCompanyVendorInfoDTO;
import cn.legendshop.tender.api.dto.UserCompanyVendorInfoDTO;
import cn.legendshop.tender.api.entity.UserCompanyVendorInfo;
import cn.legendshop.user.api.UpdateUserPasswordDTO;
import cn.legendshop.user.api.constant.OrdinaryMenuConstant;
import cn.legendshop.user.api.constant.OrdinaryUserRoleConstant;
import cn.legendshop.user.api.dto.MenuTree;
import cn.legendshop.user.api.dto.UserLoginFreeDTO;
import cn.legendshop.user.api.dto.VerifyUserDTO;
import cn.legendshop.user.api.dto.auth.OrdinaryUserDTO;
import cn.legendshop.user.api.entity.ShopV2Role;
import cn.legendshop.user.api.query.MenuQuery;
import cn.legendshop.user.api.vo.UserMessageVO;
import cn.legendshop.user.service.*;
import com.legendshop.common.data.cache.util.RedisUtil;
import com.legendshop.dao.impl.GenericDaoImpl;
import com.legendshop.model.dto.UserAccountDTO;
import com.legendshop.model.entity.User;
import com.legendshop.model.entity.UserDetail;
import com.legendshop.model.form.UserMobileForm;
import com.legendshop.model.vo.RegCompanyInfo;
import com.legendshop.util.AppUtils;
import com.legendshop.util.HttpUtil;
import com.legendshop.util.JSONUtil;
import com.legendshop.meituan.dto.LoginFreeDTO;
import com.legendshop.meituan.sqt.request.in.login.LoginStaffInfo;
import com.legendshop.meituan.sqt.response.in.BaseApiResponse;
import com.legendshop.meituan.sqt.service.MeituanApiService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import cn.legendshop.user.utils.CryptoUtil;


@Service
@Slf4j
public class UserManagerServiceImpl implements UserManagerService {

	private final String findCompanyInfo = "http://mq.nisco.cn/erp/mqz/mqzcDataToMRO";

	private final String getCompanyInfo = "https://www.njxic.com/bidprocurement/common-user/user/registerCenter/getDataByQixinHub";

	private final String isUserNameRegex = "/^(?![_0-9])(?!.*?_$)[a-zA-Z0-9_]{4,16}$/";


	private final String isTenderM1 = "/^(?![_0-9])(?!.*?_$)[a-zA-Z0-9_]{4,16}$/";
	private final String isTenderW1 = "/^(?![_0-9])(?!.*?_$)[a-zA-Z0-9_]{4,16}$/";
	private final String isTenderZ1 = "/^(?![_0-9])(?!.*?_$)[a-zA-Z0-9_]{4,16}$/";


	@Autowired
	private ThirdLogSendMsgUtil thirdLogSendMsgUtil;


	@Autowired
	private BestSignClient bestSignClient;


	@Autowired
	private UserCompanyVendorInfoClient userCompanyVendorInfoClient;


	@Autowired
	private PasswordEncoder passwordEncoder;

	@Autowired
	private UserCouponService userCouponService;

	@Autowired
	private UserDetailService userDetailService;

	@Autowired
	private AccountPeriodService accountPeriodService;

	@Autowired
	private SmsVerificationCodeService smsVerificationCodeService;


	@Autowired
	private OrdinaryMenuService ordinaryMenuService;


	@Autowired
	private OrdinaryUserService ordinaryUserService;

	@Autowired
	private ShopDetailService shopDetailService;

	@Autowired
	private ShopV2RoleService shopV2RoleService;
	
	@Autowired
	private MeituanApiService meituanApiService;

	@Autowired
	private RedisUtil redisUtil;

	@Override
	public R<Boolean> checkUserName(String userName) {
		if (AppUtils.isBlank(userName)) {
			return R.fail(false, "用户名不能为空");
		}
		if ((userName.length() < 4 || userName.length() > 16)) {
			return R.fail(false, "用户名长度不正确，应在4-16个字符之间");
		}
		Pattern isUserNameRegexPattern = Pattern.compile(isUserNameRegex);
		Pattern isTenderM1RegexPattern = Pattern.compile(isTenderM1);
		Pattern isTenderW1RegexPattern = Pattern.compile(isTenderW1);
		Pattern isTenderZ1RegexPattern = Pattern.compile(isTenderZ1);
		if (isTenderZ1RegexPattern.matcher(userName).matches() || isTenderM1RegexPattern.matcher(userName).matches() || isTenderW1RegexPattern.matcher(userName).matches()) {
			return R.fail(false, "不能以M开头，Z开头不能为6位，W开头不能为9位!");
		}

		if (isUserNameRegexPattern.matcher(userName).matches()) {
			return R.fail(false, "须以字母开头，字母和数字组合，不能以大写的Z、W、M字母开头，不能以_结尾!");
		}

		UserDetail userDetail = userDetailService.getUserDetailByUserName(userName);
		if (AppUtils.isNotBlank(userDetail)) {
			return R.fail(false, "用户名错误!");
		}


		return R.ok(Boolean.TRUE);
	}


	@Override
	public R updatePassword(UpdateUserPasswordDTO updateUserPasswordDTO) {

		// 1.校验验证码
		VerifyUserDTO verifyUserDTO = new VerifyUserDTO();
		verifyUserDTO.setMobile(updateUserPasswordDTO.getMobile());
		verifyUserDTO.setCodeType(updateUserPasswordDTO.getSmsTemplateType());
		verifyUserDTO.setCode(updateUserPasswordDTO.getCode());
		verifyUserDTO.setIsPasswordCheck(updateUserPasswordDTO.getIsPasswordCheck());
		verifyUserDTO.setUserType(UserTypeEnum.USER);
		R<String> codeExchangeCertificate = smsVerificationCodeService.verifyCode(verifyUserDTO);
		if (!codeExchangeCertificate.getSuccess()) {
			return R.fail(codeExchangeCertificate.getMsg());
		}
		//2.校验密码
		if (!updateUserPasswordDTO.getNewPassword().equals(updateUserPasswordDTO.getConfirmNewPassword())) {
			return R.fail("两次数据的密码不一致!");
		}
		UserDetail userDetail = new UserDetail();
		if(updateUserPasswordDTO.getIsPasswordCheck().equals(true) && !StringUtils.isBlank(updateUserPasswordDTO.getUserName())){
			//在确认是因为密码校验规则所需要密码重置，考虑到传入的账号有可能是手机号情况，进行筛选
			 userDetail = userDetailService.getUserDetailByUserName(updateUserPasswordDTO.getUserName());
			 if(AppUtils.isBlank(userDetail)){
				 userDetail = userDetailService.getUserDetailByMobile(updateUserPasswordDTO.getUserName());
			 }
		} else {
			userDetail = userDetailService.getUserDetailByMobile(updateUserPasswordDTO.getMobile());
		}
		if (AppUtils.isBlank(userDetail)){
			String ordinaryId =  userDetailService.getOrdinaryIdByUserName(updateUserPasswordDTO.getUserName());
			if(StringUtils.isBlank(ordinaryId)){
				return R.fail("用户信息异常!");
			}
			return R.fail("该账号为子账号，请于主账号权限管理-管理员工信息页面修改密码!");
		}
		//3.更新密码
		User user = userDetailService.getUser(userDetail.getUserId());
		user.setPassword(passwordEncoder.encode(updateUserPasswordDTO.getNewPassword()));
		userDetailService.updateUser(user);
		return R.ok();
	}


	@Override
	public R updatePaymentPassword(UpdateUserPasswordDTO updateUserPasswordDTO) {
		// 1.校验验证码
		VerifyUserDTO verifyUserDTO = new VerifyUserDTO();
		verifyUserDTO.setMobile(updateUserPasswordDTO.getMobile());
		verifyUserDTO.setCodeType(updateUserPasswordDTO.getSmsTemplateType());
		verifyUserDTO.setCode(updateUserPasswordDTO.getCode());
		verifyUserDTO.setUserType(UserTypeEnum.USER);
		R<String> codeExchangeCertificate = smsVerificationCodeService.verifyCode(verifyUserDTO);
		if (!codeExchangeCertificate.getSuccess()) {
			return R.fail(codeExchangeCertificate.getMsg());
		}
		//2.校验密码
		if (!updateUserPasswordDTO.getNewPassword().equals(updateUserPasswordDTO.getConfirmNewPassword())) {
			return R.fail("两次数据的密码不一致!");
		}
		//3.更新密码
		UserDetail userDetail = userDetailService.getUserDetailByMobile(updateUserPasswordDTO.getMobile());
		userDetail.setPayPassword(passwordEncoder.encode(updateUserPasswordDTO.getNewPassword()));
		userDetailService.updateUserDetail(userDetail);
		return R.ok();
	}

	@Override
	public R<String> checkUserLoginType(String userName) {
		UserAccountDTO userAccountDTO = userDetailService.getUserAccountDTO(userName);
		if (AppUtils.isBlank(userAccountDTO)) {
			return R.ok(UserTypeEnum.USER.getLoginType());
		}
		if ("ordinary_member".equals(userAccountDTO.getAccountType())) {
			return R.ok(UserTypeEnum.MEMBER_USER.getLoginType());
		} else {
			return R.ok(UserTypeEnum.USER.getLoginType());
		}
	}

	@Override
	@GlobalTransactional
	public R<String> regCompanyByPhone(UserMobileForm userMobileForm) {
		log.info("企业用户注册");
		String mobileCode = userMobileForm.getMobileCode();
		String code = redisUtil.getString("SMSMobileCode"+userMobileForm.getMobile());
		if(!mobileCode.equals(code)){
			return R.fail("短信验证码错误");
		}

		//1.检查用户名
		R<Boolean> booleanR = checkUserName(userMobileForm.getUserName());
		if (!booleanR.getResult()) {
			return R.fail(booleanR.getMsg());
		}
		//2.企业注册
		R<Map> enterpriseInfoVerify = enterpriseInfoVerify(userMobileForm);
		if (!enterpriseInfoVerify.isSuccess()) {
			return R.fail(enterpriseInfoVerify.getMsg());
		}
		Map dataMap = enterpriseInfoVerify.getResult();
		//3.赋予工业品角色
		OrdinaryUserDTO ordinaryUserDTO = new OrdinaryUserDTO();
		ordinaryUserDTO.setUserId(String.valueOf(dataMap.get("userId")));
		List<Long> roleId = new ArrayList<>();
		roleId.add(OrdinaryUserRoleConstant.INDUSTIRAL_ROLE_APP_ROLE_ID);
		ordinaryUserDTO.setRoleIdList(roleId);
		ordinaryUserService.updateUserRole(ordinaryUserDTO);
		//4.添加商家端用户,并赋予需要开店的角色
		shopDetailService.addShopUserV2(String.valueOf(dataMap.get("userId")),userMobileForm.getRealName(),userMobileForm.getLegalPersonIdentity());
		return R.ok();
	}


	@Override
	public List<MenuTree> getManagerMenu(BaseUserDetailDTO baseUserDetailDTO) {
		//1.查询主账号的菜单
		MenuQuery menuQuery = new MenuQuery();
		if (UserTypeEnum.MEMBER_USER.getLoginType().equals(baseUserDetailDTO.getUserType())) {
			menuQuery.setSubUserId(baseUserDetailDTO.getSubUserId());
			menuQuery.setUserType(baseUserDetailDTO.getUserType());
		}
		menuQuery.setUserId(baseUserDetailDTO.getUserId());
		menuQuery.setTopId(OrdinaryMenuConstant.ORDINARY_MEMBER_MENU_TOP_ID);
		List<MenuTree> menuTrees = ordinaryMenuService.treeUserMenu(menuQuery);

		return menuTrees;
	}

	// 校验企业信息并进行注册
	private R<Map> enterpriseInfoVerify(UserMobileForm userMobileForm) {
		R<List<UserCompanyVendorInfoDTO>> companyVendorInfoClientByUniformNo = userCompanyVendorInfoClient.queryByUniformNo(userMobileForm.getUniformNo(), SecurityConstants.FROM_IN);
		if (companyVendorInfoClientByUniformNo.isSuccess() && companyVendorInfoClientByUniformNo.hasBody() && ObjectUtil.isNotEmpty(companyVendorInfoClientByUniformNo.getResult())) {
			// 检查是企业是否被其他用户注册! 设置成平台来源！
			return R.fail("该企业已被注册！");
		}
		//1.注册用户
		boolean existed = userDetailService.isPhoneExist(userMobileForm.getMobile());
		UserDetail userDetail = userDetailService.getUserDetailByUserName(userMobileForm.getUserName());
		if (existed || AppUtils.isNotBlank(userDetail)) {
			return R.fail("用户名或手机号已存在!");
		}
		userMobileForm.setActivated(true);
		userMobileForm.setSource(4);
		User user = userDetailService.saveUserReg(IPHelper.getIp(), userMobileForm, passwordEncoder.encode(userMobileForm.getPassword()));
		if (user == null) {
			throw new BusinessException("用户注册失败！");
		}
		//发放新人代金券
		userCouponService.sendVoucherToUser(user.getId(), user.getName(), false);
		// 手机注册的用户，用户安全等级+1,手机验证变为1
		userDetailService.updatePhoneVerifn(user.getName());

		// 创建一个用户企业信息记录
		UserCompanyVendorInfo userCompanyVendorInfo = new UserCompanyVendorInfo();
		userCompanyVendorInfo.setVendorOwner(userMobileForm.getVendorOwner());
		userCompanyVendorInfo.setOwnerPhone(userMobileForm.getOwnerPhone());
		userCompanyVendorInfo.setRegisterAddr(userMobileForm.getRegisterAddr());
		userCompanyVendorInfo.setVendorAddr(userMobileForm.getVendorAddr());
		userCompanyVendorInfo.setLegalPersonIdentity(userMobileForm.getLegalPersonIdentity());
		userCompanyVendorInfo.setStatus("未知。请核实并更新");
		// 用户Id
		userCompanyVendorInfo.setUserId(user.getId());
		// 企业名称
		userCompanyVendorInfo.setFullName(userMobileForm.getFullName());
		// 社会信用代码号（企业税务号）
		userCompanyVendorInfo.setUniformNo(userMobileForm.getUniformNo());

		// 创建企业联系人
		RegsUserCompanyVendorInfoDTO regsUserCompanyVendorInfoDTO = new RegsUserCompanyVendorInfoDTO();
		BeanUtils.copyProperties(userCompanyVendorInfo, regsUserCompanyVendorInfoDTO);
		regsUserCompanyVendorInfoDTO.setAccountBookId(AccountBookEnum.XZL_ACCOUNT_BOOK.getAccountBookId().toString());
		// 保存企业信心和企业联系人信息
		R<UserCompanyVendorInfoDTO> res = this.userCompanyVendorInfoClient.regsUserCompanyVendorInfo(regsUserCompanyVendorInfoDTO, SecurityConstants.FROM_IN);
		if (!res.isSuccess()) {
			throw new RuntimeException("保存企业信息错误！");
		}
		userDetailService.updateUniformNoByUserId(user.getId(), userMobileForm.getUniformNo());
		// 上上签接口调用
		// 对接上上签
		AuthAccountDTO<CompanyCredentialDTO> authAccountDTO = new AuthAccountDTO<>();
		authAccountDTO.setAccount(userCompanyVendorInfo.getUniformNo());
		authAccountDTO.setUserId(user.getId());
		// 企业名称
		authAccountDTO.setName(userCompanyVendorInfo.getFullName());
		// 注册类型：企业
		authAccountDTO.setUserType(2L);
		CompanyCredentialDTO companyCredentialDTO = new CompanyCredentialDTO();
		// 法人电话
		companyCredentialDTO.setContactMobile(userCompanyVendorInfo.getOwnerPhone());
		// 税号
		companyCredentialDTO.setRegCode(userCompanyVendorInfo.getUniformNo());
		companyCredentialDTO.setOrgCode(userCompanyVendorInfo.getUniformNo());
		companyCredentialDTO.setTaxCode(userCompanyVendorInfo.getUniformNo());
		// 法人姓名
		companyCredentialDTO.setLegalPerson(userCompanyVendorInfo.getVendorOwner());
		companyCredentialDTO.setLegalPersonIdentityType("0");
		companyCredentialDTO.setLegalPersonIdentity(userMobileForm.getLegalPersonIdentity());
		companyCredentialDTO.setLegalPersonMobile(userMobileForm.getOwnerPhone());
		//
		authAccountDTO.setCredential(companyCredentialDTO);
		// 申请证书
		authAccountDTO.setApplyCert(1);

		authAccountDTO.setMobile(userMobileForm.getMobile());
		authAccountDTO.setPersonalMobile(userMobileForm.getMobile());
		Map resultMap = new HashMap();
		resultMap.put("userId", user.getId());
		resultMap.put("company", userMobileForm.getFullName());
		try {
			R<String> register = bestSignClient.register(authAccountDTO, SecurityConstants.FROM_IN);
			if (!register.isSuccess()) {
				log.error("上上签注册失败！");
			}
		} catch (Exception e) {
			log.error("上上签注册失败！");
		}
		return R.ok(resultMap);
	}


	@Override
	public R<RegCompanyInfo> getCompanyInfo(String uniformNo) {
		if (AppUtils.isBlank(uniformNo)) {
			return R.fail("请填写税号");
		}
		// 对接招标平台启信宝查询接口
		String result = "";
		// 加密
		String str = "qxbencrypt-"+uniformNo;
		log.info("加密前{}",str);
		String encrypt = CryptoUtil.encrypt(str);
		log.info("加密后{}",encrypt);
		String url = getCompanyInfo + "?orgCode=" + encrypt;
		log.info("招标平台启信宝查询接口请求开始 url:"+url);
		try {
			result = HttpUtil.httpGet(url);
		} catch (Exception e) {
			log.error("招标平台启信宝查询接口请求失败！请核实源接口url是否正确：" + url);
			return R.fail("招标平台启信宝查询接口请求失败！请核实源接口url是否正确：" + url);
		}

		log.info("招标平台启信宝查询接口：{}", result);
		Map<String, Map<String, String>> resultMap = JSONUtil.getMap(result);
		Map headerMap = resultMap.get("header");
		Map dataMap = resultMap.get("data");

		if (AppUtils.isBlank(headerMap) || !"0".equals(headerMap.get("ret")) || AppUtils.isBlank(dataMap)) {
			log.error("招标平台启信宝查询接口未查询到企业信息！请自助填写");
			return R.fail("招标平台启信宝查询接口未查询到企业信息！请自助填写");
		}

		// 封装企业信息
		// 公司名称
		String companyName = (String) dataMap.get("departName");
		if (AppUtils.isBlank(companyName)) {
			return R.fail("启信宝查询企业信息失败，请自行补充材料~");
		}
		//法人
		String vendorOwner = (String) dataMap.get("corporation");
		// 注册地址
		String address = (String) dataMap.get("address");

		RegCompanyInfo regCompanyInfo = new RegCompanyInfo();
		regCompanyInfo.setVendorOwner(vendorOwner);
		regCompanyInfo.setVendorAddr(address);
		regCompanyInfo.setRegisterAddr(address);
		regCompanyInfo.setFullName(companyName);

		return R.ok(regCompanyInfo);
	}
	
	@Override
	public R<String> getLoginFreeUrl(UserLoginFreeDTO userLoginFreeDTO) {
		UserMessageVO userMessageVO = userDetailService.getOpenUserByUserId(userLoginFreeDTO.getUserId());
		LoginFreeDTO loginFreeDTO = new LoginFreeDTO();
		LoginStaffInfo loginStaffInfo = new LoginStaffInfo();
		loginStaffInfo.setStaffNum(userMessageVO.getUser().getXcOpenId());
        loginStaffInfo.setStaffName(userMessageVO.getUser().getName());
        loginStaffInfo.setStaffPhone(userMessageVO.getMobile());
		loginFreeDTO.setStaffInfo(loginStaffInfo);
		BaseApiResponse<String> result = meituanApiService.loginFree(loginFreeDTO);
        return R.ok(result.getRealData());
    }
}
