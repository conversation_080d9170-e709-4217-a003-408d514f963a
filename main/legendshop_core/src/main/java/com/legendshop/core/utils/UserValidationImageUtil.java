package com.legendshop.core.utils;

import javax.servlet.http.HttpSession;

import com.legendshop.base.config.PropertiesUtilManager;
import com.legendshop.model.constant.AttributeKeys;
/**
 * 图片验证码是否需要验证
 *
 */
public class UserValidationImageUtil {

	// 是否需要验证码
	public static boolean needToValidation(HttpSession session) {
		if (session == null) {
			return false;
		}
		Integer count = (Integer) session.getAttribute(AttributeKeys.TRY_LOGIN_COUNT);

		Boolean needToValidation = true;
//		if (count == null || count < 3) {
//			needToValidation = false;
//		}
		Boolean result = PropertiesUtilManager.getPropertiesUtil().getValidationImage();
		return (result != null && result) && needToValidation;
	}
}
