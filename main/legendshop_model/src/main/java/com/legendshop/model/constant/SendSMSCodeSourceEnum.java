package com.legendshop.model.constant;

import com.legendshop.util.AppUtils;
import com.legendshop.util.constant.StringEnum;

/**
 * 发送短信验证码来源枚举
 */
public enum SendSMSCodeSourceEnum implements StringEnum{

	/** reg: 注册 */
	REG("reg"),
	
	/** reg: 第三方注册 */
	THIRD_REG("thirdReg"),

	/** 短信登录 */
	SMS_LOGIN("smsLogin"),
	
	/** 忘记密码 */
	FORGET("forget"),
	
	/** 修改手机号码 */
	UPDATE_PHONE("updatePhone"),
	
	/** 提现 */
	WITHDRAW("withdraw"),
	
	/** 修改登录密码 */
	UPDATE_PASSWD("updatePasswd"),
	
	/** 修改支付密码 */
	UPDATE_PAY_PASSWD("updatePayPasswd"),
	
	/** 绑定手机号 */
	BIND_PHONE("bindPhone"),
	
	/** 自己 */
	SELF("self"),

	/** 短信测试 */
	TEST("test"),

	/** 绑定员工卡 */
	BIND_ICARD("bindIcard"),

	/** 其他 */
		OTHER("other"),

	;
	
	private final String value;
	
	private SendSMSCodeSourceEnum(String value) {
		this.value = value;
	}

	@Override
	public String value() {
		return this.value;
	}
	
    public static Boolean inSendSMSCodeSourceEnum(String value) {
        for (SendSMSCodeSourceEnum sendSMSCodeSourceEnum : SendSMSCodeSourceEnum.values()) {
            if (sendSMSCodeSourceEnum.value().equals(value)) {
                return true;
            }
        }
        return false;
    }

	public static SendSMSCodeSourceEnum getCodeSourceEnum(String value) {
		if(AppUtils.isNotBlank(value)){
			if(SendSMSCodeSourceEnum.REG.value.equals(value)) return SendSMSCodeSourceEnum.REG;
			if(SendSMSCodeSourceEnum.THIRD_REG.value.equals(value)) return SendSMSCodeSourceEnum.THIRD_REG;
			if(SendSMSCodeSourceEnum.SMS_LOGIN.value.equals(value)) return SendSMSCodeSourceEnum.SMS_LOGIN;
			if(SendSMSCodeSourceEnum.FORGET.value.equals(value)) return SendSMSCodeSourceEnum.FORGET;
			if(SendSMSCodeSourceEnum.UPDATE_PHONE.value.equals(value)) return SendSMSCodeSourceEnum.UPDATE_PHONE;
			if(SendSMSCodeSourceEnum.WITHDRAW.value.equals(value)) return SendSMSCodeSourceEnum.WITHDRAW;
			if(SendSMSCodeSourceEnum.UPDATE_PASSWD.value.equals(value)) return SendSMSCodeSourceEnum.UPDATE_PASSWD;
			if(SendSMSCodeSourceEnum.UPDATE_PAY_PASSWD.value.equals(value)) return SendSMSCodeSourceEnum.UPDATE_PAY_PASSWD;
			if(SendSMSCodeSourceEnum.BIND_PHONE.value.equals(value)) return SendSMSCodeSourceEnum.BIND_PHONE;
			if(SendSMSCodeSourceEnum.SELF.value.equals(value)) return SendSMSCodeSourceEnum.SELF;
			if(SendSMSCodeSourceEnum.TEST.value.equals(value)) return SendSMSCodeSourceEnum.TEST;
			if(SendSMSCodeSourceEnum.BIND_ICARD.value.equals(value)) return SendSMSCodeSourceEnum.BIND_ICARD;
			if(SendSMSCodeSourceEnum.OTHER.value.equals(value)) return SendSMSCodeSourceEnum.OTHER;
		}
		return null;
	}
}
