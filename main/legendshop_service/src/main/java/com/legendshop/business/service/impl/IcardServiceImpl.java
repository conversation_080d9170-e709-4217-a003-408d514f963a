package com.legendshop.business.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.legendshop.common.core.base.ResResultManager;
import cn.legendshop.common.core.constant.SecurityConstants;
import cn.legendshop.common.core.exception.BusinessException;
import cn.legendshop.common.core.utils.R;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyConstant;
import cn.legendshop.common.rabbitmq.dto.ThirdPartyLogDTO;
import cn.legendshop.common.rabbitmq.utils.ThirdLogSendMsgUtil;
import cn.legendshop.user.api.client.ICardUserClient;
import cn.legendshop.user.api.dto.icard.ICardFlowDTO;
import cn.legendshop.user.api.dto.icard.ICardMsgDTO;
import com.legendshop.base.config.PropertiesUtil;
import com.legendshop.business.dao.IcardDao;
import com.legendshop.business.dao.UserDetailDao;
import com.legendshop.dao.support.EntityCriterion;
import com.legendshop.dao.support.PageSupport;
import com.legendshop.model.dto.IcardControl.AccInfoDTO;
import com.legendshop.model.dto.IcardControl.ICardUserInfoDTO;
import com.legendshop.model.dto.IcardControl.ICardUserInfoQuery;
import com.legendshop.model.entity.SubItem;
import com.legendshop.model.entity.UserDetail;
import com.legendshop.model.entity.icard.ICard;
import com.legendshop.spi.service.ICardService;
import com.legendshop.util.AppUtils;
import com.legendshop.util.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;


@Slf4j
@Service("icardService")
public class IcardServiceImpl implements ICardService {

	@Autowired
	private UserDetailDao userDetailDao;

	@Autowired
	private IcardDao icardDao;

	@Autowired
	private ThirdLogSendMsgUtil thirdLogSendMsgUtil;

	@Autowired
	private ICardUserClient iCardUserClient;

	/**
	 * 系统配置.
	 */
	@Autowired
	private PropertiesUtil propertiesUtil;

	//erp登录接口
	private final String erpLoginUrl = "https://dmzesb.nisco.cn/dmzesb/ZHCGW/BIGPURCHASESYS/services/IQCommonPostInterface?_pageId=iqjcForMroFunc&_action=erpLogin";

	@Override
	public List<SubItem> isUseIcard(Long[] orderIds) {
		return null;
	}

	/**
	 * 判断是否有员工卡
	 *
	 * @param userId
	 * @return
	 */
	@Override
	public Long getIdCard(String userId) {
		Long idCard = userDetailDao.getIcardByUserId(userId);
		if (idCard != null && idCard != 0) {
			return idCard;
		}
		return null;
	}

	/**
	 * pc端根据id查询员工卡流水
	 *
	 * @param iCardFlowDTO
	 * @return
	 */
	@Override
	public PageSupport<ICardFlowDTO> queryByCustNum(ICardFlowDTO iCardFlowDTO) {
		PageSupport<ICardFlowDTO> iCardFlowDTOPageSupport = icardDao.queryByCustNum(iCardFlowDTO);
		return iCardFlowDTOPageSupport;

	}

	@Override
	public ICard getByUserId(String userId) {
		return icardDao.getByUserId(userId);
	}

	@Override
	public Integer removeICard(String userId, Integer iCardId) {
		//不能删除员工卡信息， 用户解绑之后再绑定，原先的流水还需存在
		//icardDao.deleteById(Long.valueOf(iCardId));
		return icardDao.removeICard(userId, iCardId);
	}

	@Override
	public Integer bingdingICard(String userId, int icardId) {
		return icardDao.bingdingICard(userId, icardId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Object> bindingICard(ICard iCard, String userId) {
		if (!erpLogin(iCard.getJobNo(), iCard.getPassword())) {
			return ResResultManager.setResultError("工号或者密码错误!");
		}

		//判断该员工是否有员工卡
		R<ICardUserInfoDTO> result = iCardUserClient.queryUserInfo(iCard.getJobNo(), SecurityConstants.FROM_IN);
		if (!result.isSuccess()) {
			return ResResultManager.setResultError(result.getMsg());
		}

		//判断该员工卡是否其他人已经绑定
		ICard icardDaoByJobNo = icardDao.getByNo(iCard.getJobNo());
		if (icardDaoByJobNo != null) {
			log.info("找到员工卡信息");
			UserDetail icardId = userDetailDao.getByProperties(new EntityCriterion().eq("icardId", icardDaoByJobNo.getId()));
			if(icardId != null){
				log.info("员工卡ID绑定在用户信息上信息");
				return ResResultManager.setResultError("该员工卡已被绑定");
			}
//			if (!icardDaoByJobNo.getUserId().equals(userId)) {
//				return ResResultManager.setResultError("该员工卡已被绑定");
//			}
		}

		//员工卡信息
		ICardUserInfoDTO iCardUserInfoDTO = result.getResult();
		ICard findIcard = icardDao.getICard(iCard.getJobNo(), userId);
		Long cardId = 0L;
		if (Objects.isNull(findIcard)) {
			ICard card = new ICard();
			card.setUserId(userId);
			card.setIcardNumber(iCardUserInfoDTO.getSerialNum());
			card.setJobNo(iCardUserInfoDTO.getCustNum());
			card.setName(iCardUserInfoDTO.getCustName());
			card.setCompany("");
			card.setDepartment("");
			card.setOffice("");
			card.setDepartment("");
			card.setConsumeTotal(0.0);
			card.setCreateTime(new Date());
			cardId = icardDao.save(card);
		} else {
			//更新对应卡号
			cardId = findIcard.getId();
			findIcard.setIcardNumber(iCardUserInfoDTO.getSerialNum());
			findIcard.setUpdateTime(new Date());
			icardDao.update(findIcard);
		}

		//更新用户信息
		UserDetail userDetail = userDetailDao.getUserDetailById(userId);
		userDetail.setIcardId(cardId);
		userDetailDao.update(userDetail);
		return ResResultManager.setResultSuccess(cardId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Object> bindCardByMobile(String mobile, String jobNo, String userId) {

		//判断该员工是否有员工卡
		R<ICardUserInfoDTO> result = iCardUserClient.queryUserInfo(jobNo, SecurityConstants.FROM_IN);
		if (!result.isSuccess()) {
			return ResResultManager.setResultError(result.getMsg());
		}
		//员工卡信息
		ICardUserInfoDTO iCardUserInfoDTO = result.getResult();

		log.info("员工卡信息，食堂信息为：{},{}，入参信息为：{},{}",iCardUserInfoDTO.getMobile(), iCardUserInfoDTO.getCustNum(), mobile, jobNo);
		//判断该员工与员工卡在食堂数据库中是否对应
		if(!mobile.equals(iCardUserInfoDTO.getMobile())){
			return ResResultManager.setResultError("员工卡信息与手机号不匹配，绑定失败!");
		}

		//判断该员工卡是否其他人已经绑定
		ICard icardDaoByJobNo = icardDao.getByNo(jobNo);
		if (icardDaoByJobNo != null) {
			log.info("找到员工卡信息");
			UserDetail icardId = userDetailDao.getByProperties(new EntityCriterion().eq("icardId", icardDaoByJobNo.getId()));
			if(icardId != null){
				log.info("员工卡ID绑定在用户信息上信息");
				return ResResultManager.setResultError("该员工卡已被绑定");
			}
		}

		
		ICard findIcard = icardDao.getICard(jobNo, userId);
		Long cardId = 0L;
		if (Objects.isNull(findIcard)) {
			ICard card = new ICard();
			card.setUserId(userId);
			card.setIcardNumber(iCardUserInfoDTO.getSerialNum());
			card.setJobNo(iCardUserInfoDTO.getCustNum());
			card.setName(iCardUserInfoDTO.getCustName());
			card.setCompany("");
			card.setDepartment("");
			card.setOffice("");
			card.setDepartment("");
			card.setConsumeTotal(0.0);
			card.setCreateTime(new Date());
			cardId = icardDao.save(card);
		} else {
			//更新对应卡号
			cardId = findIcard.getId();
			findIcard.setIcardNumber(iCardUserInfoDTO.getSerialNum());
			findIcard.setUpdateTime(new Date());
			icardDao.update(findIcard);
		}

		//更新用户信息
		UserDetail userDetail = userDetailDao.getUserDetailById(userId);
		userDetail.setIcardId(cardId);
		userDetailDao.update(userDetail);
		return ResResultManager.setResultSuccess(cardId);
	}

	/**
	 * erp登录
	 *
	 * @param erpNo
	 * @param password
	 * @return
	 */
	private Boolean erpLogin(String erpNo, String password) {
		// 请求Erp接口，判断Erp员工工号与密码
		Map<String, Object> params = new HashMap<>(5);
		params.put("username", erpNo);
		params.put("password", password);
		String result = null;
		int httpStatus = 500;
		Map dataMap;
		String pattern = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])[A-Za-z\\d!@#$%^&*(),.?\":{}|<>]{8,20}$";
		if(StringUtils.isBlank(password) || !password.matches(pattern)) {
			throw new BusinessException("密码应为8-20位字母、须包含特殊字符、数字和大小写字母");
		}
		try {
			HttpRequest httpRequest = HttpUtil.createPost(erpLoginUrl);
			httpRequest.header("Content-Type", "application/x-www-form-urlencoded");
			httpRequest.form(params);
			HttpResponse httpResponse = httpRequest.execute();
			result = httpResponse.body();
			httpStatus = 200;
			if (AppUtils.isBlank(result)) {
				log.error("ERP员工登录,ERP接口请求链接错误");
				return false;
			}
			dataMap = JSONUtil.getMap(result);
		} catch (Exception e) {
			throw new BusinessException("ERP员工登录接口请求失败");
		} finally {
			thirdLogSendMsgUtil.sendThirdPartyLogMQ(new ThirdPartyLogDTO("legendshop-web-service",
				ThirdPartyConstant.ERP, erpLoginUrl, JSONUtil.getJson(params), httpStatus, result));
		}
		String statusCode = (String) dataMap.get("statusCode");
		String message = (String) dataMap.get("message");
		if (StringUtils.isBlank(statusCode) || !"200".equals(statusCode)) {
			log.info("ERP员工登录: " + message);
			throw new BusinessException("ERP员工登录接口请求失败, " + message);
		}
		return true;
	}


	@Override
	public BigDecimal totalPay(Long iCardId) {
		return icardDao.getTotalPay(iCardId);
	}

	@Override
	public BigDecimal totalRefund(Long iCardId) {
		return icardDao.totalRefund(iCardId);
	}

	@Override
	public List<Map> totalAmount() {
		return icardDao.totalAmount();
	}

	@Override
	public ICardFlowDTO getICardFlowDTO(Long idCard, Integer curPage, Integer type, Date startDate, Date endDate, Long valueOf) {
		ICardFlowDTO iCardFlowDTO = new ICardFlowDTO();

		BigDecimal totalRefund = totalRefund(idCard);
		BigDecimal totalPay = totalPay(idCard);
		if (totalRefund == null) {
			iCardFlowDTO.setTotalRefund(new BigDecimal("0.00"));
		} else {
			iCardFlowDTO.setTotalRefund(totalRefund);
		}

		if (totalPay == null) {
			iCardFlowDTO.setTotalPay(new BigDecimal("0.00"));
		} else {
			iCardFlowDTO.setTotalPay(totalPay);

		}

		iCardFlowDTO.setIcardId(idCard);
		iCardFlowDTO.setCurPage(curPage);
		iCardFlowDTO.setTypes(type);
		iCardFlowDTO.setStartDate(startDate);
		iCardFlowDTO.setEndDate(endDate);

		return iCardFlowDTO;
	}

	@Override
	public ICardFlowDTO getcurAmount(List<ICardFlowDTO> resultList, ICardFlowDTO iCardFlowDTO) {

		//当页支付
		BigDecimal curPay = new BigDecimal("0.00");

		//当页退款
		BigDecimal curRefund = new BigDecimal("0.00");
		for (ICardFlowDTO cardFlowDTO : resultList) {
			if (cardFlowDTO.getTypes() == 1 || cardFlowDTO.getTypes() == 0) {
				curPay = curPay.add(new BigDecimal(cardFlowDTO.getAmount()));
			} else if (cardFlowDTO.getTypes() == -1) {
				curRefund = curRefund.add(new BigDecimal(cardFlowDTO.getAmount()));
			}
		}
		iCardFlowDTO.setCurPay(curPay);
		iCardFlowDTO.setCurRefund(curRefund);
		return iCardFlowDTO;
	}

	@Override
	public R<String> queryCust(String jobNo, String userId) {
		ICardUserInfoQuery iCardUserInfoQuery = new ICardUserInfoQuery();
		iCardUserInfoQuery.setCustNum(jobNo);
		iCardUserInfoQuery.setCustThirdId(userId);
		return iCardUserClient.queryCust(iCardUserInfoQuery);
	}

	@Override
	public ICard getICardByUserId(String userId) {

		return icardDao.getByUserId(userId);
	}

	@Override
	public ICardFlowDTO getICardFlowDTO(Long iCardId) {
		ICardFlowDTO iCardFlowDTO = new ICardFlowDTO();

		BigDecimal totalRefund = totalRefund(iCardId);
		BigDecimal totalPay = totalPay(iCardId);
		if (totalRefund == null) {
			iCardFlowDTO.setTotalRefund(new BigDecimal("0.00"));
		} else {
			iCardFlowDTO.setTotalRefund(totalRefund);
		}

		if (totalPay == null) {
			iCardFlowDTO.setTotalPay(new BigDecimal("0.00"));
		} else {
			iCardFlowDTO.setTotalPay(totalPay);

		}
		return iCardFlowDTO;
	}

	@Override
	public List<ICardFlowDTO> queryDayCount() {
		List<ICardFlowDTO> iCardFlowDTOS = icardDao.queryDayCount();
		return iCardFlowDTOS;
	}


	@Override
	public PageSupport<ICardFlowDTO> appQueryByCustNum(ICardFlowDTO iCardFlowDTO) {

		List<ICardFlowDTO> iCardFlowDTOS = queryDayCount();
		iCardFlowDTO.setPageSize(iCardFlowDTOS.get(iCardFlowDTO.getCurPage() - 1).getDayCount());
		iCardFlowDTO.setOneDay(iCardFlowDTOS.get(iCardFlowDTO.getCurPage() - 1).getOneDay());
		iCardFlowDTO.setCurPage(1);
		PageSupport<ICardFlowDTO> iCardFlowDTOPageSupport = icardDao.queryByCustNum(iCardFlowDTO);
		return iCardFlowDTOPageSupport;

	}

	@Override
	public ICardMsgDTO queryAccInfo(String userId) {
		R<AccInfoDTO> accInfoDTOR = iCardUserClient.queryAccInfo(userId, SecurityConstants.FROM_IN);
		ICard iCardByUserId = getICardByUserId(userId);
		ICardMsgDTO iCardMsgDTO = new ICardMsgDTO();
		iCardMsgDTO.setCompany(iCardByUserId.getCompany());
		iCardMsgDTO.setName(iCardByUserId.getName());
		iCardMsgDTO.setDepartment(iCardByUserId.getDepartment());
		iCardMsgDTO.setIcardNumber(iCardByUserId.getIcardNumber());
		iCardMsgDTO.setJobNo(iCardByUserId.getJobNo());

		Double accbal = new Double(0.00);
		if (accInfoDTOR.isSuccess()) {
			accbal = Double.valueOf(accInfoDTOR.getResult().getAccBal()) / 100;
		}
		iCardMsgDTO.setAccBal(String.valueOf(accbal));
		iCardMsgDTO.setOffice(iCardByUserId.getOffice());
		iCardMsgDTO.setUserId(userId);
		iCardMsgDTO.setICardId(iCardByUserId.getId());

		BigDecimal totalRefund = totalRefund(iCardByUserId.getId());
		BigDecimal totalPay = totalPay(iCardByUserId.getId());
		iCardMsgDTO.setConsumeTotal(totalPay.subtract(totalRefund).doubleValue());
		return iCardMsgDTO;
	}

	@Override
	public BigDecimal getBalance() {
/*		//员工卡的金额计算
		// 用于计算统计
		ShopBillCount shopBillCount = new ShopBillCount();
		double iCard = shopBillCount.getiCard();
		double iReturnCard = shopBillCount.getiReturnCard();
		double iCardCommission = Arith.mul((iCard - iReturnCard), commissionRate);*/
		return null;
	}


	@Override
	public void refund(String number, String userId) {
		iCardUserClient.refund(number, userId, SecurityConstants.FROM_IN);
	}
}
